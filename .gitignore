# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.build/
.buildlog/
.history
.svn/
.swiftpm/
migrate_working_dir/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.packages
.pub-cache/
.pub/
/build/

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release
/upload-keystore.jks
images/app_icon.ico
installers/desktop_inno_script.iss
installers/we2up_windows_beta.exe
/installers/


# Firebase constants
/lib/data/db/firebase_constants.dart

# Flutter ephemeral files and symlinks
**/ephemeral/
**/.symlinks/
**/Pods/
**/Flutter.podspec
**/FlutterMacOS.podspec
ios/Podfile
macos/Podfile
ios/Runner.xcworkspace/
macos/Runner.xcworkspace/