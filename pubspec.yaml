name: we2up
description: An advanced Application for managing accounts, stores, projects and websites.

publish_to: "none" # Remove this line if you wish to publish to pub.dev

version: 3.6.55+97

environment:
  sdk: ^3.8.0

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  cupertino_icons: ^1.0.2
  bloc: ^9.0.0
  flutter_bloc: ^9.1.1
  dio: ^5.2.1+1
  google_fonts: ^6.1.0
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  flutter_localization: ^0.3.2
  intl: ^0.20.2
  sizer: ^3.0.0
  font_awesome_flutter: ^10.5.0
  salomon_bottom_bar: ^3.3.2
  cached_network_image: ^3.3.0
  expandable: ^5.0.1
  flutter_easyloading: ^3.0.5
  url_launcher: ^6.1.12
  equatable: ^2.0.5
  conditional_builder_null_safety: ^0.0.6
  badges: ^3.1.1
  hive_generator: ^2.0.0
  cron: ^0.6.1
  flutter_offline: ^5.0.0
  logger: ^2.4.0
  searchfield: ^1.1.6
  expansion_tile_card: ^3.0.0
  json_serializable: ^6.7.1
  json_annotation: ^4.8.1
  get_ip_address: ^0.0.6
  idkit_inputformatters: ^0.0.1
  pdf: ^3.10.4
  go_router: ^15.1.2
  printing: ^5.11.0
  image_picker: ^1.0.4
  internet_connection_checker: ^3.0.1
  dio_smart_retry: ^7.0.1
  responsive_builder: ^0.7.0
  responsive_grid: ^2.4.4
  flex_color_picker: ^3.3.0
  window_manager: ^0.5.0
  flutter_launcher_icons: ^0.14.1
  dropdown_button2: ^2.3.9
  gap: ^3.0.1
  geolocator: ^14.0.1
  maps_launcher: ^3.0.0
  #  assets_audio_player: ^3.1.1
  multi_select_flutter: ^4.1.3
  path_provider: ^2.1.2
  currency_picker: ^2.0.20
  package_info_plus: ^8.0.0
  firebase_crashlytics: ^4.3.6
  firebase_core: ^3.13.1
  dropdown_search: ^6.0.1
  image: ^4.1.7
  pdf_image_renderer: ^1.0.1
  uuid: ^4.5.1
  print_bluetooth_thermal: ^1.1.6
  permission_handler: ^12.0.0+1
  esc_pos_utils_plus: ^2.0.3
  restart_app: ^1.3.2
  flutter_pdfview: ^1.3.2
  mobile_scanner: ^7.0.0
  talker_dio_logger: ^4.4.1
  freezed: ^3.0.0-0.0.dev
  freezed_annotation: ^3.0.0
  flutter_native_splash: ^2.4.6
  app_tracking_transparency: ^2.0.6
  change_app_package_name: ^1.5.0
  just_audio: ^0.10.0
  inno_bundle: ^0.9.0
  flutter_hooks: ^0.20.5

# dependency_overrides:
#   uuid: ^3.0.7

dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^6.0.0
  build_runner: ^2.4.6

flutter:
  generate: true
  uses-material-design: true

  assets:
    - images/
    - sounds/

  fonts:
    - family: Amiri
      fonts:
        - asset: fonts/Amiri-Regular.ttf
    - family: Lalezar
      fonts:
        - asset: fonts/Lalezar-Regular.ttf
    - family: ReadexPro
      fonts:
        - asset: fonts/ReadexPro-Regular.ttf
    - family: Rubik
      fonts:
        - asset: fonts/Rubik-Regular.ttf
    - family: Almarai
      fonts:
        - asset: fonts/Almarai-Regular.ttf
    - family: Changa
      fonts:
        - asset: fonts/Changa-Regular.ttf
    - family: ElMessiri
      fonts:
        - asset: fonts/ElMessiri-Regular.ttf
    - family: Arial
      fonts:
        - asset: fonts/arial.ttf
        - asset: fonts/arial-bold.ttf
          weight: 700

# Inno Bundle Configuration
# To build the installer, run: flutter build windows && flutter pub run inno_bundle
inno_bundle:
  # Keep this GUID stable once you've shipped to users
  id: 2c7ffd7b-457a-5623-a965-13583ae66d86

  # Display names used throughout the installer and in Add-/Remove-programs
  name: "We2Up"
  publisher: "We2Up ERP"

  # Optional hyperlinks shown inside the wizard
  url: "https://we2up.com"          # "Publisher website"
  support_url: "mailto:<EMAIL>"

  # Long description (shows in the welcome page and metadata)
  description: |
    We2Up is an advanced application for managing accounts, stores, projects and websites.
    A comprehensive business management solution that helps you streamline your operations,
    track inventory, manage customer relationships, and generate detailed reports.
    Built with modern technology to provide a reliable and efficient platform for
    businesses of all sizes.

  # Legal / licence text
  # license_file: "LICENSE.txt"

  # Branding (ICO ≥ 256×256)
  installer_icon: "images/logo.ico"

  # Localisation
  languages:
    - english

  # Runtime + install scope
  arch: x64_compatible    # 64-bit binaries (still runs on WoW64 if needed)
  vc_redist: true         # bundles the VC++ runtime DLLs
  admin: auto             # lets user choose per-user or system-wide install

  # dart run inno_bundle:build --release