import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:we2up/utils/bloc_providers.dart';
import 'package:we2up/utils/color_schemes.g.dart';
import 'package:we2up/utils/custom_router.dart';
import 'package:we2up/data/db/db_manager.dart';
import 'package:we2up/utils/typography.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:sizer/sizer.dart';
import 'package:window_manager/window_manager.dart';
import 'package:flutter/foundation.dart';

import 'bloc_observer.dart';
import 'data/db/init_firebase.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  Bloc.observer = MyBlocObserver();
  await initHive();
  if (defaultTargetPlatform == TargetPlatform.windows || defaultTargetPlatform == TargetPlatform.macOS) {
    await Future.wait([
      windowManager.ensureInitialized(),
      windowManager.setMinimumSize(const Size(400, 600)),
      windowManager.maximize(),
    ]);
  } else {
    await initFirebase();
  }
  runApp(MyApp(appRouter: AppRouter()));
}

class MyApp extends StatelessWidget {
  const MyApp({super.key, required this.appRouter});

  final AppRouter appRouter;

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [...providers()],
      child: ValueListenableBuilder(
        valueListenable: Hive.box(settingsBox).listenable(),
        builder: (context, box, _) {
          return ResponsiveSizer(builder: (context, orientation, deviceType) {
            EasyLoading.instance.maskType = EasyLoadingMaskType.black;
            return MaterialApp.router(
              title: 'We2up',
              restorationScopeId: 'we2up_root_id',
              localizationsDelegates: AppLocalizations.localizationsDelegates,
              supportedLocales: AppLocalizations.supportedLocales,
              locale: Locale(language()),
              theme: ThemeData(
                useMaterial3: true,
                colorScheme: lightSchemes[currentThemeIndex()].copyWith(
                  primary:
                      primaryLight() != null ? Color(primaryLight()!) : null,
                  surface: backgroundLight() != null
                      ? Color(backgroundLight()!)
                      : null,
                ),
                textTheme: textTheme,
              ),
              darkTheme: ThemeData(
                useMaterial3: true,
                colorScheme: darkSchemes[currentThemeIndex()].copyWith(
                  primary: primaryDark() != null ? Color(primaryDark()!) : null,
                  surface: backgroundDark() != null
                      ? Color(backgroundDark()!)
                      : null,
                ),
                textTheme: textTheme,
              ),
              builder: EasyLoading.init(),
              themeMode: isDark() ? ThemeMode.dark : ThemeMode.light,
              routerConfig: appRouter.goRouter,
            );
          });
        },
      ),
    );
  }
}
