import 'package:flutter/services.dart';

class MinValueInputFormatter extends TextInputFormatter {
  final num? minValue;

  MinValueInputFormatter(this.minValue);

  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    final input = newValue.text;

    if (input.isEmpty) {
      // If the new value is empty, set it to the minimum value or return as is
      return TextEditingValue(
        text: minValue != null ? minValue.toString() : '',
        selection: const TextSelection.collapsed(offset: 0),
        composing: TextRange.empty,
      );
    }

    num parsedValue = num.tryParse(input) ?? 0;

    if (minValue != null && parsedValue < minValue!) {
      // If the input is less than the minimum value, convert it to the minimum value
      parsedValue = minValue!;
    }

    if (parsedValue == num.tryParse(oldValue.text) &&
        parsedValue != num.tryParse(newValue.text)) {
      // If the parsed value is the same as the previous value but different from the new value,
      // it means the user clicked outside the TextField. Update it to the minimum value if needed.
      return TextEditingValue(
        text: minValue != null ? minValue.toString() : '',
        selection: const TextSelection.collapsed(offset: 0),
        composing: TextRange.empty,
      );
    }

    final newText = parsedValue.toString();
    final selectionIndex = newText.length;

    return TextEditingValue(
      text: newText,
      selection: TextSelection.collapsed(offset: selectionIndex),
      composing: TextRange.empty,
    );
  }
}