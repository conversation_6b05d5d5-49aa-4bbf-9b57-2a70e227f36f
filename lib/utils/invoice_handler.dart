import 'package:dropdown_search/dropdown_search.dart';
import 'package:expandable/expandable.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:go_router/go_router.dart';
import 'package:we2up/utils/we2up_constants.dart';

import '../data/models/product.dart';
import '../bloc/contacts/contacts_cubit.dart';
import '../bloc/products/products_cubit.dart';
import '../data/db/db_manager.dart';
import '../utils/route_constants.dart';

class InvoiceHandler extends StatelessWidget {
  const InvoiceHandler({
    super.key,
    required this.child,
    required this.focusNode,
    required this.productsDropdownKey,
  });

  final Widget child;
  final FocusNode focusNode;
  final GlobalKey<DropdownSearchState<Product?>> productsDropdownKey;

  void _handleKeyEvent(KeyEvent event, BuildContext context) {
    if (event is! KeyDownEvent) return;

    final cubit = ProductsCubit.get(context);
    final isShiftPressed = event.logicalKey == LogicalKeyboardKey.shift ||
        HardwareKeyboard.instance.logicalKeysPressed.contains(LogicalKeyboardKey.shiftLeft) ||
        HardwareKeyboard.instance.logicalKeysPressed.contains(LogicalKeyboardKey.shiftRight);
    final isCtrlPressed = HardwareKeyboard.instance.logicalKeysPressed.contains(LogicalKeyboardKey.controlLeft) ||
        HardwareKeyboard.instance.logicalKeysPressed.contains(LogicalKeyboardKey.controlRight);

    // Handle keyboard shortcuts for bill actions
    switch (event.logicalKey) {
      case LogicalKeyboardKey.f1:
        // F1 -> Cash Sale (full payment with print)
        _saveCashSale(cubit, context);
        break;
      case LogicalKeyboardKey.f2:
        // F2 -> Partial Sale (partial payment without print)
        _savePartialSale(cubit, context);
        break;
      case LogicalKeyboardKey.f3:
        // F3 -> Credit Sale (no payment, on credit)
        _saveCreditSale(cubit, context);
        break;
      case LogicalKeyboardKey.f4:
        // F4 -> Show payment methods
        _showPaymentMethods(cubit, context);
        break;
      case LogicalKeyboardKey.f5:
        // F5 -> Open discount section
        _openDiscountSection(cubit, context);
        break;
      case LogicalKeyboardKey.f6:
        // F6 -> Save as quotation
        _saveAsQuotation(cubit, context);
        break;
      case LogicalKeyboardKey.f7:
        // F7 -> New bill (clear current bill)
        _deleteBillAndClear(cubit, context);
        break;
      case LogicalKeyboardKey.delete:
        // Shift + Delete -> Delete bill and clear info
        if (isShiftPressed) {
          _deleteBillAndClear(cubit, context);
        }
        break;
      case LogicalKeyboardKey.tab:
        // Tab -> Cycle through panels
        if (!_isCurrentlyInTextField(context)) {
          cubit.cycleThroughPanels();
        }
        break;
      case LogicalKeyboardKey.enter:
        // Enter -> Open product dropdown (anywhere on page)
        if (!_isCurrentlyInTextField(context)) {
          productsDropdownKey.currentState?.openDropDownSearch();
        }
        break;
      case LogicalKeyboardKey.f8:
        // F8 -> Open product dropdown (Find product)
        productsDropdownKey.currentState?.openDropDownSearch();
        break;
      case LogicalKeyboardKey.equal:
      case LogicalKeyboardKey.numpadAdd:
        // + key -> Increment number in text field
        if (_isCurrentlyInNumberTextField(context)) {
          _handlePlusKey(cubit, context);
        }
        break;
      case LogicalKeyboardKey.minus:
      case LogicalKeyboardKey.numpadSubtract:
        // - key -> Decrement number in text field
        if (_isCurrentlyInNumberTextField(context)) {
          _handleMinusKey(cubit, context);
        }
        break;
      case LogicalKeyboardKey.f9:
        // F9 -> Toggle panels (expand/collapse)
        _togglePanels(cubit);
        break;
      case LogicalKeyboardKey.keyC:
        if (isCtrlPressed && isShiftPressed) {
          // Ctrl+Shift+C -> Add customer/contact
          _addCustomer(context);
        }
        break;
      case LogicalKeyboardKey.escape:
        // Escape -> Clear focus or close dropdowns
        FocusScope.of(context).unfocus();
        break;
    }
  }

  bool _isCurrentlyInTextField(BuildContext context) {
    final focusedNode = FocusScope.of(context).focusedChild;
    if (focusedNode?.context != null) {
      final widgetElement = focusedNode!.context as Element?;
      if (widgetElement?.widget != null) {
        final widget = widgetElement!.widget;
        return widget is TextField || widget is TextFormField;
      }
    }
    return false;
  }

  bool _isCurrentlyInNumberTextField(BuildContext context) {
    final focusedNode = FocusScope.of(context).focusedChild;
    if (focusedNode?.context != null) {
      final widgetElement = focusedNode!.context as Element?;
      if (widgetElement?.widget is TextField) {
        final textField = widgetElement!.widget as TextField;
        return textField.keyboardType == TextInputType.number ||
            textField.keyboardType == const TextInputType.numberWithOptions(decimal: true);
      }
    }
    return false;
  }

  void _handlePlusKey(ProductsCubit cubit, BuildContext context) {
    final productId = _getFocusedProductId(cubit, context);
    if (productId != null) {
      cubit.incrementNumber(productId);
    }
  }

  void _handleMinusKey(ProductsCubit cubit, BuildContext context) {
    final productId = _getFocusedProductId(cubit, context);
    if (productId != null) {
      cubit.decrementNumber(productId);
    }
  }

  int? _getFocusedProductId(ProductsCubit cubit, BuildContext context) {
    // For simplicity, when +/- is pressed in a number field, we'll increment/decrement
    // the first active product in the cart. In a more complex implementation,
    // you could track which specific product quantity field is focused.
    if (cubit.cartProducts.isNotEmpty) {
      return cubit.cartProducts.first;
    }
    return null;
  }

  void _saveCashSale(ProductsCubit cubit, BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    final validationResult = _validateSaveConditions(cubit, context, isCashSale: true);
    if (validationResult.isValid) {
      // Ensure printing is enabled
      if (!cubit.printCart) {
        cubit.changePrintCart();
      }
      cubit.sendSellToAPI(context, cash: true);
      EasyLoading.showSuccess(strings.cash_sale_completed);
    } else {
      _showLocalizedError(validationResult.errorMessage, strings);
    }
  }

  void _savePartialSale(ProductsCubit cubit, BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    final validationResult = _validateSaveConditions(cubit, context, isPartialSale: true);
    if (validationResult.isValid) {
      // Disable printing for partial sales
      if (cubit.printCart) {
        cubit.changePrintCart();
      }
      cubit.sendSellToAPI(context);
      // Re-enable printing for next time
      if (!cubit.printCart) {
        cubit.changePrintCart();
      }
      EasyLoading.showSuccess(strings.partial_sale_completed);
    } else {
      _showLocalizedError(validationResult.errorMessage, strings);
    }
  }

  void _saveCreditSale(ProductsCubit cubit, BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    final validationResult = _validateSaveConditions(cubit, context, isCreditSale: true);
    if (validationResult.isValid) {
      // Disable printing for credit sales
      if (cubit.printCart) {
        cubit.changePrintCart();
      }
      cubit.sendSellToAPI(context, credit: true);
      // Re-enable printing for next time
      if (!cubit.printCart) {
        cubit.changePrintCart();
      }
      EasyLoading.showSuccess(strings.credit_sale_completed);
    } else {
      _showLocalizedError(validationResult.errorMessage, strings);
    }
  }



  void _showLocalizedError(String errorMessage, AppLocalizations strings) {
    // Try to localize common error messages
    String localizedMessage = errorMessage;

    if (errorMessage.contains('customer') || errorMessage.contains('Customer')) {
      localizedMessage = strings.validation_customer_required;
    } else if (errorMessage.contains('product') || errorMessage.contains('Product')) {
      localizedMessage = strings.validation_products_required;
    } else if (errorMessage.contains('payment') || errorMessage.contains('Payment')) {
      localizedMessage = strings.validation_payment_accounts_required;
    } else if (errorMessage.contains('stock') || errorMessage.contains('Stock')) {
      localizedMessage = strings.validation_insufficient_stock;
    } else if (errorMessage.contains('permission') || errorMessage.contains('Permission')) {
      localizedMessage = strings.validation_quotation_permission;
    }

    EasyLoading.showError(localizedMessage);
  }

  void _showPaymentMethods(ProductsCubit cubit, BuildContext context) {
    // Force expand the payment data panel
    cubit.forceExpandPaymentDataPanel();
    
    // Auto-focus on payment amount field for Windows/Mac only
    if (defaultTargetPlatform == TargetPlatform.windows || defaultTargetPlatform == TargetPlatform.macOS) {
      _focusPaymentFieldWithRetry(cubit, context);
    }
  }

  void _openDiscountSection(ProductsCubit cubit, BuildContext context) {
    // Force expand the shipping and taxes panel
    cubit.forceExpandShippingAndTaxesPanel();
    
    // Auto-focus on discount amount field for Windows/Mac only
    if (defaultTargetPlatform == TargetPlatform.windows || defaultTargetPlatform == TargetPlatform.macOS) {
      _focusDiscountFieldWithRetry(cubit, context);
    }
  }

  void _focusPaymentFieldWithRetry(ProductsCubit cubit, BuildContext context) {
    _retryFocus(
      focusNode: cubit.paymentFieldFocusNode,
      panelController: cubit.paymentDataPanelController,
      fieldName: 'payment',
      maxRetries: 3,
    );
  }

  void _focusDiscountFieldWithRetry(ProductsCubit cubit, BuildContext context) {
    _retryFocus(
      focusNode: cubit.discountFieldFocusNode,
      panelController: cubit.shippingAndTaxesPanelController,
      fieldName: 'discount',
      maxRetries: 3,
    );
  }

  void _retryFocus({
    required FocusNode focusNode,
    required ExpandableController panelController,
    required String fieldName,
    required int maxRetries,
    int retryCount = 0,
  }) {
    if (retryCount >= maxRetries) {
      return;
    }

    // Wait for panel to be collapsed and animation to complete
    Future.delayed(Duration(milliseconds: 200 + (retryCount * 100)), () {
      final isPanelCollapsed = !panelController.expanded;
      final canRequestFocus = focusNode.canRequestFocus;
      
      if (isPanelCollapsed && canRequestFocus) {
        // Use post frame callback to ensure widget is fully rendered
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (focusNode.canRequestFocus) {
            focusNode.requestFocus();
          }
        });
      } else {
        _retryFocus(
          focusNode: focusNode,
          panelController: panelController,
          fieldName: fieldName,
          maxRetries: maxRetries,
          retryCount: retryCount + 1,
        );
      }
    });
  }

  void _saveAsQuotation(ProductsCubit cubit, BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    final validationResult = _validateSaveConditions(cubit, context, isQuotation: true);
    if (validationResult.isValid) {
      cubit.sendSellToAPI(context, isQuotation: 1);
      EasyLoading.showSuccess(strings.quotation_created);
    } else {
      _showLocalizedError(validationResult.errorMessage, strings);
    }
  }

  void _togglePanels(ProductsCubit cubit) {
    // Cycle through panels - close current and open next
    cubit.cycleThroughPanels();
  }

  void _addCustomer(BuildContext context) {
    // Navigate to add customer screen if available
    if (canCreateCustomer()) {
      context.push(addEditContact, extra: (null, ContactsCubit(), true));
    }
  }

  _ValidationResult _validateSaveConditions(ProductsCubit cubit, BuildContext context, {
    bool isPartialSale = false,
    bool isCashSale = false,
    bool isCreditSale = false,
    bool isQuotation = false,
  }) {
    final strings = AppLocalizations.of(context)!;
    
    // Check if customer is selected
    if (cubit.cartCustomerContact == null) {
      return _ValidationResult(false, strings.validation_customer_required);
    }

    // Check if cart has products
    if (cubit.cartProducts.isEmpty) {
      return _ValidationResult(false, strings.validation_products_required);
    }

    // Check if quotation permissions for quotation save
    if (isQuotation && !canOfferPriceDuringSales()) {
      return _ValidationResult(false, strings.validation_quotation_permission);
    }

    // Skip further validation for quotations
    if (isQuotation) {
      return _ValidationResult(true, '');
    }

    // Check if default customer is being used (cannot sell on credit to default customer)
    if (cubit.cartCustomerContact?.isDefault == 1 && (isCreditSale || (!isCashSale && !isPartialSale))) {
      return _ValidationResult(false, strings.validation_credit_default_customer);
    }

    // Check commission agent requirement
    final isCommissionAgentRequired = ((loginData.isCommissionAgentRequired == null ||
            !loginData.isCommissionAgentRequired!) ||
        (loginData.isCommissionAgentRequired! && cubit.commAgent != null));
    
    if (!isCommissionAgentRequired) {
      return _ValidationResult(false, strings.validation_commission_agent_required);
    }

    // Check stock availability for all products
    for (int productId in cubit.cartProducts) {
      if (!(cubit.availableInStock(productId) > 0 || cubit.allowOverSelling(productId))) {
        return _ValidationResult(false, strings.validation_insufficient_stock);
      }
    }

    // Check payment accounts are selected
    for (var paymentDetail in cubit.paymentDetailsList) {
      if (paymentDetail.paymentAccount == null) {
        return _ValidationResult(false, strings.validation_payment_accounts_required);
      }
    }

    // For partial sales, check payment amounts are valid
    if (isPartialSale) {
      for (var paymentDetail in cubit.paymentDetailsList) {
        final amount = double.tryParse(paymentDetail.invoiceController.text) ?? 0;
        if (amount <= 0) {
          return _ValidationResult(false, strings.validation_payment_amounts_required);
        }
      }
    }

    return _ValidationResult(true, '');
  }

  void _deleteBillAndClear(ProductsCubit cubit, BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    // Show confirmation dialog first
    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: Text('${strings.delete} ${strings.bill_of_sale}'),
          content: Text(strings.delete_question),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(),
              child: Text(strings.cancel),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(dialogContext).pop();
                cubit.clearBillAndCart();
              },
              style: TextButton.styleFrom(foregroundColor: Colors.red),
              child: Text(strings.delete),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return KeyboardListener(
      autofocus: true,
      focusNode: focusNode,
      onKeyEvent: (event) => _handleKeyEvent(event, context),
      child: PopScope(
        canPop: false,
        onPopInvokedWithResult: (bool didPop, dynamic result) async {
          if (didPop) return;
          bool? shouldPop = await showBillOfSaleExitDialog(context);
          if (shouldPop ?? false) {
            if (context.mounted) context.pop();
          }
        },
        child: GestureDetector(
          onTap: (defaultTargetPlatform != TargetPlatform.windows && defaultTargetPlatform != TargetPlatform.macOS)
              ? FocusManager.instance.primaryFocus?.unfocus
              : () => FocusScope.of(context).requestFocus(focusNode),
          child: child,
        ),
      ),
    );
  }
}

class _ValidationResult {
  final bool isValid;
  final String errorMessage;

  _ValidationResult(this.isValid, this.errorMessage);
}
