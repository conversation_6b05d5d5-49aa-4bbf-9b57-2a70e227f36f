import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:we2up/bloc/contacts/contacts_cubit.dart';
import 'package:we2up/bloc/crm/crm_cubit.dart';
import 'package:we2up/bloc/expenses/expenses_cubit.dart';
import 'package:we2up/bloc/location/location_cubit.dart';
import 'package:we2up/bloc/payments_report/contact_payment_report_cubit.dart';
import 'package:we2up/bloc/profit_loss/profit_loss_cubit.dart';
import 'package:we2up/bloc/shifts/shifts_cubit.dart';
import 'package:we2up/data/models/bill_of_sale_data.dart';
import 'package:we2up/data/models/contact.dart';
import 'package:we2up/data/models/follow_up.dart';
import 'package:we2up/presentation/screens/add_edit_contact.dart';
import 'package:we2up/presentation/screens/bill_of_sale.dart';
import 'package:we2up/presentation/screens/cart.dart';
import 'package:we2up/presentation/screens/contacts.dart';
import 'package:we2up/presentation/screens/customer.dart';
import 'package:we2up/presentation/screens/expenses.dart';
import 'package:we2up/presentation/screens/follow_ups.dart';
import 'package:we2up/presentation/screens/locations/itinerary.dart';
import 'package:we2up/presentation/screens/locations/location_settings.dart';
import 'package:we2up/presentation/screens/locations/locations_home.dart';
import 'package:we2up/presentation/screens/login.dart';
import 'package:we2up/presentation/screens/login_intro.dart';
import 'package:we2up/presentation/screens/payment_page.dart';
import 'package:we2up/presentation/screens/contact_payment.dart';
import 'package:we2up/presentation/screens/new_sale_return.dart';
import 'package:we2up/presentation/screens/pdf_view_page.dart';
import 'package:we2up/presentation/screens/shift_details_page.dart';
import 'package:we2up/presentation/screens/shifts.dart';
import 'package:we2up/presentation/screens/shipments.dart';
import 'package:we2up/utils/route_constants.dart';
import 'package:we2up/utils/we2up_constants.dart';

import '../bloc/auth/auth_cubit.dart';
import '../bloc/business_settings/business_settings_cubit.dart';
import '../bloc/single_product/single_product_cubit.dart';
import '../data/models/business_location.dart';
import '../data/models/expense.dart';
import '../navigation_observer.dart';
import '../presentation/screens/add_edit_follow_up.dart';
import '../presentation/screens/add_edit_expense.dart';
import '../presentation/screens/business_data.dart';
import '../presentation/screens/contact_dues.dart';
import '../presentation/screens/contact_payments_report.dart';
import '../presentation/screens/label_printer.dart';
import '../presentation/screens/landing.dart';
import '../presentation/screens/locations/change_branch_location.dart';
import '../presentation/screens/locations/location_search.dart';
import '../presentation/screens/locations/user_locations.dart';
import '../presentation/screens/new_product.dart';
import '../presentation/screens/profit_loss.dart';
import '../presentation/screens/purchase_invoice.dart';
import '../presentation/screens/purchases.dart';
import '../presentation/screens/new_purchase_return.dart';
import '../presentation/screens/purchases_returns_list.dart';
import '../presentation/screens/sales_returns.dart';
import '../data/db/db_manager.dart';

class AppRouter {
  GoRouter goRouter = GoRouter(
    initialLocation:
        (loginData.accessToken == null && businessDetailsBox.values.isEmpty) ||
                !canGoToHomeBasedOnShift()
            ? loginIntro
            : landingScreen,
    observers: [We2UpNavigationObserver()],
    routes: <RouteBase>[
      GoRoute(
        path: landingScreen,
        builder: (context, state) => const LandingScreen(),
      ),
      GoRoute(
        path: cartScreen,
        builder: (context, state) => const Cart(),
      ),
      GoRoute(
        path: chooseCustomer,
        builder: (context, state) => const ChooseCustomer(),
      ),
      GoRoute(
        path: payment,
        builder: (context, state) => const PaymentPage(),
      ),
      GoRoute(
        path: expensesPage,
        builder: (context, state) => BlocProvider(
          create: (context) => ExpensesCubit(),
          child: const ExpensesPage(),
        ),
      ),
      GoRoute(
        path: addExpensePage,
        builder: (context, state) {
          var eCubit = state.extra as (ExpensesCubit? cubit, Expense? expense)?;
          if (eCubit?.$1 != null) {
            eCubit!.$1!.setInitialDataForEdit(eCubit.$2);
            return BlocProvider.value(
              value: eCubit.$1!,
              child: const AddEditExpensePage(),
            );
          } else {
            return BlocProvider(
              create: (context) => ExpensesCubit(editExpense: eCubit?.$2),
              child: const AddEditExpensePage(),
            );
          }
        },
      ),
      GoRoute(
        path: saleReturn,
        builder: (context, state) => const NewSaleReturn(),
      ),
      GoRoute(
        path: purchaseReturn,
        builder: (context, state) => const NewPurchaseReturn(),
      ),
      GoRoute(
        path: billOfSale,
        builder: (context, state) {
          BillData billOfSaleData = state.extra as BillData;
          return BillOfSale(billOfSaleData: billOfSaleData);
        },
      ),
      GoRoute(
        path: contactPaymentScreen,
        builder: (context, state) {
          bool isCustomer = state.extra as bool;
          return ContactPayment(isCustomer: isCustomer);
        },
      ),
      GoRoute(
        path: login,
        builder: (context, state) => BlocProvider(
          create: (context) => AuthCubit(),
          child: const LoginScreen(),
        ),
      ),
      GoRoute(
        path: loginIntro,
        builder: (context, state) => const LoginIntroScreen(),
      ),
      GoRoute(
        path: contactsScreen,
        builder: (context, state) {
          return BlocProvider(
            create: (context) => ContactsCubit(),
            child: const ContactsPage(),
          );
        },
      ),
      GoRoute(
        path: addEditContact,
        builder: (context, state) {
          final extra = state.extra as (
            Contact? contact,
            ContactsCubit cubit,
            bool fromDataPanel,
          );
          final AddEditContact page = AddEditContact(contact: extra.$1);
          if (extra.$3) {
            return BlocProvider(create: (_) => extra.$2, child: page);
          } else {
            return BlocProvider.value(value: extra.$2, child: page);
          }
        },
      ),
      GoRoute(
        path: contactDues,
        builder: (context, state) {
          bool? supplier = state.extra as bool?;
          return BlocProvider(
            create: (context) => ContactsCubit(),
            child: ContactsDues(supplier: supplier != null),
          );
        },
      ),
      GoRoute(
        path: salesReturns,
        builder: (context, state) => const SalesReturns(),
      ),
      GoRoute(
        path: purchaseInvoice,
        builder: (context, state) {
          BillData billOfSaleData = state.extra as BillData;
          return PurchaseInvoice(billOfSaleData: billOfSaleData);
        },
      ),
      GoRoute(
        path: followUpsPage,
        builder: (context, state) {
          return BlocProvider(
            create: (context) => CrmCubit(),
            child: const FollowUpsPage(),
          );
        },
      ),
      GoRoute(
        path: addEditFollowUp,
        builder: (context, state) {
          final extra = state.extra as (FollowUp? followUp, CrmCubit cubit);
          return BlocProvider.value(
            value: extra.$2,
            child: AddEditFollowUp(followup: extra.$1),
          );
        },
      ),
      GoRoute(
        path: purchasesPage,
        builder: (context, state) => const Purchases(),
      ),
      GoRoute(
        path: shipmentsPage,
        builder: (context, state) => const Shipments(),
      ),
      GoRoute(
        path: businessDataPage,
        builder: (context, state) => BlocProvider(
          create: (context) => BusinessSettingsCubit(),
          child: BusinessDataPage(),
        ),
      ),
      GoRoute(
        path: purchasesReturnPage,
        builder: (context, state) => const PurchasesReturnPage(),
      ),
      GoRoute(
        path: addProductPage,
        builder: (context, state) => BlocProvider(
          create: (context) => SingleProductCubit(
            state.extra as BusinessLocation,
          ),
          child: const AddProduct(),
        ),
      ),
      GoRoute(
        path: locations,
        builder: (context, state) => BlocProvider(
          create: (context) => LocationCubit(),
          child: const LocationsScreen(),
        ),
      ),
      GoRoute(
        path: itinerary,
        builder: (context, state) => BlocProvider.value(
          value: state.extra as LocationCubit,
          child: const ItineraryScreen(),
        ),
      ),
      GoRoute(
        path: locationSettings,
        builder: (context, state) => BlocProvider.value(
          value: state.extra as LocationCubit,
          child: const LocationSettings(),
        ),
      ),
      GoRoute(
        path: changeBranchLocation,
        builder: (context, state) => BlocProvider.value(
          value: state.extra as LocationCubit,
          child: const ChangeBranchLocation(),
        ),
      ),
      GoRoute(
        path: userLocationScreen,
        builder: (context, state) => BlocProvider.value(
          value: state.extra as LocationCubit,
          child: const UserLocationsScreen(),
        ),
      ),
      GoRoute(
        path: locationSearchScreen,
        builder: (context, state) => BlocProvider.value(
          value: state.extra as LocationCubit,
          child: const LocationSearch(),
        ),
      ),
      // GoRoute(
      //   path: webViewPage,
      //   builder: (context, state) => const WebViewPage(),
      // ),
      GoRoute(
        path: profitLossScreen,
        builder: (context, state) {
          return BlocProvider(
            create: (context) => ProfitLossCubit(),
            child: const ProfitLossScreen(),
          );
        },
      ),
      GoRoute(
        path: shiftDetailsPage,
        builder: (context, state) {
          return BlocProvider.value(
            value: state.extra! as ShiftsCubit,
            child: const ShiftDetailsPage(),
          );
        },
      ),
      GoRoute(
        path: shiftsScreen,
        builder: (context, state) {
          return BlocProvider(
            create: (context) => ShiftsCubit(),
            child: const Shifts(),
          );
        },
      ),
      GoRoute(
        path: paymentsReportScreen,
        builder: (context, state) {
          bool supplier = state.extra as bool;
          return BlocProvider(
            create: (context) => PaymentsReportCubit(),
            child: ContactPaymentReportScreen(supplier: supplier),
          );
        },
      ),
      GoRoute(
        path: labelPrinterScreen,
        builder: (context, state) => const LabelPrinterScreen(),
      ),
      GoRoute(
        path: pdfViewPage,
        builder: (context, state) => PDFViewPage(
          pdfData: state.extra as dynamic,
        ),
      ),
    ],
  );
}
