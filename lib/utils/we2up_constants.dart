import 'dart:developer';
import 'dart:io';

import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:geolocator/geolocator.dart';
import 'package:go_router/go_router.dart';
import 'package:hive/hive.dart';
import 'package:intl/intl.dart';
import 'package:maps_launcher/maps_launcher.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:we2up/bloc/products/products_cubit.dart';
import 'package:we2up/data/models/location_info.dart';
import 'package:we2up/data/models/product.dart';
import 'package:we2up/data/models/sell.dart';
import 'package:we2up/data/models/shipment_status.dart';
import 'package:we2up/data/repository/api_repo.dart';

import '../data/models/base_url.dart';
import '../data/db/db_manager.dart';
import '../data/models/bill_of_sale_data.dart';
import '../data/models/business_settings.dart';
import '../data/models/contact_payment_model.dart';
import '../data/models/payment_status.dart';
import '../presentation/widgets/product_number_text_field.dart';

const String businessSettingsDataKey = "businessSettingsBoxKey";
const String locationRange = "locationRange";
const String loginDataKey = "loginDataKey";
const String userAndPasswordBox = "userAndPassword";

List<BaseUrl> baseURLs = [
  BaseUrl(
    name: "Original",
    url: "https://oriapk.we2up.com/",
    id: defaultTargetPlatform == TargetPlatform.android
        ? "1"
        : defaultTargetPlatform == TargetPlatform.iOS
            ? "30"
            : "31",
    secret: defaultTargetPlatform == TargetPlatform.android
        ? "eRlRNvv0AysBul2S5YVZmpskOTmWfdg0b1pYHclf"
        : defaultTargetPlatform == TargetPlatform.iOS
            ? "8Pw30o7LOgLdPxXtTh1EqKUNfA4kCQ6UweHk0kmT"
            : "PjooxPWEA8oO58RF7SvXcVGfLTZ9SoyXCNJJqTXW",
  ),
  BaseUrl(
    name: "App",
    url: "https://appapk.we2up.com/",
    id: defaultTargetPlatform == TargetPlatform.android
        ? "1"
        : defaultTargetPlatform == TargetPlatform.iOS
            ? "2"
            : "5",
    secret: defaultTargetPlatform == TargetPlatform.android
        ? "WXfCquLSn1Ees8d3edvARnWPfMr2Uy9kubEs6Svy"
        : defaultTargetPlatform == TargetPlatform.iOS
            ? "CxTlTZ7a1kmmPZ1hKqxqkmUvpYRuqIgh58XaKO55"
            : "lSybbXeYXLKO34RO1EH1lqSs5yZxXnzi2w99gLv2",
  ),
  BaseUrl(
    name: "UAE",
    url: "https://appksa.we2up.com/",
    id: defaultTargetPlatform == TargetPlatform.android
        ? "1"
        : defaultTargetPlatform == TargetPlatform.iOS
            ? "2"
            : "3",
    secret: defaultTargetPlatform == TargetPlatform.android
        ? "VTfQpq7gV1qOx4S2duU4ZnLhmgN5hzVESNv3jZ4X"
        : defaultTargetPlatform == TargetPlatform.iOS
            ? "8kFqromagpxVnkiBpN0GHmow9k9BKU8CqCmpq6cB"
            : "Wko1h8zJWiDhg0Ycu8qaE10pMyJVp3orhZltzMQR",
  ),
  BaseUrl(
    name: "App.fahm",
    url: "https://appapk.fahmerp.com/",
    id: defaultTargetPlatform == TargetPlatform.android
        ? "7"
        : defaultTargetPlatform == TargetPlatform.iOS
            ? "8"
            : "9",
    secret: defaultTargetPlatform == TargetPlatform.android
        ? "f001s1SfTBpQHXBDQ2a8LdzMJZ5ghCM97QnbgMAS"
        : defaultTargetPlatform == TargetPlatform.iOS
            ? "YFUfhTwVM6JPurknEOBKy7aPJTO2EX4RRqQ3XJDB"
            : "Bk41KsMono4ORTsMC51YFa2f4fTeb222ceVNS1e4",
  ),
  BaseUrl(
    name: "New.fahm",
    url: "https://newapk.fahmerp.com/",
    id: defaultTargetPlatform == TargetPlatform.android
        ? "7"
        : defaultTargetPlatform == TargetPlatform.iOS
            ? "8"
            : "9",
    secret: defaultTargetPlatform == TargetPlatform.android
        ? "BdPGvgl8IqSlKCF2t3SFy96MKmsQyyzoOeFmSVR2"
        : defaultTargetPlatform == TargetPlatform.iOS
            ? "7jlvbNY0LNdnqBcIIBSaD14KyTrueO7gBumm5rQs"
            : "icWrnNCLtfmwB8YY54damrtl3PyyNIAatn7K2h8j",
  ),
  BaseUrl(
    name: "Gebak",
    url: "https://api2.gebak.org/",
    id: defaultTargetPlatform == TargetPlatform.android
        ? "4"
        : defaultTargetPlatform == TargetPlatform.iOS
            ? "5"
            : "6",
    secret: defaultTargetPlatform == TargetPlatform.android
        ? "SQgoysCRYgXm6SEXARCrMlx8sbFXJXPhY4CKkgfX"
        : defaultTargetPlatform == TargetPlatform.iOS
            ? "e1fUUySI9dxfVX4lgTywnjShIiW3GdH6oTD1cqJG"
            : "2sEpIPnA23nYr02ciwezG0VN2lNiL6NF0dkeCFaK",
  ),
];

String generateCustomUrl(BaseUrl baseUrl) => baseUrl.url.replaceFirst(
    RegExp(r'https://(.+?)\.we2up'), 'https://${baseUrl.name}.we2up');

String webViewUrl() {
  return "${generateCustomUrl(credentialsBox.get(baseUrl))}"
      "loginWebView?username=${loginData.username}"
      "&password=${loginData.password}";
}

const Map<String, String> paymentMethodTranslations = {
  "recieve_partial": "تحصيل قسط",
  "pay_partial": "دفع قسط",
  "customer_paid": "خصم ",
  "supplier_paid": "كاش باك",
  "recieve_cash": "تحصيل نقدي",
  "pay_cash": "دفع نقدي",
  "pay_contact": "الدفع كتحصيل",
  "cash": "نقدا",
  "card": "بطاقة",
  "cheque": "شيك مصرفي",
  "bank_transfer": "تحويل مصرفي",
  "other": "آخر",
  "custom_pay_1": "الدفع المخصص 1",
  "custom_pay_2": "الدفع المخصص 2",
  "custom_pay_3": "الدفع المخصص 3",
  "custom_pay_4": "الدفع المخصص 4",
  "custom_pay_5": "الدفع المخصص 5",
  "custom_pay_6": "الدفع المخصص 6",
  "custom_pay_7": "الدفع المخصص 7",
};

Future<void> checkShiftLocationAccess(
    BuildContext context, Function toDoFunction) async {
  final strings = AppLocalizations.of(context)!;

  if (loginData.locationRequired) {
    LocationPermission permission = await Geolocator.checkPermission();

    // Request location permission if not granted
    if (permission == LocationPermission.denied ||
        permission == LocationPermission.deniedForever) {
      permission = await Geolocator.requestPermission();
    }

    if (permission == LocationPermission.always ||
        permission == LocationPermission.whileInUse) {
      if (loginData.cashRegistered) {
        toDoFunction();
      } else {
        EasyLoading.showInfo(strings.open_shift_message);
      }
    } else {
      EasyLoading.showInfo(strings.allow_location_message);
    }
  } else {
    // Location access is not required
    if (loginData.cashRegistered) {
      toDoFunction();
    } else {
      EasyLoading.showInfo(strings.open_shift_message);
    }
  }
}

Future<void> checkLocationAccess(
    BuildContext context, Function toDoFunction) async {
  final strings = AppLocalizations.of(context)!;

  if (loginData.locationRequired) {
    LocationPermission permission = await Geolocator.checkPermission();

    // Request location permission if not granted
    if (permission == LocationPermission.denied ||
        permission == LocationPermission.deniedForever) {
      permission = await Geolocator.requestPermission();
    }

    if (permission == LocationPermission.always ||
        permission == LocationPermission.whileInUse) {
      toDoFunction();
    } else {
      EasyLoading.showInfo(strings.allow_location_message);
    }
  } else {
    // Location access is not required
    toDoFunction();
  }
}

Future<LocationInfo?> getLocationWithPermission(BuildContext context) async {
  final strings = AppLocalizations.of(context)!;
  LocationPermission permission = await Geolocator.checkPermission();

  // Request location permission if not granted
  if (permission == LocationPermission.denied ||
      permission == LocationPermission.deniedForever) {
    permission = await Geolocator.requestPermission();
  }

  if (permission == LocationPermission.always ||
      permission == LocationPermission.whileInUse) {
    EasyLoading.show(status: strings.location_loading);
    LocationInfo? location = await ApiRepository.get().getCurrentLocation();
    EasyLoading.dismiss();
    return location;
  } else {
    EasyLoading.showInfo(strings.allow_location_message);
    return null;
  }
}

String fixImageUrl(String? imageUrl) =>
    imageUrl?.replaceFirst(RegExp(r'(?<=https://)(.*?)(?=\.we2up)'), "app") ??
    '';

Map<String, dynamic>? locationInfoToJson(LocationInfo? locationInfo) {
  if (locationInfo != null) {
    return locationInfo.toJson();
  } else {
    return null;
  }
}

void goToLocation(LocationInfo? locationInfo, String message) {
  if (locationInfo != null) {
    MapsLauncher.launchCoordinates(
      locationInfo.latitude,
      locationInfo.longitude,
    );
  } else {
    EasyLoading.showInfo(message);
  }
}

Position cairoLocation =
    Position.fromMap({"latitude": 30.0444, "longitude": 31.2357});

String dateToString(DateTime date, {bool report = false}) {
  return DateFormat(report ? 'yyyy-MM-dd' : 'yyyy-MM-dd HH:mm:ss').format(date);
}

String intToString(int? number) => number.toString();

String previousRouteName(BuildContext context) {
  var router = GoRouter.of(context);

  // Get the index of the current route
  int currentIndex =
      router.routerDelegate.currentConfiguration.matches.length - 1;

  // Get the index of the previous route
  int previousIndex = currentIndex - 1;

  if (previousIndex >= 0) {
    // Get the name of the previous route
    String previousRouteName = router.routerDelegate.currentConfiguration
        .matches[previousIndex].matchedLocation;
    return previousRouteName;
  }

  // Return null if there's no previous route
  return "NULL";
}

bool isSameDay(DateTime dateTime, DateTime other) {
  return dateTime.year == other.year &&
      dateTime.month == other.month &&
      dateTime.day == other.day;
}

bool sameDayOrAfter(DateTime dateTime, DateTime other, {bool shift = false}) {
  if (shift) {
    return dateTime.isAfter(other);
  } else {
    return dateTime.isAfter(other) || isSameDay(dateTime, other);
  }
}

bool sameDayOrBefore(DateTime dateTime, DateTime other, {bool shift = false}) {
  if (shift) {
    return dateTime.isBefore(other);
  } else {
    return dateTime.isBefore(other) || isSameDay(dateTime, other);
  }
}

int removeLastDigit(int number) {
  String numberString = number.toString();
  String modifiedString = numberString.substring(0, numberString.length - 1);
  int modifiedNumber = int.parse(modifiedString);
  return modifiedNumber;
}

int getUpdatedProductId(int variationId) {
  for (Product product in productsBox.values) {
    if (product.productVariations.isNotEmpty) {
      for (var variation in product.productVariations.first.variations!) {
        if (variation.id == variationId) {
          return product.id;
        }
      }
    }
  }

  // Throw an exception if no matching variation ID is found
  throw Exception('No matching product found: variation ID: $variationId');
}

Map<String, dynamic> updateProductIdsInSellLines(
    Map<String, dynamic> sellJson) {
  List<Map<String, dynamic>> sellLinesJson =
      (sellJson['sell_lines'] as List?)?.cast<Map<String, dynamic>>() ?? [];

  // Update product_id for each sell_line
  for (Map<String, dynamic> sellLineJson in sellLinesJson) {
    int variationId = sellLineJson['variation_id'];
    int updatedProductId = getUpdatedProductId(variationId);
    sellLineJson['product_id'] = updatedProductId;
  }

  sellJson['sell_lines'] = sellLinesJson;
  return sellJson;
}

Map<String, dynamic> updateProductIdsInPurchaseLines(
    Map<String, dynamic> purchaseJson) {
  List<Map<String, dynamic>> purchaseLinesJson =
      (purchaseJson['purchase_lines'] as List?)?.cast<Map<String, dynamic>>() ??
          [];

  // Update product_id for each sell_line
  for (Map<String, dynamic> purchaseLineJson in purchaseLinesJson) {
    int variationId = purchaseLineJson['variation_id'];
    int updatedProductId = getUpdatedProductId(variationId);
    purchaseLineJson['product_id'] = updatedProductId;
  }

  purchaseJson['purchase_lines'] = purchaseLinesJson;
  return purchaseJson;
}

bool hasMatchingVariationId(Product product, int variationId) {
  return product.productVariations.first.variations!.every(
    (variation) => variation.id == variationId,
  );
}

bool hasComboVariationInOtherProducts(Product product) {
  return product.productVariations.first.variations!.first.comboVariations.any(
    (comboVariation) =>
        comboVariation != null &&
        productsBox.values.every(
          (otherProduct) =>
              otherProduct != product &&
              hasMatchingVariationId(
                otherProduct,
                comboVariation.variationId,
              ),
        ),
  );
}

String formatDate(DateTime date, {bool filter = false}) {
  if (filter) {
    return DateFormat.yMd().format(date);
  }
  return DateFormat.yMd().add_jm().format(date);
}

Color getStatusColor({
  PaymentStatus? sellStatus,
  ShipmentStatus? shipmentStatus,
}) {
  if (sellStatus != null) {
    switch (sellStatus) {
      case PaymentStatus.paid:
        return Colors.green.withAlpha(128);
      case PaymentStatus.partial:
        return Colors.blue.withAlpha(128);
      case PaymentStatus.due:
        return Colors.red.withAlpha(128);
      case PaymentStatus.offline:
        return Colors.white70;
    }
  } else {
    switch (shipmentStatus) {
      case ShipmentStatus.ordered:
        return Colors.green.withAlpha(128);
      case ShipmentStatus.cancelled:
        return Colors.blue.withAlpha(128);
      case ShipmentStatus.delivered:
        return Colors.red.withAlpha(128);
      case ShipmentStatus.packed:
        return Colors.amber.withAlpha(128);
      default:
        return Colors.deepOrange.withAlpha(128);
    }
  }
}

void navigateToWhatsapp(String? phoneNumber) async {
  if (Platform.isWindows || Platform.isMacOS) {
    var whatsappWebUrl = "https://web.whatsapp.com/send/?phone=$phoneNumber";
    try {
      launchUrl(Uri.parse(whatsappWebUrl));
    } catch (e) {
      EasyLoading.showToast(e.toString());
    }
  } else {
    var whatsappUrl = "whatsapp://send?phone=$phoneNumber";
    try {
      launchUrl(Uri.parse(whatsappUrl));
    } catch (e) {
      EasyLoading.showToast(e.toString());
    }
  }
}

void callNumber(String? phoneNumber) async {
  var phoneUrl = "tel://$phoneNumber";
  try {
    launchUrl(Uri.parse(phoneUrl));
  } catch (e) {
    EasyLoading.showToast(e.toString());
  }
}

String getHeaderText(BillData billOfSaleData, AppLocalizations strings) {
  if (billOfSaleData.returnSell) {
    return strings.invoice_return_details;
  } else if (billOfSaleData.sell != null) {
    return strings.edit_sell;
  } else if (billOfSaleData.quotationPage) {
    return strings.offer_price;
  } else {
    return strings.bill_of_sale;
  }
}

Future<bool?> showBillOfSaleExitDialog(BuildContext context) async {
  final strings = AppLocalizations.of(context)!;
  return showDialog<bool>(
    context: context,
    builder: (context) {
      return AlertDialog(
        title: Text(strings.exit_page),
        content: Text(strings.exit_page_content, textAlign: TextAlign.center),
        actions: <Widget>[
          TextButton(
            onPressed: () => context.pop(false),
            child: Text(strings.no),
          ),
          TextButton(
            onPressed: () => context.pop(true),
            child: Text(strings.yes),
          ),
        ],
      );
    },
  );
}

int calculateCrossAxisCount(BuildContext context) {
  double screenWidth = MediaQuery.of(context).size.width;
  // Use a fixed pixel value instead of sp to avoid scaling issues with newer sizer versions
  // For desktop windows, aim for cards around 150-200px wide
  double cardWidth = 180.0; // Fixed pixel width for better consistency
  int crossAxisCount = (screenWidth / cardWidth).floor();
  // Limit to maximum 4 columns for better layout
  crossAxisCount = crossAxisCount > 4 ? 4 : crossAxisCount;
  return crossAxisCount > 0 ? crossAxisCount : 1; // Ensure at least 1 column
}

void savePaymentsInReports(List<PaymentLine>? paymentLines) {
  if (paymentLines != null) {
    final List<ContactPaymentModel> payments = paymentLines
        .map((e) => ContactPaymentModel.fromPaymentLine(e))
        .toList();
    for (var paymentReport in payments) {
      paymentsReportBox.put(paymentReport.id, paymentReport);
    }
  }
}

double roundIfAbove075(double value) {
  double decimalPart = value - value.floor();
  return (decimalPart > 0.75) ? value.ceilToDouble() : value;
}

String formatNumber(double number) {
  double epsilon = 0.0001;

  if ((number - number.floor()).abs() < epsilon) {
    // Number has no significant decimal places
    return number.toStringAsFixed(0);
  } else {
    // Number has decimal places
    return number.toStringAsFixed(2);
  }
}

void logAppError(String message, dynamic object, StackTrace? stack) {
  if (defaultTargetPlatform != TargetPlatform.windows && defaultTargetPlatform != TargetPlatform.macOS) {
    FirebaseCrashlytics.instance.recordError(
      message,
      stack,
      reason: object.toString(),
    );
  } else {
    log("$message: $object");
    log(stack.toString());
  }
}

bool canGoToHomeBasedOnShift() {
  if (loginData.accessToken != null && businessLocationsBox.values.isNotEmpty) {
    final openCashRegisters = cashRegistersBox.values
        .where((cr) => cr.userId == loginData.userId && cr.status == "open")
        .toList()
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));


    if (openCashRegisters.isNotEmpty) {
      final isAfter = openCashRegisters.first.createdAt.isAfter(
        DateTime.now().subtract(const Duration(days: 1)),
      );
      return isAfter;
    } else {
      debugPrint("No open cash registers found, returning true.");
      return true;
    }
  }
  return false;
}

String applyArabicDigitConversion(String text) {
  if (useArabicNumbers() && _localIsArabic()) {
// Apply Arabic digit conversion only if useArabicNumbers() is true
    Map<String, String> digitsMap = {
      '0': '٠',
      '1': '١',
      '2': '٢',
      '3': '٣',
      '4': '٤',
      '5': '٥',
      '6': '٦',
      '7': '٧',
      '8': '٨',
      '9': '٩',
    };

    return text.replaceAllMapped(RegExp(r'\d'), (match) {
      return digitsMap[match.group(0)!]!;
    });
  } else {
    return text;
  }
}

bool _localIsArabic() {
  return Hive.box(settingsBox).get("language", defaultValue: "ar") == "ar";
}

Future<void> showScanQuantityDialog(BuildContext context,
    {required int id}) async {
  final cubit = ProductsCubit.get(context);
  final strings = AppLocalizations.of(context)!;
  final controller = cubit.allProducts[id]!.quantityController;
  await showDialog(
    context: context,
    builder: (context) => AlertDialog(
      title: Center(child: Text(cubit.allProducts[id]!.product.name)),
      content: ProductAmountTextField(
        controller: controller,
        autoFocus: true,
        width: 100,
        inColumn: true,
        hint: strings.amount.replaceFirst(":", ""),
        maxAmount: cubit.maxInInvoice(id) ?? double.infinity,
      ),
    ),
  );
}
