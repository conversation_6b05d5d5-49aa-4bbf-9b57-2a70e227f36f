import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

// Helper function to get font with fallback
TextStyle _getFontWithFallback(FontWeight weight, double fontSize, double height, [double? letterSpacing]) {
  try {
    return GoogleFonts.inter(
      fontWeight: weight,
      fontSize: fontSize,
      height: height,
      letterSpacing: letterSpacing,
    );
  } catch (e) {
    // Fallback to system font if Google Fonts fails
    return TextStyle(
      fontFamily: 'SF Pro Display', // macOS system font
      fontWeight: weight,
      fontSize: fontSize,
      height: height,
      letterSpacing: letterSpacing,
    );
  }
}

final textTheme = TextTheme(
  displayLarge: _getFontWithFallback(
    FontWeight.w400,
    57,
    64 / 57,
    -0.25,
  ),
  displayMedium: _getFontWithFallback(
    FontWeight.w400,
    45,
    52 / 45,
  ),
  displaySmall: _getFontWithFallback(
    FontWeight.w400,
    36,
    44 / 36,
  ),
  headlineLarge: _getFontWithFallback(
    FontWeight.w400,
    32,
    40 / 32,
  ),
  headlineMedium: _getFontWithFallback(
    FontWeight.w400,
    28,
    36 / 28,
  ),
  headlineSmall: _getFontWithFallback(
    FontWeight.w400,
    24,
    32 / 24,
  ),
  titleLarge: _getFontWithFallback(
    FontWeight.w700,
    22,
    28 / 22,
  ),
  titleMedium: _getFontWithFallback(
    FontWeight.w600,
    16,
    24 / 16,
    0.1,
  ),
  titleSmall: _getFontWithFallback(
    FontWeight.w600,
    14,
    20 / 14,
    0.1,
  ),
  labelLarge: _getFontWithFallback(
    FontWeight.w700,
    14,
    20 / 14,
  ),
  labelMedium: _getFontWithFallback(
    FontWeight.w700,
    12,
    16 / 12,
  ),
  labelSmall: _getFontWithFallback(
    FontWeight.w700,
    11,
    16 / 11,
  ),
  bodyLarge: _getFontWithFallback(
    FontWeight.w400,
    16,
    24 / 16,
  ),
  bodyMedium: _getFontWithFallback(
    FontWeight.w400,
    14,
    20 / 14,
  ),
  bodySmall: _getFontWithFallback(
    FontWeight.w400,
    12,
    16 / 12,
  ),
);