import '../data/db/db_manager.dart';

const String branchHigh = "branchHigh";
const String branchMed = "branchMid";
const String branchLow = "branchLow";
const String contactHigh = "contactHigh";
const String contactMed = "contactMid";
const String contactLow = "contactLow";
const String searchHigh = "searchHigh";
const String searchMed = "searchMid";
const String searchLow = "searchLow";
const String closestRange = "closestRange";

double getBranchHighDefault() =>
    locationRangeBox.get(branchHigh, defaultValue: 50)! / 1000;

double getBranchMedDefault() =>
    locationRangeBox.get(branchMed, defaultValue: 250)! / 1000;

double getBranchLowDefault() =>
    locationRangeBox.get(branchLow, defaultValue: 1500)! / 1000;

double getContactHighDefault() =>
    locationRangeBox.get(contactHigh, defaultValue: 50)! / 1000;

double getContactMedDefault() =>
    locationRangeBox.get(contactMed, defaultValue: 150)! / 1000;

double getContactLowDefault() =>
    locationRangeBox.get(contactLow, defaultValue: 1500)! / 1000;

double getSearchHighDefault() =>
    locationRangeBox.get(searchHigh, defaultValue: 50)! / 1000;

double getSearchMedDefault() =>
    locationRangeBox.get(searchMed, defaultValue: 150)! / 1000;

double getSearchLowDefault() =>
    locationRangeBox.get(searchLow, defaultValue: 1500)! / 1000;

double getClosestRangeDefault() =>
    locationRangeBox.get(closestRange, defaultValue: 500)! / 1000;
