import 'package:flutter_bloc/flutter_bloc.dart';

import '../bloc/contact_payment/contact_payment_cubit.dart';
import '../bloc/landing/landing_cubit.dart';
import '../bloc/products/products_cubit.dart';

List providers(){
  return [
    BlocProvider(create: (context) => LandingCubit(context)),
    BlocProvider(create: (context) => ProductsCubit()),
    BlocProvider(create: (context) => ContactPaymentCubit()),
  ];
}