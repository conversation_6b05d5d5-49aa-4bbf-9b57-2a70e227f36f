import 'dart:io';
import 'dart:typed_data';
import 'package:esc_pos_utils_plus/esc_pos_utils_plus.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:uuid/uuid.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf_image_renderer/pdf_image_renderer.dart';
import 'package:image/image.dart' as imgj;
import 'package:print_bluetooth_thermal/print_bluetooth_thermal.dart';
import 'package:we2up/utils/we2up_constants.dart';

import '../data/models/business_settings.dart';

enum We2UpPaperSize { roll57, roll80, a4, a5 }

class ThermalPrinterManager {
  static int _getPaperWidth() {
    String? paperSize = getCurrentPaperSize();
    switch (paperSize) {
      case 'roll80':        return 560;
      case 'roll57':
        return 384;
      case 'a4':
        return 1598;
      case 'a5':
        return 1102;
      default:
        throw ArgumentError('Unknown paper size: $paperSize');
    }
  }

  static Future<List<List<int>>> pdfToDecodedImage(Uint8List bytes) async {
    final String temp = (await getApplicationCacheDirectory()).path;
    final String path = '$temp/${const Uuid().v4()}.pdf';
    final File file = File(path);
    await file.writeAsBytes(bytes);

    final pdfFile = PdfImageRenderer(path: path);
    await pdfFile.open();
    await pdfFile.openPage(pageIndex: 0);
    final size = await pdfFile.getPageSize(pageIndex: 0);

    final img = await pdfFile.renderPage(
      pageIndex: 0,
      x: 0,
      y: 0,
      width: size.width,
      height: size.height,
      scale: 2,
    );

    final imageData = imgj.decodeImage(img!)!;

    int width = _getPaperWidth();

    imgj.Image resized = imgj.copyResize(
      imageData,
      interpolation: imgj.Interpolation.average,
      width: width,
      maintainAspect: true,
    );

    if (width % 8 != 0) {
      int newWidth = (width ~/ 8) * 8;
      resized = imgj.copyResize(
        resized,
        interpolation: imgj.Interpolation.cubic,
        width: newWidth,
        maintainAspect: true,
      );
    }

    await pdfFile.closePage(pageIndex: 0);
    pdfFile.close();

    final profile = await CapabilityProfile.load();
    final generator = Generator(
      getCurrentPaperSize() == 'roll57' ? PaperSize.mm58 : PaperSize.mm80,
      profile,
    );

    List<List<int>> imageParts = [];
    int height = resized.height;
    int partHeight = 1300;
    int parts = (height / partHeight).ceil();

    for (int i = 0; i < parts; i++) {
      int startY = i * partHeight;
      int endY = (i + 1) * partHeight;
      if (endY > height) endY = height;

      imgj.Image partImage = imgj.copyCrop(
        resized,
        x: 0,
        y: startY,
        width: width,
        height: endY - startY,
      );

      final data =
          imgj.decodeImage(Uint8List.fromList(imgj.encodePng(partImage)))!;

      List<int> imageBytes = [];

      imageBytes += generator.imageRaster(data, align: PosAlign.left);

      // Only add cut command for the last chunk
      if (i == parts - 1) {
        imageBytes += generator.cut();
      }

      imageParts.add(imageBytes);
    }

    return imageParts;
  }

  static Future<void> thermalPrint(List<List<int>> imageParts) async {
    for (var part in imageParts) {
      final result = await PrintBluetoothThermal.writeBytes(part);

      if (!result) {
        EasyLoading.showError("Error printing");
        logAppError('Error printing chunk', null, null);
        return;
      }
    }
  }
}
