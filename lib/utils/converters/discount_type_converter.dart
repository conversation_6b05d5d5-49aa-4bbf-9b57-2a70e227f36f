import 'package:json_annotation/json_annotation.dart';

import '../../data/models/product.dart';

class DiscountTypeConverter implements JsonConverter<DiscountType?, String?> {
  const DiscountTypeConverter();

  @override
  DiscountType? fromJson(String? json) {
    if (json == null) return null;
    return DiscountType.values.firstWhere(
          (e) => e.toString().split('.').last == json,
      orElse: () => DiscountType.percentage,
    );
  }

  @override
  String? toJson(DiscountType? type) => type?.toString().split('.').last;
}