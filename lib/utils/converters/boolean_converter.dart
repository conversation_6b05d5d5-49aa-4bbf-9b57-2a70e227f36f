import 'package:json_annotation/json_annotation.dart';

class BooleanConverter implements JsonConverter<bool, dynamic> {
  const BooleanConverter();

  @override
  bool fromJson(dynamic json) {
    if (json == null) return false;
    if (json is int) return json == 1;
    if (json is String) return json == '1';
    if (json is bool) return json;

    throw Exception('Invalid boolean value: $json');
  }

  @override
  int? toJson(bool? value) {
    return value == null ? null : (value ? 1 : 0);
  }
}
