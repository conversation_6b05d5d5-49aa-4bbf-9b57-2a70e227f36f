import 'package:flutter/material.dart';

List<ColorScheme> lightSchemes = const [
  ColorScheme(
    brightness: Brightness.light,
    primary: Color(0xFF723AD3),
    onPrimary: Color(0xFFFFFFFF),
    primaryContainer: Color(0xFFEBDDFF),
    onPrimaryContainer: Color(0xFF250059),
    secondary: Color(0xFF006874),
    onSecondary: Color(0xFFFFFFFF),
    secondaryContainer: Color(0xFF97F0FF),
    onSecondaryContainer: Color(0xFF001F24),
    tertiary: Color(0xFF7E525E),
    onTertiary: Color(0xFFFFFFFF),
    tertiaryContainer: Color(0xFFFFD9E1),
    onTertiaryContainer: Color(0xFF31101B),
    error: Color(0xFFBA1A1A),
    errorContainer: Color(0xFFFFDAD6),
    onError: Color(0xFFFFFFFF),
    onErrorContainer: Color(0xFF410002),
    surface: Color(0xFFFFFBFF),
    onSurface: Color(0xFF1D1B1E),
    surfaceContainerHighest: Color(0xFFE7E0EB),
    onSurfaceVariant: Color(0xFF49454E),
    outline: Color(0xFF7A757F),
    onInverseSurface: Color(0xFFF5EFF4),
    inverseSurface: Color(0xFF323033),
    inversePrimary: Color(0xFFD3BBFF),
    shadow: Color(0xFF000000),
    surfaceTint: Color(0xFF723AD3),
    outlineVariant: Color(0xFFCBC4CF),
    scrim: Color(0xFF000000),
  ),
  ColorScheme(
    brightness: Brightness.light,
    primary: Color(0xFF395BA9),
    onPrimary: Color(0xFFFFFFFF),
    primaryContainer: Color(0xFFDAE2FF),
    onPrimaryContainer: Color(0xFF001947),
    secondary: Color(0xFF5555A9),
    onSecondary: Color(0xFFFFFFFF),
    secondaryContainer: Color(0xFFE2DFFF),
    onSecondaryContainer: Color(0xFF0E0664),
    tertiary: Color(0xFF964900),
    onTertiary: Color(0xFFFFFFFF),
    tertiaryContainer: Color(0xFFFFDCC6),
    onTertiaryContainer: Color(0xFF311300),
    error: Color(0xFFBA1A1A),
    errorContainer: Color(0xFFFFDAD6),
    onError: Color(0xFFFFFFFF),
    onErrorContainer: Color(0xFF410002),
    surface: Color(0xFFFEFBFF),
    onSurface: Color(0xFF1B1B1F),
    surfaceContainerHighest: Color(0xFFE1E2EC),
    onSurfaceVariant: Color(0xFF44464F),
    outline: Color(0xFF757780),
    onInverseSurface: Color(0xFFF2F0F4),
    inverseSurface: Color(0xFF303034),
    inversePrimary: Color(0xFFB1C5FF),
    shadow: Color(0xFF000000),
    surfaceTint: Color(0xFF395BA9),
    outlineVariant: Color(0xFFC5C6D0),
    scrim: Color(0xFF000000),
  ),
  ColorScheme(
    brightness: Brightness.light,
    primary: Color(0xFF6349BF),
    onPrimary: Color(0xFFFFFFFF),
    primaryContainer: Color(0xFFE7DEFF),
    onPrimaryContainer: Color(0xFF1E0060),
    secondary: Color(0xFF006493),
    onSecondary: Color(0xFFFFFFFF),
    secondaryContainer: Color(0xFFCAE6FF),
    onSecondaryContainer: Color(0xFF001E30),
    tertiary: Color(0xFF0062A0),
    onTertiary: Color(0xFFFFFFFF),
    tertiaryContainer: Color(0xFFD0E4FF),
    onTertiaryContainer: Color(0xFF001D35),
    error: Color(0xFFBA1A1A),
    errorContainer: Color(0xFFFFDAD6),
    onError: Color(0xFFFFFFFF),
    onErrorContainer: Color(0xFF410002),
    surface: Color(0xFFFFFBFF),
    onSurface: Color(0xFF1C1B1E),
    surfaceContainerHighest: Color(0xFFE6E0EC),
    onSurfaceVariant: Color(0xFF48454E),
    outline: Color(0xFF79757F),
    onInverseSurface: Color(0xFFF4EFF4),
    inverseSurface: Color(0xFF313033),
    inversePrimary: Color(0xFFCCBEFF),
    shadow: Color(0xFF000000),
    surfaceTint: Color(0xFF6349BF),
    outlineVariant: Color(0xFFCAC4CF),
    scrim: Color(0xFF000000),
  ),
  ColorScheme(
    brightness: Brightness.light,
    primary: Color(0xFF036E1C),
    onPrimary: Color(0xFFFFFFFF),
    primaryContainer: Color(0xFF9AF895),
    onPrimaryContainer: Color(0xFF002204),
    secondary: Color(0xFF006C45),
    onSecondary: Color(0xFFFFFFFF),
    secondaryContainer: Color(0xFF90F7BF),
    onSecondaryContainer: Color(0xFF002112),
    tertiary: Color(0xFF00658C),
    onTertiary: Color(0xFFFFFFFF),
    tertiaryContainer: Color(0xFFC5E7FF),
    onTertiaryContainer: Color(0xFF001E2D),
    error: Color(0xFFBA1A1A),
    errorContainer: Color(0xFFFFDAD6),
    onError: Color(0xFFFFFFFF),
    onErrorContainer: Color(0xFF410002),
    surface: Color(0xFFFCFDF6),
    onSurface: Color(0xFF1A1C19),
    surfaceContainerHighest: Color(0xFFDEE5D8),
    onSurfaceVariant: Color(0xFF424940),
    outline: Color(0xFF72796F),
    onInverseSurface: Color(0xFFF0F1EB),
    inverseSurface: Color(0xFF2F312D),
    inversePrimary: Color(0xFF7EDB7B),
    shadow: Color(0xFF000000),
    surfaceTint: Color(0xFF036E1C),
    outlineVariant: Color(0xFFC2C9BD),
    scrim: Color(0xFF000000),
  ),
  ColorScheme(
    brightness: Brightness.light,
    primary: Color(0xFF695F00),
    onPrimary: Color(0xFFFFFFFF),
    primaryContainer: Color(0xFFF6E568),
    onPrimaryContainer: Color(0xFF201C00),
    secondary: Color(0xFF626100),
    onSecondary: Color(0xFFFFFFFF),
    secondaryContainer: Color(0xFFEAE86F),
    onSecondaryContainer: Color(0xFF1D1D00),
    tertiary: Color(0xFF225FA6),
    onTertiary: Color(0xFFFFFFFF),
    tertiaryContainer: Color(0xFFD5E3FF),
    onTertiaryContainer: Color(0xFF001B3B),
    error: Color(0xFFBA1A1A),
    errorContainer: Color(0xFFFFDAD6),
    onError: Color(0xFFFFFFFF),
    onErrorContainer: Color(0xFF410002),
    surface: Color(0xFFFFFBFF),
    onSurface: Color(0xFF1D1C16),
    surfaceContainerHighest: Color(0xFFE8E2D0),
    onSurfaceVariant: Color(0xFF4A473A),
    outline: Color(0xFF7B7768),
    onInverseSurface: Color(0xFFF5F0E7),
    inverseSurface: Color(0xFF32302A),
    inversePrimary: Color(0xFFD8C84F),
    shadow: Color(0xFF000000),
    surfaceTint: Color(0xFF695F00),
    outlineVariant: Color(0xFFCCC6B5),
    scrim: Color(0xFF000000),
  ),
  ColorScheme(
    brightness: Brightness.light,
    primary: Color(0xFF9B442A),
    onPrimary: Color(0xFFFFFFFF),
    primaryContainer: Color(0xFFFFDBD1),
    onPrimaryContainer: Color(0xFF3B0A00),
    secondary: Color(0xFF9B4429),
    onSecondary: Color(0xFFFFFFFF),
    secondaryContainer: Color(0xFFFFDBD1),
    onSecondaryContainer: Color(0xFF3A0A00),
    tertiary: Color(0xFF406918),
    onTertiary: Color(0xFFFFFFFF),
    tertiaryContainer: Color(0xFFBFF190),
    onTertiaryContainer: Color(0xFF0D2000),
    error: Color(0xFFBA1A1A),
    errorContainer: Color(0xFFFFDAD6),
    onError: Color(0xFFFFFFFF),
    onErrorContainer: Color(0xFF410002),
    surface: Color(0xFFFFFBFF),
    onSurface: Color(0xFF201A18),
    surfaceContainerHighest: Color(0xFFF5DED7),
    onSurfaceVariant: Color(0xFF53433F),
    outline: Color(0xFF85736E),
    onInverseSurface: Color(0xFFFBEEEB),
    inverseSurface: Color(0xFF362F2D),
    inversePrimary: Color(0xFFFFB5A0),
    shadow: Color(0xFF000000),
    surfaceTint: Color(0xFF9B442A),
    outlineVariant: Color(0xFFD8C2BC),
    scrim: Color(0xFF000000),
  ),
];

List<ColorScheme> darkSchemes = const [
  ColorScheme(
    brightness: Brightness.dark,
    primary: Color(0xFFD3BBFF),
    onPrimary: Color(0xFF3F008D),
    primaryContainer: Color(0xFF5915BA),
    onPrimaryContainer: Color(0xFFEBDDFF),
    secondary: Color(0xFF4FD8EB),
    onSecondary: Color(0xFF00363D),
    secondaryContainer: Color(0xFF004F58),
    onSecondaryContainer: Color(0xFF97F0FF),
    tertiary: Color(0xFFF0B7C5),
    onTertiary: Color(0xFF4A2530),
    tertiaryContainer: Color(0xFF643B46),
    onTertiaryContainer: Color(0xFFFFD9E1),
    error: Color(0xFFFFB4AB),
    errorContainer: Color(0xFF93000A),
    onError: Color(0xFF690005),
    onErrorContainer: Color(0xFFFFDAD6),
    surface: Color(0xFF1D1B1E),
    onSurface: Color(0xFFE6E1E6),
    surfaceContainerHighest: Color(0xFF49454E),
    onSurfaceVariant: Color(0xFFCBC4CF),
    outline: Color(0xFF948F99),
    onInverseSurface: Color(0xFF1D1B1E),
    inverseSurface: Color(0xFFE6E1E6),
    inversePrimary: Color(0xFF723AD3),
    shadow: Color(0xFF000000),
    surfaceTint: Color(0xFFD3BBFF),
    outlineVariant: Color(0xFF49454E),
    scrim: Color(0xFF000000),
  ),
  ColorScheme(
    brightness: Brightness.dark,
    primary: Color(0xFFB1C5FF),
    onPrimary: Color(0xFF002C71),
    primaryContainer: Color(0xFF1D438F),
    onPrimaryContainer: Color(0xFFDAE2FF),
    secondary: Color(0xFFC2C1FF),
    onSecondary: Color(0xFF262477),
    secondaryContainer: Color(0xFF3D3C8F),
    onSecondaryContainer: Color(0xFFE2DFFF),
    tertiary: Color(0xFFFFB786),
    onTertiary: Color(0xFF502400),
    tertiaryContainer: Color(0xFF723600),
    onTertiaryContainer: Color(0xFFFFDCC6),
    error: Color(0xFFFFB4AB),
    errorContainer: Color(0xFF93000A),
    onError: Color(0xFF690005),
    onErrorContainer: Color(0xFFFFDAD6),
    surface: Color(0xFF1B1B1F),
    onSurface: Color(0xFFE4E2E6),
    surfaceContainerHighest: Color(0xFF44464F),
    onSurfaceVariant: Color(0xFFC5C6D0),
    outline: Color(0xFF8F9099),
    onInverseSurface: Color(0xFF1B1B1F),
    inverseSurface: Color(0xFFE4E2E6),
    inversePrimary: Color(0xFF395BA9),
    shadow: Color(0xFF000000),
    surfaceTint: Color(0xFFB1C5FF),
    outlineVariant: Color(0xFF44464F),
    scrim: Color(0xFF000000),
  ),
  ColorScheme(
    brightness: Brightness.dark,
    primary: Color(0xFFCCBEFF),
    onPrimary: Color(0xFF340C90),
    primaryContainer: Color(0xFF4B2EA6),
    onPrimaryContainer: Color(0xFFE7DEFF),
    secondary: Color(0xFF8DCDFF),
    onSecondary: Color(0xFF00344F),
    secondaryContainer: Color(0xFF004B70),
    onSecondaryContainer: Color(0xFFCAE6FF),
    tertiary: Color(0xFF9CCAFF),
    onTertiary: Color(0xFF003256),
    tertiaryContainer: Color(0xFF00497A),
    onTertiaryContainer: Color(0xFFD0E4FF),
    error: Color(0xFFFFB4AB),
    errorContainer: Color(0xFF93000A),
    onError: Color(0xFF690005),
    onErrorContainer: Color(0xFFFFDAD6),
    surface: Color(0xFF1C1B1E),
    onSurface: Color(0xFFE6E1E6),
    surfaceContainerHighest: Color(0xFF48454E),
    onSurfaceVariant: Color(0xFFCAC4CF),
    outline: Color(0xFF938F99),
    onInverseSurface: Color(0xFF1C1B1E),
    inverseSurface: Color(0xFFE6E1E6),
    inversePrimary: Color(0xFF6349BF),
    shadow: Color(0xFF000000),
    surfaceTint: Color(0xFFCCBEFF),
    outlineVariant: Color(0xFF48454E),
    scrim: Color(0xFF000000),
  ),
  ColorScheme(
    brightness: Brightness.dark,
    primary: Color(0xFF7EDB7B),
    onPrimary: Color(0xFF00390A),
    primaryContainer: Color(0xFF005313),
    onPrimaryContainer: Color(0xFF9AF895),
    secondary: Color(0xFF73DAA4),
    onSecondary: Color(0xFF003822),
    secondaryContainer: Color(0xFF005233),
    onSecondaryContainer: Color(0xFF90F7BF),
    tertiary: Color(0xFF80D0FF),
    onTertiary: Color(0xFF00344B),
    tertiaryContainer: Color(0xFF004C6A),
    onTertiaryContainer: Color(0xFFC5E7FF),
    error: Color(0xFFFFB4AB),
    errorContainer: Color(0xFF93000A),
    onError: Color(0xFF690005),
    onErrorContainer: Color(0xFFFFDAD6),
    surface: Color(0xFF1A1C19),
    onSurface: Color(0xFFE2E3DD),
    surfaceContainerHighest: Color(0xFF424940),
    onSurfaceVariant: Color(0xFFC2C9BD),
    outline: Color(0xFF8C9388),
    onInverseSurface: Color(0xFF1A1C19),
    inverseSurface: Color(0xFFE2E3DD),
    inversePrimary: Color(0xFF036E1C),
    shadow: Color(0xFF000000),
    surfaceTint: Color(0xFF7EDB7B),
    outlineVariant: Color(0xFF424940),
    scrim: Color(0xFF000000),
  ),
  ColorScheme(
    brightness: Brightness.dark,
    primary: Color(0xFFD8C84F),
    onPrimary: Color(0xFF373100),
    primaryContainer: Color(0xFF4F4700),
    onPrimaryContainer: Color(0xFFF6E568),
    secondary: Color(0xFFCDCC56),
    onSecondary: Color(0xFF333200),
    secondaryContainer: Color(0xFF4A4900),
    onSecondaryContainer: Color(0xFFEAE86F),
    tertiary: Color(0xFFA7C8FF),
    onTertiary: Color(0xFF003060),
    tertiaryContainer: Color(0xFF004788),
    onTertiaryContainer: Color(0xFFD5E3FF),
    error: Color(0xFFFFB4AB),
    errorContainer: Color(0xFF93000A),
    onError: Color(0xFF690005),
    onErrorContainer: Color(0xFFFFDAD6),
    surface: Color(0xFF1D1C16),
    onSurface: Color(0xFFE7E2D9),
    surfaceContainerHighest: Color(0xFF4A473A),
    onSurfaceVariant: Color(0xFFCCC6B5),
    outline: Color(0xFF959181),
    onInverseSurface: Color(0xFF1D1C16),
    inverseSurface: Color(0xFFE7E2D9),
    inversePrimary: Color(0xFF695F00),
    shadow: Color(0xFF000000),
    surfaceTint: Color(0xFFD8C84F),
    outlineVariant: Color(0xFF4A473A),
    scrim: Color(0xFF000000),
  ),
  ColorScheme(
    brightness: Brightness.dark,
    primary: Color(0xFFFFB5A0),
    onPrimary: Color(0xFF5E1702),
    primaryContainer: Color(0xFF7C2D15),
    onPrimaryContainer: Color(0xFFFFDBD1),
    secondary: Color(0xFFFFB59F),
    onSecondary: Color(0xFF5E1701),
    secondaryContainer: Color(0xFF7C2D14),
    onSecondaryContainer: Color(0xFFFFDBD1),
    tertiary: Color(0xFFA4D577),
    onTertiary: Color(0xFF1B3700),
    tertiaryContainer: Color(0xFF2A5000),
    onTertiaryContainer: Color(0xFFBFF190),
    error: Color(0xFFFFB4AB),
    errorContainer: Color(0xFF93000A),
    onError: Color(0xFF690005),
    onErrorContainer: Color(0xFFFFDAD6),
    surface: Color(0xFF201A18),
    onSurface: Color(0xFFEDE0DC),
    surfaceContainerHighest: Color(0xFF53433F),
    onSurfaceVariant: Color(0xFFD8C2BC),
    outline: Color(0xFFA08C87),
    onInverseSurface: Color(0xFF201A18),
    inverseSurface: Color(0xFFEDE0DC),
    inversePrimary: Color(0xFF9B442A),
    shadow: Color(0xFF000000),
    surfaceTint: Color(0xFFFFB5A0),
    outlineVariant: Color(0xFF53433F),
    scrim: Color(0xFF000000),
  ),
];
