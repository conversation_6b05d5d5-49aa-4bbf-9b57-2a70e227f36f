// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      throw UnsupportedError(
        'DefaultFirebaseOptions have not been configured for web - '
        'you can reconfigure this by running the FlutterFire CLI again.',
      );
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDq01-i6ucPS1xIecDuJejgd0HAQRsvr5o',
    appId: '1:121117597593:android:1f4d338a6e2c48011cbd08',
    messagingSenderId: '121117597593',
    projectId: 'we2up-73c77',
    storageBucket: 'we2up-73c77.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDx_iP3oFEAUx0MSaKKPY3qymRDzRhLOTg',
    appId: '1:121117597593:ios:55fca45db36e93cc1cbd08',
    messagingSenderId: '121117597593',
    projectId: 'we2up-73c77',
    storageBucket: 'we2up-73c77.firebasestorage.app',
    iosBundleId: 'com.we2up.original.we2up',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyDx_iP3oFEAUx0MSaKKPY3qymRDzRhLOTg',
    appId: '1:121117597593:ios:1866e35c1cfaa3821cbd08',
    messagingSenderId: '121117597593',
    projectId: 'we2up-73c77',
    storageBucket: 'we2up-73c77.firebasestorage.app',
    iosBundleId: 'com.example.we2up',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyBTtU9QfN9x65pRZsHrmVHiCi1CkB_ZBuQ',
    appId: '1:121117597593:web:a9197bfb6670d45c1cbd08',
    messagingSenderId: '121117597593',
    projectId: 'we2up-73c77',
    authDomain: 'we2up-73c77.firebaseapp.com',
    storageBucket: 'we2up-73c77.firebasestorage.app',
    measurementId: 'G-S4EFJPN5RB',
  );

}