import 'package:flutter/widgets.dart';
import 'package:we2up/bloc/landing/landing_cubit.dart';
import 'package:we2up/utils/route_constants.dart';

class We2UpNavigationObserver extends NavigatorObserver {
  @override
  void didPush(Route<dynamic> route, Route<dynamic>? previousRoute) {
    final routeName = route.settings.name;
    final context = route.navigator!.context;
    if (routeName == landingScreen) {
      Future.delayed(const Duration(milliseconds: 250), () {
        LandingCubit.get(context).updateLandingScreen();
      });
    }
    super.didPush(route, previousRoute);
  }

  @override
  void didPop(Route<dynamic> route, Route<dynamic>? previousRoute) {
    final context = route.navigator!.context;
    if (previousRoute?.settings.name == landingScreen) {
      Future.delayed(const Duration(milliseconds: 250), () {
        LandingCubit.get(context).updateLandingScreen();
      });
    }
    super.didPop(route, previousRoute);
  }

  @override
  void didReplace({Route? newRoute, Route? oldRoute}) {
    final routeName = newRoute?.settings.name;
    final context = newRoute?.navigator!.context;
    if (routeName == landingScreen) {
      Future.delayed(const Duration(milliseconds: 250), () {
        LandingCubit.get(context).updateLandingScreen();
      });
    }
    super.didReplace(newRoute: newRoute, oldRoute: oldRoute);
  }
}
