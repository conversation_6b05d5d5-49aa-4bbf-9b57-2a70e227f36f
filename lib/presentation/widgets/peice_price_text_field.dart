import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../data/db/db_manager.dart';
import '../../utils/input_formatters/min_value_input_formatter.dart';

class PiecePriceTextField extends StatelessWidget {
  const PiecePriceTextField({super.key, required this.id});

  final int id;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 5.sp),
      child: TextField(
        enabled: canEditProductPriceFromPosScreen(),
        textAlign: TextAlign.center,
        style: TextStyle(
          color: Theme.of(context).textTheme.bodyMedium!.color,
        ),
        keyboardType: TextInputType.number,
        decoration: InputDecoration(
          alignLabelWithHint: true,
          contentPadding: EdgeInsets.symmetric(horizontal: 5.sp),
          border: OutlineInputBorder(
            borderSide: BorderSide(
              width: 2,
              color: Theme.of(context).colorScheme.inversePrimary,
            ),
            borderRadius: BorderRadius.circular(15.sp),
          ),
        ),
        inputFormatters: [
          MinValueInputFormatter(15),
        ],
        // controller: cubit.allProducts[id]!.piecePriceController,
      ),
    );
  }
}
