import 'package:dropdown_search/dropdown_search.dart';
import 'package:flutter/material.dart';
import 'package:we2up/data/models/contact.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';

import '../../data/db/db_manager.dart';
import '../style_constants.dart';

class ContactsDropdownButton extends StatelessWidget {
  const ContactsDropdownButton({
    super.key,
    required this.onChanged,
    this.filterType,
    required this.contact,
    this.isInLocation = false,
  });

  final void Function(Contact?) onChanged;
  final String? filterType;
  final Contact? contact;
  final bool isInLocation;

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    return DropdownSearch<Contact?>(
      selectedItem: contact,
      items: (String filter, _) => getDropdownItems(
        strings: strings,
        filterType: filterType,
        isInLocation: isInLocation,
        filterText: filter,
      ),
      itemAsString: (i) => i?.name ?? i?.firstName ?? strings.nothing,
      onChanged: (data) => onChanged(data),
      decoratorProps: DropDownDecoratorProps(
        textAlign: TextAlign.center,
        baseStyle: Theme.of(context).textTheme.labelLarge,
        decoration: InputDecoration(
          hintText: strings.search,
          hintStyle: Theme.of(context).textTheme.labelLarge,
          border: filterInputBorder(context),
          enabledBorder: filterInputBorder(context),
          focusedBorder: filterInputBorder(context),
        ),
      ),
      popupProps: buildPopupPropsMultiSelection(strings, context),
      compareFn: (item1, item2) => item1?.id == item2?.id,
    );
  }

  PopupPropsMultiSelection<Contact?> buildPopupPropsMultiSelection(
      AppLocalizations strings, BuildContext context) {
    Widget customPopupContactBuilder(context, contact, isDisabled, isSelected) {
      return ListTile(
        enabled: !isDisabled,
        selected: isSelected,
        title: Center(
          child: We2upText(
            contact?.name ??
                (isInLocation ? strings.location_all : strings.nothing),
          ),
        ),
        subtitle: Center(
          child: We2upText(
            contact?.mobile != null ? contact?.mobile ?? "" : "",
            isPhone: true,
          ),
        ),
      );
    }

    return PopupPropsMultiSelection.dialog(
      showSearchBox: true,
      itemBuilder: customPopupContactBuilder,
      searchDelay: Duration.zero,
      searchFieldProps: TextFieldProps(
          autofocus: true,
          decoration: InputDecoration(
            hintText: strings.search,
            suffix: const Icon(Icons.search),
            border: filterInputBorder(context),
          )),
    );
  }
}

Future<List<Contact?>> getDropdownItems({
  required AppLocalizations strings,
  String? filterType,
  required bool isInLocation,
  required String filterText,
}) async {
  final filteredContacts = contactsBox.values
      .where(
        (contact) =>
            (filterType == null || contact.type == filterType) &&
            ((contact.name != null &&
                    RegExp(RegExp.escape(filterText), caseSensitive: false)
                        .hasMatch(contact.name!)) ||
                (contact.mobile != null &&
                    RegExp(RegExp.escape(filterText), caseSensitive: false)
                        .hasMatch(contact.mobile!)) ||
                (contact.contactId != null &&
                    RegExp(RegExp.escape(filterText), caseSensitive: false)
                        .hasMatch(contact.contactId!)) ||
                (contact.supplierBusinessName != null &&
                    RegExp(RegExp.escape(filterText), caseSensitive: false)
                        .hasMatch(contact.supplierBusinessName!))),
      )
      .take(50)
      .toList();

  final dropdownItems = <Contact?>[];

  dropdownItems.insert(0, null);

  if (filteredContacts.isNotEmpty) {
    dropdownItems.addAll(filteredContacts);
  }

  return dropdownItems;
}

extension AppLocalizationsExtension on AppLocalizations {
  String translateContactType(String? contactType) {
    switch (contactType) {
      case 'customer':
        return customer;
      case 'supplier':
        return supplier;
      default:
        return nothing;
    }
  }
}
