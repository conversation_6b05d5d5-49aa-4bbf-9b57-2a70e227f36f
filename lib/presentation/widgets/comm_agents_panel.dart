import 'package:expandable/expandable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:sizer/sizer.dart';
import 'package:we2up/presentation/style_constants.dart';
import 'package:we2up/presentation/widgets/tables_dropdown_button.dart';
import 'package:we2up/presentation/widgets/user_dropdown_button.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';

import '../../bloc/products/products_cubit.dart';
import '../../data/db/db_manager.dart';

class CommAgentsPanel extends StatelessWidget {
  const CommAgentsPanel({super.key});

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    final cubit = ProductsCubit.get(context);
    return Visibility(
      visible: loginData.tablesAllowed ||
          loginData.serviceStaffAllowed ||
          loginData.commAgntAllowed != null,
      child: Column(
        children: [
          verticalSpacer,
          Card(
            child: ExpandablePanel(
              controller: cubit.commAgentsPanelController,
              theme: const ExpandableThemeData(hasIcon: false),
              header: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10.sp),
                  color: Theme.of(context)
                      .colorScheme
                      .primaryFixed
                      .withOpacity(0.2),
                ),
                padding: EdgeInsets.all(10.sp),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const SizedBox(),
                    We2upText(
                      strings.agents_and_services,
                      style: Theme.of(context).textTheme.labelLarge,
                    ),
                    const Icon(Icons.keyboard_arrow_down),
                  ],
                ),
              ),
              expanded: const SizedBox(),
              collapsed: Padding(
                padding: EdgeInsets.all(4.sp),
                child: const CommAgentsColumn(),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class CommAgentsColumn extends StatelessWidget {
  const CommAgentsColumn({super.key});

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    final cubit = ProductsCubit.get(context);
    return Column(
      children: [
        if (loginData.commAgntAllowed != null)
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                flex: 3,
                child: We2upText(
                  strings.commission_seller,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              SizedBox(width: 8.0),
              Expanded(
                flex: 7,
                child: BlocBuilder<ProductsCubit, ProductsState>(
                  buildWhen: (_, c) => c is CartCommAgentChanged,
                  builder: (context, state) {
                    return UsersDropdownButton(
                      user: cubit.commAgent,
                      commAgents: loginData.commAgntAllowed == "cmsn_agnt",
                      currentUserOnly:
                          loginData.commAgntAllowed == "logged_in_user",
                      onChanged: cubit.changeCommAgent,
                      controller: TextEditingController(),
                    );
                  },
                ),
              ),
            ],
          ),
        if (loginData.commAgntAllowed != null) verticalSpacer,
        if (loginData.serviceStaffAllowed)
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                flex: 3,
                child: We2upText(
                  strings.service_staff,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              SizedBox(width: 8.0),
              Expanded(
                flex: 7,
                child: BlocBuilder<ProductsCubit, ProductsState>(
                  buildWhen: (_, c) => c is CartServiceStaffChanged,
                  builder: (context, state) {
                    return UsersDropdownButton(
                      user: cubit.serviceStaff,
                      serviceStaff: true,
                      onChanged: cubit.changeServiceStaff,
                      controller: TextEditingController(),
                    );
                  },
                ),
              ),
            ],
          ),
        if (loginData.serviceStaffAllowed) verticalSpacer,
        if (loginData.tablesAllowed)
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                flex: 3,
                child: We2upText(
                  strings.tables,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              SizedBox(width: 8.0),
              Expanded(
                flex: 7,
                child: BlocBuilder<ProductsCubit, ProductsState>(
                  buildWhen: (_, c) => c is CartTableChanged,
                  builder: (context, state) {
                    return TablesDropdown(
                      onChanged: cubit.changeTable,
                      table: cubit.table,
                      isExpanded: true,
                    );
                  },
                ),
              ),
            ],
          ),
      ],
    );
  }
}
