import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';

import '../../data/models/business_settings.dart';

class ShowThreeTotals extends StatelessWidget {
  const ShowThreeTotals({
    super.key,
    required this.totalText,
    required this.paidText,
    required this.remainingText,
    required this.total,
    required this.paid,
    required this.remaining,
  });

  final String totalText;
  final String paidText;
  final String remainingText;
  final double total;
  final double paid;
  final double remaining;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 8.sp),
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
          border: Border.all(
            color: Theme.of(context).colorScheme.error,
            width: 1.sp,
          ),
          borderRadius: BorderRadius.circular(15.sp),
        ),
        padding: EdgeInsets.symmetric(horizontal: 10.sp, vertical: 5.sp),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Row(
              children: [
                Expanded(child: We2upText(totalText)),
                Expanded(
                  child: We2upText("${total.toStringAsFixed(4)} "
                      "${currencySymbol()}"),
                ),
              ],
            ),
            Row(
              children: [
                Expanded(child: We2upText(paidText)),
                Expanded(
                  child: We2upText("${paid.toStringAsFixed(4)} "
                      "${currencySymbol()}"),
                ),
              ],
            ),
            Row(
              children: [
                Expanded(child: We2upText(remainingText)),
                Expanded(
                  child: We2upText("${remaining.toStringAsFixed(4)} "
                      "${currencySymbol()}"),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
