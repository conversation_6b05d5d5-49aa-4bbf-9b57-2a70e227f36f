import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';

import '../../data/db/db_manager.dart';
import '../../data/models/product.dart';
import '../style_constants.dart';

class NPBrandDropdownButton extends StatelessWidget {
  const NPBrandDropdownButton({
    super.key,
    this.isExpanded = false,
    this.padding = 5,
    required this.onChanged,
    required this.unit,
  });

  final bool isExpanded;
  final double padding;
  final ValueChanged<Brand?> onChanged;
  final Brand? unit;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: dropDownDecoration(context),
      child: DropdownButton<Brand>(
        value: unit,
        padding: EdgeInsets.all(padding.sp),
        isDense: true,
        isExpanded: isExpanded,
        onChanged: onChanged,
        underline: const SizedBox(),
        items: [
          DropdownMenuItem<Brand>(
            value: null,
            child: Center(
              child: We2upText(AppLocalizations.of(context)!.nothing),
            ),
          ),
          ...brandsBox.values.map<DropdownMenuItem<Brand>>((Brand value) {
            return DropdownMenuItem<Brand>(
              value: value,
              child: Center(child: We2upText(value.name ?? "N/A")),
            );
          }),
        ],
      ),
    );
  }
}
