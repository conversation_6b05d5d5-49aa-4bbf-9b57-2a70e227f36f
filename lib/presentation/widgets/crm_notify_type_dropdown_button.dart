import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';

import '../../data/models/follow_up_to_api.dart';
import '../style_constants.dart';

class CrmNotifyTypeDropdownButton extends StatelessWidget {
  const CrmNotifyTypeDropdownButton({
    super.key,
    this.isExpanded,
    this.padding,
    required this.onChanged,
    required this.notifyType,
  });

  final bool? isExpanded;
  final double? padding;
  final ValueChanged<NotifyType?> onChanged;
  final NotifyType notifyType;

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;

    return Container(
      decoration: dropDownDecoration(context),
      child: DropdownButton<NotifyType>(
        value: notifyType,
        padding: EdgeInsets.all(padding != null ? padding!.sp : 5.sp),
        isDense: true,
        isExpanded: isExpanded ?? true,
        onChanged: onChanged,
        underline: const SizedBox(),
        items: NotifyType.values
            .map<DropdownMenuItem<NotifyType>>(
              (NotifyType value) => DropdownMenuItem<NotifyType>(
                value: value,
                child: Center(
                  child: We2upText(
                    strings.translateCrmNotifyType(value),
                  ),
                ),
              ),
            )
            .toList(),
      ),
    );
  }
}

extension AppLocalizationsExtension on AppLocalizations {
  String translateCrmNotifyType(NotifyType followUpType) {
    switch (followUpType) {
      case NotifyType.day:
        return day;
      case NotifyType.hour:
        return hour;
      case NotifyType.minute:
        return minute;
    }
  }
}
