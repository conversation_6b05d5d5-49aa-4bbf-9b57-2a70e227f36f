import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:multi_select_flutter/chip_display/multi_select_chip_display.dart';
import 'package:multi_select_flutter/dialog/multi_select_dialog_field.dart';
import 'package:multi_select_flutter/util/multi_select_list_type.dart';
import 'package:sizer/sizer.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/data/models/selected_price_group_info.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';

import '../../bloc/single_product/single_product_cubit.dart';


class PriceGroupsMultiSelect extends StatelessWidget {
  const PriceGroupsMultiSelect({
    super.key,
    required this.onConfirmed,
    required this.selectedPriceGroups,
  });

  final List<SelectedPriceGroupInfo> selectedPriceGroups;
  final void Function(List<SelectedPriceGroupInfo>) onConfirmed;

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;

    return MultiSelectDialogField<SelectedPriceGroupInfo>(
      items: context.read<SingleProductCubit>().priceGroupsMultiSelectList,
      listType: MultiSelectListType.CHIP,
      initialValue: selectedPriceGroups,
      onConfirm: onConfirmed,
      confirmText: We2upText(strings.okay),
      cancelText: We2upText(strings.cancel),
      title: We2upText(strings.all_price_groups),
      chipDisplay: MultiSelectChipDisplay.none(),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8.sp),
        border: Border.all(width: 0.5.sp),
      ),
      buttonText: We2upText(strings.priceGroups),
      buttonIcon: const Icon(Icons.arrow_drop_down),
    );
  }
}
