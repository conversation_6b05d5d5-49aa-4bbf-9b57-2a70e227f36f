import 'package:flutter/services.dart';

class MaxValueInputFormatter extends TextInputFormatter {
  final num? maxValue;

  MaxValueInputFormatter(this.maxValue);

  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    final input = newValue.text;

    if (input.isEmpty) {
      // If the new value is empty, return as is
      return newValue;
    }

    num parsedValue = num.tryParse(input) ?? 0;

    if (maxValue != null && parsedValue > maxValue!) {
      // If the input exceeds the maximum value, convert it to the maximum value
      parsedValue = maxValue!;
    }

    if (parsedValue == num.tryParse(oldValue.text)) {
      // If the parsed value is the same as the previous value, do nothing
      return newValue;
    }

    final newText = parsedValue.toString();
    final selectionIndex = newText.length;

    return TextEditingValue(
      text: newText,
      selection: TextSelection.collapsed(offset: selectionIndex),
      composing: TextRange.empty,
    );
  }
}