// import 'package:cached_network_image/cached_network_image.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:go_router/go_router.dart';
// import 'package:idkit_inputformatters/idkit_inputformatters.dart';
// import 'package:sizer/sizer.dart';
// import 'package:we2up/presentation/widgets/product_number_text_field.dart';
// import 'package:we2up/presentation/widgets/show_box.dart';
// import 'package:we2up/presentation/widgets/stock_number_box.dart';
// import 'package:we2up/presentation/widgets/tax_dropdown_button.dart';
// import 'package:we2up/presentation/widgets/unit_dropdown_button.dart';
// import 'package:flutter_gen/gen_l10n/app_localizations.dart';
// import 'package:we2up/presentation/widgets/variations_dropdown_button.dart';
//
// import '../../bloc/products/products_cubit.dart';
// import '../style_constants.dart';
// import 'max_value_input_formatter.dart';
//
// class ProductGridCard extends StatelessWidget {
//   const ProductGridCard({super.key, required this.id});
//
//   final int id;
//
//   @override
//   Widget build(BuildContext context) {
//     ProductsCubit cubit = ProductsCubit.get(context);
//     final strings = AppLocalizations.of(context)!;
//     var numberController = cubit.allProducts[id]!.quantityController;
//     var discountController = cubit.allProducts[id]!.discountController;
//     final isSingle = cubit.allProducts[id]!.product.type == "variable";
//     return BlocBuilder<ProductsCubit, ProductsState>(
//       builder: (context, state) {
//         return Card(
//           shape: cubit.isCardActive(id) ? activeCardBorder(context) : null,
//           child: SizedBox(
//             width: 90.sp,
//             child: Column(
//               crossAxisAlignment: CrossAxisAlignment.center,
//               children: [
//                 ClipRRect(
//                   borderRadius: BorderRadius.circular(5.sp),
//                   child: CachedNetworkImage(
//                     imageUrl: cubit.getImageUrl(id),
//                     errorWidget: (_, __, ___) => const Icon(Icons.error),
//                     width: 90.sp,
//                     height: 18.w,
//                     fit: BoxFit.fitWidth,
//                   ),
//                 ),
//                 We2upText(
//                   cubit.getProductName(id),
//                   overflow: TextOverflow.ellipsis,
//                   style: Theme.of(context).textTheme.labelLarge,
//                 ),
//                 verticalSpacer,
//                 StockNumberBox(inStock: cubit.availableInStock(id)),
//                 verticalSpacer,
//                 We2upText(
//                   "${cubit.piecePrice(id)} ${strings.pounds}",
//                   maxLines: 1,
//                   overflow: TextOverflow.ellipsis,
//                 ),
//                 verticalSpacer,
//                 ProductAmountTextField(
//                   inColumn: true,
//                   enabled: cubit.allowOverSelling(id),
//                   controller: numberController,
//                   hint: strings.amount.replaceFirst(":", ""),
//                   inputFormatters: [
//                     FilteringTextInputFormatter.allow(
//                       RegExp(r'^\d+\.?\d*$'),
//                     ),
//                     MaxValueInputFormatter(cubit.maxInInvoice(id)),
//                     IDKitNumeralTextInputFormatter.max(
//                       maxValue: cubit.maxInInvoice(id),
//                       maxDecimalDigit: 2,
//                       decimalPoint: true,
//                     ),
//                   ],
//                 ),
//                 SizedBox(height: 4.sp),
//                 ShowBox(text: cubit.getSellPrice(id).toString()),
//                 verticalSpacer,
//                 UnitDropdownButton(id: id),
//                 verticalSpacer,
//                 Row(
//                   mainAxisAlignment: MainAxisAlignment.center,
//                   children: [
//                     if (cubit.cartProducts.contains(id))
//                       IconButton(
//                         onPressed: () {
//                           showDialog(
//                             context: context,
//                             builder: (BuildContext context) {
//                               return AlertDialog(
//                                 title: We2upText(strings.delete_item),
//                                 content: We2upText(strings.delete_question),
//                                 actions: [
//                                   TextButton(
//                                     onPressed: () => context.pop(),
//                                     child: We2upText(strings.cancel),
//                                   ),
//                                   TextButton(
//                                     onPressed: () {
//                                       cubit.deleteFromCart(id);
//                                       context.pop();
//                                     },
//                                     child: We2upText(strings.delete),
//                                   ),
//                                 ],
//                               );
//                             },
//                           );
//                         },
//                         icon: const Icon(Icons.delete),
//                         color: Theme.of(context).colorScheme.error,
//                       ),
//                     if (cubit.cartProducts.contains(id))
//                       IconButton(
//                         onPressed: () => cubit.toggleEdit(id),
//                         icon: const Icon(Icons.edit),
//                         color: Colors.green,
//                       ),
//                   ],
//                 ),
//                 Visibility(
//                   visible: cubit.editable(id),
//                   child: Container(
//                     padding: EdgeInsets.all(5.sp),
//                     width: double.infinity,
//                     child: Column(
//                       children: [
//                         if (isSingle)
//                           VariationsDropdownButton(
//                               cubit.allProducts[id]!.product),
//                         if (isSingle) verticalSpacer,
//                         We2upText(strings.discount),
//                         ProductAmountTextField(
//                           inColumn: true,
//                           enabled: cubit.allowOverSelling(id),
//                           controller: discountController,
//                           hint: strings.table_discount,
//                           inputFormatters: [
//                             FilteringTextInputFormatter.allow(
//                               RegExp(r'^\d+\.?\d*$'),
//                             ),
//                             MaxValueInputFormatter(cubit.maxDiscount(id)),
//                             IDKitNumeralTextInputFormatter.max(
//                               maxValue: cubit.maxDiscount(id),
//                               maxDecimalDigit: 2,
//                               decimalPoint: true,
//                             ),
//                           ],
//                         ),
//                         We2upText(strings.tax),
//                         TaxDropdownButton(
//                           taxRate: cubit.getCurrentPTaxRate(id),
//                           onChanged: (tax) =>
//                               cubit.taxRateChanged(taxRate: tax, id: id),
//                         ),
//                         verticalSpacer,
//                         We2upText(strings.unit_price),
//                         ShowBox(text: cubit.piecePriceAfter(id).toString()),
//                         We2upText(strings.unit_total_price),
//                         ShowBox(text: cubit.getSellPriceAfter(id).toString()),
//                       ],
//                     ),
//                   ),
//                 ),
//               ],
//             ),
//           ),
//         );
//       },
//     );
//   }
// }
