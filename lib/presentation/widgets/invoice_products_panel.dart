import 'package:conditional_builder_null_safety/conditional_builder_null_safety.dart';
import 'package:expandable/expandable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_offline/flutter_offline.dart';
import 'package:go_router/go_router.dart';
import 'package:sizer/sizer.dart';
import 'package:we2up/presentation/widgets/products_dropdown_button.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';

import '../../bloc/products/products_cubit.dart';
import '../../data/db/db_manager.dart';
import '../../utils/route_constants.dart';
import '../style_constants.dart';
import 'bill_of_sale_single_item.dart';

class InvoiceProductsPanel extends StatelessWidget {
  const InvoiceProductsPanel({
    super.key,
    this.isReturn = false,
    this.isPurchase = false,
  });

  final bool isReturn;
  final bool isPurchase;

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    return BlocBuilder<ProductsCubit, ProductsState>(
      builder: (context, state) {
        final cubit = ProductsCubit.get(context);
        return Card(
          child: ExpandablePanel(
            controller: cubit.invoiceProductsPanelController,
            theme: const ExpandableThemeData(hasIcon: false),
            header: Padding(
              padding: EdgeInsets.symmetric(
                horizontal: 10.sp,
                vertical: canCreateProduct() ? 0 : 10.sp,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Visibility(
                    visible: canCreateProduct(),
                    child: ConditionalBuilder(
                      condition: !isReturn,
                      builder: (context) {
                        return OfflineBuilder(
                          connectivityBuilder: (
                            BuildContext context,
                            dynamic connectivity,
                            Widget child,
                          ) {
                            bool connected;

                            if (connectivity is List<ConnectivityResult>) {
                              connected = !connectivity
                                  .contains(ConnectivityResult.none);
                            } else if (connectivity is ConnectivityResult) {
                              connected =
                                  connectivity != ConnectivityResult.none;
                            } else {
                              connected = false;
                            }

                            return IconButton(
                              onPressed: connected && loginData.isProductsReady
                                  ? () => context.push(
                                        addProductPage,
                                        extra: cubit.selectedLocation,
                                      )
                                  : null,
                              icon: const Icon(Icons.add_business_outlined),
                            );
                          },
                          child: const SizedBox(),
                        );
                      },
                      fallback: (_) => const SizedBox(),
                    ),
                  ),
                  We2upText(
                    isPurchase
                        ? strings.purchase_products
                        : strings.invoice_products,
                    style: Theme.of(context).textTheme.labelLarge,
                  ),
                  const Icon(Icons.keyboard_arrow_down),
                ],
              ),
            ),
            expanded: const SizedBox(),
            collapsed: Padding(
              padding: EdgeInsets.all(4.sp),
              child: Column(
                children: [
                  if (!isReturn)
                    Row(
                      children: [
                        Expanded(
                          child: ProductsDropdownButton(
                            onChanged: (p) => cubit.addProductToCart(
                              context,
                              product: p,
                            ),
                            isInvoice: true,
                            productsList: cubit.filteredProductList(
                              isPurchase: isPurchase ||
                                  (cubit.currentBillData?.quotationPage ??
                                      false),
                            ),
                            isPurchase: isPurchase,
                          ),
                        ),
                        IconButton(
                          icon: Icon(
                            Icons.document_scanner_outlined,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                          onPressed: () => cubit.scanBarcode(context),
                        ),
                      ],
                    ),
                  if (!isReturn) verticalSpacer,
                  ListView.separated(
                    physics: const NeverScrollableScrollPhysics(),
                    shrinkWrap: true,
                    itemCount: cubit
                        .filteredProductList(
                          isPurchase: isPurchase ||
                              (cubit.currentBillData?.quotationPage ?? false) ||
                              cubit.currentBillData?.sell != null,
                        )
                        .where((p) => cubit.cartProducts.contains(p.id))
                        .length,
                    itemBuilder: (context, index) {
                      final products = cubit
                          .filteredProductList(
                            isPurchase: isPurchase ||
                                (cubit.currentBillData?.quotationPage ??
                                    false) ||
                                cubit.currentBillData?.sell != null,
                          )
                          .where((p) => cubit.cartProducts.contains(p.id))
                          .toList();
                      // Convert set to list
                      final cartProductsList = cubit.cartProducts.toList();
                      // Sort the products list
                      // based on the order of cartProducts in reverse order
                      products.sort((a, b) {
                        final indexOfA = cartProductsList.indexOf(a.id);
                        final indexOfB = cartProductsList.indexOf(b.id);
                        return indexOfB.compareTo(indexOfA);
                      });
                      return BillOfSaleSingleItem(
                        product: products[index],
                        isPurchase: isPurchase,
                        isReturn: isReturn,
                      );
                    },
                    separatorBuilder: (c, i) => Divider(
                      height: 8.sp,
                      color: Theme.of(context).colorScheme.error,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
