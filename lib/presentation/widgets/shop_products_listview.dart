import 'package:flutter/material.dart';
import 'package:we2up/presentation/widgets/shop_product_card.dart';

import '../../data/models/product.dart';
import '../../utils/we2up_constants.dart';

class ShopProductsGridView extends StatelessWidget {
  const ShopProductsGridView({
    super.key,
    required this.products,
    this.isPurchase = false,
  });

  final List<Product> products;
  final bool isPurchase;

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      padding: EdgeInsets.all(8.0),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: calculateCrossAxisCount(context),
        childAspectRatio: 0.85,
        crossAxisSpacing: 8.0,
        mainAxisSpacing: 8.0,
      ),
      itemCount: products.length,
      itemBuilder: (context, index) => ShopProductCard(
        product: products[index],
        isPurchase: isPurchase,
      ),
    );
  }
}
