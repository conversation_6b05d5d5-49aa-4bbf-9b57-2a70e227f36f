import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:we2up/bloc/products/products_cubit.dart';
import 'package:we2up/presentation/widgets/payment_account_dropdown_button.dart';
import 'package:we2up/presentation/widgets/payment_method_dropdown_button.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/presentation/widgets/product_number_text_field.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';

import '../style_constants.dart';

class AddPaymentDetailsItem extends StatelessWidget {
  const AddPaymentDetailsItem({super.key, required this.index});

  final int index;

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    return BlocBuilder<ProductsCubit, ProductsState>(
      builder: (context, state) {
        final cubit = ProductsCubit.get(context);
        final invoiceC = cubit.paymentDetailsList[index].invoiceController;
        invoiceC.addListener(cubit.applyPaymentChanged);
        return Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  flex: 3,
                  child: We2upText(
                    strings.payment_method,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                SizedBox(width: 8.0),
                Expanded(
                  flex: 7,
                  child: PaymentMethodDropdownButton(
                    isExpanded: true,
                    onChanged: (pMethod) => cubit.changeCartPaymentMethod(
                      index: index,
                      paymentMethod: pMethod!,
                    ),
                    paymentMethod: cubit.paymentDetailsList[index].paymentMethod,
                  ),
                ),
              ],
            ),
            verticalSpacer,
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  flex: 3,
                  child: We2upText(
                    strings.purchase_value,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                SizedBox(width: 8.0),
                Expanded(
                  flex: 7,
                  child: ProductAmountTextField(
                    controller: invoiceC,
                    width: 45,
                    hint: strings.purchase_value.replaceFirst(":", ""),
                    maxAmount: cubit.getTotalPaymentFees(purchase: false),
                    focusNode: index == 0 ? cubit.paymentFieldFocusNode : null,
                    noInternalExpanded: true, // Prevent internal Expanded usage
                  ),
                ),
              ],
            ),
            verticalSpacer,
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  flex: 3,
                  child: We2upText(
                    strings.payment_account,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                SizedBox(width: 8.0),
                Expanded(
                  flex: 7,
                  child: PaymentAccountDropdownButton(
                    onChanged: (pAccount) => cubit.changeCartPaymentAccount(
                      index: index,
                      paymentAccount: pAccount,
                    ),
                    paymentAccount:
                        cubit.paymentDetailsList[index].paymentAccount,
                    isExpanded: true,
                  ),
                ),
              ],
            ),
            verticalSpacer,
            TextField(
              controller:
              cubit.paymentDetailsList[index].paymentNoteController,
              decoration: InputDecoration(
                hintText: strings.payment_note,
                prefixIcon: const Icon(Icons.note_alt),
                border: const OutlineInputBorder(
                  borderRadius: BorderRadius.all(
                    Radius.circular(7.0),
                  ),
                ),
              ),
            ),
            verticalSpacer,
          ],
        );
      },
    );
  }
}
