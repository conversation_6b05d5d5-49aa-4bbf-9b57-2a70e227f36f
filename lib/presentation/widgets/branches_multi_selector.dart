import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:multi_select_flutter/chip_display/multi_select_chip_display.dart';
import 'package:multi_select_flutter/dialog/multi_select_dialog_field.dart';
import 'package:multi_select_flutter/util/multi_select_list_type.dart';
import 'package:sizer/sizer.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

import '../../bloc/single_product/single_product_cubit.dart';
import '../../data/models/selected_branch_info.dart';

class BranchesMultiSelect extends StatelessWidget {
  const BranchesMultiSelect({
    super.key,
    required this.onConfirmed,
    required this.selectedBranches,
  });

  final List<SelectedBranchInfo> selectedBranches;
  final void Function(List<SelectedBranchInfo>) onConfirmed;

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;

    return MultiSelectDialogField<SelectedBranchInfo>(
      items: context.read<SingleProductCubit>().branchesMultiSelectList,
      listType: MultiSelectListType.CHIP,
      initialValue: selectedBranches,
      onConfirm: onConfirmed,
      confirmText: Text(strings.okay),
      cancelText: Text(strings.cancel),
      title: Text(strings.all_branches),
      chipDisplay: MultiSelectChipDisplay.none(),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8.sp),
        border: Border.all(width: 0.5.sp),
      ),
      buttonText: Text(strings.productLocations),
      buttonIcon: const Icon(Icons.arrow_drop_down),
    );
  }
}
