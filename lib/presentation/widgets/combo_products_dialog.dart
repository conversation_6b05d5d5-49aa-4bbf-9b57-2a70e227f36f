import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/presentation/style_constants.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';

import '../../data/db/db_manager.dart';

class ComboProductsDialog extends StatelessWidget {
  const ComboProductsDialog({
    super.key,
    required this.id,
  });

  final int id;

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    return AlertDialog(
      title: We2upText(strings.combo_products),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: productsBox
            .get(id)!
            .productVariations
            .first
            .variations!
            .first
            .comboVariations
            .map(
              (e) => Column(
                children: [
                  Row(
                    children: [
                      Expanded(
                        flex: 3,
                        child: We2upText(
                          productsBox.values
                                  .where((element) =>
                                      element.productVariations.isNotEmpty &&
                                      element.productVariations.first
                                          .variations!.isNotEmpty &&
                                      element.productVariations.first
                                              .variations!.first.id ==
                                          e?.variationId)
                                  .firstOrNull
                                  ?.name ??
                              "NULL!!!!",
                        ),
                      ),
                      Expanded(child: We2upText(e!.quantity.toString())),
                    ],
                  ),
                  verticalSpacer,
                ],
              ),
            )
            .toList(),
      ),
    );
  }
}
