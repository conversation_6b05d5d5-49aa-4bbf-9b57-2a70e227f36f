import 'package:flutter/material.dart';

import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:gap/gap.dart';
import 'package:sizer/sizer.dart';
import 'package:we2up/presentation/widgets/print_bill.dart';
import 'package:we2up/presentation/widgets/product_number_text_field.dart';
import 'package:we2up/presentation/widgets/show_box.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';
import '../../bloc/products/products_cubit.dart';
import '../../data/models/bill_of_sale_data.dart';

class TotalReturnAmount extends StatelessWidget {
  const TotalReturnAmount({
    super.key,
    required BillData billData,
  }) : _billData = billData;

  final BillData _billData;

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    final cubit = ProductsCubit.get(context);
    final maxDiscount = double.parse(_billData.sell?.discountAmount ??
        _billData.purchase?.discountAmount ??
        "0");
    return Visibility(
      visible: _billData.pReturn || _billData.returnSell,
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 5.sp),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                We2upText(
                  _billData.returnSell
                      ? strings.total_sell_return
                      : strings.total_purchase_return,
                ),
                Gap(5.sp),
                ShowBox(text: cubit.totalReturnValue().toStringAsFixed(4)),
              ],
            ),
            if (maxDiscount > 0 && _billData.returnSell)
              Column(
                children: [
                  const Divider(),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      We2upText(AppLocalizations.of(context)!.discount),
                      SizedBox(width: 8.sp),
                      ProductAmountTextField(
                        controller: cubit.cartDiscountController,
                        onChanged: (d) => cubit.applyCartDiscount(),
                        hint: cubit.cartDiscountTypePercentage
                            ? strings.percentage_discount
                            : strings.fixed_discount,
                        maxAmount: cubit.cartDiscountTypePercentage
                            ? 100
                            : cubit.getCartTotalBeforeTax(
                                purchase: _billData.purchase != null,
                              ),
                                                  focusNode: cubit.discountFieldFocusNode,
                      ),
                      SizedBox(width: 8.sp),
                      Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10.sp),
                          border: Border.all(
                            color: Theme.of(context).colorScheme.inversePrimary,
                            width: 2,
                          ),
                        ),
                        padding: EdgeInsetsDirectional.only(start: 5.sp),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Text("%"),
                            Checkbox(
                              value: cubit.cartDiscountTypePercentage,
                              onChanged: (_) => cubit.switchCartDiscountType(),
                            )
                          ],
                        ),
                      ),
                    ],
                  ),
                  const Divider(),
                ],
              ),
            if (_billData.returnSell)
              const Column(
                children: [
                  Divider(),
                  PrintBillCheckBox(sReturn: true),
                ],
              ),
          ],
        ),
      ),
    );
  }
}
