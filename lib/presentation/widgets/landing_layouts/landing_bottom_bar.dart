import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:salomon_bottom_bar/salomon_bottom_bar.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:sizer/sizer.dart';
import 'package:we2up/bloc/products/products_cubit.dart';
import 'package:we2up/data/db/db_manager.dart';

import '../../../bloc/landing/landing_cubit.dart';
import '../we2up_text.dart';

class LandingBottomBar extends StatelessWidget {
  const LandingBottomBar({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<LandingCubit, LandingState>(
      builder: (context, state) {
        LandingCubit cubit = context.read<LandingCubit>();
        return SalomonBottomBar(
          currentIndex: cubit.currentIndex,
          onTap: (i) {
            cubit.switchPage(i);
            if (i == cubit.salesPageIndex()) {
              ProductsCubit.get(context).getFilteredSales(
                isNewSearch: true,
                refresh: true,
              );
            }
          },
          margin: EdgeInsets.symmetric(horizontal: 15.sp, vertical: 5.sp),
          items: [
            SalomonBottomBarItem(
              icon: const Icon(Icons.home),
              title: We2upText(AppLocalizations.of(context)!.home),
              selectedColor: Theme.of(context).colorScheme.primary,
            ),
            if (canViewProduct())
              SalomonBottomBarItem(
                icon: const Icon(Icons.shopping_cart),
                title: We2upText(AppLocalizations.of(context)!.products),
                selectedColor: Theme.of(context).colorScheme.primary,
              ),
            if (canViewSales())
              SalomonBottomBarItem(
                icon: const Icon(Icons.menu_book),
                title: We2upText(AppLocalizations.of(context)!.sales),
                selectedColor: Theme.of(context).colorScheme.primary,
              ),
          ],
        );
      },
    );
  }
}
