import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_offline/flutter_offline.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:go_router/go_router.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:restart_app/restart_app.dart';
import 'package:sizer/sizer.dart';
import 'package:we2up/bloc/landing/landing_cubit.dart';
import 'package:we2up/data/repository/api_repo.dart';
import 'package:we2up/presentation/style_constants.dart';
import 'package:we2up/presentation/widgets/cash_register_dialog.dart';
import 'package:we2up/presentation/widgets/clock_dialog.dart';
import 'package:we2up/presentation/widgets/my_loading_indicator.dart';
import 'package:we2up/utils/color_schemes.g.dart';
import 'package:we2up/utils/route_constants.dart';

import '../../../data/db/db_manager.dart';
import '../../../data/models/business_settings.dart';
import '../../../main.dart';
import '../../../utils/custom_router.dart';
import '../../../utils/we2up_constants.dart';
import '../color_list_tile.dart';
import '../login_intro_layouts/language_dropdown_button.dart';
import '../we2up_text.dart';

class MainDrawer extends StatelessWidget {
  const MainDrawer({super.key});

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    return Drawer(
      child: ListView(
        padding: EdgeInsets.only(top: 15.sp),
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              Container(
                padding: const EdgeInsets.all(20),
                color: Theme.of(context).colorScheme.primaryContainer,
                child: Center(child: We2upText(strings.ad_space)),
              ),
              Container(
                padding: const EdgeInsets.all(20),
                color: Theme.of(context).colorScheme.inversePrimary,
                child: Center(child: We2upText(strings.ad_space)),
              ),
            ],
          ),
          verticalSpacer,
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              Container(
                padding: const EdgeInsets.all(20),
                color: Theme.of(context).colorScheme.secondaryContainer,
                child: Center(child: We2upText(strings.ad_space)),
              ),
              Container(
                padding: const EdgeInsets.all(20),
                color: Theme.of(context).colorScheme.secondary,
                child: Center(child: We2upText(strings.ad_space)),
              ),
            ],
          ),
          verticalSpacer,
          Column(
            children: [
              ValueListenableBuilder(
                valueListenable: Hive.box(userDetailsBox).listenable(),
                builder: (context, box, _) {
                  return ListTile(
                    enabled: (!loginData.cashRegistered ||
                        (loginData.cashRegistered &&
                            canCloseCashRegister() &&
                            isDataInSync())) &&
                        !stopSyncingTransactions(),
                    title: We2upText(
                      loginData.cashRegistered
                          ? strings.closeCashRegister
                          : strings.openCashRegister,
                    ),
                    trailing: const Icon(Icons.lock_clock),
                    onTap: () async {
                      await InternetConnectionChecker.instance.hasConnection.then(
                          (online) => online
                              ? checkLocationAccess(
                                  context,
                                  () => showDialog(
                                      context: context,
                                      builder: (_) =>
                                          const CashRegisterDialog()))
                              : EasyLoading.showError('Offline'));
                    },
                  );
                },
              ),
              const Divider(),
            ],
          ),
          Visibility(
            visible: canViewCustomer() ||
                canViewSupplier() ||
                canOnlyViewOwnCustomer() ||
                canOnlyViewOwnSupplier(),
            child: Column(
              children: [
                ListTile(
                  title: We2upText(strings.contacts),
                  trailing: const Icon(Icons.contacts),
                  onTap: () => context.push(contactsScreen),
                ),
                const Divider(),
              ],
            ),
          ),
          Visibility(
            visible: canAccessLocationSection(),
            child: Column(
              children: [
                ListTile(
                  title: We2upText(strings.location),
                  trailing: const Icon(Icons.location_on),
                  onTap: () => context.push(locations),
                ),
                const Divider(),
              ],
            ),
          ),
          Visibility(
            visible: canViewCrmDetails(),
            child: Column(
              children: [
                ListTile(
                  title: We2upText(strings.follow_ups),
                  trailing: const Icon(Icons.people_outline_rounded),
                  onTap: () => context.push(followUpsPage),
                ),
                const Divider(),
              ],
            ),
          ),
          Visibility(
            visible: canAccessShipping(),
            child: Column(
              children: [
                ListTile(
                  title: We2upText(strings.shipments),
                  trailing: const Icon(Icons.car_crash_outlined),
                  onTap: () => context.push(shipmentsPage),
                ),
                const Divider(),
              ],
            ),
          ),
          BlocBuilder<LandingCubit, LandingState>(
            builder: (context, state) {
              return OfflineBuilder(
                connectivityBuilder: (
                  BuildContext context,
                  dynamic connectivity,
                  Widget child,
                ) {
                  bool connected;

                  if (connectivity is List<ConnectivityResult>) {
                    connected = !connectivity.contains(ConnectivityResult.none);
                  } else if (connectivity is ConnectivityResult) {
                    connected = connectivity != ConnectivityResult.none;
                  } else {
                    connected = false;
                  }

                  return ListTile(
                    enabled: !isDataInSync() &&
                        connected &&
                        !stopSyncingTransactions(),
                    title: We2upText(strings.sync),
                    trailing: !isDataInSync()
                        ? Icon(
                            Icons.sync_problem,
                            color: Theme.of(context).colorScheme.error,
                          )
                        : const Icon(Icons.done_all),
                    onTap: () => ApiRepository.get()
                        .syncOfflineDataWithAPI(context: context),
                  );
                },
                child: const SizedBox(),
              );
            },
          ),
          const Divider(),
          BlocBuilder<LandingCubit, LandingState>(
            buildWhen: (previous, current) =>
                current is UpdateButtonStateChange,
            builder: (context, state) {
              Duration difference = DateTime.now()
                  .difference(loginData.lastUpdateTime ?? DateTime.now());
              int hours = difference.inHours;
              int minutes = difference.inMinutes.remainder(60);
              return ListTile(
                enabled: !stopSyncingTransactions(),
                title: We2upText(strings.update),
                trailing: const Icon(Icons.update),
                subtitle: Text(
                  "${strings.since} $hours ${strings.hour},"
                  " $minutes ${strings.minute}",
                ),
                // enabled: (state is UpdateButtonStateChange)
                //     ? isDataInSync() && state.canUpdate
                //     : !isWithinLast10Minutes(),
                onTap: () => fetchDataAndStoreAllInHive(context, refresh: true),
              );
            },
          ),
          const Divider(),
          ValueListenableBuilder(
            valueListenable: Hive.box(userDetailsBox).listenable(),
            builder: (context, box, _) {
              return ListTile(
                enabled: !stopSyncingTransactions(),
                title: We2upText(
                  loginData.clockedIn ? strings.clockOut : strings.clockIn,
                ),
                trailing: const Icon(Icons.access_time_sharp),
                onTap: () async {
                  await InternetConnectionChecker.instance.hasConnection.then(
                      (online) => online
                          ? showDialog(
                              context: context,
                              builder: (_) => const ClockDialog())
                          : EasyLoading.showError(strings.offline));
                },
              );
            },
          ),
          // const Divider(),
          // ExpansionTile(
          //   title: We2upText(strings.products),
          //   children: [
          //     ListTile(
          //       title: We2upText(strings.products),
          //       trailing: const Icon(Icons.production_quantity_limits),
          //       onTap: null,
          //     ),
          //     const Divider(),
          //     ListTile(
          //       title: We2upText(strings.new_product),
          //       trailing: const Icon(Icons.add),
          //       onTap: null,
          //     ),
          //     const Divider(),
          //     ListTile(
          //       title: We2upText("Label Printer"),
          //       trailing: const Icon(Icons.print),
          //       onTap: () => context.push(labelPrinterScreen),
          //     ),
          //   ],
          // ),
          const Divider(),
          ExpansionTile(
            title: We2upText(strings.reports),
            children: [
              Visibility(
                visible: canViewCashRegisterDetails(),
                child: Column(
                  children: [
                    ListTile(
                      title: We2upText(strings.shifts),
                      trailing: const Icon(Icons.my_library_books_sharp),
                      onTap: () async {
                        await InternetConnectionChecker.instance.hasConnection.then(
                            (online) => online
                                ? context.push(shiftsScreen)
                                : EasyLoading.showError(strings.offline));
                      },
                    ),
                    const Divider(),
                  ],
                ),
              ),
              Visibility(
                visible: canViewProfitLossReport(),
                child: Column(
                  children: [
                    ListTile(
                      title: We2upText(strings.profit_loss_report),
                      trailing: const Icon(Icons.note_alt_rounded),
                      onTap: () async {
                        await InternetConnectionChecker.instance.hasConnection.then(
                            (online) => online
                                ? context.push(profitLossScreen)
                                : EasyLoading.showError(strings.offline));
                      },
                    ),
                    const Divider(),
                  ],
                ),
              ),
              Visibility(
                visible: canViewSuppliersPaymentReport(),
                child: Column(
                  children: [
                    ListTile(
                      title: We2upText(strings.supplier_payments),
                      trailing: const Icon(FontAwesomeIcons.moneyCheckDollar),
                      onTap: () =>
                          context.push(paymentsReportScreen, extra: true),
                    ),
                    const Divider(),
                  ],
                ),
              ),
              Visibility(
                visible: canViewCustomersPaymentReport(),
                child: Column(
                  children: [
                    ListTile(
                      title: We2upText(strings.customer_collections),
                      trailing: const Icon(FontAwesomeIcons.moneyCheck),
                      onTap: () =>
                          context.push(paymentsReportScreen, extra: false),
                    ),
                    const Divider(),
                  ],
                ),
              ),
              Visibility(
                visible: canAccessSellReturn(),
                child: Column(
                  children: [
                    ListTile(
                      title: We2upText(strings.sales_returns),
                      trailing: const Icon(Icons.attach_money),
                      onTap: () => context.push(salesReturns),
                    ),
                    const Divider(),
                  ],
                ),
              ),
              Visibility(
                visible: canViewPurchase(),
                child: Column(
                  children: [
                    ListTile(
                      title: We2upText(strings.purchases),
                      trailing: const Icon(Icons.credit_card),
                      onTap: () => context.push(purchasesPage),
                    ),
                    const Divider(),
                  ],
                ),
              ),
              Visibility(
                visible: canViewPurchaseReturns(),
                child: Column(
                  children: [
                    ListTile(
                      title: We2upText(strings.purchase_returns),
                      trailing: const Icon(Icons.shopping_cart_outlined),
                      onTap: () => context.push(purchasesReturnPage),
                    ),
                    const Divider(),
                  ],
                ),
              ),
              Visibility(
                visible: canViewAllExpenses(),
                child: Column(
                  children: [
                    ListTile(
                      title: We2upText(strings.expenses_and_revenues),
                      trailing: const Icon(Icons.wallet),
                      onTap: () => context.push(expensesPage),
                    ),
                    const Divider(),
                  ],
                ),
              ),
              Visibility(
                visible: canViewSellPaymentReport(),
                child: Column(
                  children: [
                    ListTile(
                      title: We2upText(strings.customers_dues),
                      trailing: const Icon(FontAwesomeIcons.wallet),
                      onTap: () => context.push(contactDues),
                    ),
                    const Divider(),
                  ],
                ),
              ),
              Visibility(
                visible: canViewPurchasePaymentReport(),
                child: ListTile(
                  title: We2upText(strings.suppliers_dues),
                  trailing: const Icon(FontAwesomeIcons.sackDollar),
                  onTap: () => context.push(contactDues, extra: true),
                ),
              ),
            ],
          ),
          const Divider(),
          ExpansionTile(
            title: We2upText(strings.settings),
            children: [
              const LanguageDropdownButton(drawer: true),
              const Divider(),
              ListTile(
                title: We2upText(strings.business_data),
                trailing: const Icon(Icons.business),
                onTap: () => context.push(businessDataPage),
              ),
              const Divider(),
              ListTile(
                title: We2upText(strings.dark_mode),
                trailing: ValueListenableBuilder(
                  valueListenable: Hive.box(settingsBox).listenable(),
                  builder: (context, box, _) {
                    final bool isDark = box.get("isDark", defaultValue: false);
                    return Switch(
                      value: isDark,
                      onChanged: (val) => box.put("isDark", val),
                    );
                  },
                ),
              ),
              const Divider(),
              ListTile(
                title: We2upText(strings.color_scheme),
                trailing: ValueListenableBuilder(
                  valueListenable: Hive.box(settingsBox).listenable(),
                  builder: (context, box, _) {
                    final int currentIndex = box.get("currentThemeIndex") ?? 0;
                    return Container(
                      decoration: dropDownDecoration(context),
                      child: DropdownButton<int>(
                        value: currentIndex,
                        isDense: true,
                        padding: EdgeInsets.all(2.sp),
                        onChanged: (index) {
                          box.put("currentThemeIndex", index);
                          box.put("primaryLight", null);
                        },
                        underline: const SizedBox(),
                        items: List.generate(
                          lightSchemes.length,
                          (index) => DropdownMenuItem(
                            value: index,
                            child: We2upText(
                              "${strings.color_scheme} ${index + 1}",
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
              const Divider(),
              ColorListTile(
                title: strings.background_color,
                message: strings.background_color_message,
                lightKey: "backgroundLight",
                darkKey: "backgroundDark",
                defaultColor: Theme.of(context).colorScheme.surface,
              ),
              ColorListTile(
                title: strings.primary_color,
                message: strings.select_primary_color,
                lightKey: "primaryLight",
                darkKey: "primaryDark",
                defaultColor: Theme.of(context).colorScheme.primary,
              ),
              const Divider(),
            ],
          ),
          const Divider(),
          ListTile(
            title: We2upText(strings.contact_us),
            trailing: const Icon(FontAwesomeIcons.whatsapp),
            onTap: () => navigateToWhatsapp("0201015156872"),
          ),
          const Divider(),
          BlocBuilder<LandingCubit, LandingState>(
            builder: (context, state) {
              return OfflineBuilder(
                connectivityBuilder: (
                  BuildContext context,
                  dynamic connectivity,
                  Widget child,
                ) {
                  bool connected;

                  if (connectivity is List<ConnectivityResult>) {
                    connected = !connectivity.contains(ConnectivityResult.none);
                  } else if (connectivity is ConnectivityResult) {
                    connected = connectivity != ConnectivityResult.none;
                  } else {
                    connected = false;
                  }

                  return ListTile(
                    enabled: isDataInSync() && connected && !stopSyncingTransactions(),
                    title: We2upText(strings.sign_out),
                    trailing: connected
                        ? const Icon(Icons.exit_to_app)
                        : Icon(
                            Icons.wifi_off,
                            color: Theme.of(context).colorScheme.error,
                          ),
                    onTap: () {
                      ApiRepository.get().logout().then((loggedOut) {
                        if (loggedOut) {
                          EasyLoading.showInfo(strings.signedOut);
                          loginData.resetValues();
                          loginData.save();
                          if (defaultTargetPlatform == TargetPlatform.windows || defaultTargetPlatform == TargetPlatform.macOS) {
                            runApp(MyApp(appRouter: AppRouter()));
                          } else {
                            Restart.restartApp();
                          }
                        } else {
                          EasyLoading.showError("Error!");
                        }
                      });
                    },
                  );
                },
                child: const SizedBox(),
              );
            },
          ),
          const Divider(),
          Padding(
            padding: EdgeInsets.symmetric(vertical: 15.sp),
            child: Column(
              children: [
                Center(
                  child: GestureDetector(
                    onTap: () => navigateToWhatsapp("0201015156872"),
                    child: ClipOval(
                      child: Image.asset(
                        "images/we2up_logo.jpg",
                        width: 35.w,
                      ),
                    ),
                  ),
                ),
                verticalSpacer,
                BlocBuilder<LandingCubit, LandingState>(
                  buildWhen: (_, c) =>
                      c is AppVersionLoading || c is AppVersionReady,
                  builder: (context, state) {
                    if (state is AppVersionLoading) {
                      return const MyLoadingIndicator();
                    }
                    return Text(
                      "${strings.application_version} "
                      "${LandingCubit.get(context).packageInfo.version}",
                    );
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
