import 'package:expandable/expandable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_offline/flutter_offline.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:go_router/go_router.dart';
import 'package:sizer/sizer.dart';
import 'package:we2up/bloc/contacts/contacts_cubit.dart';
import 'package:we2up/presentation/widgets/price_group_list_dropdown_button.dart';
import 'package:we2up/presentation/widgets/show_box.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';

import '../../bloc/contact_payment/contact_payment_cubit.dart';
import '../../bloc/products/products_cubit.dart';
import '../../data/db/db_manager.dart';
import '../../data/models/business_settings.dart';
import '../../utils/route_constants.dart';
import '../style_constants.dart';
import 'contacts_dropdown_button.dart';
import 'locations_dropdown_button.dart';

class CustomerDataPanel extends StatelessWidget {
  const CustomerDataPanel({super.key, this.isPurchase = false});

  final bool isPurchase;

  bool canAddContact() =>
      (canCreateCustomer() && !isPurchase) ||
      (canCreateSupplier() && isPurchase);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ProductsCubit, ProductsState>(
      builder: (context, state) {
        final cubit = ProductsCubit.get(context);
        final strings = AppLocalizations.of(context)!;
        return Card(
          child: ExpandablePanel(
            controller: cubit.customerDataPanelController,
            theme: const ExpandableThemeData(hasIcon: false),
            header: Padding(
              padding: EdgeInsets.symmetric(
                horizontal: 10.sp,
                vertical: canAddContact() ? 0 : 10.sp,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  AddContactButton(canAddContact: canAddContact()),
                  We2upText(
                    isPurchase ? strings.supplier_data : strings.customer_data,
                    style: Theme.of(context).textTheme.labelLarge,
                  ),
                  const Icon(Icons.keyboard_arrow_down),
                ],
              ),
            ),
            expanded: const SizedBox(),
            collapsed: Padding(
              padding: EdgeInsets.all(4.sp),
              child: Column(
                children: [
                  const BusinessLocationInCustomerPanel(),
                  verticalSpacer,
                  CustomerNameInCustomerPanel(isPurchase: isPurchase),
                  verticalSpacer,
                  PriceGroupInCustomerPanel(isPurchase: isPurchase),
                  const DueAmountInCustomerPanel(isPurchase: false),
                  verticalSpacer,
                  NetBillInCustomerPanel(isPurchase: isPurchase),
                  verticalSpacer,
                  DueAfterInCustomerPanel(isPurchase: isPurchase),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

class DueAfterInCustomerPanel extends StatelessWidget {
  const DueAfterInCustomerPanel({super.key, required this.isPurchase});

  final bool isPurchase;

  @override
  Widget build(BuildContext context) {
    final cubit = ProductsCubit.get(context);
    final strings = AppLocalizations.of(context)!;
    final theme = Theme.of(context);
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          flex: 3,
          child: We2upText(
            "${strings.due_after_this_bill}:",
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        SizedBox(width: 8.0),
        Expanded(
          flex: 7,
          child: BlocBuilder<ProductsCubit, ProductsState>(
          builder: (context, state) {
            final due =
                double.tryParse(cubit.cartCustomerContact?.due ?? "0.0") ?? 0.0;
            final currentBillTotal =
                cubit.getTotalPaymentFees(purchase: isPurchase);
            final isEditing = cubit.currentBillData != null;

            // If editing, we need to consider the original bill's total
            if (isEditing) {
              final originalBillTotal = isPurchase
                  ? double.tryParse(
                          cubit.currentBillData?.purchase?.finalTotal ??
                              "0.0") ??
                      0.0
                  : double.tryParse(
                          cubit.currentBillData?.sell?.finalTotal ?? "0.0") ??
                      0.0;

              // For due after, we need to:
              // 1. Start with the current due (which already has original bill subtracted)
              // 2. Add back the original bill total
              // 3. Add the new bill total
              final displayDue = (due - originalBillTotal + currentBillTotal)
                  .toStringAsFixed(4);
              return ShowBox(
                text: displayDue,
                color: theme.colorScheme.inversePrimary.withAlpha(102),
                width: 45,
              );
            } else {
              // For new bills, just add current bill to due
              final displayDue = (due + currentBillTotal).toStringAsFixed(4);
              return ShowBox(
                text: displayDue,
                color: theme.colorScheme.inversePrimary.withAlpha(102),
                width: 45,
              );
            }
          },
        ),
        ),
      ],
    );
  }
}

class NetBillInCustomerPanel extends StatelessWidget {
  const NetBillInCustomerPanel({super.key, required this.isPurchase});

  final bool isPurchase;

  @override
  Widget build(BuildContext context) {
    final cubit = ProductsCubit.get(context);
    final strings = AppLocalizations.of(context)!;
    final theme = Theme.of(context);
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          flex: 3,
          child: We2upText(
            strings.net_bill,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        SizedBox(width: 8.0),
        Expanded(
          flex: 7,
          child: ShowBox(
            text: cubit
                .getTotalPaymentFees(purchase: isPurchase)
                .toStringAsFixed(4),
            color: theme.colorScheme.tertiaryContainer,
            width: 45,
          ),
        ),
      ],
    );
  }
}

class PriceGroupInCustomerPanel extends StatelessWidget {
  const PriceGroupInCustomerPanel({super.key, required this.isPurchase});

  final bool isPurchase;

  @override
  Widget build(BuildContext context) {
    final cubit = ProductsCubit.get(context);
    final strings = AppLocalizations.of(context)!;
    return Visibility(
      visible: sellingPriceGroupsBox.values
          .any((e) => canUseSellingPrice(e.id) && !isPurchase),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                flex: 3,
                child: We2upText(
                  "${strings.price_group_list}:",
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              SizedBox(width: 8.0),
              Expanded(
                flex: 7,
                child: Container(
                  height: 48.0, // Fixed height to match other input fields
                  decoration: dropDownDecoration(context),
                  child: PriceGroupListDropdown(
                    onChanged: cubit.changeCartPriceGroup,
                    value: cubit.cartPriceGroup,
                    isExpanded: true,
                  ),
                ),
              ),
            ],
          ),
          if (showShopProductsGridView()) verticalSpacer,
        ],
      ),
    );
  }
}

class DueAmountInCustomerPanel extends StatelessWidget {
  const DueAmountInCustomerPanel({super.key, required this.isPurchase});

  final bool isPurchase;

  @override
  Widget build(BuildContext context) {
    final cubit = ProductsCubit.get(context);
    final strings = AppLocalizations.of(context)!;

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          flex: 3,
          child: We2upText(
            "${strings.invoice_previous_balance}:",
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        SizedBox(width: 8.0),
        Expanded(
          flex: 7,
          child: BlocBuilder<ProductsCubit, ProductsState>(
          builder: (context, state) {
            final due =
                double.tryParse(cubit.cartCustomerContact?.due ?? "0.0") ?? 0.0;
            // Check if we're editing an existing bill by looking at the current bill data
            final isEditing = cubit.currentBillData?.sell != null ||
                cubit.currentBillData?.purchase != null;

            if (isEditing) {
              final originalBillTotal = isPurchase
                  ? double.tryParse(
                          cubit.currentBillData?.purchase?.finalTotal ??
                              "0.0") ??
                      0.0
                  : double.tryParse(
                          cubit.currentBillData?.sell?.finalTotal ?? "0.0") ??
                      0.0;

              // For previous balance when editing, we need to subtract the original bill total
              // since the current due already includes it
              final displayDue = (due - originalBillTotal).toStringAsFixed(4);
              return ShowBox(
                text: displayDue,
                width: 45,
              );
            } else {
              // For new bills, just show the due amount
              return ShowBox(
                text: due.toStringAsFixed(4),
                width: 45,
              );
            }
          },
        ),
        ),
      ],
    );
  }
}

class CustomerNameInCustomerPanel extends StatelessWidget {
  const CustomerNameInCustomerPanel({super.key, required this.isPurchase});

  final bool isPurchase;

  @override
  Widget build(BuildContext context) {
    final cubit = ProductsCubit.get(context);
    final strings = AppLocalizations.of(context)!;
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.error.withAlpha(51),
        borderRadius: BorderRadius.circular(16.sp),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            flex: 3,
            child: We2upText(
              isPurchase ? strings.supplier_name : strings.customer_name,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          SizedBox(width: 8.0),
          Expanded(
            flex: 7,
            child: ContactsDropdownButton(
              filterType: isPurchase ? "supplier" : 'customer',
              onChanged: cubit.changeSelectedContact,
              contact: cubit.cartCustomerContact,
            ),
          ),
          Visibility(
            visible: cubit.cartCustomerContact != null &&
                (canCreateSellPayment() &&
                        cubit.cartCustomerContact?.type == "customer" ||
                    canCreatePurchasePayment() &&
                        cubit.cartCustomerContact?.type == "supplier"),
            child: IconButton(
              onPressed: () {
                context.push(
                  contactPaymentScreen,
                  extra: cubit.cartCustomerContact!.type == "customer",
                );
                ContactPaymentCubit.get(context)
                    .changeCustomerContact(cubit.cartCustomerContact);
              },
              icon: const Icon(FontAwesomeIcons.dollarSign),
            ),
          )
        ],
      ),
    );
  }
}

class BusinessLocationInCustomerPanel extends StatelessWidget {
  const BusinessLocationInCustomerPanel({super.key});

  @override
  Widget build(BuildContext context) {
    final cubit = ProductsCubit.get(context);
    final strings = AppLocalizations.of(context)!;
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          flex: 3,
          child: We2upText(
            strings.branch,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        SizedBox(width: 8.0),
        Expanded(
          flex: 7,
          child: BusinessLocationDropdown(
            isExpanded: true,
            onChanged: (l) => cubit.changeSelectedLocation(location: l!),
            value: cubit.selectedLocation,
          ),
        ),
      ],
    );
  }
}

class AddContactButton extends StatelessWidget {
  const AddContactButton({super.key, required this.canAddContact});

  final bool canAddContact;

  @override
  Widget build(BuildContext context) {
    return Visibility(
      visible: canAddContact,
      child: OfflineBuilder(
        connectivityBuilder: (
          BuildContext context,
          dynamic connectivity,
          Widget child,
        ) {
          bool connected;

          if (connectivity is List<ConnectivityResult>) {
            connected = !connectivity.contains(ConnectivityResult.none);
          } else if (connectivity is ConnectivityResult) {
            connected = connectivity != ConnectivityResult.none;
          } else {
            connected = false;
          }

          return IconButton(
            onPressed: connected && loginData.isContactsReady
                ? () => context.push(
                      addEditContact,
                      extra: (null, ContactsCubit(), true),
                    )
                : null,
            icon: const Icon(Icons.person_add),
          );
        },
        child: const SizedBox(),
      ),
    );
  }
}
