import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';

import '../../data/db/db_manager.dart';
import '../../data/models/product.dart';
import '../style_constants.dart';

class NPCategoryDropdownButton extends StatelessWidget {
  const NPCategoryDropdownButton({
    super.key,
    this.isExpanded = false,
    this.padding = 5,
    required this.onChanged,
    required this.category,
  });

  final bool isExpanded;
  final double padding;
  final ValueChanged<ProductCategory?> onChanged;
  final ProductCategory? category;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: dropDownDecoration(context),
      child: DropdownButton<ProductCategory?>(
        value: category,
        padding: EdgeInsets.all(padding.sp),
        isDense: true,
        isExpanded: isExpanded,
        onChanged: onChanged,
        underline: const SizedBox(),
        items: [
          DropdownMenuItem<ProductCategory?>(
            value: null,
            child: Center(
              child: We2upText(AppLocalizations.of(context)!.nothing),
            ),
          ),
          ...categoriesBox.values
              .map<DropdownMenuItem<ProductCategory>>((ProductCategory value) {
            return DropdownMenuItem<ProductCategory>(
              value: value,
              child: Center(child: We2upText(value.name)),
            );
          }),
        ],
      ),
    );
  }
}
