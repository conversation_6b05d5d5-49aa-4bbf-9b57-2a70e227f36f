import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';

import '../../data/models/business_settings.dart';

class ShowTotal extends StatelessWidget {
  const ShowTotal({
    super.key,
    required this.amountText,
    required this.amountValue,
  });

  final String amountText;
  final String amountValue;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 8.sp),
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
          border: Border.all(
            color: Theme.of(context).colorScheme.error,
            width: 1.sp,
          ),
          borderRadius: BorderRadius.circular(15.sp),
        ),
        padding: EdgeInsets.symmetric(
          horizontal: 10.sp,
          vertical: 5.sp,
        ),
        child: Row(
          children: [
            Expanded(child: We2upText(amountText)),
            Expanded(child: We2upText("$amountValue ${currencySymbol()}")),
          ],
        ),
      ),
    );
  }
}
