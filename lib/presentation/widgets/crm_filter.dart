import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:we2up/bloc/crm/crm_cubit.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/data/models/follow_up_to_api.dart';
import 'package:we2up/presentation/widgets/crm_status_dropdown_button.dart';
import 'package:we2up/presentation/widgets/sale_filter_dialog.dart';
import 'package:we2up/presentation/widgets/user_dropdown_button.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';

import '../style_constants.dart';
import 'contacts_dropdown_button.dart';
import 'date_range_dropdown.dart';
import 'date_range_list_tile.dart';

class FollowUpFilter extends StatelessWidget {
  const FollowUpFilter({super.key});

  @override
  Widget build(BuildContext context) {
    final cubit = CrmCubit.get(context);
    final strings = AppLocalizations.of(context)!;
    cubit.followUpFilterTitle.addListener(cubit.followUpFilterTitleChanged);
    return BlocBuilder<CrmCubit, CrmState>(
      builder: (context, state) {
        return AlertDialog(
          title: Center(child: We2upText(strings.filter)),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextFormField(
                  controller: cubit.followUpFilterTitle,
                  decoration: InputDecoration(
                    labelText: strings.title,
                    prefixIcon: const Icon(Icons.title),
                    border: myTextFieldBorder,
                  ),
                ),
                CustomDivider(strings.date_range),
                DateRangeDropdown(
                  changeStartDate: cubit.changeFilterStartDate,
                  changeEndDate: cubit.changeFilterEndDate,
                  onChanged: cubit.updateDateRange,
                  range: cubit.range,
                ),
                DateRangeListTile(
                  isStartDate: true,
                  changeDate: cubit.changeFilterStartDate,
                  changeRange: cubit.updateDateRange,
                  date: cubit.filterStartDate,
                ),
                DateRangeListTile(
                  isStartDate: false,
                  startDate: cubit.filterStartDate,
                  changeDate: cubit.changeFilterEndDate,
                  changeRange: cubit.updateDateRange,
                  date: cubit.filterEndDate,
                ),
                CustomDivider(strings.user),
                UsersDropdownButton(
                  user: cubit.filterUser,
                  onChanged: cubit.updateCRMFilterUser,
                  controller: cubit.userTextEditingController,
                ),
                CustomDivider(strings.customer),
                ContactsDropdownButton(
                  contact: cubit.followUpFilterContact,
                  onChanged: cubit.changeFollowUpFilterContact,
                ),
                verticalSpacer,
                const Divider(),
                DropdownButtonFormField<Status>(
                  value: cubit.followUpFilterStatus,
                  isExpanded: true,
                  onChanged: cubit.changeFollowUpFilterStatus,
                  items: [
                    // Add a null or empty value as the first item
                    DropdownMenuItem<Status>(
                      value: null,
                      child: Center(
                        child: We2upText(strings.translateCrmStatus(null)),
                      ),
                    ),
                    ...Status.values.map<DropdownMenuItem<Status>>(
                      (Status value) => DropdownMenuItem<Status>(
                        value: value,
                        child: Center(
                          child: We2upText(strings.translateCrmStatus(value)),
                        ),
                      ),
                    ),
                  ],
                  decoration: InputDecoration(labelText: strings.status),
                ),
              ],
            ),
          ),
          actionsAlignment: MainAxisAlignment.spaceAround,
          actions: [
            ElevatedButton(
              child: We2upText(strings.reset),
              onPressed: () {
                showDialog(
                  context: context,
                  builder: (context) {
                    return AlertDialog(
                      actionsAlignment: MainAxisAlignment.spaceAround,
                      title:
                          Center(child: We2upText(strings.clear_all_filters)),
                      actions: [
                        ElevatedButton(
                          child: We2upText(strings.clear),
                          onPressed: () {
                            cubit.resetFollowUpsFilters();
                            context.pop();
                            context.pop();
                          },
                        ),
                        ElevatedButton(
                          child: We2upText(strings.cancel),
                          onPressed: () => context.pop(),
                        ),
                      ],
                    );
                  },
                );
              },
            ),
            ElevatedButton(
              child: We2upText(strings.apply),
              onPressed: () {
                context.pop();
                cubit.getFilteredFollowUps(isNewSearch: true);
              },
            ),
          ],
        );
      },
    );
  }
}
