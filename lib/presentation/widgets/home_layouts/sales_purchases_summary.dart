import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:sizer/sizer.dart';

import '../../../bloc/landing/landing_cubit.dart';
import '../../style_constants.dart';
import '../we2up_text.dart';

class SalesAndPurchasesSummary extends StatelessWidget {
  const SalesAndPurchasesSummary({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<LandingCubit, LandingState>(
      builder: (context, state) {
        final strings = AppLocalizations.of(context)!;
        final lCubit = LandingCubit.get(context);
        return Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            Flexible(
              child: Card(
                surfaceTintColor: Theme.of(context).colorScheme.surface,
                shape: homeCardShape,
                child: Padding(
                  padding: EdgeInsets.all(5.sp),
                  child: Column(
                    children: [
                      Center(
                        child: We2upText(
                          strings.sales,
                          style: TextStyle(
                            fontSize: 16.sp,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                        ),
                      ),
                      const Divider(),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(child: We2upText(strings.number, maxLines: 2)),
                          We2upText(lCubit.shiftSalesTotalNumber()),
                        ],
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(child: We2upText(strings.total, maxLines: 2)),
                          We2upText(lCubit.shiftSalesTotalAmount()),
                        ],
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(child: We2upText(strings.paid_up, maxLines: 2)),
                          We2upText(lCubit.shiftSalesPaidUp()),
                        ],
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(child: We2upText(strings.due, maxLines: 2)),
                          We2upText(lCubit.shiftSalesDue()),
                        ],
                      ),
                      // Row(
                      //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      //   children: [
                      //     Expanded(
                      //       child: We2upText(strings.sum_returns, maxLines: 2),
                      //     ),
                      //     We2upText(lCubit.shiftSalesReturns()),
                      //   ],
                      // ),
                    ],
                  ),
                ),
              ),
            ),
            Flexible(
              child: Card(
                surfaceTintColor: Theme.of(context).colorScheme.surface,
                shape: homeCardShape,
                child: Padding(
                  padding: EdgeInsets.all(5.sp),
                  child: Column(
                    children: [
                      We2upText(
                        strings.purchases,
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                      const Divider(),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(child: We2upText(strings.number, maxLines: 2)),
                          We2upText(lCubit.shiftPurchasesTotalNumber()),
                        ],
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(child: We2upText(strings.total, maxLines: 2)),
                          We2upText(lCubit.shiftPurchasesTotalAmount()),
                        ],
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(child: We2upText(strings.paid_up, maxLines: 2)),
                          We2upText(lCubit.shiftPurchasesPaidUp()),
                        ],
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(child: We2upText(strings.due, maxLines: 2)),
                          We2upText(lCubit.purchasesDue()),
                        ],
                      ),
                      // Row(
                      //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      //   children: [
                      //     Expanded(
                      //       child: We2upText(strings.sum_returns, maxLines: 2),
                      //     ),
                      //     We2upText(lCubit.shiftPurchasesReturns()),
                      //   ],
                      // ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
