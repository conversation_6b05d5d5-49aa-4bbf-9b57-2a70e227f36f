import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sizer/sizer.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

import '../../../bloc/landing/landing_cubit.dart';
import '../../../data/models/business_settings.dart';
import '../../style_constants.dart';
import '../we2up_text.dart';

class RosarySummary extends StatelessWidget {
  const RosarySummary({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<LandingCubit, LandingState>(
      builder: (context, state) {
        final strings = AppLocalizations.of(context)!;
        final lCubit = LandingCubit.get(context);
        return Card(
          surfaceTintColor: Theme.of(context).colorScheme.surface,
          shape: homeCardShape,
          child: Container(
            padding: EdgeInsets.all(5.sp),
            width: double.infinity,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                We2upText(
                  strings.rosary_summary,
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
                const Divider(),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Flexible(
                      child: Column(
                        children: [
                          // Row(
                          //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          //   children: [
                          //     Expanded(
                          //       child: We2upText(
                          //         strings.pay_purchases,
                          //         maxLines: 2,
                          //       ),
                          //     ),
                          //     We2upText(lCubit.shiftPurchasesPaid()),
                          //   ],
                          // ),
                          // const Divider(),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(
                                child: We2upText(
                                  strings.modern_payment,
                                  maxLines: 2,
                                ),
                              ),
                              We2upText(lCubit.shiftSupplierPayment()),
                            ],
                          ),
                          const Divider(),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              We2upText("${strings.expenses} :"),
                              We2upText(lCubit.expensesAmounts()),
                            ],
                          ),
                          const Divider(),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Flexible(
                                child: We2upText("${strings.sales_returns} :"),
                              ),
                              We2upText(
                                lCubit.shiftDetails?.payments.first
                                        .totalSellReturnPaid ??
                                    "0",
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    SizedBox(width: 15.sp),
                    Flexible(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        mainAxisSize: MainAxisSize.max,
                        children: [
                          // Row(
                          //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          //   children: [
                          //     Expanded(
                          //       child: We2upText(
                          //         strings.sale_collection,
                          //         maxLines: 2,
                          //       ),
                          //     ),
                          //     We2upText(lCubit.shiftSalesCollected()),
                          //   ],
                          // ),
                          // const Divider(),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(
                                child: We2upText(
                                  strings.customer_collection,
                                  maxLines: 2,
                                ),
                              ),
                              We2upText(lCubit.shiftCustomerCollection()),
                            ],
                          ),
                          const Divider(),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              We2upText("${strings.revenues} :"),
                              We2upText(lCubit.expensesAmounts(refund: true)),
                            ],
                          ),
                          const Divider(),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Flexible(
                                  child: We2upText(
                                      "${strings.purchase_returns} :")),
                              We2upText(
                                lCubit.shiftDetails?.payments.first
                                        .totalPurchaseReturnPaid ??
                                    "0",
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const Divider(),
                We2upText(
                  "${strings.cash_left} "
                  "${lCubit.remainingCash()} ${currencySymbol()}",
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
