import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:go_router/go_router.dart';
import 'package:sizer/sizer.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:we2up/data/db/db_manager.dart';

import '../../../data/models/bill_of_sale_data.dart';
import '../../../utils/route_constants.dart';
import '../../../utils/we2up_constants.dart';
import '../home_page_card_item.dart';

class ActionButtonsGridView extends StatelessWidget {
  const ActionButtonsGridView({super.key});

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    return GridView.count(
      primary: false,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: EdgeInsets.all(5.sp),
      crossAxisSpacing: 2.sp,
      crossAxisCount: 3,
      childAspectRatio: 1.08,
      children: <Widget>[
        HomePageCardItem(
          icon: FontAwesomeIcons.handHoldingDollar,
          label: strings.bill_of_sale,
          onTap: canCreateSell()
              ? () async => await checkShiftLocationAccess(
                    context,
                    () => context.push(billOfSale, extra: BillData()),
                  )
              : null,
        ),
        HomePageCardItem(
          icon: FontAwesomeIcons.moneyBillTransfer,
          label: strings.sale_return,
          onTap: canAccessSellReturn()
              ? () async => await checkShiftLocationAccess(
                    context,
                    () => context.push(saleReturn),
                  )
              : null,
        ),
        HomePageCardItem(
          icon: FontAwesomeIcons.calculator,
          label: strings.offer_price,
          onTap: canOfferPriceDuringSales()
              ? () async => await context.push(
                    billOfSale,
                    extra: BillData(quotationPage: true),
                  )
              : null,
        ),
        HomePageCardItem(
          icon: FontAwesomeIcons.receipt,
          label: strings.receipt,
          onTap: canCreatePurchase()
              ? () async => await checkShiftLocationAccess(
                    context,
                    () => context.push(
                      purchaseInvoice,
                      extra: BillData(),
                    ),
                  )
              : null,
        ),
        HomePageCardItem(
          icon: FontAwesomeIcons.rotate,
          label: strings.purchase_return,
          onTap: canCreatePurchaseReturn()
              ? () async => await checkShiftLocationAccess(
                    context,
                    () => context.push(purchaseReturn),
                  )
              : null,
        ),
        HomePageCardItem(
          icon: FontAwesomeIcons.globe,
          label: strings.main_site,
          onTap: () {
            launchUrl(
              Uri.parse(webViewUrl()),
              mode: LaunchMode.externalApplication,
            );
            // if (Platform.isWindows) {
            //   launchUrl(Uri.parse(webViewUrl()));
            // } else {
            //   context.push(webViewPage);
            // }
          },
        ),
        HomePageCardItem(
          icon: FontAwesomeIcons.vault,
          label: strings.sale_collection_button,
          onTap: canCreateSellPayment()
              ? () async => await checkShiftLocationAccess(
                    context,
                    () => context.push(contactPaymentScreen, extra: true),
                  )
              : null,
        ),
        HomePageCardItem(
          icon: FontAwesomeIcons.creditCard,
          label: strings.purchase_payment,
          onTap: canCreatePurchasePayment()
              ? () async => await checkShiftLocationAccess(
                    context,
                    () => context.push(contactPaymentScreen, extra: false),
                  )
              : null,
        ),
        HomePageCardItem(
          icon: FontAwesomeIcons.coins,
          label: strings.enter_an_expense,
          onTap: canCreateExpense()
              ? () async => await checkShiftLocationAccess(
                    context,
                    () => context.push(addExpensePage),
                  )
              : null,
        ),
      ],
    );
  }
}
