import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:we2up/bloc/products/products_cubit.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/presentation/widgets/products_dropdown_button.dart';
import 'package:we2up/presentation/widgets/sale_filter_dialog.dart';
import 'package:we2up/presentation/widgets/user_dropdown_button.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';

import '../style_constants.dart';
import 'business_filter_dropdown_button.dart';
import 'contacts_dropdown_button.dart';
import 'date_range_dropdown.dart';
import 'date_range_list_tile.dart';

class PurchaseFilterDialog extends StatelessWidget {
  const PurchaseFilterDialog({super.key, this.isReturn = false});

  final bool isReturn;

  @override
  Widget build(BuildContext context) {
    final cubit = ProductsCubit.get(context);
    final strings = AppLocalizations.of(context)!;
    return BlocBuilder<ProductsCubit, ProductsState>(
      builder: (context, state) {
        return AlertDialog(
          title: Center(child: We2upText(strings.filter)),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextFormField(
                  controller: cubit.purchaseRefFilterController,
                  decoration: InputDecoration(
                    labelText: strings.ref_no,
                    prefixIcon: const Icon(Icons.title),
                    border: myTextFieldBorder,
                  ),
                ),
                CustomDivider(strings.date_range),
                DateRangeDropdown(
                  changeStartDate: cubit.changeFilterStartDate,
                  changeEndDate: cubit.changeFilterEndDate,
                  onChanged: cubit.updateDateRange,
                  range: cubit.range,
                ),
                DateRangeListTile(
                  isStartDate: true,
                  changeDate: cubit.changeFilterStartDate,
                  changeRange: cubit.updateDateRange,
                  date: cubit.saleFilterStartDate,
                ),
                DateRangeListTile(
                  isStartDate: false,
                  startDate: cubit.saleFilterStartDate,
                  changeDate: cubit.changeFilterEndDate,
                  changeRange: cubit.updateDateRange,
                  date: cubit.saleFilterEndDate,
                ),
                CustomDivider(strings.user),
                UsersDropdownButton(
                  user: cubit.filterUser,
                  onChanged: cubit.updateSaleFilterUser,
                  controller: cubit.userTextEditingController,
                ),
                CustomDivider(strings.customer),
                ContactsDropdownButton(
                  contact: cubit.purchaseFilterContact,
                  onChanged: cubit.changePurchaseFilterContact,
                  filterType: 'supplier',
                ),
                CustomDivider(strings.product),
                ProductsDropdownButton(
                  onChanged: cubit.changePurchaseFilterProduct,
                  product: cubit.purchaseFilterProduct,
                ),
                CustomDivider(strings.branch.replaceFirst(":", "")),
                BusinessLocationFilterDropdown(
                  isExpanded: true,
                  onChanged: cubit.changePurchaseFilterBusinessLocation,
                  value: cubit.purchaseFilterBusinessLocation,
                ),
              ],
            ),
          ),
          actionsAlignment: MainAxisAlignment.spaceAround,
          actions: [
            ElevatedButton(
              child: We2upText(strings.reset),
              onPressed: () {
                showDialog(
                  context: context,
                  builder: (context) {
                    return AlertDialog(
                      actionsAlignment: MainAxisAlignment.spaceAround,
                      title: Center(child: We2upText(strings.clear_all_filters)),
                      actions: [
                        ElevatedButton(
                          child: We2upText(strings.clear),
                          onPressed: () {
                            cubit.resetPurchasesFilters();
                            cubit.getFilteredSales(
                              isNewSearch: true,
                              saleReturn: isReturn,
                            );
                            context.pop();
                            context.pop();
                          },
                        ),
                        ElevatedButton(
                          child: We2upText(strings.cancel),
                          onPressed: () => context.pop(),
                        ),
                      ],
                    );
                  },
                );
              },
            ),
            ElevatedButton(
              child: We2upText(strings.apply),
              onPressed: () {
                context.pop();
                cubit.getFilteredPurchases(
                  isNewSearch: true,
                  purchaseReturn: isReturn,
                );
              },
            ),
          ],
        );
      },
    );
  }
}
