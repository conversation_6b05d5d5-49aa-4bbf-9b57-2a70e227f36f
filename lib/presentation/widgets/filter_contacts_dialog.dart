import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:we2up/bloc/contacts/contacts_cubit.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/presentation/widgets/contacts_dropdown_button.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';

import '../style_constants.dart';

class FilterContactsDialog extends StatelessWidget {
  const FilterContactsDialog({super.key, this.dues = false});

  final bool dues;

  @override
  Widget build(BuildContext context) {
    final cubit = ContactsCubit.get(context);
    final strings = AppLocalizations.of(context)!;
    cubit.businessFilterController.addListener(cubit.businessListener);
    cubit.nameFilterController.addListener(cubit.nameListener);
    cubit.numberFilterController.addListener(cubit.numberListener);
    return BlocBuilder<ContactsCubit, ContactsState>(
      builder: (context, state) {
        return AlertDialog(
          title: Center(child: We2upText(strings.filter)),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextFormField(
                  controller: cubit.businessFilterController,
                  decoration: InputDecoration(
                    labelText: strings.business_name,
                    prefixIcon: const Icon(Icons.business_sharp),
                    border: myTextFieldBorder,
                  ),
                ),
                const Divider(),
                TextFormField(
                  controller: cubit.nameFilterController,
                  decoration: InputDecoration(
                    labelText: strings.name,
                    prefixIcon: const Icon(Icons.person),
                    border: myTextFieldBorder,
                  ),
                ),
                const Divider(),
                TextFormField(
                  controller: cubit.numberFilterController,
                  decoration: InputDecoration(
                    labelText: strings.mobile_number,
                    prefixIcon: const Icon(Icons.phone_android_outlined),
                    border: myTextFieldBorder,
                  ),
                ),
                Visibility(
                  visible: !dues,
                  child: Column(
                    children: [
                      const Divider(),
                      DropdownButtonFormField<String?>(
                        value: cubit.typeFilter,
                        isExpanded: true,
                        onChanged: (c) => cubit.changeContactTypeFilter(c),
                        decoration: InputDecoration(
                          labelText: strings.contact_type,
                          prefixIcon: const Icon(Icons.type_specimen),
                          border: myTextFieldBorder,
                        ),
                        items: [null, 'customer', 'supplier']
                            .map<DropdownMenuItem<String?>>(
                              (String? value) => DropdownMenuItem<String?>(
                                value: value,
                                child: Center(
                                  child: We2upText(
                                    strings.translateContactType(value),
                                  ),
                                ),
                              ),
                            )
                            .toList(),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          actionsAlignment: MainAxisAlignment.spaceAround,
          actions: [
            ElevatedButton(
              child: We2upText(strings.reset),
              onPressed: () {
                showDialog(
                  context: context,
                  builder: (context) {
                    return AlertDialog(
                      actionsAlignment: MainAxisAlignment.spaceAround,
                      title: Center(child: We2upText(strings.clear_all_filters)),
                      actions: [
                        ElevatedButton(
                          child: We2upText(strings.clear),
                          onPressed: () {
                            cubit.resetContactsFilters();
                            context.pop();
                            context.pop();
                          },
                        ),
                        ElevatedButton(
                          child: We2upText(strings.cancel),
                          onPressed: () => context.pop(),
                        ),
                      ],
                    );
                  },
                );
              },
            ),
            ElevatedButton(
              child: We2upText(strings.apply),
              onPressed: () => context.pop(),
            ),
          ],
        );
      },
    );
  }
}
