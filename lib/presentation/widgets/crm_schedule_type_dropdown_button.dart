import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';

import '../../data/models/follow_up_to_api.dart';
import '../style_constants.dart';

class CrmScheduleTypeDropdownButton extends StatelessWidget {
  const CrmScheduleTypeDropdownButton({
    super.key,
    this.isExpanded,
    this.padding,
    required this.onChanged,
    required this.followupType,
  });

  final bool? isExpanded;
  final double? padding;
  final ValueChanged<FollowUpType?> onChanged;
  final FollowUpType followupType;

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;

    return Container(
      decoration: dropDownDecoration(context),
      child: DropdownButton<FollowUpType>(
        value: followupType,
        padding: EdgeInsets.all(padding != null ? padding!.sp : 5.sp),
        isDense: true,
        isExpanded: isExpanded ?? true,
        onChanged: onChanged,
        underline: const SizedBox(),
        items: FollowUpType.values
            .map<DropdownMenuItem<FollowUpType>>(
              (FollowUpType value) => DropdownMenuItem<FollowUpType>(
                value: value,
                child: Center(
                  child: We2upText(
                    strings.translateCrmScheduleType(value),
                  ),
                ),
              ),
            )
            .toList(),
      ),
    );
  }
}

extension AppLocalizationsExtension on AppLocalizations {
  String translateCrmScheduleType(FollowUpType followUpType) {
    switch (followUpType) {
      case FollowUpType.email:
        return email;
      case FollowUpType.sms:
        return sms;
      case FollowUpType.call:
        return call;
      case FollowUpType.meeting:
        return meeting;
    }
  }
}
