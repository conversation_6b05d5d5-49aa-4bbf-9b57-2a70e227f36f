import 'package:flutter/material.dart';
import 'package:hive/hive.dart';
import 'package:sizer/sizer.dart';

import '../../../data/models/language.dart';
import '../../../data/db/db_manager.dart';
import '../../style_constants.dart';
import '../we2up_text.dart';

class LanguageDropdownButton extends StatelessWidget {
  const LanguageDropdownButton({super.key, this.drawer = false});

  final bool drawer;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: !drawer ? dropDownDecoration(context) : null,
      child: DropdownButton<Language>(
        underline: const SizedBox(),
        isExpanded: true,
        onChanged: (Language? language) async {
          if (language != null) {
            Hive.box(settingsBox).put("language", language.languageCode);
          }
        },
        hint: drawer
            ? Padding(
                padding: EdgeInsets.symmetric(horizontal: 10.sp),
                child: const Text("اللغة | Language"),
              )
            : const Center(child: Text("اللغة | Language")),
        items: Language.languageList()
            .map<DropdownMenuItem<Language>>(
              (e) => DropdownMenuItem<Language>(
                value: e,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: <Widget>[
                    We2upText(
                      e.flag,
                      style: const TextStyle(fontSize: 30),
                    ),
                    We2upText(e.name)
                  ],
                ),
              ),
            )
            .toList(),
        icon: drawer
            ? Padding(
                padding: EdgeInsets.symmetric(horizontal: 18.sp),
                child: const Icon(Icons.language),
              )
            : null,
      ),
    );
  }
}
