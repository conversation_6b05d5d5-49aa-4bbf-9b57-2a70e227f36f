import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:sizer/sizer.dart';

import '../../../utils/we2up_constants.dart';
import '../we2up_text.dart';


class WhatsAppContactUs extends StatelessWidget {
  const WhatsAppContactUs({super.key});

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    return Padding(
      padding: EdgeInsets.all(15.sp),
      child: ListTile(
        onTap: () => navigateToWhatsapp("0201015156872"),
        trailing: Icon(
          FontAwesomeIcons.whatsapp,
          color: Colors.green,
          size: 25.sp,
        ),
        title: We2upText(strings.whatsapp),
      ),
    );
  }
}