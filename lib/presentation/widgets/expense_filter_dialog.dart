import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:sizer/sizer.dart';
import 'package:we2up/bloc/expenses/expenses_cubit.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/presentation/widgets/expense_categories_dropdown_button.dart';
import 'package:we2up/presentation/widgets/user_dropdown_button.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';

import '../style_constants.dart';
import 'contacts_dropdown_button.dart';
import 'date_range_dropdown.dart';
import 'date_range_list_tile.dart';

class ExpenseFilterDialog extends StatelessWidget {
  const ExpenseFilterDialog({super.key});

  @override
  Widget build(BuildContext context) {
    final cubit = ExpensesCubit.get(context);
    final strings = AppLocalizations.of(context)!;
    cubit.invoiceRefFilterController.addListener(cubit.invoiceRefFilterChanged);
    return BlocBuilder<ExpensesCubit, ExpensesState>(
      builder: (context, state) {
        return AlertDialog(
          title: Center(child: We2upText(strings.filter)),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextFormField(
                  controller: cubit.invoiceRefFilterController,
                  decoration: InputDecoration(
                    labelText: strings.ref_no,
                    prefixIcon: const Icon(Icons.title),
                    border: myTextFieldBorder,
                  ),
                ),
                verticalSpacer,
                Row(
                  children: [
                    Expanded(child: Divider(endIndent: 8.sp)),
                    We2upText(strings.date_range),
                    Expanded(child: Divider(indent: 8.sp)),
                  ],
                ),
                verticalSpacer,
                DateRangeDropdown(
                  changeStartDate: cubit.changeEStartDate,
                  changeEndDate: cubit.changeEEndDate,
                  onChanged: cubit.updateDateRange,
                  range: cubit.range,
                ),
                DateRangeListTile(
                  isStartDate: true,
                  changeDate: cubit.changeEStartDate,
                  changeRange: cubit.updateDateRange,
                  date: cubit.expenseStartDate,
                ),
                DateRangeListTile(
                  isStartDate: false,
                  startDate: cubit.expenseStartDate,
                  changeDate: cubit.changeEEndDate,
                  changeRange: cubit.updateDateRange,
                  date: cubit.expenseEndDate,
                ),
                Row(
                  children: [
                    Expanded(child: Divider(endIndent: 8.sp)),
                    We2upText(strings.expense_category.replaceFirst(":", "")),
                    Expanded(child: Divider(indent: 8.sp)),
                  ],
                ),
                verticalSpacer,
                ExpenseCategoriesDropdownButton(
                  onChanged: cubit.changeEFilterCategory,
                  category: cubit.expenseFilterCategory,
                  isExpanded: true,
                ),
                verticalSpacer,
                Row(
                  children: [
                    Expanded(child: Divider(endIndent: 8.sp)),
                    We2upText(strings.customer),
                    Expanded(child: Divider(indent: 8.sp)),
                  ],
                ),
                verticalSpacer,
                ContactsDropdownButton(
                  contact: cubit.expenseFilterContact,
                  onChanged: cubit.changeEFilterContact,
                ),
                verticalSpacer,
                Row(
                  children: [
                    Expanded(child: Divider(endIndent: 8.sp)),
                    We2upText(strings.user),
                    Expanded(child: Divider(indent: 8.sp)),
                  ],
                ),
                verticalSpacer,
                UsersDropdownButton(
                  onChanged: cubit.changeEFilterUser,
                  controller: cubit.filterUserController,
                  user: cubit.expenseFilterUser,
                ),
              ],
            ),
          ),
          actionsAlignment: MainAxisAlignment.spaceAround,
          actions: [
            ElevatedButton(
              child: We2upText(strings.reset),
              onPressed: () {
                showDialog(
                  context: context,
                  builder: (context) {
                    return AlertDialog(
                      actionsAlignment: MainAxisAlignment.spaceAround,
                      title: Center(child: We2upText(strings.clear_all_filters)),
                      actions: [
                        ElevatedButton(
                          child: We2upText(strings.clear),
                          onPressed: () {
                            cubit.resetExpensesFilters();
                            context.pop();
                            context.pop();
                          },
                        ),
                        ElevatedButton(
                          child: We2upText(strings.cancel),
                          onPressed: () => context.pop(),
                        ),
                      ],
                    );
                  },
                );
              },
            ),
            ElevatedButton(
              child: We2upText(strings.apply),
              onPressed: () {
                context.pop();
                cubit.getFilteredExpenses(isNewSearch: true);
              },
            ),
          ],
        );
      },
    );
  }
}
