import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';
import 'package:we2up/utils/we2up_constants.dart';

import 'date_range_dropdown.dart';

class DateRangeListTile extends StatelessWidget {
  const DateRangeListTile({
    super.key,
    required this.date,
    this.startDate,
    required this.isStartDate,
    this.isLocation = false,
    required this.changeDate,
    required this.changeRange,
  });

  final DateTime? date;

  /// don't pass it if isStartDate is true,
  /// pass it if you want the 7 days range only.
  final DateTime? startDate;
  final bool isStartDate;
  final bool isLocation;
  final Function(DateTime?) changeDate;
  final Function(DateRange) changeRange;

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    final currentDate = DateTime.now();
    final fiveYearsAgo = currentDate.subtract(
      const Duration(days: 365 * 5),
    );
    return ListTile(
      title: We2upText(isStartDate ? strings.start_date : strings.end_date),
      trailing: const Icon(Icons.calendar_month_rounded),
      enabled: isStartDate || (!isStartDate && startDate != null) || isLocation,
      onTap: () async {
        final selectedDate = await showDatePicker(
          context: context,
          initialDate: date,
          firstDate: isStartDate
              ? fiveYearsAgo
              : startDate != null
                  ? startDate!
                  : fiveYearsAgo,
          lastDate: isStartDate
              ? currentDate
              : startDate != null
                  ? startDate!
                          .add(const Duration(days: 7))
                          .isBefore(currentDate)
                      ? startDate!.add(const Duration(days: 7))
                      : currentDate
                  : currentDate,
        );

        if (selectedDate != null) {
          changeDate(selectedDate);
          changeRange(DateRange.customRange);
        }
      },
      onLongPress: () => changeDate(null),
      subtitle: We2upText(
        date != null ? formatDate(date!, filter: true) : strings.nothing,
      ),
    );
  }
}
