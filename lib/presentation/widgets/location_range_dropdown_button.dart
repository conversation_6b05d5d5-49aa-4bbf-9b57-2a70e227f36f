import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';

import '../style_constants.dart';

/// TO be used in future updates
class LocationRangeDropdownButton extends StatelessWidget {
  const LocationRangeDropdownButton({
    super.key,
    this.isExpanded,
    this.padding,
    required this.onChanged,
    required this.locationRange,
  });

  final bool? isExpanded;
  final double? padding;
  final ValueChanged<LocationRange> onChanged;
  final LocationRange locationRange;

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    return Container(
      decoration: dropDownDecoration(context),
      child: DropdownButton<LocationRange>(
        value: locationRange,
        padding: EdgeInsets.all(padding != null ? padding!.sp : 5.sp),
        isDense: true,
        isExpanded: isExpanded ?? true,
        onChanged: (l) => onChanged(l!),
        underline: const SizedBox(),
        items: LocationRange.values
            .map<DropdownMenuItem<LocationRange>>(
              (LocationRange value) => DropdownMenuItem<LocationRange>(
                value: value,
                child: Center(
                  child: We2upText(
                    strings.translateLocationAccuracy(value),
                  ),
                ),
              ),
            )
            .toList(),
      ),
    );
  }
}

extension AppLocalizationsExtension on AppLocalizations {
  String translateLocationAccuracy(LocationRange accuracy) {
    switch (accuracy) {
      case LocationRange.all:
        return location_all;
      case LocationRange.high:
        return location_high;
      case LocationRange.medium:
        return location_medium;
      case LocationRange.low:
        return location_low;
      case LocationRange.none:
        return location_put_of_range;
    }
  }
}

enum LocationRange { all, high, medium, low, none }
