import 'package:conditional_builder_null_safety/conditional_builder_null_safety.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:sizer/sizer.dart';
import 'package:we2up/bloc/contact_payment/contact_payment_cubit.dart';
import 'package:we2up/bloc/products/products_cubit.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';

class PrintBillCheckBox extends StatelessWidget {
  const PrintBillCheckBox(
      {super.key, this.payment = false, this.sReturn = false});

  final bool payment;
  final bool sReturn;

  @override
  Widget build(BuildContext context) {
    return ConditionalBuilder(
      condition: !payment,
      builder: (context) {
        return BlocBuilder<ProductsCubit, ProductsState>(
          builder: (context, state) {
            final strings = AppLocalizations.of(context)!;
            final cubit = context.read<ProductsCubit>();
            return CheckboxListTile(
              title: We2upText(strings.print_invoice),
              shape: RoundedRectangleBorder(
                side: BorderSide(
                  color: Theme.of(context).colorScheme.secondaryContainer,
                  width: 2,
                ),
                borderRadius: BorderRadius.circular(10.sp),
              ),
              value: cubit.printCart,
              onChanged: (v) => cubit.changePrintCart(),
            );
          },
        );
      },
      fallback: (context) {
        return BlocBuilder<ContactPaymentCubit, ContactPaymentState>(
          builder: (context, state) {
            final strings = AppLocalizations.of(context)!;
            final cubit = context.read<ContactPaymentCubit>();
            return CheckboxListTile(
              title: We2upText(strings.print_invoice),
              shape: RoundedRectangleBorder(
                side: BorderSide(
                  color: Theme.of(context).colorScheme.secondaryContainer,
                  width: 2,
                ),
                borderRadius: BorderRadius.circular(10.sp),
              ),
              value: cubit.printReceipt,
              onChanged: (v) => cubit.changePrintCart(),
            );
          },
        );
      },
    );
  }
}
