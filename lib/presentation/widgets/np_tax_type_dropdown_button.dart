import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:we2up/data/models/new_product_to_api.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';

import '../style_constants.dart';

class NPTaxTypeDropdownButton extends StatelessWidget {
  const NPTaxTypeDropdownButton({
    super.key,
    this.isExpanded = false,
    this.padding = 5,
    required this.onChanged,
    required this.taxType,
  });

  final bool isExpanded;
  final double padding;
  final ValueChanged<TaxType?> onChanged;
  final TaxType taxType;

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    return Container(
      decoration: dropDownDecoration(context),
      child: DropdownButton<TaxType>(
        value: taxType,
        padding: EdgeInsets.all(padding.sp),
        isDense: true,
        isExpanded: isExpanded,
        onChanged: onChanged,
        underline: const SizedBox(),
        items: TaxType.values.map<DropdownMenuItem<TaxType>>((TaxType value) {
          return DropdownMenuItem<TaxType>(
            value: value,
            child: Center(child: We2upText(strings.translateTaxType(value))),
          );
        }).toList(),
      ),
    );
  }
}

extension AppLocalizationsExtension on AppLocalizations {
  String translateTaxType(TaxType taxType) {
    switch (taxType) {
      case TaxType.exclusive:
        return exclusive;
      case TaxType.inclusive:
        return inclusive;
    }
  }
}
