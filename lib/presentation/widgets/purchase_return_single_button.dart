import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/presentation/style_constants.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';

import '../../data/models/bill_of_sale_data.dart';
import '../../data/db/db_manager.dart';

class PurchaseReturnContactData extends StatelessWidget {
  const PurchaseReturnContactData({
    super.key,
    required BillData billData,
  }) : _billData = billData;

  final BillData _billData;

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(5.sp),
      decoration: showBoxDecoration(context),
      child: Column(
        children: [
          ListTile(
            title: We2upText(
              "${strings.name}:",
              style: Theme.of(context).textTheme.titleMedium,
            ),
            trailing: We2upText(
              contactsBox
                  .get(_billData.purchase!.contactId)!
                  .name ??
                  "",
            ),
          ),
          const Divider(),
          ListTile(
            title: We2upText(
              "${strings.mobile}:",
              style: Theme.of(context).textTheme.titleMedium,
            ),
            trailing: We2upText(
              contactsBox
                  .get(_billData.purchase!.contactId)!
                  .mobile ??
                  "",
            ),
          ),
          const Divider(),
          ListTile(
            title: We2upText(
              "${strings.due_amount}:",
              style: Theme.of(context).textTheme.titleMedium,
            ),
            trailing: We2upText(
              contactsBox
                  .get(_billData.purchase!.contactId)!
                  .due ??
                  "",
            ),
          ),
        ],
      ),
    );
  }
}
