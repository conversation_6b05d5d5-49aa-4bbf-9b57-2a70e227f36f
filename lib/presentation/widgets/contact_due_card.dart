import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:go_router/go_router.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';

import '../../bloc/contact_payment/contact_payment_cubit.dart';
import '../../data/models/business_settings.dart';
import '../../data/models/contact.dart';
import '../../utils/route_constants.dart';
import '../../utils/we2up_constants.dart';

class ContactDueCard extends StatelessWidget {
  const ContactDueCard({
    super.key,
    this.supplier = false,
    required this.contact,
  });

  final bool supplier;
  final Contact contact;

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    return ListTile(
      title: We2upText(
        contact.name ?? "N/A",
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      subtitle: We2upText(
        "${strings.due}  ${contact.due ?? "N/A"} "
        "${currencySymbol()}",
      ),
      trailing: ElevatedButton(
        onPressed: () async => await checkShiftLocationAccess(
          context,
          () {
            context.push(
              contactPaymentScreen,
              extra: !supplier,
            );
            ContactPaymentCubit.get(context).changeCustomerContact(contact);
          },
        ),
        child: We2upText(supplier ? strings.pay : strings.collect),
      ),
    );
  }
}
