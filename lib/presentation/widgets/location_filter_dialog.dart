import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:we2up/bloc/location/location_cubit.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';

import 'date_range_dropdown.dart';
import 'date_range_list_tile.dart';

class LocationFilterDialog extends StatelessWidget {
  const LocationFilterDialog({super.key});

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<LocationCubit>();
    final strings = AppLocalizations.of(context)!;
    return BlocBuilder<LocationCubit, LocationState>(
      buildWhen: (_, c) =>
          c is StartDateUpdated || c is EndDateUpdated || c is DateRangeUpdated,
      builder: (context, state) {
        return AlertDialog(
          title: Center(child: We2upText(strings.date_range)),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                DateRangeDropdown(
                  changeStartDate: cubit.updateStartDate,
                  changeEndDate: cubit.updateEndDate,
                  onChanged: cubit.updateDateRange,
                  range: cubit.range,
                  isLocation: true,
                ),
                DateRangeListTile(
                  isStartDate: true,
                  changeDate: cubit.updateStartDate,
                  changeRange: cubit.updateDateRange,
                  date: cubit.startDate,
                ),
                DateRangeListTile(
                  isStartDate: false,
                  isLocation: true,
                  changeDate: cubit.updateEndDate,
                  changeRange: cubit.updateDateRange,
                  date: cubit.endDate,
                ),
              ],
            ),
          ),
          actionsAlignment: MainAxisAlignment.spaceAround,
          actions: [
            ElevatedButton(
              child: We2upText(strings.button_return),
              onPressed: () => context.pop(),
            ),
            ElevatedButton(
              child: We2upText(strings.apply),
              onPressed: () async {
                context.pop();
                await cubit.getRangedData();
              },
            ),
          ],
        );
      },
    );
  }
}
