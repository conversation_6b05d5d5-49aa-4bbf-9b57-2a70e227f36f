import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/data/models/table.dart';
import 'package:we2up/presentation/style_constants.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';

import '../../../data/db/db_manager.dart';

class TablesDropdown extends StatelessWidget {
  const TablesDropdown({
    super.key,
    this.isExpanded = false,
    this.padding = 5,
    required this.onChanged,
    required this.table,
  });

  final bool isExpanded;
  final double padding;
  final ValueChanged<BusinessTable?> onChanged;
  final BusinessTable? table;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 48.0, // Fixed height to match other input fields
      decoration: dropDownDecoration(context),
      child: DropdownButton<BusinessTable>(
        value: table,
        padding: EdgeInsets.all(padding.sp),
        isDense: true,
        isExpanded: isExpanded,
        onChanged: onChanged,
        underline: const SizedBox(),
        items: [
          DropdownMenuItem<BusinessTable>(
            value: null,
            child: Center(
              child: We2upText(AppLocalizations.of(context)!.nothing),
            ),
          ),
          ...tablesBox.values.map((BusinessTable value) {
            return DropdownMenuItem<BusinessTable>(
              value: value,
              child: Center(child: We2upText(value.name ?? "N/A")),
            );
          }),
        ],
      ),
    );
  }
}
