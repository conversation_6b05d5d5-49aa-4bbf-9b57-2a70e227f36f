import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';

import '../style_constants.dart';

/// TO be used in future updates
class ShipmentStatusDropdownButton extends StatelessWidget {
  const ShipmentStatusDropdownButton({
    super.key,
    this.isExpanded,
    this.padding,
    required this.onChanged,
    required this.status,
    this.filter = false,
  });

  final bool? isExpanded;
  final double? padding;
  final ValueChanged<String?> onChanged;
  final String? status;
  final bool filter;

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    final constantValues = [
      'ordered',
      'packed',
      'shipped',
      'delivered',
      'cancelled',
    ];

    return Container(
      height: 48.0, // Fixed height to match other input fields
      decoration: dropDownDecoration(context),
      child: DropdownButton<String?>(
        value: status,
        padding: EdgeInsets.all(padding != null ? padding!.sp : 5.sp),
        isDense: true,
        isExpanded: isExpanded ?? true,
        onChanged: onChanged,
        underline: const SizedBox(),
        items: [
          if (!filter)
            DropdownMenuItem(
              value: null,
              child: Center(child: We2upText(strings.status)),
            ),
          if (filter)
            DropdownMenuItem(
              value: null,
              child: Center(child: We2upText(strings.nothing)),
            ),
          ...constantValues.map<DropdownMenuItem<String?>>(
            (String? value) => DropdownMenuItem<String?>(
              value: value,
              child: Center(
                child: We2upText(
                  strings.translateShipmentStatus(value),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

extension AppLocalizationsExtension on AppLocalizations {
  String translateShipmentStatus(String? contactType) {
    switch (contactType) {
      case 'ordered':
        return ordered;
      case 'packed':
        return packed;
      case 'shipped':
        return shipped;
      case 'delivered':
        return delivered;
      case 'cancelled':
        return cancelled;
      default:
        return status;
    }
  }
}
