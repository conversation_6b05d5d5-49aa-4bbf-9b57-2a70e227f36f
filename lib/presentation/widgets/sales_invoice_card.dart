import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_offline/flutter_offline.dart';
import 'package:go_router/go_router.dart';
import 'package:sizer/sizer.dart';
import 'package:we2up/bloc/products/products_cubit.dart';
import 'package:we2up/data/models/bill_of_sale_data.dart';
import 'package:we2up/data/models/sell.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/data/models/shipment_status.dart';
import 'package:we2up/presentation/style_constants.dart';
import 'package:we2up/presentation/widgets/shipment_status_dropdown_button.dart';
import 'package:we2up/data/db/db_manager.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';
import 'package:we2up/utils/route_constants.dart';
import 'package:we2up/utils/we2up_constants.dart';

import '../../bloc/products/pdf_function.dart';
import '../../data/models/business_settings.dart';
import '../../data/models/payment_status.dart';

class SalesInvoiceCard extends StatelessWidget {
  const SalesInvoiceCard({
    super.key,
    required this.sell,
    this.isReturn = false,
    this.shipment = false,
  });

  final Sell sell;
  final bool isReturn;
  final bool shipment;

  String paidAmount() {
    double total = 0.0;
    if (sell.paymentLines != null) {
      for (PaymentLine paymentLine in sell.paymentLines!) {
        total += double.parse(paymentLine.amount!);
      }
    }
    return total.toString();
  }

  @override
  Widget build(BuildContext context) {
    // print("canUpdateSell: ${canUpdateSell()}");
    // print("isProductsAndContactsReady: ${isProductsAndContactsReady()}");
    // print("!hasDuplicateLines(sell): ${!hasDuplicateLines(sell)}");
    // print("isWithinTransactionEditDays(sell.transactionDate): ${isWithinTransactionEditDays(sell.transactionDate)}");
    final strings = AppLocalizations.of(context)!;
    final cubit = ProductsCubit.get(context);
    final canUpdate = canUpdateSell() &&
        isProductsAndContactsReady() &&
        !hasDuplicateLines(sell) &&
        isWithinTransactionEditDays(sell.transactionDate);
    final canReturn = !hasReturnItems(sell) &&
        !sell.offline &&
        isProductsAndContactsReady() &&
        isWithinTransactionEditDays(sell.transactionDate);
    return BlocBuilder<ProductsCubit, ProductsState>(
      buildWhen: (previous, current) => current is ShipmentStatusChanged,
      builder: (context, state) {
        return Card(
          child: Container(
            padding: EdgeInsets.all(10.sp),
            width: double.infinity,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          We2upText(
                            '${strings.sell_id}: ${sell.invoiceNo}',
                            style: Theme.of(context).textTheme.labelLarge,
                          ),
                          We2upText(
                            "${strings.invoice_amount}: "
                            "${sell.finalTotal} ${currencySymbol()}",
                          ),
                          We2upText(
                            "${strings.paid_amount}: "
                            "${paidAmount()} ${currencySymbol()}",
                          ),
                          We2upText(
                            "${strings.customer_name} "
                            "${contactsBox.get(sell.contactId)?.name}",
                          ),
                          We2upText(
                            "${strings.location}: "
                            "${businessLocationsBox.get(sell.locationId)?.name}",
                          ),
                        ],
                      ),
                    ),
                    Column(
                      children: [
                        if (hasReturnItems(sell) && isReturn) ...[
                          const ReturnContainer(),
                          SizedBox(height: 8.sp),
                        ],
                        if (!isReturn && !shipment && sell.isQuotation == 0) ...[
                          SellStatusContainer(sell: sell, strings: strings),
                          SizedBox(height: 8.sp),
                        ],
                        if (shipment) ...[
                          ShippingStatusContainer(sell: sell),
                          SizedBox(height: 8.sp),
                        ],
                        if (sell.isQuotation! == 1 && !isReturn) ...[
                          CreditContainer(strings: strings),
                          SizedBox(height: 8.sp),
                        ],
                        if (!isReturn)
                          Row(
                            children: [
                              ViewPdfButton(sell: sell),
                              PrintForWarehouse(sell: sell),
                            ],
                          ),
                      ],
                    ),
                  ],
                ),
                Row(
                  children: [
                    Expanded(
                      child: We2upText(
                        formatDate(sell.transactionDate),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    if (!isReturn && !shipment)
                      Row(
                        children: [
                          IconButton(
                            onPressed: canUpdate
                                ? () async => await checkShiftLocationAccess(
                                      context,
                                      () => context.push(
                                        billOfSale,
                                        extra: BillData(sell: sell),
                                      ),
                                    )
                                : null,
                            icon: Icon(
                              Icons.edit,
                              color: canUpdate
                                  ? Colors.green
                                  : Theme.of(context).disabledColor,
                            ),
                          ),
                          buildDeleteButton(strings, cubit),
                          IconButton(
                            onPressed: isProductsAndContactsReady()
                                ? () => ProductsCubit.get(context)
                                    .generatePdf(context, sell: sell)
                                : null,
                            icon: Icon(
                              Icons.print,
                              color: Theme.of(context).colorScheme.secondary,
                            ),
                          ),
                          IconButton(
                            onPressed: isProductsAndContactsReady()
                                ? () => cubit.generatePdf(
                                      context,
                                      sell: sell,
                                      share: true,
                                    )
                                : null,
                            icon: const Icon(Icons.share, color: Colors.blue),
                          ),
                        ],
                      ),
                    if (isReturn)
                      ElevatedButton.icon(
                        onPressed: canReturn
                            ? () => context.push(
                                  billOfSale,
                                  extra: BillData(sell: sell, returnSell: true),
                                )
                            : null,
                        style: ElevatedButton.styleFrom(
                          shape: RoundedRectangleBorder(
                            side: BorderSide(
                              color: Theme.of(context).colorScheme.outline,
                              width: 1.sp,
                            ),
                            borderRadius: BorderRadius.circular(10.sp),
                          ),
                        ),
                        label: We2upText(strings.button_return
                            // hasReturnItems() ? strings.update : strings.button_return,
                            ),
                        icon: !sell.offline
                            ? const SizedBox()
                            : const Icon(Icons.sync_problem_outlined),
                      ),
                  ],
                ),
                if (shipment)
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      We2upText(strings.shipment_status),
                      SizedBox(
                        width: 40.w,
                        child: ShipmentStatusDropdownButton(
                          onChanged: (status) => cubit.changeShipmentStatus(
                            id: sell.id!,
                            status: status,
                          ),
                          status: cubit.shipmentsStatus![sell.id],
                        ),
                      ),
                    ],
                  ),
                if (cubit.shipmentsStatus![sell.id] != null) verticalSpacer,
                if (cubit.shipmentsStatus![sell.id] != null)
                  Center(
                    child: FilledButton(
                      onPressed: () async => await checkShiftLocationAccess(
                        context,
                        () => cubit.sendShipmentStatusUpdate(sell.id!),
                      ),
                      child: We2upText(strings.save),
                    ),
                  ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget buildDeleteButton(AppLocalizations strings, ProductsCubit cubit) {
    bool isSellOnline = !sell.offline;
    return OfflineBuilder(
      connectivityBuilder: (
        BuildContext context,
        dynamic connectivity,
        Widget child,
      ) {
        bool isOnline;

        if (connectivity is List<ConnectivityResult>) {
          isOnline = !connectivity.contains(ConnectivityResult.none);
        } else if (connectivity is ConnectivityResult) {
          isOnline = connectivity != ConnectivityResult.none;
        } else {
          isOnline = false;
        }

        final canDelete =
            (isOnline && isSellOnline || !isOnline && !isSellOnline) &&
                canDeleteSell() &&
                !hasReturnItems(sell) &&
                isWithinTransactionEditDays(sell.transactionDate);

        return IconButton(
          onPressed: canDelete
              ? () async => await checkShiftLocationAccess(
                    context,
                    () => showDialog(
                      context: context,
                      builder: (BuildContext context) {
                        return AlertDialog(
                          title: We2upText(strings.delete_sell_item),
                          content: We2upText(strings.delete_sell_question),
                          actions: [
                            TextButton(
                              onPressed: () => Navigator.pop(context),
                              child: We2upText(strings.cancel),
                            ),
                            TextButton(
                              onPressed: () => cubit
                                  .deleteSell(
                                context,
                                sellID: sell.id ?? sell.invoiceNo,
                                offline: sell.offline,
                              )
                                  .then((_) {
                                if (context.mounted) context.pop();
                              }),
                              child: We2upText(strings.delete),
                            ),
                          ],
                        );
                      },
                    ),
                  )
              : null,
          icon: Icon(
            Icons.delete,
            color: canDelete
                ? Theme.of(context).colorScheme.error
                : Theme.of(context).disabledColor,
          ),
        );
      },
      child: const SizedBox(),
    );
  }
}

class ViewPdfButton extends StatelessWidget {
  const ViewPdfButton({
    super.key,
    required this.sell,
  });

  final Sell sell;

  @override
  Widget build(BuildContext context) {
    return IconButton(
      onPressed: isProductsAndContactsReady()
          ? () async => context.push(
                pdfViewPage,
                extra: await generatePdfFunction(
                  context,
                  sell: sell,
                  view: true,
                ),
              )
          : null,
      icon: Icon(
        Icons.remove_red_eye_sharp,
        color: Theme.of(context).colorScheme.tertiaryFixed,
      ),
    );
  }
}

class PrintForWarehouse extends StatelessWidget {
  const PrintForWarehouse({
    super.key,
    required this.sell,
  });

  final Sell sell;

  @override
  Widget build(BuildContext context) {
    return IconButton(
      onPressed: () async => await generatePdfFunction(
        context,
        sell: sell,
        warehouse: true,
      ),
      icon: Icon(
        Icons.warehouse,
        color: Theme.of(context).colorScheme.tertiaryFixed,
      ),
    );
  }
}

class CreditContainer extends StatelessWidget {
  const CreditContainer({super.key, required this.strings});

  final AppLocalizations strings;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(8.sp),
      constraints: BoxConstraints(minWidth: 20.w),
      decoration: ShapeDecoration(
        color: Theme.of(context).colorScheme.outlineVariant,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(15),
        ),
      ),
      child: Center(
        child: We2upText(
          strings.quotation,
          textAlign: TextAlign.center,
        ),
      ),
    );
  }
}

class SellStatusContainer extends StatelessWidget {
  const SellStatusContainer(
      {super.key, required this.sell, required this.strings});

  final Sell sell;
  final AppLocalizations strings;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Container(
          padding: EdgeInsets.all(8.sp),
          constraints: BoxConstraints(minWidth: 20.w),
          decoration: ShapeDecoration(
            color: getStatusColor(sellStatus: sell.paymentStatus),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(15),
            ),
          ),
          child: Center(
            child: We2upText(
              strings.translatePaymentStatus(sell.paymentStatus),
              style: TextStyle(
                color: sell.paymentStatus == PaymentStatus.offline
                    ? Theme.of(context).colorScheme.error
                    : null,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
        if (hasReturnItems(sell))
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 8),
            child: ReturnContainer(),
          )
      ],
    );
  }
}

class ShippingStatusContainer extends StatelessWidget {
  const ShippingStatusContainer({super.key, required this.sell});

  final Sell sell;

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    return Container(
      padding: EdgeInsets.all(8.sp),
      constraints: BoxConstraints(minWidth: 20.w),
      decoration: ShapeDecoration(
        color: getStatusColor(shipmentStatus: sell.shippingStatus),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(15),
        ),
      ),
      child: Center(
        child: We2upText(
          strings.translateShippingStatus(sell.shippingStatus),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }
}

class ReturnContainer extends StatelessWidget {
  const ReturnContainer({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(8.sp),
      constraints: BoxConstraints(minWidth: 20.w),
      decoration: ShapeDecoration(
        color: Theme.of(context).colorScheme.onSecondary,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(15),
        ),
      ),
      child: const Center(
        child: Icon(Icons.settings_backup_restore_outlined),
      ),
    );
  }
}

bool hasReturnItems(Sell sell) {
  bool hasReturnItems = false;
  for (var sellLine in sell.sellLines!) {
    if ((double.parse(sellLine.quantityReturned ?? "0")) != 0) {
      hasReturnItems = true;
      break;
    }
  }
  return hasReturnItems;
}

bool hasDuplicateLines(Sell sell) {
  Set<int> uniqueProductIds = <int>{};
  for (var sellLine in sell.sellLines!) {
    if (!uniqueProductIds.add(sellLine.productId!)) {
      // it's a duplicate
      return true;
    }
  }
  // No duplicates found
  return false;
}
