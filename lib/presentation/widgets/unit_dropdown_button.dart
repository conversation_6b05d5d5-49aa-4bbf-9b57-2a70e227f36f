import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sizer/sizer.dart';
import 'package:we2up/bloc/products/products_cubit.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';

import '../../data/models/product.dart';
import '../style_constants.dart';

class UnitDropdownButton extends StatelessWidget {
  const UnitDropdownButton({
    super.key,
    this.isExpanded = false,
    this.padding = 5,
    required this.id,
  });

  final bool isExpanded;
  final double padding;
  final int id;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ProductsCubit, ProductsState>(
      builder: (context, state) {
        final cubit = ProductsCubit.get(context);
        return Container(
          height: 48.0, // Fixed height to match other input fields
          decoration: dropDownDecoration(context),
          child: DropdownButton<Unit>(
            value: cubit.getCurrentUnit(id),
            padding: EdgeInsets.all(padding.sp),
            isDense: true,
            isExpanded: isExpanded,
            onChanged: (unit) => cubit.changeSelectedUnit(unit: unit!, id: id),
            underline: const SizedBox(),
            items: cubit
                .getAssociatedUnits(id)
                .map<DropdownMenuItem<Unit>>((Unit value) {
              return DropdownMenuItem<Unit>(
                value: value,
                child: Center(child: We2upText(value.actualName)),
              );
            }).toList(),
          ),
        );
      },
    );
  }
}
