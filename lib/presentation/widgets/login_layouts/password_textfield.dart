import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

import '../../../bloc/auth/auth_cubit.dart';
import '../../style_constants.dart';

class PasswordTextField extends StatelessWidget {
  const PasswordTextField({
    super.key,
    required this.passwordController,
  });

  final TextEditingController passwordController;

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    final oauthCubit = context.read<AuthCubit>();
    return TextFormField(
      validator: (value) {
        if (value == null || value.isEmpty) {
          return strings.empty_password_validation;
        }
        return null;
      },
      controller: passwordController,
      obscureText: oauthCubit.obscureText,
      decoration: InputDecoration(
        hintText: strings.password,
        suffixIcon: IconButton(
          icon: Icon(
            oauthCubit.obscureText ? Icons.visibility : Icons.visibility_off,
          ),
          onPressed: () => oauthCubit.changeVisibility(),
        ),
        border: loginButtonInputBorder,
      ),
      // keyboardType: TextInputType.visiblePassword,
    );
  }
}
