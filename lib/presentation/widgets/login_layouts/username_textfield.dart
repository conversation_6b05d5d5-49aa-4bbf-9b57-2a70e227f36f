import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

import '../../style_constants.dart';


class UsernameTextField extends StatelessWidget {
  const UsernameTextField({
    super.key,
    required this.usernameController,
  });

  final TextEditingController usernameController;

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    return TextFormField(
      validator: (value) {
        if (value == null || value.isEmpty) {
          return strings.empty_user_validation;
        }
        return null;
      },
      controller: usernameController,
      decoration: InputDecoration(
        hintText: strings.username,
        suffixIcon: const Icon(Icons.person),
        border: loginButtonInputBorder,
      ),
    );
  }
}