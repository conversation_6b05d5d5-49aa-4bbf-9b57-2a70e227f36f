import 'package:app_tracking_transparency/app_tracking_transparency.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:go_router/go_router.dart';

import '../../../data/models/location_permission.dart';
import '../../../utils/route_constants.dart';
import '../ios_tracking_dialog.dart';

class LoginButton extends StatelessWidget {
  const LoginButton({super.key, this.onPressed});

  final VoidCallback? onPressed;

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    return ElevatedButton(
      onPressed: onPressed ??
          () async {
            if (defaultTargetPlatform != TargetPlatform.windows && defaultTargetPlatform != TargetPlatform.macOS) {
              // Handle iOS tracking permission
              if (defaultTargetPlatform == TargetPlatform.iOS) {
                final trackingStatus =
                    await AppTrackingTransparency.trackingAuthorizationStatus;
                if (trackingStatus == TrackingStatus.notDetermined) {
                  if (context.mounted) {
                    await showDialog(
                      context: context,
                      barrierDismissible: false,
                      builder: (_) => const IOSTrackingDialog(),
                    );
                  }
                }
              }

              // Show location disclosure for all mobile platforms
              if (context.mounted) {
                await showDialog(
                  context: context,
                  builder: (_) => const LocationDisclosureDialog(),
                );
              }

              if (context.mounted) {
                context.push(login);
              }
            } else {
              context.push(login);
            }
          },
      style: ElevatedButton.styleFrom(
        backgroundColor: Theme.of(context).colorScheme.primaryContainer,
      ),
      child: Text(strings.login),
    );
  }
}
