import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/presentation/widgets/login_layouts/password_textfield.dart';
import 'package:we2up/presentation/widgets/login_layouts/username_textfield.dart';

import '../../../bloc/auth/auth_cubit.dart';
import '../../style_constants.dart';
import '../base_urls_dropdown.dart';

class LoginFormColumn extends StatelessWidget {
  const LoginFormColumn({
    super.key,
    required this.usernameController,
    required this.passwordController,
  });

  final TextEditingController usernameController;
  final TextEditingController passwordController;

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    final oauthCubit = context.read<AuthCubit>();
    return Column(
      children: [
        BaseUrlsDropdownButton(onChanged: oauthCubit.changeBaseUrl),
        verticalSpacer,
        UsernameTextField(usernameController: usernameController),
        verticalSpacer,
        PasswordTextField(passwordController: passwordController),
        verticalSpacer,
        CheckboxListTile(
          title: Text(strings.save_credentials_question),
          value: oauthCubit.saveCredentials(),
          onChanged: oauthCubit.changeSavingCredentials,
        ),
        verticalSpacer,
      ],
    );
  }
}
