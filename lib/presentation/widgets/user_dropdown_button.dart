import 'dart:io';

import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/data/models/user_model.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';

import '../../data/db/db_manager.dart';
import '../style_constants.dart';

class UsersDropdownButton extends StatelessWidget {
  const UsersDropdownButton({
    super.key,
    required this.onChanged,
    required this.controller,
    required this.user,
    this.commAgents = false,
    this.serviceStaff = false,
    this.currentUserOnly = false,
  });

  final void Function(UserModel?) onChanged;
  final UserModel? user;
  final TextEditingController controller;
  final bool commAgents;
  final bool serviceStaff;
  final bool currentUserOnly;

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;

    List<DropdownMenuItem<UserModel>> dropdownItems = [
      DropdownMenuItem(
        value: null,
        child: Center(
          child: We2upText(strings.nothing, textAlign: TextAlign.center),
        ),
      ),
    ];

    dropdownItems.addAll((currentUserOnly
            ? usersBox.values.where(
                (element) {
                  return element.id == loginData.userId;
                },
              )
            : serviceStaff
                ? serviceStaffBox.values
                : commAgents
                    ? commAgentsBox.values
                    : usersBox.values)
        .map<DropdownMenuItem<UserModel>>(
      (UserModel value) {
        return DropdownMenuItem(
          value: value,
          child: Center(
            child: We2upText(
              value.username ?? value.firstName ?? strings.nothing,
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
              textAlign: TextAlign.center,
            ),
          ),
        );
      },
    ));

    return Visibility(
      visible: dropdownItems.length > 1,
      child: Container(
        height: 48.0, // Fixed height to match other input fields
        decoration: dropDownDecoration(context),
        child: DropdownButton2<UserModel?>(
          isExpanded: true,
          items: dropdownItems,
          value: user,
          underline: const SizedBox(),
          hint: Center(child: We2upText(strings.user)),
          onChanged: onChanged,
          dropdownStyleData: DropdownStyleData(maxHeight: 30.h),
          dropdownSearchData: DropdownSearchData(
            searchController: controller,
            searchInnerWidgetHeight: 45.sp,
            searchInnerWidget: Container(
              height: (Platform.isWindows || Platform.isMacOS) ? 5.5.h : 50.sp,
              padding: EdgeInsets.all(3.5.sp),
              child: TextFormField(
                expands: true,
                maxLines: null,
                controller: controller,
                decoration: InputDecoration(
                  isDense: true,
                  hintText: strings.search,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8.sp),
                  ),
                ),
              ),
            ),
            searchMatchFn: (item, searchValue) {
              final fields = [
                item.value?.username,
                item.value?.contactNumber,
                item.value?.firstName,
              ];
              final regex = RegExp(searchValue, caseSensitive: false);
              return fields.any((field) => regex.hasMatch(field ?? ""));
            },
          ),
        ),
      ),
    );
  }
}
