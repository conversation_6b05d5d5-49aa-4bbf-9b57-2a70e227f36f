import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:sizer/sizer.dart';

import '../../bloc/single_product/single_product_cubit.dart';
import '../../data/db/db_manager.dart';
import '../style_constants.dart';

class BranchOpeningStock extends StatelessWidget {
  const BranchOpeningStock({super.key});

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    return BlocBuilder<SingleProductCubit, SingleProductState>(
      buildWhen: (_, current) => current is SingleProductBranchesListChanged,
      builder: (context, state) {
        final cubit = context.read<SingleProductCubit>();
        return Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: cubit.selectedBranches.map(
            (e) {
              if (e.purchasePrice.text.isEmpty) {
                e.purchasePrice.text = cubit.singleProductDPP.text;
              }
              return Card(
                child: Padding(
                  padding: EdgeInsets.all(4.sp),
                  child: Column(
                    children: [
                      Padding(
                        padding: EdgeInsets.symmetric(vertical: 8.sp),
                        child: Text(
                          e.businessLocation.name,
                          style: Theme.of(context).textTheme.bodyLarge,
                        ),
                      ),
                      Visibility(
                        visible: canAddOpeningStockForProduct(),
                        child: NewProductNumberTextField(
                          controller: e.quantity,
                          labelText: strings.opening_quantity,
                          icon: Icons.numbers_sharp,
                        ),
                      ),
                      verticalSpacer,
                      NewProductNumberTextField(
                        controller: e.purchasePrice,
                        labelText: strings.purchase_price,
                        icon: Icons.attach_money,
                      ),
                    ],
                  ),
                ),
              );
            },
          ).toList(),
        );
      },
    );
  }
}

class NewProductNumberTextField extends StatelessWidget {
  const NewProductNumberTextField({
    super.key,
    required this.controller,
    required this.labelText,
    required this.icon,
    this.validate = false,
    this.important = false,
    this.isNumber = true,
    this.validator,
  });

  final TextEditingController controller;
  final String? Function(String?)? validator;
  final String labelText;
  final IconData icon;
  final bool validate;
  final bool isNumber;
  final bool important;

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    return TextFormField(
      controller: controller,
      keyboardType: isNumber ? TextInputType.number : null,
      validator: validate
          ? validator ??
              (v) {
                if (v == null || v.isEmpty) {
                  return strings.field_cannot_be_empty;
                }
                return null;
              }
          : null,
      decoration: InputDecoration(
        labelText: labelText,
        prefixIcon: Icon(icon),
        border: myTextFieldBorder,
        suffixIcon: important ? importantIcon(context) : null,
      ),
      inputFormatters: [
        if (isNumber)
          FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,4}?$')),
      ],
      onTap: isNumber
          ? () {
              controller.selection = TextSelection(
                baseOffset: 0,
                extentOffset: controller.text.length,
              );
            }
          : null,
    );
  }
}
