import 'package:dropdown_search/dropdown_search.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/bloc/products/products_cubit.dart';
import 'package:we2up/data/db/db_manager.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';

import '../../data/models/business_settings.dart';
import '../../data/models/product.dart';
import '../style_constants.dart';

class ProductsDropdownButton extends StatelessWidget {
  const ProductsDropdownButton({
    super.key,
    required this.onChanged,
    this.product,
    this.isInvoice = false,
    this.isPurchase = false,
    this.productsList,
  });

  final void Function(Product?) onChanged;
  final Product? product;
  final bool isInvoice;
  final bool isPurchase;
  final Iterable<Product>? productsList;

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    return DropdownSearch<Product?>(
      key: ProductsCubit.get(context).productsDropdownKey,
      selectedItem: isInvoice ? null : product,
      items: (String filterText, _) async {
        List<Product?> items = await getDropdownItems(
          context,
          strings: strings,
          filterText: filterText,
          productsList: productsList,
        ).then((items) {
          // Check if asyncItems length is 2 (including the null value)
          // and if sku of that item matches filterText
          if (items.length == 2 && items[1]?.sku == filterText) {
            onChanged(items[1]);
            // Don't close the dropdown automatically - let user continue adding products
          }
          return items;
        });
        return items;
      },
      itemAsString: (i) =>
          isInvoice ? strings.search : (i?.name ?? strings.nothing),
      onChanged: (data) => onChanged(data),
      decoratorProps: DropDownDecoratorProps(
        textAlign: TextAlign.center,
        baseStyle: Theme.of(context).textTheme.labelLarge,
        decoration: InputDecoration(
          hintText: strings.search,
          hintStyle: Theme.of(context).textTheme.labelLarge,
          border: filterInputBorder(context),
          enabledBorder: filterInputBorder(context),
          focusedBorder: filterInputBorder(context),
        ),
      ),
      popupProps: buildPopupPropsMultiSelection(strings, context),
      compareFn: (item1, item2) => item1?.id == item2?.id,
    );
  }

  PopupPropsMultiSelection<Product?> buildPopupPropsMultiSelection(
      AppLocalizations strings, BuildContext context) {
    Widget customPopupContactBuilder(
        BuildContext context, Product? product, bool isDisabled, bool isSelected) {
      final cubit = ProductsCubit.get(context);
      return ListTile(
        enabled: !isDisabled,
        selected: isSelected,
        title: Center(
          child: We2upText(product?.name ?? strings.nothing),
        ),
        subtitle: Center(
          child: We2upText(
            product != null
                ? "${cubit.piecePrice(product.id, isPurchase: isPurchase)} "
                    "${currencySymbol()} "
                    "${canShowCurrentStockInPos() ? "&& ${cubit.availableInStock(product.id)} "
                        "${cubit.allProducts[product.id]?.unit.actualName}" : ""}"
                : "",
            textDirection: TextDirection.ltr,
            decimalPlaces: 2,
          ),
        ),
      );
    }

    return PopupPropsMultiSelection.dialog(
      showSearchBox: true,
      itemBuilder: customPopupContactBuilder,
      searchDelay: Duration.zero,
      searchFieldProps: TextFieldProps(
        autofocus: true,
        decoration: InputDecoration(
          hintText: strings.search,
          suffix: const Icon(Icons.search),
          border: filterInputBorder(context),
        ),
      ),
    );
  }
}

Future<List<Product?>> getDropdownItems(
  BuildContext context, {
  required AppLocalizations strings,
  Iterable<Product>? productsList,
  required String filterText,
}) async {
  final filteredProducts = (productsList ?? productsBox.values)
      .where(
        (product) {
          final id = product.id
              .toString()
              .substring(0, product.id.toString().length - 1);
          final availInStock = ProductsCubit.get(context)
              .availableInStock(product.id)
              .toString();
          return RegExp(RegExp.escape(filterText), caseSensitive: false)
                  .hasMatch(product.name) ||
              RegExp(RegExp.escape(filterText), caseSensitive: false)
                  .hasMatch(id) ||
              RegExp(RegExp.escape(filterText), caseSensitive: false)
                  .hasMatch(product.sku ?? "") ||
              RegExp(RegExp.escape(filterText), caseSensitive: false)
                  .hasMatch(availInStock) ||
              RegExp(RegExp.escape(filterText), caseSensitive: false)
                  .hasMatch(product.productDescription ?? "");
        },
      )
      .take(50)
      .toList();

  final dropdownItems = <Product?>[];

  dropdownItems.insert(0, null);

  if (filteredProducts.isNotEmpty) {
    dropdownItems.addAll(filteredProducts);
  }

  return dropdownItems;
}
