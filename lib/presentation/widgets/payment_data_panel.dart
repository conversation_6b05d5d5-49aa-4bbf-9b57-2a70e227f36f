import 'package:expandable/expandable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:gap/gap.dart';
import 'package:sizer/sizer.dart';
import 'package:we2up/presentation/widgets/payment_account_dropdown_button.dart';
import 'package:we2up/presentation/widgets/payment_method_dropdown_button.dart';
import 'package:we2up/presentation/widgets/product_number_text_field.dart';
import 'package:we2up/presentation/widgets/show_box.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';

import '../../bloc/products/products_cubit.dart';
import '../style_constants.dart';

class PaymentDataPanel extends StatelessWidget {
  const PaymentDataPanel({super.key, this.isPurchase = false});

  final bool isPurchase;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ProductsCubit, ProductsState>(
      builder: (context, state) {
        final strings = AppLocalizations.of(context)!;
        final cubit = ProductsCubit.get(context);
        if(cubit.paymentDetailsList.isNotEmpty){
          cubit.paymentDetailsList[0].invoiceController
              .addListener(cubit.updateProducts);
        }
        return Card(
          key: cubit.paymentPanelKey,
          child: ExpandablePanel(
            controller: cubit.paymentDataPanelController,
            theme: const ExpandableThemeData(hasIcon: false),
            header: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10.sp),
                color: Theme.of(context).colorScheme.secondary.withOpacity(0.1),
              ),
              padding: EdgeInsets.all(10.sp),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const SizedBox(),
                  We2upText(
                    strings.payment_data,
                    style: Theme.of(context).textTheme.labelLarge,
                  ),
                  const Icon(Icons.keyboard_arrow_down),
                ],
              ),
            ),
            expanded: const SizedBox(),
            collapsed: Padding(
              padding: EdgeInsets.all(4.sp),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        flex: 3,
                        child: We2upText(
                          strings.net_bill,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      SizedBox(width: 8.0),
                      Expanded(
                        flex: 7,
                        child: ShowBox(
                          color: Theme.of(context).colorScheme.tertiaryContainer,
                          text: cubit
                              .getTotalPaymentFees(purchase: isPurchase)
                              .toString(),
                          width: 45,
                        ),
                      ),
                    ],
                  ),
                  verticalSpacer,
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        flex: 3,
                        child: We2upText(
                          strings.purchase_value,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      SizedBox(width: 8.0),
                      Expanded(
                        flex: 7,
                        child: ProductAmountTextField(
                          controller:
                              cubit.paymentDetailsList[0].invoiceController,
                          width: 45,
                          hint: (strings.purchase_value).replaceFirst(":", ""),
                          maxAmount:
                              cubit.getTotalPaymentFees(purchase: isPurchase),
                          focusNode: cubit.paymentFieldFocusNode,
                          fieldKey: cubit.paymentFieldKey, // Pass the GlobalKey
                          noInternalExpanded: true, // Prevent internal Expanded usage
                        ),
                      ),
                    ],
                  ),
                  verticalSpacer,
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        flex: 3,
                        child: We2upText(
                          strings.payment_method,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      SizedBox(width: 8.0),
                      Expanded(
                        flex: 7,
                        child: PaymentMethodDropdownButton(
                          isExpanded: true,
                          onChanged: (pMethod) => cubit.changeCartPaymentMethod(
                              paymentMethod: pMethod!),
                          paymentMethod:
                              cubit.paymentDetailsList[0].paymentMethod,
                        ),
                      ),
                    ],
                  ),
                  verticalSpacer,
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        flex: 3,
                        child: We2upText(
                          strings.payment_account,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      SizedBox(width: 8.0),
                      Expanded(
                        flex: 7,
                        child: PaymentAccountDropdownButton(
                          onChanged: (pAccount) => cubit.changeCartPaymentAccount(
                            index: 0,
                            paymentAccount: pAccount,
                          ),
                          paymentAccount:
                              cubit.paymentDetailsList[0].paymentAccount,
                          isExpanded: true,
                        ),
                      ),
                    ],
                  ),
                  verticalSpacer,
                  Row(
                    children: [
                      if (!isPurchase)
                        Expanded(
                          child: TextField(
                            controller: cubit.cartSaleNoteController,
                            decoration: InputDecoration(
                              hintText: isPurchase
                                  ? strings.purchase_note
                                  : strings.sale_note,
                              prefixIcon: const Icon(Icons.note_alt_rounded),
                              border: myTextFieldBorder,
                            ),
                          ),
                        ),
                      if (!isPurchase) Gap(8.sp),
                      Expanded(
                        child: TextField(
                          controller: cubit.cartEmployeeNoteController,
                          decoration: InputDecoration(
                            hintText: strings.employee_note,
                            prefixIcon: const Icon(Icons.note_add_sharp),
                            border: myTextFieldBorder,
                          ),
                        ),
                      )
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
