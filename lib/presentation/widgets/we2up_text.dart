import 'package:flutter/material.dart';

import '../../utils/we2up_constants.dart';

class We2upText extends Text {
  We2upText(
    String data, {
    Key? key,
    TextStyle? style,
    TextAlign? textAlign,
    TextDirection? textDirection,
    Locale? locale,
    bool? softWrap,
    TextOverflow? overflow,
    int? maxLines,
    int? decimalPlaces,
    String? semanticsLabel,
    bool isPhone = false,
  }) : super(
          _formatData(data, isPhone, decimalPlaces),
          key: key,
          style: style,
          textAlign: textAlign,
          textDirection: textDirection,
          locale: locale,
          softWrap: softWrap,
          overflow: overflow,
          maxLines: maxLines,
          semanticsLabel: semanticsLabel,
        );

  static String _formatData(String data, bool isPhone, int? decimalPlaces) {
    if (isPhone) {
      // If it's a phone number, preserve leading zeros and apply Arabic digit conversion
      return applyArabicDigitConversion(data);
    } else if (_containsNumbersOnly(data)) {
      // If the string contains only digits, apply Arabic digit conversion and number formatting
      return applyArabicDigitConversion(_formatNumber(
        data,
        decimalPlaces: decimalPlaces,
      ));
    } else if (_containsNumberPattern(data)) {
      // If the string contains a pattern like "price is: 1.56",
      // extract and format the numeric part
      return applyArabicDigitConversion(
          _replaceNumberPattern(data, decimalPlaces));
    } else {
      // For other cases, always apply Arabic digit conversion
      return applyArabicDigitConversion(data);
    }
  }

  static bool _containsNumbersOnly(String text) {
    return RegExp(r'^\d+(\.\d+)?$').hasMatch(text);
  }

  static bool _containsNumberPattern(String text) {
    // Check if the string contains a pattern like "price is: 1.56"
    return RegExp(r'\d+(\.\d+)?').hasMatch(text);
  }

  static String _formatNumber(String text, {int? decimalPlaces}) {
    if (text.isEmpty) {
      return text; // Return the original text if it's empty
    }

    try {
      double number = double.parse(text);

      if (number == number.floor()) {
        // Number has no decimal places
        return number.toStringAsFixed(0);
      } else {
        // Number has decimal places
        return number.toStringAsFixed(decimalPlaces ?? 4);
      }
    } catch (e) {
      // Handle the case where text is not a valid number
      return text;
    }
  }

  static String _replaceNumberPattern(String text, int? decimalPlaces) {
    return text.replaceAllMapped(RegExp(r'(\d+(\.\d+)?)'), (match) {
      return _formatNumber(match.group(1)!, decimalPlaces: decimalPlaces);
    });
  }
}
