import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/data/models/customer_group.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';
import '../../data/db/db_manager.dart';
import '../style_constants.dart';

class CustomerGroupDropdown extends StatelessWidget {
  const CustomerGroupDropdown({
    super.key,
    this.isExpanded,
    required this.onChanged,
    this.value,
  });

  final bool? isExpanded;
  final Function(CustomerGroup?) onChanged;
  final CustomerGroup? value;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: dropDownDecoration(context),
      child: DropdownButton<CustomerGroup>(
        value: value,
        underline: const SizedBox(),
        isExpanded: isExpanded ?? false,
        onChanged: onChanged,
        items: [
          DropdownMenuItem<CustomerGroup>(
            value: null,
            child:
            Center(child: We2upText(AppLocalizations.of(context)!.nothing)),
          ),
          ...customerGroupBox.values.map((group) {
            return DropdownMenuItem<CustomerGroup>(
              value: group,
              child: Center(child: We2upText(group.name ?? "N/A")),
            );
          }),
        ],
      ),
    );
  }
}