import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/data/models/service_model.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';

import '../../data/db/db_manager.dart';
import '../style_constants.dart';

class BillServiceDropdown extends StatelessWidget {
  const BillServiceDropdown({
    super.key,
    this.isExpanded = false,
    this.padding = 5,
    required this.onChanged,
    required this.service,
  });

  final bool isExpanded;
  final double padding;
  final ValueChanged<ServiceModel?> onChanged;
  final ServiceModel? service;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 48.0, // Fixed height to match other input fields
      decoration: dropDownDecoration(context),
      child: DropdownButton<ServiceModel>(
        value: service,
        padding: EdgeInsets.all(padding.sp),
        isDense: true,
        isExpanded: isExpanded,
        onChanged: onChanged,
        underline: const SizedBox(),
        items: [
          DropdownMenuItem<ServiceModel>(
            value: null,
            child: Center(child: We2upText(AppLocalizations.of(context)!.nothing)),
          ),
          ...serviceModelBox.values.map((ServiceModel value) {
            return DropdownMenuItem<ServiceModel>(
              value: value,
              child: Center(child: We2upText(value.name)),
            );
          }),
        ],
      ),
    );
  }
}
