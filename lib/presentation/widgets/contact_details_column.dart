import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:sizer/sizer.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';

import '../../data/db/db_manager.dart';
import '../../data/models/bill_of_sale_data.dart';
import '../style_constants.dart';

class ContactDetailsColumn extends StatelessWidget {
  const ContactDetailsColumn({
    super.key,
    required BillData billOfSaleData,
  }) : _billOfSaleData = billOfSaleData;

  final BillData _billOfSaleData;

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    final contact = contactsBox.get(_billOfSaleData.sell!.contactId)!;
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(5.sp),
      decoration: showBoxDecoration(context),
      child: Column(
        children: [
          ListTile(
            title: We2upText(
              "${strings.name}:",
              style: Theme.of(context).textTheme.titleMedium,
            ),
            trailing: We2upText(contact.name ?? ""),
          ),
          const Divider(),
          ListTile(
            title: We2upText(
              "${strings.mobile}:",
              style: Theme.of(context).textTheme.titleMedium,
              isPhone: true,
            ),
            trailing: We2upText(contact.mobile ?? ""),
          ),
          const Divider(),
          ListTile(
            title: We2upText(
              "${strings.due_amount}:",
              style: Theme.of(context).textTheme.titleMedium,
            ),
            trailing: We2upText(contact.due ?? ""),
          ),
        ],
      ),
    );
  }
}
