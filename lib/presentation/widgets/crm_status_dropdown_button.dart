import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';

import '../../data/models/follow_up_to_api.dart';
import '../style_constants.dart';

class CrmStatusDropdownButton extends StatelessWidget {
  const CrmStatusDropdownButton({
    super.key,
    this.isExpanded,
    this.padding,
    required this.onChanged,
    required this.status,
  });

  final bool? isExpanded;
  final double? padding;
  final ValueChanged<Status?> onChanged;
  final Status status;

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;

    return Container(
      decoration: dropDownDecoration(context),
      child: DropdownButton<Status>(
        value: status,
        padding: EdgeInsets.all(padding != null ? padding!.sp : 5.sp),
        isDense: true,
        isExpanded: isExpanded ?? true,
        onChanged: onChanged,
        underline: const SizedBox(),
        items: Status.values
            .map<DropdownMenuItem<Status>>(
              (Status value) => DropdownMenuItem<Status>(
            value: value,
            child: Center(
              child: We2upText(
                strings.translateCrmStatus(value),
              ),
            ),
          ),
        )
            .toList(),
      ),
    );
  }
}

extension AppLocalizationsExtension on AppLocalizations {
  String translateCrmStatus(Status? followUpType) {
    switch (followUpType) {
      case Status.scheduled:
        return scheduled;
      case Status.open:
        return open_status;
      case Status.completed:
        return completed;
      case Status.cancelled:
        return cancelled_status;
      default:
        return nothing;
    }
  }
}