import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:go_router/go_router.dart';
import 'package:we2up/bloc/landing/landing_cubit.dart';
import 'package:we2up/data/models/business_location.dart';
import 'package:we2up/data/models/cash_register_to_api.dart';
import 'package:we2up/data/models/login_data.dart';
import 'package:we2up/data/repository/api_repo.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';
import 'package:we2up/utils/we2up_constants.dart';

import '../../data/db/db_manager.dart';
import 'locations_dropdown_button.dart';

class CashRegisterDialog extends StatefulWidget {
  const CashRegisterDialog({super.key});

  @override
  State<CashRegisterDialog> createState() => _CashRegisterDialogState();
}

class _CashRegisterDialogState extends State<CashRegisterDialog> {
  late final TextEditingController noteController;
  late BusinessLocation location;

  @override
  void initState() {
    noteController = TextEditingController();
    location = businessLocationsBox.values.first;
    super.initState();
  }

  @override
  void dispose() {
    noteController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    final LandingCubit cubit = LandingCubit.get(context);

    return AlertDialog(
      title: We2upText(
        loginData.cashRegistered
            ? strings.closeCashRegister
            : strings.openCashRegister,
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          if (!loginData.cashRegistered)
            BusinessLocationDropdown(
              value: location,
              isExpanded: true,
              onChanged: (l) => setState(() => location = l!),
            ),
          if (loginData.cashRegistered)
            TextField(
              controller: noteController,
              decoration: InputDecoration(
                labelText: strings.closeCashRegisterNote,
              ),
            ),
        ],
      ),
      actions: <Widget>[
        TextButton(
          onPressed: () => context.pop(),
          child: We2upText(strings.cancel),
        ),
        TextButton(
          onPressed: () async {
            EasyLoading.show(status: strings.loading);
            final locationInfo = await ApiRepository.get().getCurrentLocation();
            ApiRepository.get()
                .sendCashRegister(
              cashRegister: CashRegisterToAPI(
                id: loginData.cashRegisteredId,
                locationInfo: locationInfo,
                locationId: location.id,
                createdAt: DateTime.now(),
                status: loginData.cashRegistered
                    ? CashRegisterStatus.close
                    : CashRegisterStatus.open,
                closingAmount: double.parse(cubit.remainingCash()),
                closingNote: noteController.text,
              ),
            )
                .then((bool response) async {
              if (response) {
                if (!loginData.cashRegistered) {
                  await fetchDataAndStoreAllInHive(context, refresh: true);
                } else {
                  // Update the Hive box directly to ensure the ValueListenableBuilder is notified
                  final updatedLoginData = LoginData(
                    loginData.tokenType,
                    loginData.expiresIn,
                    loginData.accessToken,
                    loginData.refreshToken,
                    loginData.userId,
                    loginData.clockedIn,
                    loginData.clockedInTime,
                    loginData.isAdmin,
                    loginData.permissions,
                    false, // cashRegistered = false
                    null, // cashRegisteredTime = null
                    null, // cashRegisteredId = null
                    loginData.allowOverSelling,
                    loginData.locationRequired,
                    loginData.username,
                    loginData.password,
                    loginData.defaultAccount,
                    loginData.inSync,
                    loginData.permittedLocations,
                    loginData.lastUpdateTime,
                    loginData.isProductsReady,
                    loginData.isContactsReady,
                    loginData.commAgntAllowed,
                    loginData.tablesAllowed,
                    loginData.serviceStaffAllowed,
                    loginData.transactionEditDays,
                    loginData.isCommissionAgentRequired,
                  );
                  credentialsBox.put(loginDataKey, updatedLoginData);
                  cubit.resetTotalReturns();
                  EasyLoading.dismiss();
                }
              } else {
                EasyLoading.showError("Error!!");
              }
              cubit.updateLandingScreen();
              context.pop();
            });
          },
          child: We2upText(
            loginData.cashRegistered ? strings.end : strings.open,
          ),
        ),
      ],
    );
  }
}
