import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';
import '../../data/models/business_location.dart';
import '../../data/db/db_manager.dart';
import '../style_constants.dart';

class BusinessLocationFilterDropdown extends StatelessWidget {
  const BusinessLocationFilterDropdown({
    super.key,
    this.isExpanded,
    required this.onChanged,
    this.value,
  });

  final bool? isExpanded;
  final Function(BusinessLocation?) onChanged;
  final BusinessLocation? value;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: dropDownDecoration(context),
      child: DropdownButton<BusinessLocation>(
        value: value,
        underline: const SizedBox(),
        isExpanded: isExpanded ?? false,
        onChanged: onChanged,
        items: [
          DropdownMenuItem<BusinessLocation>(
            value: null,
            child:
                Center(child: We2upText(AppLocalizations.of(context)!.all_branches)),
          ),
          ...businessLocationsBox.values.map((location) {
            return DropdownMenuItem<BusinessLocation>(
              value: location,
              child: Center(child: We2upText(location.name)),
            );
          }),
        ],
      ),
    );
  }
}
