import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';

import '../../data/db/db_manager.dart';
import '../../data/models/product.dart';
import '../style_constants.dart';

class TaxDropdownButton extends StatelessWidget {
  const TaxDropdownButton({
    super.key,
    this.isExpanded = false,
    this.padding = 5,
    required this.onChanged,
    this.taxRate,
  });

  final bool isExpanded;
  final double padding;
  final ValueChanged<TaxRate?> onChanged;
  final TaxRate? taxRate;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 48.0, // Fixed height to match other input fields
      width: isExpanded ? double.infinity : null,
      decoration: dropDownDecoration(context),
      child: DropdownButton<TaxRate>(
        value: taxRate,
        padding: EdgeInsets.all(padding.sp),
        isDense: true,
        isExpanded: isExpanded,
        onChanged: onChanged,
        underline: const SizedBox(),
        items: [
          DropdownMenuItem<TaxRate>(
            value: null,
            child: Center(
              child: We2upText(AppLocalizations.of(context)!.nothing),
            ),
          ),
          ...taxRatesBox.values
              .map<DropdownMenuItem<TaxRate>>((TaxRate value) {
            return DropdownMenuItem<TaxRate>(
              value: value,
              child: Center(child: We2upText(value.name ?? "")),
            );
          }),
        ],
      ),
    );
  }
}
