import 'package:conditional_builder_null_safety/conditional_builder_null_safety.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/bloc/products/products_cubit.dart';
import 'package:we2up/presentation/style_constants.dart';
import 'package:we2up/presentation/widgets/invoice_products_panel.dart';

import '../../../data/models/bill_of_sale_data.dart';
import '../customer_data_panel.dart';
import '../payment_data_panel.dart';
import '../purchase_return_single_button.dart';
import '../purchase_row_buttons.dart';
import '../shipping_and_taxes_panel.dart';
import '../total_return_amount.dart';
import '../we2up_text.dart';

class PurchaseDetailsSection extends StatelessWidget {
  const PurchaseDetailsSection({
    super.key,
    required BillData billData,
  }) : _billData = billData;

  final BillData _billData;

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    final cubit = ProductsCubit.get(context);
    return ListView(
      children: [
        ConditionalBuilder(
          condition: _billData.pReturn,
          builder: (context) => PurchaseReturnContactData(
            billData: _billData,
          ),
          fallback: (_) => const CustomerDataPanel(isPurchase: true),
        ),
        verticalSpacer,
        InvoiceProductsPanel(isPurchase: true, isReturn: _billData.pReturn),
        TotalReturnAmount(billData: _billData),
        ConditionalBuilder(
          condition: !_billData.pReturn,
          builder: (context) {
            return Column(
              children: [
                verticalSpacer,
                const ShippingAndTaxesPanel(isPurchase: true),
                verticalSpacer,
                const PaymentDataPanel(isPurchase: true),
              ],
            );
          },
          fallback: (_) => const SizedBox(),
        ),
        const Divider(),
        ConditionalBuilder(
          condition: _billData.pReturn,
          builder: (context) {
            return SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: cubit.cartCustomerContact != null &&
                        cubit.cartProducts.isNotEmpty &&
                        _billData.purchase!.id != null
                    ? () async {
                        final result = await cubit.sendPurchaseReturn(
                          context,
                          purchaseID: _billData.purchase!.id!,
                        );
                        if (result && context.mounted) context.pop();
                      }
                    : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.error,
                ),
                child: We2upText(
                  strings.button_return,
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.surface,
                  ),
                ),
              ),
            );
          },
          fallback: (_) => PurchaseRowButtons(billData: _billData),
        ),
      ],
    );
  }
}
