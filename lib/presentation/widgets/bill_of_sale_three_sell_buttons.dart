import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:sizer/sizer.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';
import 'package:we2up/utils/route_constants.dart';

import '../../bloc/products/products_cubit.dart';
import '../../data/db/db_manager.dart';

class ThreeSellButtons extends StatelessWidget {
  const ThreeSellButtons({
    super.key,
    this.sellID,
    this.offlineID,
    this.goHome = false,
  });

  final int? sellID;
  final String? offlineID;
  final bool goHome;

  @override
  Widget build(BuildContext context) {
    final cubit = ProductsCubit.get(context);
    final theme = Theme.of(context);
    final strings = AppLocalizations.of(context)!;
    final isEdit = cubit.currentBillData != null;
    final isCommissionAgentRequired =
        ((loginData.isCommissionAgentRequired == null ||
                !loginData.isCommissionAgentRequired!) ||
            (loginData.isCommissionAgentRequired! && cubit.commAgent != null));

    void afterSale(bool value) {
      cubit.checkToPrint(context);
      if (value && (goHome || sellID != null)) {
        context.go(landingScreen);
      } else if(isEdit){
        context.pop();
      }
    }

    return BlocConsumer<ProductsCubit, ProductsState>(
      listener: (context, state) {
        if (state is NewSalesLoading || state is SellLoading) {
          EasyLoading.show(status: strings.loading);
        } else {
          EasyLoading.dismiss();
        }
      },
      buildWhen: (_, current) =>
          current is PaymentMethodPaymentAccountChanged ||
          current is CartPaymentChanged,
      builder: (context, state) {
        return Container(
          width: double.infinity,
          padding: EdgeInsets.all(5.sp),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  if (canSellOnCredit() &&
                      cubit.cartCustomerContact?.isDefault != 1)
                    Expanded(
                      child: ElevatedButton(
                        onPressed: cubit.cartCustomerContact != null &&
                                cubit.cartProducts.isNotEmpty &&
                                cubit.cartProducts.every((e) =>
                                    cubit.availableInStock(e) > 0 ||
                                    cubit.allowOverSelling(e)) &&
                                isCommissionAgentRequired
                            ? () => cubit
                                .sendSellToAPI(
                                  context,
                                  sellID: sellID,
                                  offlineID: offlineID,
                                  credit: true,
                                )
                                .then(afterSale)
                            : null,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: theme.colorScheme.error,
                          padding: EdgeInsets.zero,
                        ),
                        child: We2upText(
                          strings.order_sale,
                          style: TextStyle(
                            color: theme.colorScheme.surface,
                          ),
                        ),
                      ),
                    ),
                  if (canSellOnCredit() &&
                      cubit.cartCustomerContact?.isDefault != 1)
                    Gap(5.w),
                  if (cubit.cartCustomerContact?.isDefault != 1)
                    Expanded(
                      child: ElevatedButton(
                        onPressed: cubit.cartCustomerContact != null &&
                                cubit.cartProducts.isNotEmpty &&
                                cubit.paymentDetailsList.every((e) =>
                                    e.paymentAccount != null &&
                                    ((double.tryParse(
                                                e.invoiceController.text) ??
                                            0) >
                                        0)) &&
                                cubit.cartProducts.every((e) =>
                                    cubit.availableInStock(e) > 0 ||
                                    cubit.allowOverSelling(e)) &&
                                isCommissionAgentRequired
                            ? () => cubit
                                .sendSellToAPI(
                                  context,
                                  sellID: sellID,
                                  offlineID: offlineID,
                                )
                                .then(afterSale)
                            : null,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          padding: EdgeInsets.zero,
                        ),
                        child: We2upText(
                          strings.partial,
                          style: TextStyle(
                            color: theme.colorScheme.surface,
                          ),
                        ),
                      ),
                    ),
                  if (cubit.cartCustomerContact?.isDefault != 1) Gap(5.w),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: cubit.cartCustomerContact != null &&
                              cubit.cartProducts.isNotEmpty &&
                              cubit.paymentDetailsList.every((element) =>
                                  element.paymentAccount != null) &&
                              cubit.cartProducts.every((e) =>
                                  cubit.availableInStock(e) > 0 ||
                                  cubit.allowOverSelling(e)) &&
                              isCommissionAgentRequired
                          ? () => cubit
                              .sendSellToAPI(context,
                                  cash: true,
                                  sellID: sellID,
                                  offlineID: offlineID)
                              .then(afterSale)
                          : null,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        padding: EdgeInsets.zero,
                      ),
                      child: We2upText(
                        strings.cash_sale,
                        style: TextStyle(
                          color: theme.colorScheme.surface,
                        ),
                      ),
                    ),
                  ),
                  Gap(5.w),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: cubit.cartProducts.isNotEmpty &&
                              cubit.cartCustomerContact != null &&
                              canOfferPriceDuringSales()
                          ? () => cubit
                              .sendSellToAPI(
                                context,
                                isQuotation: 1,
                                sellID: sellID,
                                offlineID: offlineID,
                              )
                              .then(afterSale)
                          : null,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.amber,
                        padding: EdgeInsets.zero,
                      ),
                      child: We2upText(
                        strings.quotation_button,
                        style: TextStyle(
                          color: theme.colorScheme.surface,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }
}
