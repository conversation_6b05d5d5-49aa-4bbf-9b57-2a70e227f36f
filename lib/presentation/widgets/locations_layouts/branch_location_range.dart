import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:sizer/sizer.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/bloc/location/location_cubit.dart';

import '../../../utils/location_range_constants.dart';
import '../../style_constants.dart';
import '../we2up_text.dart';

class BranchLocationRange extends StatelessWidget {
  const BranchLocationRange({super.key});

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    final cubit = context.read<LocationCubit>();
    cubit.branchHighController.text = getBranchHighDefault().toString();
    cubit.branchMidController.text = getBranchMedDefault().toString();
    cubit.branchLowController.text = getBranchLowDefault().toString();
    return Column(
      children: [
        ListTile(
          visualDensity: VisualDensity.comfortable,
          title: We2upText(strings.branch_location_range),
          trailing: FilledButton(
            onPressed: () => cubit.updateBranchRanges(reset: true),
            child: We2upText(strings.reset_ranges),
          ),
        ),
        Gap(8.sp),
        Row(
          children: [
            Expanded(flex: 2,child: We2upText(strings.location_high)),
            Expanded(
              flex: 2,
              child: TextField(
                keyboardType: TextInputType.number,
                controller: cubit.branchHighController,
                textAlign: TextAlign.center,
                decoration: searchDropdownButtonDecoration(context),
              ),
            ),
            Gap(5.sp),
            Expanded(child: We2upText(strings.kilometer)),
          ],
        ),
        Gap(8.sp),
        Row(
          children: [
            Expanded(flex: 2, child: We2upText(strings.location_medium)),
            Expanded(
              flex: 2,
              child: TextField(
                keyboardType: TextInputType.number,
                controller: cubit.branchMidController,
                textAlign: TextAlign.center,
                decoration: searchDropdownButtonDecoration(context),
              ),
            ),
            Gap(5.sp),
            Expanded(child: We2upText(strings.kilometer)),
          ],
        ),
        Gap(8.sp),
        Row(
          children: [
            Expanded(flex: 2, child: We2upText(strings.location_low)),
            Expanded(
              flex: 2,
              child: TextField(
                keyboardType: TextInputType.number,
                controller: cubit.branchLowController,
                textAlign: TextAlign.center,
                decoration: searchDropdownButtonDecoration(context),
              ),
            ),
            Gap(5.sp),
            Expanded(child: We2upText(strings.kilometer)),
          ],
        ),
        Gap(8.sp),
        SizedBox(
          width: 50.w,
          child: OutlinedButton.icon(
            icon: const Icon(Icons.save),
            label: We2upText(strings.save),
            onPressed: cubit.updateBranchRanges,
          ),
        ),
      ],
    );
  }
}
