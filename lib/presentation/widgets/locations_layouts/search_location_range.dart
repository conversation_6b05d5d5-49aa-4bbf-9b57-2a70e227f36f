import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:sizer/sizer.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/bloc/location/location_cubit.dart';

import '../../../utils/location_range_constants.dart';
import '../../style_constants.dart';
import '../we2up_text.dart';

class SearchLocationRange extends StatelessWidget {
  const SearchLocationRange({super.key});

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    final cubit = context.read<LocationCubit>();
    cubit.searchHighController.text = getSearchHighDefault().toString();
    cubit.searchMidController.text = getSearchMedDefault().toString();
    cubit.searchLowController.text = getSearchLowDefault().toString();
    return Column(
      children: [
        ListTile(
          visualDensity: VisualDensity.comfortable,
          title: We2upText(strings.search_location_range),
          trailing: Filled<PERSON>utton(
            onPressed: () => cubit.updateSearchRanges(reset: true),
            child: We2upText(strings.reset_ranges),
          ),
        ),
        Gap(8.sp),
        Row(
          children: [
            Expanded(flex: 2, child: We2upText(strings.location_high)),
            Expanded(
              flex: 2,
              child: TextField(
                keyboardType: TextInputType.number,
                controller: cubit.searchHighController,
                textAlign: TextAlign.center,
                decoration: searchDropdownButtonDecoration(context),
              ),
            ),
            Gap(5.sp),
            Expanded(child: We2upText(strings.kilometer)),
          ],
        ),
        Gap(8.sp),
        Row(
          children: [
            Expanded(flex: 2, child: We2upText(strings.location_medium)),
            Expanded(
              flex: 2,
              child: TextField(
                keyboardType: TextInputType.number,
                controller: cubit.searchMidController,
                textAlign: TextAlign.center,
                decoration: searchDropdownButtonDecoration(context),
              ),
            ),
            Gap(5.sp),
            Expanded(child: We2upText(strings.kilometer)),
          ],
        ),
        Gap(8.sp),
        Row(
          children: [
            Expanded(flex: 2, child: We2upText(strings.location_low)),
            Expanded(
              flex: 2,
              child: TextField(
                keyboardType: TextInputType.number,
                controller: cubit.searchLowController,
                textAlign: TextAlign.center,
                decoration: searchDropdownButtonDecoration(context),
              ),
            ),
            Gap(5.sp),
            Expanded(child: We2upText(strings.kilometer)),
          ],
        ),
        Gap(8.sp),
        SizedBox(
          width: 50.w,
          child: OutlinedButton.icon(
            icon: const Icon(Icons.save),
            label: We2upText(strings.save),
            onPressed: cubit.updateSearchRanges,
          ),
        ),
      ],
    );
  }
}
