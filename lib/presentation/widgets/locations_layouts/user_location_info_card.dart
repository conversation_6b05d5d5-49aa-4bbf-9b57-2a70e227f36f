import 'package:expandable/expandable.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:sizer/sizer.dart';
import 'package:we2up/presentation/widgets/location_range_dropdown_button.dart';

import '../../../data/models/user_location_info.dart';
import '../../../utils/we2up_constants.dart';
import '../we2up_text.dart';

class UserLocationInfoCard extends StatelessWidget {
  final UserLocationInfo userLocationInfo;

  const UserLocationInfoCard({super.key, required this.userLocationInfo});

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;

    String lastTransactionDateTime() {
      return userLocationInfo.lastTransactionDateTime != null
          ? formatDate(userLocationInfo.lastTransactionDateTime!)
          : strings.nothing;
    }

    String shiftOpeningDateTime() {
      return userLocationInfo.openShiftDateTime != null
          ? formatDate(userLocationInfo.openShiftDateTime!)
          : strings.nothing;
    }

    return Card(
      child: ExpandablePanel(
        header: Padding(
          padding: EdgeInsets.all(4.sp),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              IconButton(
                onPressed: () => goToLocation(
                  userLocationInfo.userLocation,
                  strings.no_location,
                ),
                icon: const Icon(Icons.location_history),
              ),
              We2upText(userLocationInfo.username),
              const SizedBox(),
            ],
          ),
        ),
        expanded: const SizedBox(),
        collapsed: Column(
          children: [
            ListTile(
              visualDensity: VisualDensity.comfortable,
              title: We2upText(strings.open_shift_location),
              subtitle: We2upText(
                "${strings.translateLocationAccuracy(
                  userLocationInfo.shiftOpeningRange,
                )} - ${shiftOpeningDateTime()}",
              ),
              trailing: IconButton(
                onPressed: () => goToLocation(
                  userLocationInfo.openShiftLocation,
                  strings.no_location,
                ),
                icon: const Icon(Icons.location_on),
              ),
            ),
            Gap(8.sp),
            ListTile(
              visualDensity: VisualDensity.comfortable,
              title: We2upText(strings.last_transaction_location),
              subtitle: We2upText(
                "${strings.translateLocationAccuracy(
                  userLocationInfo.lastTransactionRange,
                )} - ${lastTransactionDateTime()}",
              ),
              trailing: IconButton(
                onPressed: () => goToLocation(
                  userLocationInfo.lastTransactionLocation,
                  strings.no_location,
                ),
                icon: const Icon(Icons.location_on),
              ),
            ),
            Gap(8.sp),
            ListTile(
              visualDensity: VisualDensity.comfortable,
              title: We2upText(strings.closest_location),
              subtitle: We2upText(
                  userLocationInfo.closestContact?.name ?? strings.nothing),
              trailing: IconButton(
                onPressed: () => goToLocation(
                  userLocationInfo.closestContact?.locationInfo,
                  strings.no_location,
                ),
                icon: const Icon(Icons.location_on),
              ),
            ),
            Gap(8.sp),
          ],
        ),
      ),
    );
  }
}
