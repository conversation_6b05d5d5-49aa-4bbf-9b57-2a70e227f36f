import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:sizer/sizer.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/bloc/location/location_cubit.dart';
import 'package:we2up/utils/location_range_constants.dart';

import '../../style_constants.dart';
import '../we2up_text.dart';

class ClosestLocationRange extends StatelessWidget {
  const ClosestLocationRange({super.key});

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    final cubit = context.read<LocationCubit>();
    cubit.closestRangeController.text = getClosestRangeDefault().toString();
    return Column(
      children: [
        Row(
          children: [
            Expanded(flex: 2, child: We2upText(strings.closest_location_range)),
            Expanded(
              flex: 2,
              child: TextField(
                keyboardType: TextInputType.number,
                controller: cubit.closestRangeController,
                textAlign: TextAlign.center,
                decoration: searchDropdownButtonDecoration(context),
              ),
            ),
            Gap(5.sp),
            Expanded(child: We2upText(strings.kilometer)),
          ],
        ),
        Gap(8.sp),
        SizedBox(
          width: 50.w,
          child: OutlinedButton.icon(
            icon: const Icon(Icons.save),
            label: We2upText(strings.save),
            onPressed: cubit.updateClosestRange,
          ),
        ),
        Gap(8.sp),
      ],
    );
  }
}
