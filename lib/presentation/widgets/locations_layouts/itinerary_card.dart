import 'package:expandable/expandable.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:hive/hive.dart';
import 'package:intl/intl.dart';
import 'package:sizer/sizer.dart';
import 'package:we2up/data/db/db_manager.dart';
import 'package:we2up/data/models/location_transaction_row.dart';
import 'package:we2up/presentation/widgets/location_range_dropdown_button.dart';

import '../../../utils/we2up_constants.dart';
import '../we2up_text.dart';

class ItineraryCard extends StatelessWidget {
  final LocationTransaction transaction;

  const ItineraryCard({super.key, required this.transaction});

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    return Card(
      child: ExpandablePanel(
        controller: ExpandableController(initialExpanded: true),
        header: Padding(
          padding: EdgeInsets.all(4.sp),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  IconButton(
                    onPressed: () => goToLocation(
                      transaction.locationInfo,
                      strings.no_location,
                    ),
                    icon: const Icon(Icons.my_location),
                  ),
                  We2upText(transaction.transactionType),
                  const SizedBox(),
                ],
              ),
              We2upText(
                DateFormat.yMd(
                  Hive.box(settingsBox).get("language", defaultValue: "ar"),
                ).add_jm().format(transaction.dateTime),
              ),
            ],
          ),
        ),
        expanded: const SizedBox(),
        collapsed: Column(
          children: [
            ListTile(
              visualDensity: VisualDensity.comfortable,
              title: We2upText(strings.closest_location),
              subtitle: We2upText(transaction.closestContact?.name ?? "N/A"),
              trailing: IconButton(
                onPressed: () => goToLocation(
                  transaction.closestContact?.locationInfo,
                  strings.no_location,
                ),
                icon: const Icon(Icons.location_on),
              ),
            ),
            Gap(8.sp),
            ListTile(
              visualDensity: VisualDensity.comfortable,
              title: We2upText(strings.username),
              trailing: We2upText(transaction.userName),
            ),
            Gap(8.sp),
            ListTile(
              visualDensity: VisualDensity.comfortable,
              title: We2upText(transaction.amountWithdrawn != null
                  ? strings.amount_withdrawn
                  : strings.amount_entered),
              trailing: We2upText(
                transaction.amountWithdrawn != null
                    ? transaction.amountWithdrawn!
                    : transaction.amountEntered!,
              ),
            ),
            Gap(8.sp),
            ListTile(
              visualDensity: VisualDensity.comfortable,
              title: We2upText(strings.contact),
              trailing: We2upText(transaction.contactName),
            ),
            Gap(8.sp),
            ListTile(
              visualDensity: VisualDensity.comfortable,
              title: We2upText(strings.distance_from_contact),
              trailing: We2upText(
                strings.translateLocationAccuracy(
                  transaction.distanceFromContact,
                ),
              ),
            ),
            Gap(8.sp),
            ListTile(
              visualDensity: VisualDensity.comfortable,
              title: We2upText(strings.distance_from_company),
              trailing: We2upText(
                strings.translateLocationAccuracy(
                  transaction.distanceFromCompany,
                ),
              ),
            ),
            Gap(8.sp),
          ],
        ),
      ),
    );
  }
}
