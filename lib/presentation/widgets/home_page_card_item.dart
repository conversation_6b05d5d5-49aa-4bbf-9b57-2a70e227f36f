import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';

class HomePageCardItem extends StatelessWidget {
  const HomePageCardItem({
    super.key,
    required this.icon,
    required this.label,
    this.onTap,
  });
  final IconData icon;
  final String label;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Card(
        elevation: 5,
        surfaceTintColor: onTap != null
            ? Theme.of(context).colorScheme.surface
            : Theme.of(context).disabledColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadiusDirectional.circular(10.sp),
        ),
        child: Column(
          children: [
            Expanded(
              flex: 5,
              child: Center(
                child: Icon(
                  icon,
                  size: 22.sp,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
            ),
            Expanded(
              flex: 3,
              child: We2upText(
                label,
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                  fontSize: 18.sp,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
