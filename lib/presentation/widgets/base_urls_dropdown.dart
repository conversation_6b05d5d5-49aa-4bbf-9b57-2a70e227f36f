import 'package:flutter/material.dart';
import 'package:hive/hive.dart';
import 'package:sizer/sizer.dart';

import '../../data/models/base_url.dart';
import '../../utils/we2up_constants.dart';
import '../../data/db/db_manager.dart';
import '../style_constants.dart';

class BaseUrlsDropdownButton extends StatelessWidget {
  const BaseUrlsDropdownButton({
    super.key,
    required this.onChanged,
  });

  final void Function(BaseUrl?) onChanged;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: dropDownDecoration(context),
      child: DropdownButton<BaseUrl>(
        value: Hive.box(userDetailsBox).get(baseUrl,defaultValue: baseURLs[0]),
        padding: EdgeInsets.all(10.sp),
        isDense: true,
        isExpanded: true,
        underline: const SizedBox(),
        onChanged: onChanged,
        items: baseURLs.map<DropdownMenuItem<BaseUrl>>((baseUrl) {
          return DropdownMenuItem<BaseUrl>(
            value: baseUrl,
            child: Center(child: Text(baseUrl.name)),
          );
        }).toList(),
      ),
    );
  }
}