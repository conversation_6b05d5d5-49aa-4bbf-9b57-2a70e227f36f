import 'package:flutter/material.dart';
import 'package:flutter_offline/flutter_offline.dart';
import 'package:go_router/go_router.dart';
import 'package:sizer/sizer.dart';
import 'package:we2up/bloc/products/products_cubit.dart';
import 'package:we2up/data/models/bill_of_sale_data.dart';
import 'package:we2up/data/models/purchase.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/data/db/db_manager.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';
import 'package:we2up/utils/route_constants.dart';
import 'package:we2up/utils/we2up_constants.dart';

import '../../bloc/products/pdf_function.dart';
import '../../data/models/business_settings.dart';

class PurchaseInvoiceCard extends StatelessWidget {
  const PurchaseInvoiceCard({
    super.key,
    required this.purchase,
    this.isReturn = false,
  });

  final Purchase purchase;
  final bool isReturn;

  bool hasReturnItems() {
    bool hasReturnItems = false;
    for (var purchaseLine in purchase.purchaseLines!) {
      if ((double.parse(purchaseLine.quantityReturned ?? "0")) != 0) {
        hasReturnItems = true;
        break;
      }
    }
    return hasReturnItems;
  }

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    final cubit = ProductsCubit.get(context);
    final canReturn = !hasReturnItems() &&
        !purchase.offline &&
        isProductsAndContactsReady() &&
        isWithinTransactionEditDays(purchase.transactionDate);
    final canUpdate = canUpdatePurchase() &&
        isProductsAndContactsReady() &&
        isWithinTransactionEditDays(purchase.transactionDate);
    return Card(
      child: Container(
        padding: EdgeInsets.all(10.sp),
        width: double.infinity,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      We2upText(
                        '${strings.ref_no}: ${purchase.refNo}',
                        style: Theme.of(context).textTheme.labelLarge,
                      ),
                      We2upText(
                        "${strings.invoice_amount}: ${purchase.finalTotal} "
                        "${currencySymbol()}",
                      ),
                      We2upText(
                        "${strings.paid_amount}: "
                        "${purchase.paymentLines != null && purchase.paymentLines!.isNotEmpty ? purchase.paymentLines![0].amount ?? "0.00" : "0.00"} "
                        "${currencySymbol()}",
                      ),
                      We2upText("${strings.customer_name} "
                          "${contactsBox.get(purchase.contactId)?.name}"),
                      We2upText("${strings.location}: "
                          "${businessLocationsBox.get(purchase.locationId)?.name}"),
                    ],
                  ),
                ),
                Column(
                  children: [
                    if (hasReturnItems() && isReturn)
                      Container(
                        padding: EdgeInsets.all(8.sp),
                        decoration: ShapeDecoration(
                          color: Theme.of(context).colorScheme.onSecondary,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(15),
                          ),
                        ),
                        child:
                            const Icon(Icons.settings_backup_restore_outlined),
                      ),
                    Visibility(
                      visible: !isReturn,
                      child: Container(
                        padding: EdgeInsets.all(8.sp),
                        decoration: ShapeDecoration(
                          color: purchase.status == "Offline"
                              ? Theme.of(context).colorScheme.onError
                              : Theme.of(context).colorScheme.tertiaryContainer,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(15),
                          ),
                        ),
                        child: We2upText(
                          purchase.status ?? '??',
                          style: TextStyle(
                            color: purchase.status == "Offline"
                                ? Theme.of(context).colorScheme.error
                                : null,
                          ),
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: isProductsAndContactsReady()
                          ? () async => context.push(
                                pdfViewPage,
                                extra: await generatePdfFunction(
                                  context,
                                  purchase: purchase,
                                  view: true,
                                ),
                              )
                          : null,
                      icon: Icon(
                        Icons.remove_red_eye_sharp,
                        color: Theme.of(context).colorScheme.tertiaryFixed,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            Row(
              children: [
                Expanded(
                  child: We2upText(
                    formatDate(purchase.transactionDate),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                Visibility(
                  visible: !isReturn,
                  child: Row(
                    children: [
                      IconButton(
                        onPressed: canUpdate
                            ? () async => await checkShiftLocationAccess(
                                  context,
                                  () => context.push(
                                    purchaseInvoice,
                                    extra: BillData(purchase: purchase),
                                  ),
                                )
                            : null,
                        icon: Icon(
                          Icons.edit,
                          color: canUpdate
                              ? Colors.green
                              : Theme.of(context).disabledColor,
                        ),
                      ),
                      buildDeleteButton(strings, cubit),
                      IconButton(
                        onPressed: isProductsAndContactsReady()
                            ? () => cubit.generatePdf(context,
                                purchase: purchase, share: true)
                            : null,
                        icon: const Icon(Icons.share, color: Colors.blue),
                      ),
                    ],
                  ),
                ),
                Visibility(
                  visible: isReturn,
                  child: ElevatedButton.icon(
                    onPressed: canReturn
                        ? () => context.push(
                              purchaseInvoice,
                              extra:
                                  BillData(purchase: purchase, pReturn: true),
                            )
                        : null,
                    style: ElevatedButton.styleFrom(
                      shape: RoundedRectangleBorder(
                        side: BorderSide(
                          color: Theme.of(context).colorScheme.outline,
                          width: 1.sp,
                        ),
                        borderRadius: BorderRadius.circular(10.sp),
                      ),
                    ),
                    label: We2upText(strings.button_return
                        // hasReturnItems() ? strings.update : strings.button_return,
                        ),
                    icon: !purchase.offline
                        ? const SizedBox()
                        : const Icon(Icons.sync_problem_outlined),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget buildDeleteButton(AppLocalizations strings, ProductsCubit cubit) {
    bool isPurchaseOnline = !purchase.offline;
    return OfflineBuilder(
      connectivityBuilder: (
        BuildContext context,
        dynamic connectivity,
        Widget child,
      ) {
        bool isOnline;

        if (connectivity is List<ConnectivityResult>) {
          isOnline = !connectivity.contains(ConnectivityResult.none);
        } else if (connectivity is ConnectivityResult) {
          isOnline = connectivity != ConnectivityResult.none;
        } else {
          isOnline = false;
        }

        final canDelete =
            (isOnline && isPurchaseOnline || !isOnline && !isPurchaseOnline) &&
                canDeletePurchase() &&
                !hasReturnItems() &&
                isWithinTransactionEditDays(purchase.transactionDate);

        return IconButton(
          onPressed: canDelete
              ? () async => await checkShiftLocationAccess(
                    context,
                    () => showDialog(
                      context: context,
                      builder: (BuildContext context) {
                        return AlertDialog(
                          title: We2upText(strings.delete_sell_item),
                          content: We2upText(strings.delete_sell_question),
                          actions: [
                            TextButton(
                              onPressed: () => context.pop(),
                              child: We2upText(strings.cancel),
                            ),
                            TextButton(
                              onPressed: () async {
                                await cubit.deletePurchase(
                                  context,
                                  purchaseID: purchase.id ?? purchase.refNo,
                                  offline: purchase.offline,
                                );
                                if (context.mounted) context.pop();
                              },
                              child: We2upText(strings.delete),
                            ),
                          ],
                        );
                      },
                    ),
                  )
              : null,
          icon: Icon(
            Icons.delete,
            color: canDelete
                ? Theme.of(context).colorScheme.error
                : Theme.of(context).disabledColor,
          ),
        );
      },
      child: const SizedBox(),
    );
  }
}
