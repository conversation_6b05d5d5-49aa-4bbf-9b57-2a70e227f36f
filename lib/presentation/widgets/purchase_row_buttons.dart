import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:go_router/go_router.dart';
import 'package:sizer/sizer.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';

import '../../bloc/products/products_cubit.dart';
import '../../data/db/db_manager.dart';
import '../../data/models/bill_of_sale_data.dart';

class PurchaseRowButtons extends StatelessWidget {
  const PurchaseRowButtons({
    super.key,
    required BillData billData,
  }) : _billData = billData;

  final BillData _billData;

  @override
  Widget build(BuildContext context) {
    final String? offlineID = _billData.purchase?.offline == true
        ? _billData.purchase?.refNo
        : _billData.purchase?.id.toString();
    final isEdit = _billData.purchase != null;
    final cubit = ProductsCubit.get(context);
    final strings = AppLocalizations.of(context)!;
    void afterPurchase(bool value) {
      if (value && isEdit) context.pop();
    }

    return BlocConsumer<ProductsCubit, ProductsState>(
      listener: (context, state) {
        if (state is PurchaseLoading) {
          EasyLoading.show(status: strings.loading);
        } else {
          EasyLoading.dismiss();
        }
      },
      builder: (context, state) {
        return Padding(
          padding: EdgeInsets.only(bottom: 10.sp),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              if (canSellOnCredit())
                Expanded(
                  child: ElevatedButton(
                    onPressed: cubit.cartCustomerContact != null &&
                            cubit.cartProducts.isNotEmpty
                        ? () => cubit
                            .sendPurchaseToAPI(
                              purchaseID: _billData.purchase?.id,
                              offlineID: offlineID,
                              credit: true,
                            )
                            .then(afterPurchase)
                        : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).colorScheme.error,
                    ),
                    child: We2upText(
                      strings.credit,
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.surface,
                      ),
                    ),
                  ),
                ),
              if (canSellOnCredit()) SizedBox(width: 8.sp),
              Expanded(
                child: ElevatedButton(
                  onPressed: cubit.cartCustomerContact != null &&
                          cubit.cartProducts.isNotEmpty &&
                          cubit.paymentDetailsList.first.paymentAccount !=
                              null &&
                          ((double.tryParse(cubit.paymentDetailsList.first
                                      .invoiceController.text) ??
                                  0) >
                              0)
                      ? () => cubit
                          .sendPurchaseToAPI(
                            purchaseID: _billData.purchase?.id,
                            offlineID: offlineID,
                          )
                          .then(afterPurchase)
                      : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                  ),
                  child: We2upText(
                    _billData.purchase == null ? strings.partial : strings.edit,
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.surface,
                    ),
                  ),
                ),
              ),
              SizedBox(width: 8.sp),
              Expanded(
                child: ElevatedButton(
                  onPressed: cubit.cartCustomerContact != null &&
                          cubit.cartProducts.isNotEmpty &&
                          cubit.paymentDetailsList.first.paymentAccount != null
                      ? () => cubit
                          .sendPurchaseToAPI(
                            purchaseID: _billData.purchase?.id,
                            offlineID: offlineID,
                            cash: true,
                          )
                          .then(afterPurchase)
                      : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                  ),
                  child: We2upText(
                    strings.cash_sale,
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.surface,
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
