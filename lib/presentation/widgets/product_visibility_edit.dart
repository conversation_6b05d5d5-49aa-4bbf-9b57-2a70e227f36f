import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:sizer/sizer.dart';
import 'package:we2up/data/db/db_manager.dart';
import 'package:we2up/presentation/widgets/product_number_text_field.dart';
import 'package:we2up/presentation/widgets/show_box.dart';
import 'package:we2up/presentation/widgets/stock_number_box.dart';
import 'package:we2up/presentation/widgets/tax_dropdown_button.dart';
import 'package:we2up/presentation/widgets/unit_dropdown_button.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';

import '../../bloc/products/products_cubit.dart';
import '../../data/models/business_settings.dart';
import '../style_constants.dart';
import 'combo_products_dialog.dart';

class ProductVisibilityEdit extends StatelessWidget {
  const ProductVisibilityEdit(
      {super.key, required this.id, this.purchase = false});

  final int id;
  final bool purchase;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ProductsCubit, ProductsState>(
      builder: (context, state) {
        final cubit = ProductsCubit.get(context);
        final strings = AppLocalizations.of(context)!;
        return Visibility(
          visible: cubit.editable(id),
          child: Container(
            padding: EdgeInsets.all(5.sp),
            width: double.infinity,
            child: Column(
              children: [
                Visibility(
                  visible: productsBox.get(id)?.type == "combo" &&
                      canShowComboDetailsInPos(),
                  child: Column(
                    children: [
                      SizedBox(
                        width: double.infinity,
                        child: FilledButton(
                          onPressed: () => showDialog(
                            context: context,
                            builder: (context) => ComboProductsDialog(id: id),
                          ),
                          child: We2upText(strings.show_combo_products),
                        ),
                      ),
                      verticalSpacer,
                    ],
                  ),
                ),
                PriceAndUnitInItem(id: id, purchase: purchase),
                verticalSpacer,
                TaxInItem(id: id, purchase: purchase),
                DiscountInItem(id: id),
                TotalUnitPriceInItem(id: id, purchase: purchase),
                ProductNoteInItem(id: id, purchase: purchase),
                verticalSpacer,
                Row(
                  children: [
                    if (canShowCurrentStockInPos())
                      Expanded(child: InStockInItem(id: id)),
                    DescriptionInItem(id: id),
                  ],
                ),
                PurchasePriceInItem(id: id),
              ],
            ),
          ),
        );
      },
    );
  }
}

class PurchasePriceInItem extends StatelessWidget {
  const PurchasePriceInItem({super.key, required this.id});

  final int id;

  @override
  Widget build(BuildContext context) {
    final cubit = ProductsCubit.get(context);
    final strings = AppLocalizations.of(context)!;
    final showColumn = !showShopProductsGridView() &&
        (defaultTargetPlatform == TargetPlatform.windows || defaultTargetPlatform == TargetPlatform.macOS);

    return Visibility(
      visible: canViewPurchasePrice() || kDebugMode,
      child: Column(
        children: [
          if (!showColumn) verticalSpacer,
          showColumn
              ? We2upText(strings.purchase_price)
              : Row(
                  children: [
                    We2upText("${strings.purchase_price}:"),
                    Gap(3.sp),
                    Expanded(child: _buildShowBox(context, cubit)),
                  ],
                ),
          if (showColumn) _buildShowBox(context, cubit),
        ],
      ),
    );
  }

  Widget _buildShowBox(BuildContext context, ProductsCubit cubit) {
    return ShowBox(
      color: Theme.of(context).colorScheme.surfaceContainerLow,
      text: cubit.getPurchasePrice(id).toStringAsFixed(4),
    );
  }
}

class InStockInItem extends StatelessWidget {
  const InStockInItem({super.key, required this.id});

  final int id;

  @override
  Widget build(BuildContext context) {
    final cubit = ProductsCubit.get(context);
    final strings = AppLocalizations.of(context)!;
    final showColumn = !businessSettings.showShopProductsGridView &&
        (defaultTargetPlatform == TargetPlatform.windows || defaultTargetPlatform == TargetPlatform.macOS);

    return showColumn
        ? Column(
            children: [
              We2upText(strings.in_stock),
              StockNumberBox(inStock: cubit.availableInStock(id)),
            ],
          )
        : Row(
            children: [
              We2upText("${strings.in_stock}:"),
              Gap(8.sp),
              Expanded(
                child: StockNumberBox(inStock: cubit.availableInStock(id)),
              ),
              Gap(8.sp),
            ],
          );
  }
}

class DescriptionInItem extends StatelessWidget {
  const DescriptionInItem({super.key, required this.id});

  final int id;

  @override
  Widget build(BuildContext context) {
    final cubit = ProductsCubit.get(context);
    final strings = AppLocalizations.of(context)!;

    return Visibility(
      visible: cubit.allProducts[id]!.product.productDescription != null,
      child: Expanded(
        child: ElevatedButton(
          onPressed: () => _showDescriptionDialog(context, cubit, strings),
          child: Text(strings.product_description),
        ),
      ),
    );
  }

  void _showDescriptionDialog(
      BuildContext context, ProductsCubit cubit, AppLocalizations strings) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        content: We2upText(
          cubit.allProducts[id]!.product.productDescription ?? strings.nothing,
          textAlign: TextAlign.center,
        ),
      ),
    );
  }
}

class ProductNoteInItem extends StatelessWidget {
  const ProductNoteInItem({
    super.key,
    required this.purchase,
    required this.id,
  });

  final bool purchase;
  final int id;

  @override
  Widget build(BuildContext context) {
    final cubit = ProductsCubit.get(context);
    final strings = AppLocalizations.of(context)!;
    var noteController = cubit.allProducts[id]!.noteController;
    return Visibility(
      visible: !purchase,
      child: Column(
        children: [
          verticalSpacer,
          TextField(
            controller: noteController,
            decoration: InputDecoration(
              hintText: strings.product_note,
              prefixIcon: const Icon(Icons.note_alt_rounded),
              border: myTextFieldBorder,
            ),
          ),
        ],
      ),
    );
  }
}

class TotalUnitPriceInItem extends StatelessWidget {
  const TotalUnitPriceInItem(
      {super.key, required this.id, required this.purchase});

  final int id;
  final bool purchase;

  @override
  Widget build(BuildContext context) {
    final cubit = ProductsCubit.get(context);
    final strings = AppLocalizations.of(context)!;
    final showColumn = !showShopProductsGridView() &&
        (defaultTargetPlatform == TargetPlatform.windows || defaultTargetPlatform == TargetPlatform.macOS);

    return showColumn
        ? Column(children: [
            We2upText(strings.unit_total_price.replaceAll(":", "")),
            _buildShowBox(context, cubit)
          ])
        : Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              We2upText(strings.unit_total_price),
              Gap(8.sp),
              Expanded(child: _buildShowBox(context, cubit)),
            ],
          );
  }

  Widget _buildShowBox(BuildContext context, ProductsCubit cubit) {
    return ShowBox(
      color: Theme.of(context).colorScheme.tertiaryContainer,
      text: cubit.getPriceAfter(id, purchase: purchase).toStringAsFixed(4),
    );
  }
}

class DiscountInItem extends StatelessWidget {
  const DiscountInItem({super.key, required this.id});

  final int id;

  @override
  Widget build(BuildContext context) {
    final cubit = ProductsCubit.get(context);
    final strings = AppLocalizations.of(context)!;
    final discountController = cubit.allProducts[id]!.discountController;
    final showColumn = !showShopProductsGridView() &&
        (defaultTargetPlatform == TargetPlatform.windows || defaultTargetPlatform == TargetPlatform.macOS);

    return Visibility(
      visible: canEditProductDiscountFromPosScreen(),
      child: showColumn
          ? Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    We2upText(strings.discount.replaceAll(":", "")),
                    Gap(8.sp),
                    _buildDiscountTypeWidget(context, cubit),
                  ],
                ),
                _buildProductAmountTextField(
                  context,
                  cubit,
                  discountController,
                  inColumn: true,
                ),
              ],
            )
          : Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    We2upText(strings.discount),
                    _buildProductAmountTextField(
                      context,
                      cubit,
                      discountController,
                    ),
                    _buildDiscountTypeWidget(context, cubit),
                  ],
                ),
                verticalSpacer,
              ],
            ),
    );
  }

  Widget _buildDiscountTypeWidget(BuildContext context, ProductsCubit cubit) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10.sp),
        border: Border.all(
          color: Theme.of(context).colorScheme.inversePrimary,
          width: 2,
        ),
      ),
      padding: EdgeInsetsDirectional.only(start: 5.sp),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Text("%"),
          Checkbox(
            value: cubit.isProductDiscountPercentage(id),
            onChanged: (_) => cubit.switchProductDiscountType(id),
          )
        ],
      ),
    );
  }

  Widget _buildProductAmountTextField(BuildContext context, ProductsCubit cubit,
      TextEditingController discountController,
      {bool inColumn = false}) {
    return ProductAmountTextField(
      controller: discountController,
      hint: cubit.isProductDiscountPercentage(id)
          ? AppLocalizations.of(context)!.percentage_discount
          : AppLocalizations.of(context)!.fixed_discount,
      maxAmount: cubit.actualMaxDiscount(id),
      inColumn: inColumn,
    );
  }
}

class TaxInItem extends StatelessWidget {
  const TaxInItem({
    super.key,
    required this.id,
    required this.purchase,
  });

  final int id;
  final bool purchase;

  @override
  Widget build(BuildContext context) {
    final cubit = ProductsCubit.get(context);
    final strings = AppLocalizations.of(context)!;
    final showColumn = !showShopProductsGridView() &&
        (defaultTargetPlatform == TargetPlatform.windows || defaultTargetPlatform == TargetPlatform.macOS);

    return Visibility(
      visible: productsBox.get(id)?.productTax == null || purchase,
      child: showColumn
          ? SizedBox(
              height: 33.sp,
              width: double.infinity,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  We2upText(strings.tax.replaceAll(":", "")),
                  Expanded(
                    child: TaxDropdownButton(
                      isExpanded: true,
                      taxRate: cubit.getCurrentPTaxRate(id),
                      onChanged: (tax) =>
                          cubit.taxRateChanged(taxRate: tax, id: id),
                    ),
                  ),
                ],
              ),
            )
          : Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      flex: 3,
                      child: We2upText(
                        strings.tax,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    SizedBox(width: 8.sp),
                    Expanded(
                      flex: 7,
                      child: TaxDropdownButton(
                        isExpanded: true,
                        taxRate: cubit.getCurrentPTaxRate(id),
                        onChanged: (tax) =>
                            cubit.taxRateChanged(taxRate: tax, id: id),
                      ),
                    ),
                  ],
                ),
                verticalSpacer,
              ],
            ),
    );
  }
}

class PriceAndUnitInItem extends StatelessWidget {
  const PriceAndUnitInItem({
    super.key,
    required this.id,
    required this.purchase,
  });

  final int id;
  final bool purchase;

  @override
  Widget build(BuildContext context) {
    final cubit = ProductsCubit.get(context);
    final strings = AppLocalizations.of(context)!;
    final showColumn = !showShopProductsGridView() &&
        (defaultTargetPlatform == TargetPlatform.windows || defaultTargetPlatform == TargetPlatform.macOS);

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        if (showColumn)
          Expanded(child: _buildPriceColumn(context, cubit, strings))
        else ...[
          Flexible(child: We2upText(strings.unit_price)),
          _buildPriceGesture(context, cubit, strings),
        ],
        if (showColumn)
          Expanded(child: _buildUnitColumn(strings))
        else ...[
          We2upText("${strings.unit}: "),
          UnitDropdownButton(id: id),
        ],
      ],
    );
  }

  Widget _buildPriceColumn(
      BuildContext context, ProductsCubit cubit, AppLocalizations strings) {
    return Column(
      children: [
        We2upText(strings.unit_price.replaceAll(":", "")),
        _buildPriceGesture(context, cubit, strings),
      ],
    );
  }

  Widget _buildUnitColumn(AppLocalizations strings) {
    return Column(
      children: [
        We2upText(strings.unit),
        UnitDropdownButton(id: id),
      ],
    );
  }

  Widget _buildPriceGesture(
      BuildContext context, ProductsCubit cubit, AppLocalizations strings) {
    return GestureDetector(
      onTap: canEditProductPriceFromPosScreen()
          ? () => _showEditDialog(context)
          : null,
      child: ShowBox(
        text:
            cubit.piecePriceAfter(id, isPurchase: purchase).toStringAsFixed(4),
      ),
    );
  }

  void _showEditDialog(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    ProductsCubit cubit = ProductsCubit.get(context);
    var priceController = cubit.allProducts[id]!.customPiecePriceController;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Center(child: We2upText(strings.edit_piece_price)),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            We2upText(strings.unit_price_without_tax),
            ProductAmountTextField(
              controller: priceController,
              width: 100,
              inColumn: true,
              hint: strings.unit_price_without_tax.replaceFirst(":", ""),
            ),
          ],
        ),
      ),
    );
  }
}
