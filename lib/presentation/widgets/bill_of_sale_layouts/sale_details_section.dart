import 'package:conditional_builder_null_safety/conditional_builder_null_safety.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:sizer/sizer.dart';
import 'package:we2up/data/models/bill_of_sale_data.dart';
import 'package:we2up/presentation/widgets/invoice_products_panel.dart';

import '../../../bloc/products/products_cubit.dart';

import '../../../data/db/db_manager.dart';
import '../../../data/models/business_settings.dart';
import '../../style_constants.dart';
import '../bill_of_sale_single_button.dart';
import '../bill_of_sale_three_sell_buttons.dart';
import '../comm_agents_panel.dart';
import '../contact_details_column.dart';
import '../customer_data_panel.dart';
import '../payment_data_panel.dart';
import '../print_bill.dart';
import '../shipping_and_taxes_panel.dart';
import '../total_return_amount.dart';

class SaleDetailsSection extends StatelessWidget {
  const SaleDetailsSection({
    super.key,
    required BillData billOfSaleData,
  }) : _billOfSaleData = billOfSaleData;

  final BillData _billOfSaleData;

  @override
  Widget build(BuildContext context) {
    final cubit = ProductsCubit.get(context);
    return Column(
      children: [
        CustomerDataIfNoGridView(
          billOfSaleData: _billOfSaleData,
          isPurchase: false,
        ),
        Expanded(
          child: ListView(
            controller: cubit.billScrollController,
            children: [
              ConditionalBuilder(
                condition: _billOfSaleData.returnSell,
                builder: (context) =>
                    ContactDetailsColumn(billOfSaleData: _billOfSaleData),
                fallback: (_) => showShopProductsGridView()
                    ? const CustomerDataPanel()
                    : const SizedBox(),
              ),
              verticalSpacer,
              InvoiceProductsPanel(isReturn: _billOfSaleData.returnSell),
              TotalReturnAmount(billData: _billOfSaleData),
              ConditionalBuilder(
                condition: !_billOfSaleData.returnSell,
                builder: (context) {
                  return Column(
                    children: [
                      verticalSpacer,
                      const ShippingAndTaxesPanel(),
                      const CommAgentsPanel(),
                      if (!_billOfSaleData.quotationPage) verticalSpacer,
                      if (!_billOfSaleData.quotationPage)
                        const PaymentDataPanel(),
                    ],
                  );
                },
                fallback: (_) => const SizedBox(),
              ),
              const Divider(),
              if (_billOfSaleData.purchase == null &&
                  !_billOfSaleData.pReturn &&
                  !_billOfSaleData.returnSell)
                const Column(
                  children: [
                    PrintBillCheckBox(),
                    Divider(),
                  ],
                ),
              if (showShopProductsGridView())
                SellButtonsGroup(billOfSaleData: _billOfSaleData),
            ],
          ),
        ),
      ],
    );
  }
}

class CustomerDataIfNoGridView extends StatelessWidget {
  const CustomerDataIfNoGridView(
      {super.key, required BillData billOfSaleData, required this.isPurchase})
      : _billOfSaleData = billOfSaleData;

  final BillData _billOfSaleData;
  final bool isPurchase;

  @override
  Widget build(BuildContext context) {
    return Visibility(
      visible: !showShopProductsGridView(),
      child: Column(
        children: [
          Row(
            children: [
              const Expanded(child: BusinessLocationInCustomerPanel()),
              Gap(8.sp),
              Expanded(
                child: CustomerNameInCustomerPanel(isPurchase: isPurchase),
              ),
              AddContactButton(canAddContact: canCreateCustomer()),
              Gap(8.sp),
              Expanded(
                child: PriceGroupInCustomerPanel(isPurchase: isPurchase),
              ),
            ],
          ),
          Gap(5.sp),
          Row(
            children: [
              const Expanded(
                child: DueAmountInCustomerPanel(isPurchase: false),
              ),
              Gap(8.sp),
              Expanded(
                child: NetBillInCustomerPanel(isPurchase: isPurchase),
              ),
              Gap(8.sp),
              Expanded(
                child: DueAfterInCustomerPanel(isPurchase: isPurchase),
              ),
            ],
          ),
          SellButtonsGroup(billOfSaleData: _billOfSaleData),
        ],
      ),
    );
  }
}

class SellButtonsGroup extends StatelessWidget {
  const SellButtonsGroup({
    super.key,
    required BillData billOfSaleData,
  }) : _billOfSaleData = billOfSaleData;

  final BillData _billOfSaleData;

  @override
  Widget build(BuildContext context) {
    final String? offlineID = _billOfSaleData.sell?.offline == true
        ? _billOfSaleData.sell?.invoiceNo
        : _billOfSaleData.sell?.id.toString();
    return ConditionalBuilder(
      condition: _billOfSaleData.returnSell,
      builder: (context) {
        return SingleBillOfSaleButton(
          sellID: _billOfSaleData.sell?.id,
          isReturn: true,
          offlineID: offlineID,
        );
      },
      fallback: (context) {
        return ConditionalBuilder(
          condition: _billOfSaleData.quotationPage,
          builder: (context) {
            return SingleBillOfSaleButton(
              sellID: _billOfSaleData.sell?.id,
              offlineID: offlineID,
            );
          },
          fallback: (context) {
            return ThreeSellButtons(
              sellID: _billOfSaleData.sell?.id,
              offlineID: offlineID,
            );
          },
        );
      },
    );
  }
}
