import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:we2up/presentation/widgets/location_range_dropdown_button.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';

import '../../data/models/search_contact.dart';
import '../../utils/we2up_constants.dart';

class LocationSearchContactCard extends StatelessWidget {
  final SearchContact searchContact;

  const LocationSearchContactCard({super.key, required this.searchContact});

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    return Card(
      child: ListTile(
        title: We2upText(searchContact.contactName),
        subtitle: We2upText(
          strings.translateLocationAccuracy(searchContact.locationRange),
        ),
        trailing: IconButton(
          onPressed: () async {
            var whatsappUrl =
                "whatsapp://send?phone=${searchContact.phoneNumber}";
            try {
              launchUrl(Uri.parse(whatsappUrl));
            } catch (e) {
              EasyLoading.showToast(e.toString());
            }
          },
          icon: const Icon(FontAwesomeIcons.whatsapp),
        ),
        leading: IconButton(
          onPressed: () => goToLocation(
            searchContact.userLocation,
            strings.no_location,
          ),
          icon: const Icon(Icons.location_history),
        ),
      ),
    );
  }
}
