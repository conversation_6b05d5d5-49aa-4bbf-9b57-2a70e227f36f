import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/data/models/shipping_company.dart';
import 'package:we2up/presentation/style_constants.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';

import '../../../data/db/db_manager.dart';

class ShippingCompaniesDropdown extends StatelessWidget {
  const ShippingCompaniesDropdown({
    super.key,
    this.isExpanded = false,
    this.padding = 5,
    required this.onChanged,
    required this.company,
  });

  final bool isExpanded;
  final double padding;
  final ValueChanged<ShippingCompany?> onChanged;
  final ShippingCompany? company;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 48.0, // Fixed height to match other input fields
      decoration: dropDownDecoration(context),
      child: DropdownButton<ShippingCompany>(
        value: company,
        padding: EdgeInsets.all(padding.sp),
        isDense: true,
        isExpanded: isExpanded,
        onChanged: onChanged,
        underline: const SizedBox(),
        items: [
          DropdownMenuItem<ShippingCompany>(
            value: null,
            child: Center(
              child: We2upText(AppLocalizations.of(context)!.nothing),
            ),
          ),
          ...shippingCompaniesBox.values.map((ShippingCompany value) {
            return DropdownMenuItem<ShippingCompany>(
              value: value,
              child: Center(child: We2upText(value.name ?? "N/A")),
            );
          }),
        ],
      ),
    );
  }
}
