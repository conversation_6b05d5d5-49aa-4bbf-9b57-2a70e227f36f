import 'package:flutter/material.dart';
import 'package:we2up/data/models/selling_price_group.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';
import '../../data/db/db_manager.dart';

class PriceGroupListDropdown extends StatelessWidget {
  const PriceGroupListDropdown({
    super.key,
    this.isExpanded,
    required this.onChanged,
    required this.value,
    this.filter = false,
  });

  final bool? isExpanded;
  final Function(SellingPriceGroup?) onChanged;
  final SellingPriceGroup? value;
  final bool filter;

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    final permissibleList = sellingPriceGroupsBox.values
        .where((element) => canUseSellingPrice(element.id));
    if (filter) {
      return DropdownButtonFormField<SellingPriceGroup?>(
        value: value,
        decoration: InputDecoration(labelText: strings.price_group_list),
        isExpanded: isExpanded ?? false,
        onChanged: onChanged,
        items: [
          DropdownMenuItem<SellingPriceGroup?>(
            value: null,
            child: We2upText(strings.default_text),
          ),
          ...permissibleList.map((sellingPriceGroup) {
            return DropdownMenuItem<SellingPriceGroup?>(
              value: sellingPriceGroup,
              child: We2upText(sellingPriceGroup.name),
            );
          }),
        ],
      );
    } else {
      return DropdownButton<SellingPriceGroup?>(
        value: value,
        underline: const SizedBox(),
        isExpanded: isExpanded ?? false,
        onChanged: onChanged,
        items: [
          DropdownMenuItem<SellingPriceGroup?>(
            value: null,
            child: Center(child: We2upText(strings.default_text)),
          ),
          ...permissibleList.map((sellingPriceGroup) {
            return DropdownMenuItem<SellingPriceGroup?>(
              value: sellingPriceGroup,
              child: Center(child: We2upText(sellingPriceGroup.name)),
            );
          }),
        ],
      );
    }
  }
}
