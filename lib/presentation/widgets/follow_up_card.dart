import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:go_router/go_router.dart';
import 'package:sizer/sizer.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/bloc/crm/crm_cubit.dart';
import 'package:we2up/data/models/follow_up.dart';
import 'package:we2up/data/db/db_manager.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';
import 'package:we2up/utils/we2up_constants.dart';

import '../../utils/route_constants.dart';
import '../style_constants.dart';

class FollowUpDetailsCard extends StatelessWidget {
  final FollowUp followUp;

  const FollowUpDetailsCard({super.key, required this.followUp});

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    return Card(
      elevation: 2.0,
      child: Padding(
        padding: EdgeInsets.all(8.sp),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: We2upText(
                    '${strings.follow_up_ref}: '
                    '${followUp.offline ? followUp.refNo : followUp.id}',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                ),
                Container(
                  padding: EdgeInsets.all(8.sp),
                  decoration: ShapeDecoration(
                    color: Theme.of(context).colorScheme.tertiaryContainer,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(15),
                    ),
                  ),
                  child: We2upText(followUp.status ?? "N/A"),
                ),
              ],
            ),
            verticalSpacer,
            We2upText('${strings.title}: ${followUp.title}'),
            verticalSpacer,
            Row(
              children: [
                Expanded(
                  child: We2upText(
                    '${strings.agent_name}: '
                    '${contactsBox.get(followUp.contactId)?.name}',
                  ),
                ),
                if (followUp.scheduleType!.isNotEmpty)
                  Container(
                    padding: EdgeInsets.all(8.sp),
                    decoration: ShapeDecoration(
                      color: Theme.of(context).colorScheme.onPrimary,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(15),
                      ),
                    ),
                    child: We2upText(followUp.scheduleType ?? "N/A"),
                  ),
              ],
            ),
            verticalSpacer,
            We2upText(
              '${strings.description}: ${followUp.description}',
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            verticalSpacer,
            Row(
              children: [
                Expanded(
                  child: We2upText(
                    '${strings.notify_before}: ${followUp.notifyBefore}'
                    ' ${followUp.notifyType}',
                    maxLines: 2,
                  ),
                ),
                ElevatedButton(
                  onPressed: () async => await checkShiftLocationAccess(
                    context,
                    () => context.push(
                      addEditFollowUp,
                      extra: (followUp, context.read<CrmCubit>()),
                    ),
                  ),
                  child: We2upText(strings.edit),
                ),
                IconButton(
                  onPressed: () => navigateToWhatsapp(
                    contactsBox.get(followUp.customer?.id)?.mobile,
                  ),
                  icon: const Icon(FontAwesomeIcons.whatsapp),
                ),
                IconButton(
                  onPressed: () => callNumber(
                    contactsBox.get(followUp.customer?.id)?.mobile,
                  ),
                  icon: const Icon(FontAwesomeIcons.phone),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
