import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:sizer/sizer.dart';
import 'package:we2up/bloc/products/products_cubit.dart';
import 'package:we2up/data/models/product.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';

class ShopProductCard extends StatelessWidget {
  const ShopProductCard({
    super.key,
    required this.product,
    this.isPurchase = false,
  });

  final Product product;
  final bool isPurchase;

  @override
  Widget build(BuildContext context) {
    final cubit = ProductsCubit.get(context);
    return GestureDetector(
      onTap: () => cubit.addProductToCart(context, product: product),
      child: Card(
        child: Padding(
          padding: EdgeInsets.all(5.sp),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                child: CachedNetworkImage(
                    imageUrl: product.imageUrl, fit: BoxFit.fitHeight),
              ),
              // const Spacer(),
              We2upText(product.name, maxLines: 3, textAlign: TextAlign.center),
              Gap(2.sp),
              We2upText(
                cubit
                    .piecePrice(product.id, isPurchase: isPurchase)
                    .toStringAsFixed(4),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
