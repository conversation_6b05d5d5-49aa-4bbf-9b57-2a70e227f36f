import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:sizer/sizer.dart';
import 'package:we2up/bloc/payments_report/contact_payment_report_cubit.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/presentation/widgets/date_range_dropdown.dart';
import 'package:we2up/presentation/widgets/user_dropdown_button.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';

import '../../utils/we2up_constants.dart';
import 'contacts_dropdown_button.dart';

class PaymentReportFilterDialog extends StatelessWidget {
  const PaymentReportFilterDialog(this.supplier, {super.key});

  final bool supplier;

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<PaymentsReportCubit>();
    final strings = AppLocalizations.of(context)!;
    final now = DateTime.now();
    final oneMonthAgo = now.subtract(const Duration(days: 30));
    final oneYearAgo = now.subtract(const Duration(days: 365 * 3));
    return AlertDialog(
      title: Center(child: We2upText(strings.filter)),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: We2upText(strings.start_date),
              trailing: const Icon(Icons.calendar_month_rounded),
              onTap: () async {
                final selectedDate = await showDatePicker(
                  context: context,
                  initialDate: cubit.startDate ?? oneMonthAgo,
                  firstDate: oneYearAgo,
                  lastDate: now,
                );

                if (selectedDate != null) {
                  cubit.updateStartDate(selectedDate);
                  cubit.updateDateRange(DateRange.customRange);
                }
              },
              onLongPress: () => cubit.updateStartDate(null),
              subtitle: BlocBuilder<PaymentsReportCubit, PaymentsReportState>(
                buildWhen: (_, c) => c is StartDateUpdated,
                builder: (context, state) {
                  return We2upText(
                    cubit.startDate != null
                        ? formatDate(cubit.startDate!, filter: true)
                        : strings.nothing,
                  );
                },
              ),
            ),
            ListTile(
              title: We2upText(strings.end_date),
              trailing: const Icon(Icons.calendar_month_rounded),
              onTap: () async {
                final selectedDate = await showDatePicker(
                  context: context,
                  initialDate: cubit.endDate ?? now,
                  firstDate: oneYearAgo,
                  lastDate: now,
                );

                if (selectedDate != null) {
                  cubit.updateEndDate(selectedDate);
                  cubit.updateDateRange(DateRange.customRange);
                }
              },
              onLongPress: () => cubit.updateEndDate(null),
              subtitle: BlocBuilder<PaymentsReportCubit, PaymentsReportState>(
                buildWhen: (_, c) => c is EndDateUpdated,
                builder: (context, state) {
                  return We2upText(
                    cubit.endDate != null
                        ? formatDate(cubit.endDate!, filter: true)
                        : strings.nothing,
                  );
                },
              ),
            ),
            Row(
              children: [
                Expanded(child: Divider(endIndent: 8.sp)),
                We2upText(strings.user_name),
                Expanded(child: Divider(indent: 8.sp)),
              ],
            ),
            Gap(8.sp),
            BlocBuilder<PaymentsReportCubit, PaymentsReportState>(
              buildWhen: (_, c) => c is FilterUserUpdated,
              builder: (context, state) {
                return UsersDropdownButton(
                  user: cubit.user,
                  onChanged: cubit.updateFilterUser,
                  controller: cubit.filterUserController,
                );
              },
            ),
            Gap(8.sp),
            Row(
              children: [
                Expanded(child: Divider(endIndent: 8.sp)),
                We2upText(
                  (supplier ? strings.supplier_name : strings.customer_name)
                      .replaceFirst(":", ""),
                ),
                Expanded(child: Divider(indent: 8.sp)),
              ],
            ),
            Gap(8.sp),
            BlocBuilder<PaymentsReportCubit, PaymentsReportState>(
              buildWhen: (_, c) => c is FilterContactUpdated,
              builder: (context, state) {
                return ContactsDropdownButton(
                  filterType: supplier ? "supplier" : 'customer',
                  onChanged: cubit.updateFilterContact,
                  contact: cubit.contact,
                  isInLocation: true,
                );
              },
            ),
          ],
        ),
      ),
      actionsAlignment: MainAxisAlignment.spaceAround,
      actions: [
        ElevatedButton(
          child: We2upText(strings.reset),
          onPressed: () {
            showDialog(
              context: context,
              builder: (context) {
                return AlertDialog(
                  actionsAlignment: MainAxisAlignment.spaceAround,
                  title: Center(child: We2upText(strings.clear_all_filters)),
                  actions: [
                    ElevatedButton(
                      child: We2upText(strings.clear),
                      onPressed: () {
                        cubit.reset();
                        context.pop();
                        context.pop();
                      },
                    ),
                    ElevatedButton(
                      child: We2upText(strings.cancel),
                      onPressed: () => context.pop(),
                    ),
                  ],
                );
              },
            );
          },
        ),
        ElevatedButton(
          child: We2upText(strings.apply),
          onPressed: () => context.pop(),
        ),
      ],
    );
  }
}
