import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';

class StockNumberBox extends StatelessWidget {
  const StockNumberBox({
    super.key,
    required this.inStock,
  });

  final num inStock;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 48.0, // Fixed height to match other input fields
      padding: EdgeInsetsDirectional.only(
        bottom: 1.sp,
        top: 1.sp,
        start: 1.sp,
        end: 3.sp,
      ),
      constraints: BoxConstraints(maxWidth: 35.w),
      decoration: ShapeDecoration(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(5.sp),
          side: BorderSide(
            width: 1.sp,
            color: Theme.of(context).colorScheme.secondary,
          ),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.home_outlined,
            color: Theme.of(context).colorScheme.secondary,
            size: 18.0, // Fixed icon size for consistency
          ),
          SizedBox(width: 3.sp),
          Flexible(
            child: We2upText(
              inStock.toStringAsFixed(2),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }
}
