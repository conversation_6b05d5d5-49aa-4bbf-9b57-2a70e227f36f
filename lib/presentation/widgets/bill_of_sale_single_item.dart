import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:sizer/sizer.dart';
import 'package:we2up/bloc/products/products_cubit.dart';
import 'package:we2up/data/models/product.dart';
import 'package:we2up/presentation/widgets/product_number_text_field.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/presentation/widgets/product_visibility_edit.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';

import '../../data/db/db_manager.dart';
import '../../data/models/business_settings.dart';
import '../../data/models/purchase.dart';
import '../../data/models/sell.dart';

class BillOfSaleSingleItem extends StatelessWidget {
  const BillOfSaleSingleItem({
    super.key,
    required this.product,
    this.isReturn = false,
    this.isPurchase = false,
  });

  final Product product;
  final bool isReturn;
  final bool isPurchase;

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    final cubit = ProductsCubit.get(context);
    final int id = product.id;

    double unitPrice = double.parse(
      cubit.currentBillData?.sell?.sellLines!
              .where((sl) => sl.productId == id)
              .firstOrNull
              ?.unitPriceIncTax ??
          cubit.currentBillData?.purchase?.purchaseLines!
              .where((pr) => pr.productId == id)
              .firstOrNull
              ?.purchasePriceIncTax ??
          "0",
    );

    double totalPrice =
        (double.tryParse(cubit.allProducts[id]!.quantityController.text) ?? 0) *
            unitPrice;
    return BlocBuilder<ProductsCubit, ProductsState>(
      builder: (context, state) {
        if (!showShopProductsGridView() &&
            (defaultTargetPlatform == TargetPlatform.windows || defaultTargetPlatform == TargetPlatform.macOS)) {
          return SizedBox(
            width: MediaQuery.of(context).size.width,
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          ProductName(product: product, flex: 2),
                          SizedBox(width: 8.0),
                          SizedBox(
                            width: 80.0,
                            child: ProductQuantityTextField(
                              id: id,
                              isPurchase: isPurchase,
                              isReturn: isReturn,
                            ),
                          ),
                          SizedBox(width: 8.0),
                          Expanded(
                            flex: 2,
                            child: PriceAndUnitInItem(
                              id: id,
                              purchase: isPurchase,
                            ),
                          ),
                          SizedBox(width: 8.0),
                          SizedBox(
                            width: 100.0,
                            child: TotalUnitPriceInItem(
                              id: id,
                              purchase: isPurchase,
                            ),
                          ),
                        ],
                      ),
                      Row(
                        children: [
                          Expanded(flex: 2, child: DiscountInItem(id: id)),
                          SizedBox(width: 8.0),
                          Expanded(
                            flex: 2,
                            child: TaxInItem(id: id, purchase: isPurchase),
                          ),
                          SizedBox(width: 8.0),
                          SizedBox(
                            width: 80.0,
                            child: DescriptionInItem(id: id),
                          ),
                          SizedBox(width: 8.0),
                          Expanded(
                            flex: 2,
                            child: ProductNoteInItem(
                              id: id,
                              purchase: isPurchase,
                            ),
                          ),
                          if (canShowCurrentStockInPos()) SizedBox(width: 8.0),
                          if (canShowCurrentStockInPos())
                            SizedBox(
                              width: 120.0,
                              child: InStockInItem(id: id),
                            ),
                          if (canViewPurchasePrice() || kDebugMode) SizedBox(width: 8.0),
                          if (canViewPurchasePrice() || kDebugMode)
                            SizedBox(
                              width: 100.0,
                              child: PurchasePriceInItem(id: id),
                            ),
                        ],
                      ),
                    ],
                  ),
                ),
                SizedBox(width: 50, child: DeleteButton(id: id)),
              ],
            ),
          );
        }
        return Column(
          children: [
            Row(
              children: [
                ProductName(product: product),
                SizedBox(width: 8.sp),
                ProductQuantityTextField(
                  id: id,
                  isPurchase: isPurchase,
                  isReturn: isReturn,
                ),
                Visibility(
                  visible: !isReturn,
                  child: IconButton(
                    icon: const Icon(Icons.edit, color: Colors.green),
                    onPressed: () => cubit.toggleEdit(id),
                  ),
                ),
                DeleteButton(id: id),
              ],
            ),
            ProductVisibilityEdit(id: id, purchase: isPurchase),
            Visibility(
              visible: isReturn,
              child: Column(
                children: [
                  Gap(5.sp),
                  Row(
                    children: [
                      We2upText(strings.unit_price),
                      Gap(3.sp),
                      We2upText(unitPrice.toStringAsFixed(4)),
                      const Spacer(),
                      We2upText(strings.unit_total_price),
                      Gap(3.sp),
                      We2upText(totalPrice.toStringAsFixed(4)),
                      const Spacer(),
                    ],
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }
}

class ProductName extends StatelessWidget {
  const ProductName({
    super.key,
    required this.product,
    this.flex = 2,
  });

  final Product product;
  final int flex;

  @override
  Widget build(BuildContext context) {
    return Expanded(
      flex: flex,
      child: Padding(
        padding: EdgeInsets.only(right: 8.0),
        child: Text(
          product.name,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
          style: Theme.of(context).textTheme.bodyMedium,
        ),
      ),
    );
  }
}

class ProductQuantityTextField extends StatelessWidget {
  const ProductQuantityTextField({
    super.key,
    required this.id,
    required this.isPurchase,
    required this.isReturn,
  });

  final int id;
  final bool isPurchase;
  final bool isReturn;

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    final cubit = ProductsCubit.get(context);
    num? maxForReturn;

    if (cubit.currentBillData != null) {
      List<SellLine>? sellLines = cubit.currentBillData!.sell?.sellLines;
      List<PurchaseLine>? purchaseLines =
          cubit.currentBillData!.purchase?.purchaseLines;
      if (sellLines != null) {
        for (SellLine? sellLine in sellLines) {
          if (sellLine?.productId == id) {
            maxForReturn = sellLine?.quantity;
            break;
          }
        }
      }
      if (maxForReturn == null && purchaseLines != null) {
        for (PurchaseLine? purchaseLine in purchaseLines) {
          if (purchaseLine?.productId == id) {
            maxForReturn = purchaseLine?.quantity;
            break;
          }
        }
      }
    }
    return ProductAmountTextField(
      controller: cubit.allProducts[id]!.quantityController,
      hint: strings.amount.replaceFirst(":", ""),
      maxAmount: isPurchase
          ? null
          : !isReturn
              ? cubit.maxInInvoice(id) ?? double.infinity
              : maxForReturn,
      noInternalExpanded: true, // Prevent Expanded usage when wrapped in SizedBox
    );
  }
}

class DeleteButton extends StatelessWidget {
  const DeleteButton({super.key, required this.id});

  final int id;

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    final cubit = ProductsCubit.get(context);
    return IconButton(
      icon: Icon(
        Icons.delete,
        color: Theme.of(context).colorScheme.error,
      ),
      onPressed: () {
        showDialog(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              title: We2upText(strings.delete_item),
              content: We2upText(strings.delete_question),
              actions: [
                TextButton(
                  onPressed: () => context.pop(),
                  child: We2upText(strings.cancel),
                ),
                TextButton(
                  onPressed: () {
                    cubit.deleteFromCart(id);
                    context.pop();
                  },
                  child: We2upText(strings.delete),
                ),
              ],
            );
          },
        );
      },
    );
  }
}
