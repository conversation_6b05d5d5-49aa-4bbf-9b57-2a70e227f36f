import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sizer/sizer.dart';
import 'package:we2up/presentation/widgets/product_number_text_field.dart';
import 'package:we2up/presentation/widgets/product_visibility_edit.dart';
import 'package:we2up/presentation/widgets/show_box.dart';
import 'package:we2up/presentation/widgets/stock_number_box.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/presentation/widgets/unit_dropdown_button.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';

import '../../bloc/products/products_cubit.dart';
import '../../data/db/db_manager.dart';
import '../../data/models/business_settings.dart';
import '../style_constants.dart';
import 'product_image_column.dart';

class ProductCard extends StatelessWidget {
  const ProductCard({super.key, required this.id});

  final int id;

  @override
  Widget build(BuildContext context) {
    ProductsCubit cubit = ProductsCubit.get(context);
    final strings = AppLocalizations.of(context)!;
    var numberController = cubit.allProducts[id]!.quantityController;
    return BlocBuilder<ProductsCubit, ProductsState>(
      builder: (context, state) {
        return Card(
          shape: cubit.isCardActive(id) ? activeCardBorder(context) : null,
          child: Column(
            children: [
              Container(
                padding: EdgeInsets.all(5.sp),
                width: double.infinity,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ProductImageColumn(
                      imageUrl: cubit.getImageUrl(id),
                      inCart: cubit.cartProducts.contains(id),
                      onTogglePressed: () => cubit.toggleEdit(id),
                      onDeletePressed: () => cubit.deleteFromCart(id),
                    ),
                    Expanded(
                      child: Padding(
                        padding: EdgeInsets.symmetric(horizontal: 5.sp),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            We2upText(
                              cubit.getProductName(id),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                              style: Theme.of(context).textTheme.labelLarge,
                            ),
                            verticalSpacer,
                            if (canShowCurrentStockInPos())
                              StockNumberBox(
                                inStock: cubit.availableInStock(id),
                              ),
                            if (canShowCurrentStockInPos()) verticalSpacer,
                            We2upText(
                              "${cubit.piecePriceAfter(id).toStringAsFixed(4)} "
                              "${currencySymbol()}",
                              style:
                                  const TextStyle(fontWeight: FontWeight.bold),
                            ),
                            if (canViewPurchasePrice()) // TODO: check purchase price here.
                              Column(
                                children: [
                                  verticalSpacer,
                                  We2upText(
                                    "${strings.purchase_price}: "
                                    "${cubit.getPurchasePrice(id).toStringAsFixed(4)} "
                                    "${currencySymbol()}",
                                  ),
                                ],
                              ),
                            verticalSpacer,
                            Row(
                              children: [
                                IconButton(
                                  style: IconButton.styleFrom(
                                    side: const BorderSide(color: Colors.green),
                                  ),
                                  onPressed: () => cubit.incrementNumber(id),
                                  icon: const Icon(Icons.plus_one),
                                  color: Colors.green,
                                ),
                                SizedBox(width: 5.w),
                                IconButton(
                                  style: IconButton.styleFrom(
                                    side: BorderSide(
                                      color:
                                          Theme.of(context).colorScheme.error,
                                    ),
                                  ),
                                  onPressed: () => cubit.decrementNumber(id),
                                  icon: const Icon(Icons.exposure_minus_1),
                                  color: Theme.of(context).colorScheme.error,
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 5.sp),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          ProductAmountTextField(
                            inColumn: true,
                            // enabled: cubit.allowOverSelling(id),
                            controller: numberController,
                            hint: strings.amount.replaceFirst(":", ""),
                            maxAmount: cubit.maxInInvoice(id),
                          ),
                          verticalSpacer,
                          ShowBox(
                            text: cubit.piecePriceAfter(id).toStringAsFixed(4),
                          ),
                          verticalSpacer,
                          Visibility(
                            visible: !cubit.editable(id),
                            child: UnitDropdownButton(id: id),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              ProductVisibilityEdit(id: id),
            ],
          ),
        );
      },
    );
  }
}
