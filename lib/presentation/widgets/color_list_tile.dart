import 'package:flex_color_picker/flex_color_picker.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hive_flutter/adapters.dart';
import 'package:sizer/sizer.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';

import '../../data/db/db_manager.dart';

class ColorListTile extends StatelessWidget {
  const ColorListTile({
    super.key,
    required this.title,
    required this.message,
    required this.lightKey,
    required this.darkKey,
    required this.defaultColor,
  });

  final String title;
  final String message;
  final String lightKey;
  final String darkKey;
  final Color defaultColor;

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    return ListTile(
      title: We2upText(title),
      trailing: ValueListenableBuilder(
        valueListenable: Hive.box(settingsBox).listenable(),
        builder: (context, box, _) {
          final bool isDark = box.get("isDark", defaultValue: false);
          final int? lightColor = box.get(lightKey);
          final int? darkColor = box.get(darkKey);
          Color getColor() {
            if (isDark && darkColor != null) {
              return Color(darkColor);
            } else if (lightColor != null) {
              return Color(lightColor);
            } else {
              return defaultColor;
            }
          }

          int? selectedColor;
          return ColorIndicator(
            borderRadius: 6.sp,
            hasBorder: true,
            color: getColor(),
            onSelect: () {
              showDialog(
                context: context,
                builder: (BuildContext context) {
                  return AlertDialog(
                    title: We2upText(message),
                    content: SingleChildScrollView(
                      child: ColorPicker(
                        onColorChanged: (Color color) {
                          selectedColor = color.value;
                        },
                        showColorName: true,
                        color: getColor(),
                        enableTonalPalette: true,
                        padding: EdgeInsets.all(8.sp),
                        enableShadesSelection: true,
                      ),
                    ),
                    actions: <Widget>[
                      TextButton(
                        onPressed: () => context.pop(),
                        child: We2upText(strings.cancel),
                      ),
                      TextButton(
                        onPressed: () {
                          isDark
                              ? box.put(darkKey, selectedColor)
                              : box.put(lightKey, selectedColor);
                          context.pop();
                        },
                        child: We2upText(strings.apply),
                      ),
                    ],
                  );
                },
              );
            },
          );
        },
      ),
    );
  }
}
