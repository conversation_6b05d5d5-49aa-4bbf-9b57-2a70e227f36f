import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:idkit_inputformatters/idkit_inputformatters.dart';
import 'package:sizer/sizer.dart';

import 'max_value_input_formatter.dart';

class ProductAmountTextField extends StatelessWidget {
  const ProductAmountTextField({
    super.key,
    required this.controller,
    this.hint = "Number",
    this.width = 25,
    this.maxAmount,
    this.inColumn = false,
    this.autoFocus = false,
    this.onChanged,
    this.enabled = true,
    this.focusNode,
    this.noInternalExpanded = false,
    this.fieldKey,
  });

  final TextEditingController controller;
  final String? hint;
  final double? width;
  final num? maxAmount;
  final bool inColumn;
  final bool enabled;
  final bool autoFocus;
  final void Function(String?)? onChanged;
  final FocusNode? focusNode;
  final bool noInternalExpanded;
  final GlobalKey<FormFieldState>? fieldKey;

  @override
  Widget build(BuildContext context) {
    return inColumn
        ? SizedBox(
            width: width!.w,
            child: buildTextFormField(context),
          )
        : noInternalExpanded
            ? SizedBox(
                width: 200,
                child: buildTextFormField(context),
              )
            : Expanded(
                flex: 1,
                child: buildTextFormField(context),
              );
  }

  TextFormField buildTextFormField(BuildContext context) {
    if(autoFocus){
      controller.selection = TextSelection(
        baseOffset: 0,
        extentOffset: controller.text.length,
      );
    }
    return TextFormField(
      key: fieldKey,
      autofocus: autoFocus,
      focusNode: focusNode,
      inputFormatters: [
        FilteringTextInputFormatter.allow(
          RegExp(r'^\d*\.?\d{0,4}?$'),
        ),
        if (maxAmount != null) MaxValueInputFormatter(maxAmount),
        if (maxAmount != null)
          IDKitNumeralTextInputFormatter.max(
            maxValue: maxAmount,
            maxDecimalDigit: 4,
            decimalPoint: true,
          ),
      ],
      controller: controller..addListener(() {
        if(onChanged != null){
          onChanged!(controller.text);
        }
      }),
      onTap: () {
        controller.selection = TextSelection(
          baseOffset: 0,
          extentOffset: controller.text.length,
        );
      },
      enabled: enabled,
      textAlign: TextAlign.center,
      keyboardType: TextInputType.number,
      decoration: InputDecoration(
        filled: true,
        fillColor: Theme.of(context).colorScheme.secondaryContainer,
        contentPadding: EdgeInsets.only(bottom: 2.sp),
        hintText: hint,
        enabledBorder: OutlineInputBorder(
          borderSide: BorderSide(
            width: 2,
            color: Colors.grey,
          ),
          borderRadius: BorderRadius.circular(15.sp),
        ),
        border: OutlineInputBorder(
          borderSide: BorderSide(
            width: 2,
            color: Colors.grey,
          ),
          borderRadius: BorderRadius.circular(15.sp),
        ),
      ),
    );
  }
}