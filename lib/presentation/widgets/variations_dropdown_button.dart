import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:sizer/sizer.dart';
import 'package:we2up/bloc/products/products_cubit.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';

import '../../data/models/product.dart';
import '../../data/db/db_manager.dart';
import '../style_constants.dart';

class VariationsDropdownButton extends StatelessWidget {
  const VariationsDropdownButton(this.product, {super.key});

  final Product product;

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: product.productVariations.length,
      itemBuilder: (context, index) {
        final productVariations = product.productVariations;
        final cubit = ProductsCubit.get(context);
        bool enableVariation(Variation variation) {
          return variation.variationLocationDetails
              .firstWhere((vl) => vl.locationId == cubit.selectedLocation.id)
              .qtyAvailable > 0 || loginData.allowOverSelling;
        }

        return Row(
          children: [
            Expanded(child: We2upText("${productVariations[index].name} :")),
            Gap(8.sp),
            Expanded(
              flex: 4,
              child: Container(
                decoration: dropDownDecoration(context),
                child: BlocBuilder<ProductsCubit, ProductsState>(
                  builder: (context, state) {
                    return DropdownButton<Variation?>(
                      value: cubit.getVariation(product.id),
                      padding: EdgeInsets.all(5.sp),
                      isDense: true,
                      isExpanded: true,
                      onChanged: (v) => cubit.updateVariation(product.id, v),
                      underline: const SizedBox(),
                      items: [
                        ...productVariations[index]
                            .variations!
                            .where((variation) =>
                                variation.variationLocationDetails.any((vld) =>
                                    vld.locationId ==
                                    cubit.selectedLocation.id))
                            .map(
                              (variation) => DropdownMenuItem(
                                value: variation,
                                enabled: enableVariation(variation),
                                child: Center(child: We2upText(variation.name)),
                              ),
                            ),
                      ],
                    );
                  },
                ),
              ),
            ),
            Gap(8.sp),
            Expanded(
              child: BlocBuilder<ProductsCubit, ProductsState>(
                builder: (context, state) {
                  Variation? selectedVariation = cubit.getVariation(product.id);
                  String imageUrl =
                      selectedVariation?.media[0].displayUrl ?? "";
                  return ClipOval(
                    child: CachedNetworkImage(
                      imageUrl: imageUrl,
                      width: 18.w,
                      height: 18.w,
                      fit: BoxFit.fitWidth,
                      placeholder: (_, __) => const CircularProgressIndicator(),
                      errorWidget: (_, __, e) => const Icon(Icons.error),
                    ),
                  );
                },
              ),
            ),
          ],
        );
      },
    );
  }
}
