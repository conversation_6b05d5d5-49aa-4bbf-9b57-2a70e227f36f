import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:sizer/sizer.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/bloc/expenses/expenses_cubit.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';
import 'package:we2up/utils/we2up_constants.dart';

import '../../data/db/db_manager.dart';
import '../../data/models/business_settings.dart';
import '../../data/models/expense.dart';
import '../../utils/route_constants.dart';
import '../style_constants.dart';

class ExpenseDetailsCard extends StatelessWidget {
  final Expense expense;

  const ExpenseDetailsCard({
    super.key,
    required this.expense,
  });

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    final username = expense.expenseFor?['first_name'];
    final contactName = contactsBox.get(expense.contactId)?.name;

    return Card(
      elevation: 2.0,
      child: Padding(
        padding: EdgeInsets.all(10.sp),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: We2upText(
                    '${strings.ref_no}: ${expense.refNo}',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                ),
                if (expense.isRefund == 1)
                  Icon(Icons.restart_alt, color: Colors.green, size: 25.sp),
              ],
            ),
            verticalSpacer,
            We2upText('${strings.transaction_date}: '
                '${formatDate(expense.transactionDate)}'),
            verticalSpacer,
            We2upText(
              '${strings.total_expense_amount} ${expense.finalTotal} '
              '${currencySymbol()}',
            ),
            if (username != null) verticalSpacer,
            if (username != null) We2upText("${strings.user_name}: $username"),
            if (contactName != null) verticalSpacer,
            if (contactName != null) We2upText("${strings.contact}: $contactName"),
            verticalSpacer,
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                ElevatedButton(
                  onPressed: canEditExpense()
                      ? () async => await checkShiftLocationAccess(
                            context,
                            () => context.push(
                              addExpensePage,
                              extra: (context.read<ExpensesCubit>(), expense),
                            ),
                          )
                      : null,
                  child: We2upText(strings.edit),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
