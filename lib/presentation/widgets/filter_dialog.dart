import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:we2up/bloc/products/products_cubit.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/presentation/widgets/price_group_list_dropdown_button.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';

import '../../data/db/db_manager.dart';
import '../../data/models/product.dart';

class FilterDialog extends StatelessWidget {
  const FilterDialog({super.key, this.isPurchase = false});

  final bool isPurchase;

  @override
  Widget build(BuildContext context) {
    final cubit = ProductsCubit.get(context);
    final strings = AppLocalizations.of(context)!;
    return BlocBuilder<ProductsCubit, ProductsState>(
      builder: (context, state) {
        return AlertDialog(
          title: Center(child: We2upText(strings.filter)),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                OutlinedButton.icon(
                  onPressed: () => cubit.changeFilterAlphabeticalOrder(),
                  icon: cubit.getSortIcon(),
                  label: We2upText(strings.alphabetical_order),
                ),
                const Divider(),
                // Visibility(
                //   visible: !isPurchase,
                //   child: Column(
                //     children: [
                //       CheckboxListTile(
                //         title: We2upText(strings.in_stock),
                //         value: cubit.filterInStock,
                //         onChanged: (value) => cubit.changeFilterInStock(value!),
                //       ),
                //       const Divider(),
                //     ],
                //   ),
                // ),
                Visibility(
                  visible: sellingPriceGroupsBox.values
                      .any((e) => canUseSellingPrice(e.id)),
                  child: PriceGroupListDropdown(
                    onChanged: cubit.changeCartPriceGroup,
                    value: cubit.cartPriceGroup,
                    filter: true,
                    isExpanded: true,
                  ),
                ),
                const Divider(),
                DropdownButtonFormField<ProductCategory>(
                  value: cubit.filterCategory,
                  onChanged: cubit.changeFilterCategory,
                  items: [
                    DropdownMenuItem(
                      value: null,
                      child: We2upText(strings.all_categories),
                    ),
                    ...categoriesBox.values.map((category) {
                      return DropdownMenuItem(
                        value: category,
                        child: We2upText(category.name),
                      );
                    }),
                  ],
                  decoration: InputDecoration(labelText: strings.category),
                ),
                const Divider(),
                DropdownButtonFormField<ProductCategory>(
                  value: cubit.filterCategory != null
                      ? cubit.filterSubCategory
                      : null,
                  onChanged: (v) => cubit.changeFilterSubCategory(v),
                  items: [
                    if (cubit.filterCategory != null)
                      DropdownMenuItem(
                        value: null,
                        child: We2upText(strings.all_sub_categories),
                      ),
                    if (cubit.filterCategory != null)
                      ...cubit.filterCategory!.subCategories.map((category) {
                        return DropdownMenuItem(
                          value: category,
                          child: We2upText(category.name),
                        );
                      }),
                  ],
                  decoration: InputDecoration(labelText: strings.sub_category),
                ),
                const Divider(),
                DropdownButtonFormField<Brand>(
                  value: cubit.filterBrand,
                  onChanged: cubit.changeFilterBrand,
                  items: [
                    // Add a null option
                    DropdownMenuItem(
                      value: null,
                      child: We2upText(strings.all_brands),
                    ),
                    ...brandsBox.values.map((brand) {
                      return DropdownMenuItem(
                        value: brand,
                        child: We2upText(brand.name ?? ""),
                      );
                    }),
                  ],
                  decoration: InputDecoration(labelText: strings.brand),
                ),
              ],
            ),
          ),
          actionsAlignment: MainAxisAlignment.spaceAround,
          actions: [
            ElevatedButton(
              child: We2upText(strings.reset),
              onPressed: () {
                showDialog(
                  context: context,
                  builder: (context) {
                    return AlertDialog(
                      actionsAlignment: MainAxisAlignment.spaceAround,
                      title: Center(child: We2upText(strings.clear_all_filters)),
                      actions: [
                        ElevatedButton(
                          child: We2upText(strings.clear),
                          onPressed: () {
                            cubit.resetSellsFilters();
                            context.pop();
                            context.pop();
                          },
                        ),
                        ElevatedButton(
                          child: We2upText(strings.cancel),
                          onPressed: () => context.pop(),
                        ),
                      ],
                    );
                  },
                );
              },
            ),
            ElevatedButton(
              child: We2upText(strings.apply),
              onPressed: () => context.pop(),
            ),
          ],
        );
      },
    );
  }
}
