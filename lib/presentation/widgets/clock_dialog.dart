import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:get_ip_address/get_ip_address.dart';
import 'package:go_router/go_router.dart';
import 'package:we2up/bloc/landing/landing_cubit.dart';
import 'package:we2up/data/models/clock.dart';
import 'package:we2up/data/repository/api_repo.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';

import '../../data/db/db_manager.dart';

class ClockDialog extends StatefulWidget {
  const ClockDialog({super.key});

  @override
  State<ClockDialog> createState() => _ClockDialogState();
}

class _ClockDialogState extends State<ClockDialog> {
  final TextEditingController noteController = TextEditingController();
  late final ApiRepository _apiRepository;

  @override
  void initState() {
    _apiRepository = ApiRepository.get();
    super.initState();
  }

  @override
  void dispose() {
    noteController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    return AlertDialog(
      title: We2upText(loginData.clockedIn ? strings.clockOut : strings.clockIn),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          TextField(
            controller: noteController,
            decoration: InputDecoration(
              labelText: loginData.clockedIn
                  ? strings.clockOutNote
                  : strings.clockInNote,
            ),
          ),
        ],
      ),
      actions: <Widget>[
        TextButton(
          onPressed: () => context.pop(),
          child: We2upText(strings.cancel),
        ),
        TextButton(
          onPressed: () async {
            EasyLoading.show(status: strings.loading);
            String note = noteController.text;
            final ipAddress = await IpAddress().getIpAddress();
            final locationInfo = await _apiRepository.getCurrentLocation();
            _apiRepository
                .clock(
              clock: Clock(
                clockNote: note,
                ipAddress: ipAddress.toString(),
                locationInfo: locationInfo,
              ),
              clockIn: !loginData.clockedIn,
            )
                .then((ClockResponse response) {
              if (response.success) {
                loginData.clockedIn = !loginData.clockedIn;
                loginData.clockedInTime = DateTime.now();
                loginData.save();
                EasyLoading.showSuccess(response.message);
              } else if (!response.success) {
                if (response.message == "تم تسجيل الدخول بالفعل") {
                  loginData.clockedIn = true;
                  loginData.clockedInTime = DateTime.now();
                  loginData.save();
                  EasyLoading.showSuccess(response.message);
                } else if (response.message == "لم يتم تسجيل الدخول") {
                  loginData.clockedIn = false;
                  loginData.save();
                  EasyLoading.showError(response.message);
                } else {
                  EasyLoading.showError(response.message);
                }
              }
              LandingCubit.get(context).updateLandingScreen();
              context.pop();
            });
          },
          child: We2upText(loginData.clockedIn ? strings.end : strings.open),
        ),
      ],
    );
  }
}
