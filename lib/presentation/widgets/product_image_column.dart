import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:sizer/sizer.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';

class ProductImageColumn extends StatelessWidget {
  const ProductImageColumn({
    super.key,
    required this.imageUrl,
    required this.inCart,
    this.onTogglePressed,
    this.onDeletePressed,
  });

  final String imageUrl;
  final bool inCart;
  final void Function()? onTogglePressed;
  final void Function()? onDeletePressed;

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    return Column(
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(5.sp),
          child: CachedNetworkImage(
            imageUrl: imageUrl,
            errorWidget: (_, __, ___) => const Icon(Icons.error),
            width: 18.w,
            height: 18.w,
            fit: BoxFit.fill,
          ),
        ),
        SizedBox(height: 5.5.w),
        Row(
          children: [
            if (inCart)
              IconButton(
                onPressed: (){
                  showDialog(
                    context: context,
                    builder: (BuildContext context) {
                      return AlertDialog(
                        title: We2upText(strings.delete_item),
                        content: We2upText(strings.delete_question),
                        actions: [
                          TextButton(
                            onPressed: () => context.pop(),
                            child: We2upText(strings.cancel),
                          ),
                          TextButton(
                            onPressed: (){
                              onDeletePressed!();
                              context.pop();
                            },
                            child: We2upText(strings.delete),
                          ),
                        ],
                      );
                    },
                  );
                },
                icon: const Icon(Icons.delete),
                color: Theme.of(context).colorScheme.error,
              ),
            if (inCart)
              IconButton(
                onPressed: onTogglePressed,
                icon: const Icon(Icons.edit),
                color: Colors.green,
              ),
          ],
        ),
      ],
    );
  }
}
