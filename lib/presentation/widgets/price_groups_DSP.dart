import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:sizer/sizer.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';

import '../../bloc/single_product/single_product_cubit.dart';
import 'branch_opening_stock_card.dart';

class PriceGroupDSP extends StatelessWidget {
  const PriceGroupDSP({super.key});

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    return BlocBuilder<SingleProductCubit, SingleProductState>(
      buildWhen: (_, current) => current is SingleProductPriceGroupsListChanged,
      builder: (context, state) {
        final cubit = context.read<SingleProductCubit>();
        return Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: cubit.selectedPriceGroups
              .map(
                (e) => Card(
              child: Padding(
                padding: EdgeInsets.all(4.sp),
                child: Column(
                  children: [
                    Padding(
                      padding: EdgeInsets.symmetric(vertical: 8.sp),
                      child: We2upText(
                        e.priceGroup.name,
                        style: Theme.of(context).textTheme.bodyLarge,
                      ),
                    ),
                    NewProductNumberTextField(
                      controller: e.sellPrice,
                      labelText: strings.sell_price,
                      icon: Icons.attach_money,
                    ),
                  ],
                ),
              ),
            ),
          )
              .toList(),
        );
      },
    );
  }
}