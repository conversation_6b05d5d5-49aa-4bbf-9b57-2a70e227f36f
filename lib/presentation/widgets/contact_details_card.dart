import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:go_router/go_router.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:we2up/bloc/contacts/contacts_cubit.dart';
import 'package:we2up/data/db/db_manager.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';
import 'package:we2up/utils/route_constants.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/utils/we2up_constants.dart';

import '../../data/models/contact.dart';

class ContactDetailsCard extends StatelessWidget {
  final Contact contact;
  final bool isLocation;

  const ContactDetailsCard({
    super.key,
    required this.contact,
    this.isLocation = false,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      child: ListTile(
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            if (contact.supplierBusinessName != null)
              We2upText(
                contact.supplierBusinessName ?? "N/A",
                overflow: TextOverflow.ellipsis,
              ),
            We2upText(
              contact.name ?? "N/A",
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
        subtitle: Center(
          child: We2upText(contact.mobile ?? 'N/A', isPhone: true),
        ),
        trailing: IconButton(
          onPressed: () => goToLocation(
            contact.locationInfo,
            AppLocalizations.of(context)!.no_location,
          ),
          icon: const Icon(FontAwesomeIcons.mapLocationDot),
        ),
        leading: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              onPressed: contact.mobile != null && contact.mobile!.isNotEmpty
                  ? () => _initiateCall(contact.mobile!)
                  : null,
              icon: const Icon(Icons.call),
            ),
            IconButton(
              onPressed: contact.mobile != null && contact.mobile!.isNotEmpty
                  ? () => navigateToWhatsapp(contact.mobile!)
                  : null,
              icon: const Icon(FontAwesomeIcons.whatsapp),
            ),
          ],
        ),
        titleAlignment: ListTileTitleAlignment.center,
        onLongPress: !isLocation &&
                (contact.type == "supplier" && canUpdateSupplier() ||
                    contact.type == "customer" && canUpdateCustomer()) &&
                loginData.isContactsReady
            ? () async => await checkShiftLocationAccess(
                  context,
                  () => context.push(
                    addEditContact,
                    extra: (contact, context.read<ContactsCubit>(), false),
                  ),
                )
            : null,
      ),
    );
  }

  Future<void> _initiateCall(String phoneNumber) async {
    final uri = Uri.parse('tel:$phoneNumber');
    launchUrl(uri);
  }
}
