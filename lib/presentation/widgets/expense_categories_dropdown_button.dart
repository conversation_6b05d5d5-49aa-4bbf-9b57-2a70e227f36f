import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/data/db/db_manager.dart';
import 'package:we2up/data/models/expense_category.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';

import '../style_constants.dart';

class ExpenseCategoriesDropdownButton extends StatelessWidget {
  const ExpenseCategoriesDropdownButton({
    super.key,
    this.isExpanded = false,
    this.padding = 5,
    required this.onChanged,
    required this.category,
  });

  final bool isExpanded;
  final double padding;
  final ValueChanged<ExpenseCategory?> onChanged;
  final ExpenseCategory? category;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: dropDownDecoration(context),
      child: DropdownButton<ExpenseCategory?>(
        value: category,
        padding: EdgeInsets.all(padding.sp),
        isDense: true,
        isExpanded: isExpanded,
        onChanged: onChanged,
        underline: const SizedBox(),
        items: [
          DropdownMenuItem<ExpenseCategory?>(
            value: null,
            child: Center(
              child: We2upText(AppLocalizations.of(context)!.nothing),
            ),
          ),
          ...expenseCategoriesBox.values
              .map<DropdownMenuItem<ExpenseCategory>>((value) {
            return DropdownMenuItem<ExpenseCategory>(
              value: value,
              child: Center(child: We2upText(value.name)),
            );
          }),
        ],
      ),
    );
  }
}
