import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';

import '../../utils/we2up_constants.dart';
import '../style_constants.dart';

class PaymentMethodDropdownButton extends StatelessWidget {
  const PaymentMethodDropdownButton({
    super.key,
    this.isExpanded = false,
    this.padding = 5,
    required this.onChanged,
    required this.paymentMethod,
  });

  final bool isExpanded;
  final double padding;
  final ValueChanged<String?> onChanged;
  final String paymentMethod;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 48.0, // Fixed height to match other input fields
      decoration: dropDownDecoration(context),
      child: DropdownButton<String>(
        value: paymentMethod,
        padding: EdgeInsets.all(padding.sp),
        isDense: true,
        isExpanded: isExpanded,
        onChanged: onChanged,
        underline: const SizedBox(),
        items: paymentMethodTranslations.entries
            .map<DropdownMenuItem<String>>(
              (MapEntry<String, String> entry) {
            final translatedValue = entry.value;
            return DropdownMenuItem<String>(
              value: entry.key,
              child: Center(child: We2upText(translatedValue)),
            );
          },
        )
            .toList(),
      ),
    );
  }
}