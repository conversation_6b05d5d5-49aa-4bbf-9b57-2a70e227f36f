import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:go_router/go_router.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:print_bluetooth_thermal/print_bluetooth_thermal.dart';
import 'package:we2up/data/models/sell.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';

import '../../bloc/contact_payment/payment_pdf.dart';
import '../../bloc/products/pdf_function.dart';
import '../../data/db/db_manager.dart';
import '../../data/models/business_settings.dart';
import '../../data/models/payment.dart';
import '../../data/models/purchase.dart';
import '../../data/models/sell_return.dart';
import '../../utils/thermal_printer_manager.dart';
import '../../utils/we2up_constants.dart';

class PrinterAlertDialog extends StatefulWidget {
  const PrinterAlertDialog({
    super.key,
    this.sell,
    this.sReturn,
    this.purchase,
    this.payment,
    this.oldDue,
    this.isSetting = false,
  });

  final Sell? sell;
  final SellReturn? sReturn;
  final Purchase? purchase;
  final Payment? payment;
  final double? oldDue;
  final bool isSetting;

  @override
  State<PrinterAlertDialog> createState() => _PrinterAlertDialogState();
}

class _PrinterAlertDialogState extends State<PrinterAlertDialog> {
  // TODO: it's not showing enable bt message when printing sell.
  late final bool isSetting;
  bool bEnabled = false;
  List<BluetoothInfo> listResult = [];
  BluetoothInfo? selectedDevice;

  @override
  void initState() {
    isSetting = widget.isSetting;
    initBluetoothList();
    super.initState();
  }

  Future<void> initBluetoothList() async {
    try {
      bEnabled = await PrintBluetoothThermal.bluetoothEnabled;
      listResult = await PrintBluetoothThermal.pairedBluetooths;
      if (listResult.isNotEmpty && !widget.isSetting) {
        selectedDevice = listResult
            .where((b) => b.macAdress == getBluetoothDeviceAddress())
            .firstOrNull;
      }
      setState(() {});
    } catch (e) {
      debugPrint("Bluetooth list error: ${e.toString()}");
    }
  }

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    return AlertDialog(
      actionsAlignment: MainAxisAlignment.center,
      title: Center(
        child: Text(isSetting ? strings.bluetooth_device : strings.printer),
      ),
      content: buildBluetoothWidget(
        bEnabled: bEnabled,
        isSetting: isSetting,
        listResult: listResult,
        selectedDevice: selectedDevice,
        strings: strings,
      ),
      actions: [
        if (bEnabled && !isSetting)
          ElevatedButton(
            onPressed: selectedDevice != null
                ? () async {
                    if (await PrintBluetoothThermal.connectionStatus ||
                        await PrintBluetoothThermal.connect(
                            macPrinterAddress: selectedDevice!.macAdress)) {
                      await printReceipt(selectedDevice!.macAdress);
                    } else {
                      EasyLoading.showError("غير قادر على الاتصال بالطابعة");
                    }
                  }
                : null,
            child: Text(strings.print),
          ),
        if (bEnabled && isSetting)
          ElevatedButton(
            onPressed: selectedDevice != null
                ? () async {
                    final newSettings = businessSettings.copyWith(
                      bluetoothDeviceName: selectedDevice!.name,
                      bluetoothDeviceAddress: selectedDevice!.macAdress,
                    );
                    await businessDetailsBox.put(
                      businessSettingsDataKey,
                      newSettings,
                    );
                    if (context.mounted) context.pop();
                  }
                : null,
            child: Text(strings.choose),
          ),
        if (!bEnabled)
          ElevatedButton(
            onPressed: () async {
              await Permission.bluetooth.request();
              await Permission.bluetoothScan.request();
              await Permission.bluetoothConnect.request();
              if (await PrintBluetoothThermal.isPermissionBluetoothGranted) {
                await initBluetoothList();
              }
            },
            child: Text(strings.enable_bluetooth),
          ),
      ],
    );
  }

  Future<void> printReceipt(String address) async {
    try {
      // Note: You need to invoke the function
      if (widget.payment != null && widget.oldDue != null) {
        await generatePaymentPdf(
          context,
          widget.payment!,
          widget.oldDue!,
        );
      } else {
        await generatePdfFunction(
          context,
          sell: widget.sell,
          purchase: widget.purchase,
          sReturn: widget.sReturn,
          address: address,
        );
      }
    } catch (e) {
      debugPrint("ERROR!!!!!!!!!!!!");
      debugPrint(e.toString());
    }
  }

  Widget buildBluetoothWidget({
    required bool bEnabled,
    required bool isSetting,
    required List<BluetoothInfo> listResult,
    required BluetoothInfo? selectedDevice,
    required AppLocalizations strings,
  }) {
    if (bEnabled && isSetting) {
      return SizedBox(
        width: double.maxFinite,
        child: listResult.isNotEmpty
            ? ListView.builder(
                shrinkWrap: true,
                itemCount: listResult.length,
                itemBuilder: (context, index) {
                  return ListTile(
                    leading: const Icon(Icons.bluetooth),
                    selected: this.selectedDevice == listResult[index],
                    selectedTileColor: Theme.of(context).colorScheme.onTertiary,
                    title: Text(
                      listResult[index].name,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    subtitle: Text(listResult[index].macAdress),
                    onTap: () {
                      setState(() => this.selectedDevice = listResult[index]);
                    },
                  );
                },
              )
            : Text(strings.no_bluetooth_devices),
      );
    } else if (bEnabled && !isSetting) {
      return listResult.isNotEmpty
          ? Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ListTile(
                  leading: const Icon(Icons.print),
                  selected: true,
                  selectedTileColor: Theme.of(context).colorScheme.onTertiary,
                  title: Text(
                    selectedDevice?.name ?? strings.nothing,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  subtitle: Text(selectedDevice?.macAdress ?? strings.nothing),
                ),
                const Divider(),
                ListTile(
                  title: We2upText(strings.paper_size),
                  trailing: DropdownButton<String>(
                    value: getCurrentPaperSize(),
                    items: We2UpPaperSize.values
                        .map(
                          (e) => DropdownMenuItem<String>(
                            value: e.name,
                            child: Center(child: Text(e.name)),
                          ),
                        )
                        .toList(),
                    onChanged: (p) async {
                      final newSettings = businessSettings.copyWith(
                        currentPaperSize: p!,
                      );
                      await businessDetailsBox.put(
                        businessSettingsDataKey,
                        newSettings,
                      );
                      setState(() {});
                    },
                  ),
                ),
              ],
            )
          : Text(strings.nothing); // empty list
    } else {
      return Text(strings.enable_bluetooth_message);
    }
  }
}
