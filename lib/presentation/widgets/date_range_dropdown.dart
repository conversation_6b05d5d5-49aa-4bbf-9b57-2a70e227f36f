import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';

import '../../data/db/db_manager.dart';
import '../style_constants.dart';

enum DateRange {
  shift,
  today,
  yesterday,
  last7days,
  last30days,
  thisMonth,
  lastMonth,
  thisMonthLastYear,
  thisYear,
  lastYear,
  customRange,
}

// Filter only the desired values
List<DateRange> allowedDateRanges = [
  DateRange.shift,
  DateRange.today,
  DateRange.yesterday,
  DateRange.last7days,
  DateRange.customRange,
];

class DateRangeDropdown extends StatelessWidget {
  const DateRangeDropdown({
    super.key,
    this.isExpanded,
    this.padding,
    required this.changeStartDate,
    required this.changeEndDate,
    required this.range,
    required this.onChanged,
    this.isLocation = false,
  });

  final bool? isExpanded;
  final double? padding;
  final Function(DateTime?) changeStartDate;
  final Function(DateTime?) changeEndDate;
  final Function(DateRange) onChanged;
  final DateRange range;
  final bool isLocation;

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    return Container(
      decoration: dropDownDecoration(context),
      child: DropdownButton<DateRange>(
        value: range,
        padding: EdgeInsets.all(padding != null ? padding!.sp : 5.sp),
        isDense: true,
        isExpanded: isExpanded ?? true,
        onChanged: (DateRange? selectedRange) {
          DateTime? start;
          DateTime? end;

          if (selectedRange != null) {
            onChanged(selectedRange);
            DateTime now = DateTime.now();
            switch (selectedRange) {
              case DateRange.shift:
                start = loginData.cashRegisteredTime;
                end = now;
                break;
              case DateRange.today:
                start = now;
                end = now;
                break;
              case DateRange.yesterday:
                start = now.subtract(const Duration(days: 1));
                end = now.subtract(const Duration(days: 1));
                break;
              case DateRange.last7days:
                start = now.subtract(const Duration(days: 6));
                end = now;
                break;
              case DateRange.last30days:
                start = now.subtract(const Duration(days: 29));
                end = now;
                break;
              case DateRange.thisMonth:
                start = DateTime(now.year, now.month, 1);
                end = now;
                break;
              case DateRange.lastMonth:
                start = DateTime(now.year, now.month - 1, 1);
                end = DateTime(now.year, now.month, 0);
                break;
              case DateRange.thisMonthLastYear:
                start = DateTime(now.year - 1, now.month, 1);
                end = DateTime(now.year - 1, now.month + 1, 0);
                break;
              case DateRange.thisYear:
                start = DateTime(now.year, 1, 1);
                end = now;
                break;
              case DateRange.lastYear:
                start = DateTime(now.year - 1, 1, 1);
                end = DateTime(now.year - 1, 12, 31);
                break;
              case DateRange.customRange:
                // Nothing
                break;
            }
          }

          changeStartDate(start);
          changeEndDate(end);
        },
        underline: const SizedBox(),
        items: (isLocation ? DateRange.values : allowedDateRanges)
            .map<DropdownMenuItem<DateRange>>(
              (DateRange value) => DropdownMenuItem<DateRange>(
                value: value,
                child: Center(
                  child: We2upText(strings.translateDateRange(value)),
                ),
              ),
            )
            .toList(),
      ),
    );
  }
}

extension AppLocalizationsExtension on AppLocalizations {
  String translateDateRange(DateRange range) {
    switch (range) {
      case DateRange.shift:
        return shift;
      case DateRange.today:
        return today;
      case DateRange.yesterday:
        return yesterday;
      case DateRange.last7days:
        return last7days;
      case DateRange.last30days:
        return last30days;
      case DateRange.thisMonth:
        return thisMonth;
      case DateRange.lastMonth:
        return lastMonth;
      case DateRange.thisMonthLastYear:
        return thisMonthLastYear;
      case DateRange.thisYear:
        return thisYear;
      case DateRange.lastYear:
        return lastYear;
      case DateRange.customRange:
        return customRange;
    }
  }
}
