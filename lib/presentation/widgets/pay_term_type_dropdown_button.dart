import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';

import '../style_constants.dart';

class PayTermTypeDropdownButton extends StatelessWidget {
  const PayTermTypeDropdownButton({
    super.key,
    this.isExpanded,
    this.padding,
    required this.onChanged,
    required this.payTermType,
  });

  final bool? isExpanded;
  final double? padding;
  final ValueChanged<String?> onChanged;
  final String? payTermType;

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    final constantValues = [null, 'days', 'months'];

    return Container(
      decoration: dropDownDecoration(context),
      child: DropdownButton<String?>(
        value: payTermType,
        padding: EdgeInsets.all(padding != null ? padding!.sp : 5.sp),
        isDense: true,
        isExpanded: isExpanded ?? true,
        onChanged: onChanged,
        underline: const SizedBox(),
        items: constantValues
            .map<DropdownMenuItem<String?>>(
              (String? value) => DropdownMenuItem<String?>(
                value: value,
                child: Center(
                  child: We2upText(
                    strings.translatePayTermType(value),
                  ),
                ),
              ),
            )
            .toList(),
      ),
    );
  }
}

extension AppLocalizationsExtension on AppLocalizations {
  String translatePayTermType(String? payTerm) {
    switch (payTerm) {
      case 'days':
        return daily;
      case 'months':
        return monthly;
      case null:
        return not_selected;
      default:
        return '';
    }
  }
}
