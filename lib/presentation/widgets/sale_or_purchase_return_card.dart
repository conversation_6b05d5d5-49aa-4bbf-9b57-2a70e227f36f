import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/bloc/products/products_cubit.dart';
import 'package:we2up/data/models/purchase_return.dart';
import 'package:we2up/data/db/db_manager.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';
import 'package:we2up/utils/we2up_constants.dart';

import '../../data/models/business_settings.dart';
import '../../data/models/sell_return.dart';
import '../style_constants.dart';

class SaleOrPurchaseReturnCard extends StatelessWidget {
  final SellReturn? sReturn;
  final PurchaseReturn? pReturn;

  const SaleOrPurchaseReturnCard({super.key, this.sReturn, this.pReturn});

  String getInvoiceOrRefNo() {
    if (sReturn != null) {
      return sReturn!.returnParentSell?.invoiceNo ?? "N/A";
    } else if (pReturn != null) {
      return pReturn!.returnParentPurchase?.refNo ?? "N/A";
    } else {
      return "N/A";
    }
  }

  String getTransactionDateOrNA() {
    if (sReturn != null) {
      return formatDate(sReturn!.transactionDate);
    } else if (pReturn != null) {
      return formatDate(pReturn!.transactionDate);
    } else {
      return "N/A";
    }
  }

  String getContactNameOrNA() {
    if (sReturn != null) {
      final contact = contactsBox.get(sReturn!.contactId);
      return contact?.name ?? "N/A";
    } else if (pReturn != null) {
      final contact = contactsBox.get(pReturn!.contactId!);
      return contact?.name ?? "N/A";
    } else {
      return "N/A";
    }
  }

  String getFinalTotalOrNA() {
    if (sReturn != null) {
      return sReturn!.finalTotal.toString();
    } else if (pReturn != null) {
      return pReturn!.finalTotal.toString();
    } else {
      return "N/A";
    }
  }

  int getSellOrPurchaseLinesCount() {
    if (sReturn != null) {
      return sReturn!.returnParentSell?.sellLines
              ?.where(
                  (line) => double.parse(line.quantityReturned ?? "0.0") != 0)
              .length ??
          0;
    } else if (pReturn != null) {
      return pReturn!.returnParentPurchase?.purchaseLines
              ?.where(
                  (line) => double.parse(line.quantityReturned ?? "0.0") != 0)
              .length ??
          0;
    } else {
      return 0;
    }
  }

  List<dynamic>? getFilteredLines() {
    if (sReturn != null) {
      return sReturn!.returnParentSell?.sellLines
          ?.where((line) => double.parse(line.quantityReturned ?? "0.0") != 0)
          .toList();
    } else if (pReturn != null) {
      return pReturn!.returnParentPurchase?.purchaseLines
          ?.where((line) => double.parse(line.quantityReturned ?? "0.0") != 0)
          .toList();
    } else {
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;

    return Card(
      elevation: 2.0,
      child: Padding(
        padding: EdgeInsets.all(10.sp),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: We2upText(
                    '${strings.ref_no}: ${getInvoiceOrRefNo()}',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                ),
                if ((sReturn?.offline ?? false) || (pReturn?.offline ?? false))
                  Container(
                    padding: EdgeInsets.all(8.sp),
                    decoration: ShapeDecoration(
                      color: Colors.white70,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(15),
                        side: BorderSide(
                          color: Theme.of(context).colorScheme.error,
                        ),
                      ),
                    ),
                    child: We2upText(strings.offline),
                  ),
              ],
            ),
            verticalSpacer,
            We2upText(
                '${strings.transaction_date}: ${getTransactionDateOrNA()}'),
            verticalSpacer,
            We2upText('${strings.customer_name} ${getContactNameOrNA()}'),
            verticalSpacer,
            We2upText("${strings.invoice_products}:"),
            verticalSpacer,
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(5.sp),
              decoration: showBoxDecoration(context),
              child: ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: getSellOrPurchaseLinesCount(),
                itemBuilder: (context, index) {
                  return ListTile(
                    title: We2upText(
                      "${productsBox.get(getFilteredLines()?[index].productId)?.name} ",
                      overflow: TextOverflow.ellipsis,
                      maxLines: 2,
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    trailing: We2upText(
                      getFilteredLines()?[index].quantityReturned.toString() ??
                          "N/A",
                    ),
                  );
                },
              ),
            ),
            verticalSpacer,
            Row(
              children: [
                We2upText('${strings.total} ${getFinalTotalOrNA()} '
                    '${currencySymbol()}'),
                const Spacer(),
                if (sReturn != null)
                  IconButton(
                    onPressed: isProductsAndContactsReady()
                        ? () => ProductsCubit.get(context)
                            .generatePdf(context, sReturn: sReturn)
                        : null,
                    icon: Icon(
                      Icons.print,
                      color: Theme.of(context).colorScheme.secondary,
                    ),
                  ),
                if (sReturn != null)
                  IconButton(
                    onPressed: isProductsAndContactsReady()
                        ? () => ProductsCubit.get(context)
                            .generatePdf(context, sReturn: sReturn, share: true)
                        : null,
                    icon: const Icon(Icons.share, color: Colors.blue),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
