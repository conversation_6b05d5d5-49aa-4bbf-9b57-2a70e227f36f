import 'package:expandable/expandable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:sizer/sizer.dart';
import 'package:we2up/presentation/widgets/bill_service_dropdown.dart';
import 'package:we2up/presentation/widgets/product_number_text_field.dart';
import 'package:we2up/presentation/widgets/shipment_status_dropdown_button.dart';
import 'package:we2up/presentation/widgets/shipping_companies_dropdown_button.dart';
import 'package:we2up/presentation/widgets/show_box.dart';
import 'package:we2up/presentation/widgets/tax_dropdown_button.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';

import '../../bloc/products/products_cubit.dart';
import '../../data/db/db_manager.dart';
import '../style_constants.dart';

class ShippingAndTaxesPanel extends StatelessWidget {
  const ShippingAndTaxesPanel({super.key, this.isPurchase = false});

  final bool isPurchase;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ProductsCubit, ProductsState>(
      builder: (context, state) {
        final strings = AppLocalizations.of(context)!;
        final cubit = ProductsCubit.get(context);
        final cartShippingController = cubit.cartShippingFeesController;
        cartShippingController.addListener(cubit.applyPaymentChanged);
        return Card(
          key: cubit.shippingPanelKey,
          child: ExpandablePanel(
            controller: cubit.shippingAndTaxesPanelController,
            theme: const ExpandableThemeData(hasIcon: false),
            header: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10.sp),
                color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
              ),
              padding: EdgeInsets.all(10.sp),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const SizedBox(),
                  We2upText(
                    strings.shipping_and_taxes_data,
                    style: Theme.of(context).textTheme.labelLarge,
                  ),
                  const Icon(Icons.keyboard_arrow_down),
                ],
              ),
            ),
            expanded: const SizedBox(),
            collapsed: Padding(
              padding: EdgeInsets.all(4.sp),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        flex: 3,
                        child: We2upText(
                          strings.total_bill,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      SizedBox(width: 8.0),
                      Expanded(
                        flex: 7,
                        child: ShowBox(
                          text: cubit
                              .getCartTotalBeforeTax(purchase: isPurchase)
                              .toString(),
                          width: 45,
                        ),
                      ),
                    ],
                  ),
                  verticalSpacer,
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        flex: 3,
                        child: We2upText(
                          strings.tax,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      SizedBox(width: 8.0),
                      Expanded(
                        flex: 7,
                        child: TaxDropdownButton(
                          isExpanded: true,
                          taxRate: cubit.cartTaxRate,
                          onChanged: cubit.cartTaxRateChanged,
                        ),
                      ),
                    ],
                  ),
                  if (!isPurchase) verticalSpacer,
                  if (!isPurchase)
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          flex: 3,
                          child: We2upText(
                            "${strings.total_weight} :",
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        SizedBox(width: 8.0),
                        Expanded(
                          flex: 7,
                          child: ShowBox(
                            text: cubit.saleWeight().toStringAsFixed(4),
                            color: Theme.of(context).colorScheme.primaryContainer,
                            width: 45,
                          ),
                        ),
                      ],
                    ),
                  if (!isPurchase) verticalSpacer,
                  if (!isPurchase)
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          flex: 3,
                          child: We2upText(
                            "${strings.service}: ",
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        SizedBox(width: 8.0),
                        Expanded(
                          flex: 7,
                          child: BillServiceDropdown(
                            isExpanded: true,
                            onChanged: cubit.updateCartServiceModel,
                            service: cubit.cartServiceModel,
                          ),
                        ),
                      ],
                    ),
                  if (!isPurchase && cubit.cartServiceModel != null)
                    verticalSpacer,
                  if (!isPurchase && cubit.cartServiceModel != null)
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          flex: 3,
                          child: We2upText(
                            "${strings.service_price} :",
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        SizedBox(width: 8.0),
                        Expanded(
                          flex: 7,
                          child: GestureDetector(
                            onTap: () => showCustomServicePriceDialog(context),
                            child: ShowBox(
                              text: cubit
                                  .calculatePackingCharge()
                                  .toStringAsFixed(4),
                              color:
                                  Theme.of(context).colorScheme.primaryContainer,
                              width: 45,
                            ),
                          ),
                        ),
                      ],
                    ),
                  if (!isPurchase) verticalSpacer,
                  if (!isPurchase)
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          flex: 3,
                          child: We2upText(
                            strings.shipping_fee,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        SizedBox(width: 8.0),
                        Expanded(
                          flex: 7,
                          child: ProductAmountTextField(
                            controller: cartShippingController,
                            hint: strings.shipping_fee.replaceFirst(":", ""),
                            noInternalExpanded: true, // Prevent internal Expanded usage
                          ),
                        ),
                      ],
                    ),
                  if (!isPurchase) verticalSpacer,
                  if (!isPurchase)
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          flex: 3,
                          child: We2upText(
                            "${strings.shipping_companies}:",
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        SizedBox(width: 8.0),
                        Expanded(
                          flex: 7,
                          child: ShippingCompaniesDropdown(
                            isExpanded: true,
                            onChanged: cubit.changeShippingCompany,
                            company: cubit.cartShippingCompany,
                          ),
                        ),
                      ],
                    ),
                  if (!isPurchase) verticalSpacer,
                  if (!isPurchase)
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          flex: 3,
                          child: We2upText(
                            strings.shipment_status,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        SizedBox(width: 8.0),
                        Expanded(
                          flex: 7,
                          child: ShipmentStatusDropdownButton(
                            onChanged: cubit.changeShippingStatus,
                            status: cubit.cartShippingStatus,
                            isExpanded: true,
                          ),
                        ),
                      ],
                    ),
                  Column(
                    children: [
                      verticalSpacer,
                      TextField(
                        controller: cubit.cartShippingDetailsController,
                        decoration: InputDecoration(
                          hintText: strings.shipping_details,
                          prefixIcon: const Icon(Icons.note_alt_rounded),
                          border: myTextFieldBorder,
                        ),
                      ),
                      if (!isPurchase) verticalSpacer,
                      if (!isPurchase)
                        TextField(
                          controller: cubit.cartShippingAddressController,
                          decoration: InputDecoration(
                            hintText: strings.shipping_address,
                            prefixIcon: const Icon(Icons.local_shipping),
                            border: myTextFieldBorder,
                          ),
                        ),
                    ],
                  ),
                  if (canAccessDiscounts()) verticalSpacer,
                  if (canAccessDiscounts())
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        We2upText(AppLocalizations.of(context)!.discount),
                        SizedBox(width: 8.sp),
                        ProductAmountTextField(
                          controller: cubit.cartDiscountController,
                          onChanged: (d) => cubit.applyCartDiscount(),
                          hint: cubit.cartDiscountTypePercentage
                              ? strings.percentage_discount
                              : strings.fixed_discount,
                          maxAmount: cubit.cartDiscountTypePercentage
                              ? 100
                              : cubit.getCartTotalBeforeTax(
                                  purchase: isPurchase),
                          focusNode: cubit.discountFieldFocusNode,
                          fieldKey: cubit.discountFieldKey, // Pass the GlobalKey
                        ),
                        SizedBox(width: 8.sp),
                        Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10.sp),
                            border: Border.all(
                              color:
                                  Theme.of(context).colorScheme.inversePrimary,
                              width: 2,
                            ),
                          ),
                          padding: EdgeInsetsDirectional.only(start: 5.sp),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Text("%"),
                              Checkbox(
                                value: cubit.cartDiscountTypePercentage,
                                onChanged: (_) =>
                                    cubit.switchCartDiscountType(),
                              )
                            ],
                          ),
                        ),
                      ],
                    ),
                  verticalSpacer,
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(child: We2upText(strings.net_bill)),
                      ShowBox(
                        text: cubit
                            .getTotalPaymentFees(purchase: isPurchase)
                            .toStringAsFixed(4),
                        color: Theme.of(context).colorScheme.tertiaryContainer,
                        width: 45,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  void showCustomServicePriceDialog(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    ProductsCubit cubit = ProductsCubit.get(context);
    var customServiceController = cubit.customServicePriceController;

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Center(
            child: We2upText(
              strings.custom_service_price.replaceFirst(":", ""),
            ),
          ),
          content: ProductAmountTextField(
            controller: customServiceController,
            width: 100,
            inColumn: true,
            hint: strings.custom_service_price.replaceFirst(":", ""),
            onChanged: (_) => cubit.updateProductsCubit(),
          ),
        );
      },
    );
  }
}
