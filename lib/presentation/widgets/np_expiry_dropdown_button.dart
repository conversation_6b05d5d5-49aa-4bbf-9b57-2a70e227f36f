import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/data/models/new_product_to_api.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';

import '../style_constants.dart';

class NPExpiryPeriodDropdownButton extends StatelessWidget {
  const NPExpiryPeriodDropdownButton({
    super.key,
    this.isExpanded = false,
    this.padding = 5,
    required this.onChanged,
    required this.expiryPeriod,
  });

  final bool isExpanded;
  final double padding;
  final ValueChanged<ExpiryPeriodType?> onChanged;
  final ExpiryPeriodType? expiryPeriod;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: dropDownDecoration(context),
      child: DropdownButton<ExpiryPeriodType>(
        value: expiryPeriod,
        padding: EdgeInsets.all(padding.sp),
        isDense: true,
        isExpanded: isExpanded,
        onChanged: onChanged,
        underline: const SizedBox(),
        items: [
          DropdownMenuItem<ExpiryPeriodType>(
            value: null,
            child: Center(
              child: We2upText(AppLocalizations.of(context)!.nothing),
            ),
          ),
          ...ExpiryPeriodType.values.map<DropdownMenuItem<ExpiryPeriodType>>(
              (ExpiryPeriodType value) {
            return DropdownMenuItem<ExpiryPeriodType>(
              value: value,
              child: Center(child: We2upText(value.name)),
            );
          }),
        ],
      ),
    );
  }
}

extension AppLocalizationsExtension on AppLocalizations {
  String translateExpiryPeriodType(ExpiryPeriodType expiryPeriodType) {
    switch (expiryPeriodType) {
      case ExpiryPeriodType.years:
        return years;
      case ExpiryPeriodType.months:
        return months;
      case ExpiryPeriodType.days:
        return days;
    }
  }
}
