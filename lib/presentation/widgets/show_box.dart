import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';

import '../style_constants.dart';

class ShowBox extends StatelessWidget {
  const ShowBox({
    super.key,
    required this.text,
    this.width,
    this.color,
  });

  final String text;
  final Color? color;
  final double? width;

  @override
  Widget build(BuildContext context) {
    double? parsedValue = double.tryParse(text);
    String formattedText = parsedValue?.toStringAsFixed(3) ?? text;
    return Container(
      width: width == null ? 22.5.w : width!.w,
      height: 48.0, // Fixed height to match standard input field height
      alignment: Alignment.center,
      decoration: showBoxDecoration(context, color: color),
      child: We2upText(
        formattedText,
        overflow: TextOverflow.ellipsis,
        maxLines: 1,
      ),
    );
  }
}
