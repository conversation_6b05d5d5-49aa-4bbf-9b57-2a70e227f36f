import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';

import '../../data/db/db_manager.dart';
import '../../data/models/payment_account.dart';
import '../style_constants.dart';

class PaymentAccountDropdownButton extends StatelessWidget {
  const PaymentAccountDropdownButton({
    super.key,
    this.isExpanded = false,
    this.padding = 5,
    required this.onChanged,
    required this.paymentAccount,
  });

  final bool isExpanded;
  final double padding;
  final ValueChanged<PaymentAccount?> onChanged;
  final PaymentAccount? paymentAccount;

  @override
  Widget build(BuildContext context) {
    List<PaymentAccount> dropList = paymentAccountsBox.values.toList();
    return Container(
      height: 48.0, // Fixed height to match other input fields
      decoration: dropDownDecoration(context),
      child: DropdownButton<PaymentAccount>(
        value: paymentAccount,
        padding: EdgeInsets.all(padding.sp),
        isDense: true,
        isExpanded: isExpanded,
        onChanged: onChanged,
        underline: const SizedBox(),
        items: [
          DropdownMenuItem<PaymentAccount>(
            value: null,
            child: Center(
              child: We2upText(AppLocalizations.of(context)!.nothing),
            ),
          ),
          ...dropList
              .map<DropdownMenuItem<PaymentAccount>>((PaymentAccount value) {
            return DropdownMenuItem<PaymentAccount>(
              value: value,
              child: Center(child: We2upText(value.name)),
            );
          }),
        ],
      ),
    );
  }
}
