import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:go_router/go_router.dart';

import '../../bloc/products/products_cubit.dart';

class SingleBillOfSaleButton extends StatelessWidget {
  const SingleBillOfSaleButton({
    super.key,
    this.sellID,
    this.purchaseID,
    this.isReturn = false,
    this.offlineID,
  });

  final int? sellID;
  final int? purchaseID;
  final bool isReturn;
  final String? offlineID;

  @override
  Widget build(BuildContext context) {
    final cubit = ProductsCubit.get(context);
    final strings = AppLocalizations.of(context)!;
    void afterSale() {
      cubit.checkToPrint(context);
      if(context.mounted) context.pop();
    }

    return BlocConsumer<ProductsCubit, ProductsState>(
      listener: (context, state) {
        if (state is NewSalesLoading ||
            state is SellLoading ||
            state is SellReturnLoading) {
          EasyLoading.show(status: strings.loading);
        } else {
          EasyLoading.dismiss();
        }
      },
      buildWhen: (_, current) =>
          current is PaymentMethodPaymentAccountChanged ||
          current is CartPaymentChanged,
      builder: (context, state) {
        return SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: (cubit.cartCustomerContact != null &&
                    cubit.cartProducts.isNotEmpty)
                ? () async {
                    if (!isReturn) {
                      await cubit.sendSellToAPI(context,
                          isQuotation: 1, sellID: sellID, offlineID: offlineID);
                    } else {
                      cubit
                          .sendSellReturn(sellID: sellID!, offlineID: offlineID)
                          .then((v) => v ? afterSale() : null);
                    }
                  }
                : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
            child: Text(
              !isReturn ? strings.offer_price : strings.button_return,
              style: TextStyle(
                color: Theme.of(context).colorScheme.surface,
              ),
            ),
          ),
        );
      },
    );
  }
}
