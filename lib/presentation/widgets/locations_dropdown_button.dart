import 'package:flutter/material.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';
import '../../data/models/business_location.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import '../../data/db/db_manager.dart';
import '../style_constants.dart';

class BusinessLocationDropdown extends StatelessWidget {
  const BusinessLocationDropdown({
    super.key,
    this.isExpanded,
    required this.onChanged,
    this.value,
    this.isFilter = false,
  });

  final bool? isExpanded;
  final Function(BusinessLocation?) onChanged;
  final BusinessLocation? value;
  final bool isFilter;

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    return Container(
      height: 48.0, // Fixed height to match other input fields
      decoration: dropDownDecoration(context),
      child: DropdownButton<BusinessLocation>(
        value: value,
        underline: const SizedBox(),
        isExpanded: isExpanded ?? false,
        onChanged: onChanged,
        items: [
          if (isFilter == true) // Conditionally add null item
            DropdownMenuItem(
              value: null,
              child: Center(child: We2upText(strings.location_all)),
            ),
          ...businessLocationsBox.values.map((location) {
            return DropdownMenuItem<BusinessLocation>(
              value: location,
              child: Center(child: We2upText(location.name)),
            );
          }),
        ],
      ),
    );
  }
}
