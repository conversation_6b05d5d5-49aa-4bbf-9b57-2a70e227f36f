import 'package:flutter/material.dart';
import 'package:app_tracking_transparency/app_tracking_transparency.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class IOSTrackingDialog extends StatelessWidget {
  const IOSTrackingDialog({super.key});

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;

    return AlertDialog(
      title: Text(strings.tracking_dialog_title),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            strings.tracking_dialog_subtitle,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 12),
          Text('• ${strings.tracking_dialog_point1}'),
          Text('• ${strings.tracking_dialog_point2}'),
          const SizedBox(height: 16),
          Text(
            strings.tracking_dialog_note,
            style: const TextStyle(
              fontSize: 12,
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text(strings.tracking_dialog_cancel),
        ),
        ElevatedButton(
          onPressed: () async {
            await AppTrackingTransparency.requestTrackingAuthorization();
            if (context.mounted) {
              Navigator.of(context).pop();
            }
          },
          child: Text(strings.tracking_dialog_continue),
        ),
      ],
    );
  }
}