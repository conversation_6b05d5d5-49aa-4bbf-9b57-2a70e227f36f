import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';

import '../../data/models/product.dart';
import '../../data/db/db_manager.dart';
import '../style_constants.dart';

class NPUnitDropdownButton extends StatelessWidget {
  const NPUnitDropdownButton({
    super.key,
    this.isExpanded = false,
    this.padding = 5,
    required this.onChanged,
    required this.unit,
  });

  final bool isExpanded;
  final double padding;
  final ValueChanged<Unit?> onChanged;
  final Unit? unit;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: dropDownDecoration(context),
      child: DropdownButton<Unit>(
        value: unit,
        padding: EdgeInsets.all(padding.sp),
        isDense: true,
        isExpanded: isExpanded,
        onChanged: onChanged,
        underline: const SizedBox(),
        items: unitsBox.values
            // .where((element) => element.baseUnitId == null)
            .map<DropdownMenuItem<Unit>>((Unit value) {
          return DropdownMenuItem<Unit>(
            value: value,
            child: Center(child: We2upText(value.actualName)),
          );
        }).toList(),
      ),
    );
  }
}
