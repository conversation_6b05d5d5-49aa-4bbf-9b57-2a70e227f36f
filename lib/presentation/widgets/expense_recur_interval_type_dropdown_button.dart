import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';

import '../../bloc/expenses/expenses_cubit.dart';
import '../style_constants.dart';

class ExpenseRecurIntervalTypeDropdownButton extends StatelessWidget {
  const ExpenseRecurIntervalTypeDropdownButton({
    super.key,
    this.isExpanded,
    this.padding,
    required this.onChanged,
    required this.intervalType,
  });

  final bool? isExpanded;
  final double? padding;
  final ValueChanged<RecurIntervalType?> onChanged;
  final RecurIntervalType intervalType;

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;

    return Container(
      decoration: dropDownDecoration(context),
      child: DropdownButton<RecurIntervalType>(
        value: intervalType,
        padding: EdgeInsets.all(padding != null ? padding!.sp : 5.sp),
        isDense: true,
        isExpanded: isExpanded ?? true,
        onChanged: onChanged,
        underline: const SizedBox(),
        items: RecurIntervalType.values
            .map<DropdownMenuItem<RecurIntervalType>>(
              (RecurIntervalType value) => DropdownMenuItem<RecurIntervalType>(
                value: value,
                child: Center(
                  child: We2upText(
                    strings.translateRecurIntervalType(value),
                  ),
                ),
              ),
            )
            .toList(),
      ),
    );
  }
}

extension AppLocalizationsExtension on AppLocalizations {
  String translateRecurIntervalType(RecurIntervalType recurIntervalType) {
    switch (recurIntervalType) {
      case RecurIntervalType.days:
        return days;
      case RecurIntervalType.months:
        return months;
      case RecurIntervalType.years:
        return years;
    }
  }
}
