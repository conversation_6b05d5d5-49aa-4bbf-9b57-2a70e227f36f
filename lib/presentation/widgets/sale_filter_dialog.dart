import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:sizer/sizer.dart';
import 'package:we2up/bloc/products/products_cubit.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/data/models/payment_status.dart';
import 'package:we2up/presentation/widgets/contacts_dropdown_button.dart';
import 'package:we2up/presentation/widgets/shipment_status_dropdown_button.dart';
import 'package:we2up/presentation/widgets/user_dropdown_button.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';

import '../style_constants.dart';
import 'business_filter_dropdown_button.dart';
import 'date_range_dropdown.dart';
import 'date_range_list_tile.dart';
import 'products_dropdown_button.dart';

class SaleFilterDialog extends StatelessWidget {
  const SaleFilterDialog(
      {super.key, this.isReturn = false, this.shipment = false});

  final bool isReturn;
  final bool shipment;

  @override
  Widget build(BuildContext context) {
    final cubit = ProductsCubit.get(context);
    final strings = AppLocalizations.of(context)!;
    return BlocBuilder<ProductsCubit, ProductsState>(
      builder: (context, state) {
        return AlertDialog(
          title: Center(child: We2upText(strings.filter)),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextFormField(
                  controller: cubit.invoiceRefFilterController,
                  decoration: InputDecoration(
                    labelText: strings.ref_no,
                    prefixIcon: const Icon(Icons.title),
                    border: myTextFieldBorder,
                  ),
                ),
                CustomDivider(strings.date_range),
                DateRangeDropdown(
                  changeStartDate: cubit.changeFilterStartDate,
                  changeEndDate: cubit.changeFilterEndDate,
                  onChanged: cubit.updateDateRange,
                  range: cubit.range,
                ),
                const Divider(),
                DateRangeListTile(
                  isStartDate: true,
                  changeDate: cubit.changeFilterStartDate,
                  changeRange: cubit.updateDateRange,
                  date: cubit.saleFilterStartDate,
                ),
                DateRangeListTile(
                  isStartDate: false,
                  startDate: cubit.saleFilterStartDate,
                  changeDate: cubit.changeFilterEndDate,
                  changeRange: cubit.updateDateRange,
                  date: cubit.saleFilterEndDate,
                ),
                Visibility(
                  visible: !isReturn,
                  child: Column(
                    children: [
                      CustomDivider(strings.status),
                      Container(
                        decoration: dropDownDecoration(context),
                        child: DropdownButton<PaymentStatus>(
                          underline: const SizedBox(),
                          value: cubit.filterPaymentStatus,
                          onChanged: cubit.changeFilterPaymentStatus,
                          isExpanded: true,
                          items: [
                            DropdownMenuItem(
                              value: null,
                              child: Center(child: We2upText(strings.nothing)),
                            ),
                            ...PaymentStatus.values.map((status) {
                              return DropdownMenuItem(
                                value: status,
                                child: Center(
                                  child: We2upText(
                                      strings.translatePaymentStatus(status)),
                                ),
                              );
                            }),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                Visibility(
                  visible: shipment,
                  child: Column(
                    children: [
                      CustomDivider(
                        strings.shipment_status.replaceFirst(":", ""),
                      ),
                      ShipmentStatusDropdownButton(
                        onChanged: cubit.changeSaleFilterShipmentStatus,
                        status: cubit.saleFilterShipmentStatus,
                        filter: true,
                      ),
                    ],
                  ),
                ),
                CustomDivider(strings.user),
                UsersDropdownButton(
                  user: cubit.filterUser,
                  onChanged: cubit.updateSaleFilterUser,
                  controller: cubit.userTextEditingController,
                ),
                CustomDivider(strings.customer),
                ContactsDropdownButton(
                  contact: cubit.saleFilterContact,
                  onChanged: cubit.changeSaleFilterContact,
                ),
                CustomDivider(strings.product),
                ProductsDropdownButton(
                  onChanged: cubit.changeSaleFilterProduct,
                  product: cubit.saleFilterProduct,
                  productsList: cubit.filteredProductList(
                    isPurchase: cubit.currentBillData?.quotationPage ?? false,
                  ),
                ),
                CustomDivider(strings.branch),
                BusinessLocationFilterDropdown(
                  isExpanded: true,
                  onChanged: cubit.changeSaleFilterBusinessLocation,
                  value: cubit.saleFilterBusinessLocation,
                ),
              ],
            ),
          ),
          actionsAlignment: MainAxisAlignment.spaceAround,
          actions: [
            ElevatedButton(
              child: We2upText(strings.reset),
              onPressed: () {
                showDialog(
                  context: context,
                  builder: (context) {
                    return AlertDialog(
                      actionsAlignment: MainAxisAlignment.spaceAround,
                      title: Center(
                        child: We2upText(strings.clear_all_filters),
                      ),
                      actions: [
                        ElevatedButton(
                          child: We2upText(strings.clear),
                          onPressed: () {
                            cubit.resetSalesFilters();
                            cubit.getFilteredSales(
                              isNewSearch: true,
                              saleReturn: isReturn,
                            );
                            context.pop();
                            context.pop();
                          },
                        ),
                        ElevatedButton(
                          child: We2upText(strings.cancel),
                          onPressed: () => context.pop(),
                        ),
                      ],
                    );
                  },
                );
              },
            ),
            ElevatedButton(
              child: We2upText(strings.apply),
              onPressed: () {
                context.pop();
                cubit.getFilteredSales(isNewSearch: true, saleReturn: isReturn);
              },
            ),
          ],
        );
      },
    );
  }
}

class CustomDivider extends StatelessWidget {
  const CustomDivider(this.text, {super.key});

  final String text;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        verticalSpacer,
        Row(
          children: [
            Expanded(child: Divider(endIndent: 8.sp)),
            We2upText(text.replaceFirst(":", "")),
            Expanded(child: Divider(indent: 8.sp)),
          ],
        ),
        verticalSpacer,
      ],
    );
  }
}
