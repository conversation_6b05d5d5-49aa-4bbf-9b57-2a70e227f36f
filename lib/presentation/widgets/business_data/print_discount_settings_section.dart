import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:we2up/presentation/widgets/business_data/print_sequence_section.dart';

import '../../../bloc/business_settings/business_settings_cubit.dart';
import 'discount_option_section.dart';

enum PrintSequence { firstAddedFirst, firstAddedLast }

class PrintAndDiscountSettingsSection extends StatelessWidget {
  const PrintAndDiscountSettingsSection({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<BusinessSettingsCubit, BusinessSettingsState>(
      builder: (context, state) {
        return Column(
          children: [
            const Divider(),
            PrintSequenceSection(
              printSequence: state.settings.printSequenceFirstAddedFirst
                  ? PrintSequence.firstAddedFirst
                  : PrintSequence.firstAddedLast,
              onChanged: (value) => context
                  .read<BusinessSettingsCubit>()
                  .updatePrintSequence(value == PrintSequence.firstAddedFirst),
            ),
            const Divider(),
            DiscountOptionSection(
              discountOption: state.settings.isDiscountOptionAmount
                  ? DiscountOption.amount
                  : DiscountOption.percentage,
              onChanged: (value) => context
                  .read<BusinessSettingsCubit>()
                  .updateDiscountOption(value == DiscountOption.amount),
            ),
            const Divider(),
          ],
        );
      },
    );
  }
}