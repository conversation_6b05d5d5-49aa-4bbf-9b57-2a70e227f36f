import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/presentation/widgets/business_data/print_discount_settings_section.dart';

class PrintSequenceSection extends StatelessWidget {
  final PrintSequence printSequence;
  final ValueChanged<PrintSequence> onChanged;

  const PrintSequenceSection({
    super.key,
    required this.printSequence,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(strings.print_sequence_message),
        RadioListTile<PrintSequence>(
          title: Text(strings.first_added_first),
          value: PrintSequence.firstAddedFirst,
          groupValue: printSequence,
          onChanged: (value) => onChanged(value!),
        ),
        RadioListTile<PrintSequence>(
          title: Text(strings.first_added_last),
          value: PrintSequence.firstAddedLast,
          groupValue: printSequence,
          onChanged: (value) => onChanged(value!),
        ),
      ],
    );
  }
}
