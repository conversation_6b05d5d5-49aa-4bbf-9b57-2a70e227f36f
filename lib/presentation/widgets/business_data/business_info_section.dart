import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

import '../../../bloc/business_settings/business_settings_cubit.dart';
import '../../style_constants.dart';
import 'business_text_field.dart';

class BusinessInfoSection extends StatelessWidget {
  const BusinessInfoSection({super.key});

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;

    return BlocBuilder<BusinessSettingsCubit, BusinessSettingsState>(
      builder: (context, state) {
        return Column(
          children: [
            BusinessTextField(
              label: strings.business_name,
              icon: Icons.text_format,
              value: state.settings.businessName,
              onChanged: (value) => context
                  .read<BusinessSettingsCubit>()
                  .updateBusinessName(value),
            ),
            verticalSpacer,
            BusinessTextField(
              label: strings.business_mobile_number,
              icon: Icons.mobile_friendly,
              value: state.settings.businessMobileNumber,
              keyboardType: TextInputType.number,
              onChanged: (value) => context
                  .read<BusinessSettingsCubit>()
                  .updateBusinessMobileNumber(value),
            ),
            verticalSpacer,
            BusinessTextField(
              label: strings.footer_text,
              icon: Icons.short_text,
              value: state.settings.businessFooterText,
              maxLines: null,
              onChanged: (value) => context
                  .read<BusinessSettingsCubit>()
                  .updateBusinessFooterText(value),
            ),
          ],
        );
      },
    );
  }
}