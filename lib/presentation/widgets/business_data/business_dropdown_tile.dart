import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../we2up_text.dart';

class BusinessDropdownTile<T> extends StatelessWidget {
  final String title;
  final T value;
  final List<DropdownMenuItem<T>> items;
  final Function(T?) onChanged;

  const BusinessDropdownTile({
    super.key,
    required this.title,
    required this.value,
    required this.items,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(5.sp),
        child: SizedBox(
          width: 90.w,
          child: ListTile(
            title: We2upText(title),
            trailing: DropdownButton<T>(
              value: value,
              items: items,
              onChanged: onChanged,
            ),
          ),
        ),
      ),
    );
  }
}