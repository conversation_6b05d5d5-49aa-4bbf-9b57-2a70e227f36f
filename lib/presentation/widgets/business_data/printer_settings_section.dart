import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

import '../../../bloc/business_settings/business_settings_cubit.dart';
import '../../../data/models/we2up_font.dart';
import '../../../utils/thermal_printer_manager.dart';
import '../printer_alert_dialog.dart';
import 'business_dropdown_tile.dart';
import 'business_font_size_tile.dart';
import 'business_printer_selection_tile.dart';
import 'business_switch_tile.dart';

class PrinterSettingsSection extends StatelessWidget {
  const PrinterSettingsSection({super.key});

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;

    return BlocBuilder<BusinessSettingsCubit, BusinessSettingsState>(
      builder: (context, state) {
        final cubit = context.read<BusinessSettingsCubit>();

        return Column(
          children: [
            if (defaultTargetPlatform != TargetPlatform.windows && defaultTargetPlatform != TargetPlatform.macOS)
              BusinessPrinterSelectionTile(
                title: strings.choose_bluetooth_printer,
                subtitle: "${strings.current_printer}: "
                    "${state.settings.bluetoothDeviceName ?? strings.nothing}",
                onSelectPrinter: () {
                  showDialog(
                    context: context,
                    builder: (context) {
                      return const PrinterAlertDialog(isSetting: true);
                    },
                  );
                },
              ),
            BusinessDropdownTile<String>(
              title: strings.paper_size,
              value: state.settings.currentPaperSize,
              items: We2UpPaperSize.values
                  .map((e) => DropdownMenuItem(
                        value: e.name,
                        child: Center(child: Text(e.name)),
                      ))
                  .toList(),
              onChanged: (value) => cubit.updatePaperSize(value!),
            ),
            BusinessDropdownTile<String>(
              title: strings.font_type,
              value: state.settings.currentFont,
              items: we2upFonts
                  .map((e) => DropdownMenuItem(
                        value: e.englishName,
                        child: Center(
                          child: Text(
                            strings.localeName == "ar"
                                ? e.arabicName
                                : e.englishName,
                            style: TextStyle(
                              fontFamily: e.fontFamily,
                            ),
                          ),
                        ),
                      ))
                  .toList(),
              onChanged: (value) => cubit.updateFont(value!),
            ),
            BusinessFontSizeTile(
              title: "${strings.font_size} (${state.settings.currentFontSize})",
              value: state.settings.currentFontSize.toDouble(),
              onChanged: (value) => cubit.updateFontSize(value.toInt()),
            ),
            BusinessSwitchTile(
              title: strings.print_qr_code,
              value: state.settings.printQRCode,
              onChanged: (value) => cubit.updatePrintQRCode(value),
            ),
            BusinessSwitchTile(
              title: strings.showUnitInReceipt,
              value: state.settings.showUnit,
              onChanged: (value) => cubit.updateShowUnit(value),
            ),
          ],
        );
      },
    );
  }
}
