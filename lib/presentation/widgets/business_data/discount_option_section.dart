import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class DiscountOptionSection extends StatelessWidget {
  final DiscountOption discountOption;
  final ValueChanged<DiscountOption> onChanged;

  const DiscountOptionSection({
    super.key,
    required this.discountOption,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(strings.discount_options),
        RadioListTile<DiscountOption>(
          title: Text(strings.amount_option),
          value: DiscountOption.amount,
          groupValue: discountOption,
          onChanged: (value) => onChanged(value!),
        ),
        RadioListTile<DiscountOption>(
          title: Text(strings.percentage_option),
          value: DiscountOption.percentage,
          groupValue: discountOption,
          onChanged: (value) => onChanged(value!),
        ),
      ],
    );
  }
}

enum DiscountOption { percentage, amount }
