import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

import '../../../bloc/business_settings/business_settings_cubit.dart';
import '../../../data/models/app_currency.dart';
import 'business_currency_tile.dart';
import 'business_switch_tile.dart';

class BusinessOptionsSection extends StatelessWidget {
  const BusinessOptionsSection({super.key});

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;

    return BlocBuilder<BusinessSettingsCubit, BusinessSettingsState>(
      builder: (context, state) {
        final cubit = context.read<BusinessSettingsCubit>();

        return Column(
          children: [
            BusinessSwitchTile(
              title: strings.include_service_in_taxes,
              value: state.settings.includeServiceInTaxes,
              onChanged: (value) => cubit.updateIncludeServiceInTaxes(value),
            ),
            BusinessCurrencyTile(
              title: strings.choose_currency,
              subtitle: "${strings.chosen_currency} "
                  "${state.settings.currency.name} ${state.settings.currency.symbol}",
              onCurrencySelected: (currency) => cubit.updateCurrency(
                AppCurrency.fromCurrency(currency),
              ),
            ),
            BusinessSwitchTile(
              title: strings.toggle_sound,
              value: state.settings.playSoundWhenAddingProduct,
              onChanged: (value) => cubit.updatePlaySound(value),
            ),
            BusinessSwitchTile(
              title: strings.use_arabic_numbers,
              value: state.settings.useArabicNumbers,
              onChanged: (value) => cubit.updateUseArabicNumbers(value),
            ),
            BusinessSwitchTile(
              title: strings.stop_syncing,
              value: state.settings.stopSyncing,
              onChanged: (value) => cubit.updateStopSyncing(value),
            ),
            if (defaultTargetPlatform == TargetPlatform.windows || defaultTargetPlatform == TargetPlatform.macOS)
              BusinessSwitchTile(
                title: strings.showShopProductsGridView,
                value: state.settings.showShopProductsGridView,
                onChanged: (value) => cubit.updateShowShopProductsGridView(value),
              ),
            BusinessSwitchTile(
              title: strings.showPopupMenuToSelectNumber,
              value: state.settings.showPopupMenuToSelectNumber,
              onChanged: (value) => cubit.updateShowPopupMenuToSelectNumber(value),
            ),
          ],
        );
      },
    );
  }
}