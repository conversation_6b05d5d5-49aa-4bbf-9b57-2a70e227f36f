import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:image_picker/image_picker.dart';
import 'package:sizer/sizer.dart';

import '../../../bloc/business_settings/business_settings_cubit.dart';
import '../../style_constants.dart';
import '../we2up_text.dart';

class BusinessImageSection extends StatelessWidget {
  const BusinessImageSection({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<BusinessSettingsCubit, BusinessSettingsState>(
      builder: (context, state) {
        final path = state.settings.imagePath;
        final strings = AppLocalizations.of(context)!;
        final cubit = context.read<BusinessSettingsCubit>();

        return Card(
          child: Padding(
            padding: EdgeInsets.all(5.sp),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                if (path != null)
                  Image.file(
                    File(path),
                    height: 80.w,
                    fit: BoxFit.fitWidth,
                    errorBuilder: (context, error, stackTrace) {
                      return const Icon(Icons.error_outline);
                    },
                  ),
                verticalSpacer,
                FilledButton(
                  onPressed: () async {
                    final imagePicker = ImagePicker();
                    final pickedFile = await imagePicker.pickImage(
                      source: ImageSource.gallery,
                    );
                    if (pickedFile != null) {
                      if (context.mounted) {
                        cubit.updateImagePath(pickedFile.path);
                      }
                    }
                  },
                  child: We2upText(strings.load_image),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}