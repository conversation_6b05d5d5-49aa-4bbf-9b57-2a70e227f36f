import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../we2up_text.dart';

class BusinessSwitchTile extends StatelessWidget {
  final String title;
  final bool value;
  final Function(bool) onChanged;

  const BusinessSwitchTile({
    super.key,
    required this.title,
    required this.value,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(5.sp),
        child: SizedBox(
          width: 90.w,
          child: ListTile(
            title: We2upText(title),
            trailing: Switch(
              value: value,
              onChanged: onChanged,
            ),
          ),
        ),
      ),
    );
  }
}