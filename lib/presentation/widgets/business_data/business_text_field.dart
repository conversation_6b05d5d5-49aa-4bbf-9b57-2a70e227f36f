import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../style_constants.dart';

class BusinessTextField extends StatelessWidget {
  final String label;
  final IconData icon;
  final String value;
  final Function(String) onChanged;
  final TextInputType? keyboardType;
  final int? maxLines;

  const BusinessTextField({
    super.key,
    required this.label,
    required this.icon,
    required this.value,
    required this.onChanged,
    this.keyboardType,
    this.maxLines = 1,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(5.sp),
        child: SizedBox(
          width: 90.w,
          child: TextFormField(
            initialValue: value,
            maxLines: maxLines,
            keyboardType: keyboardType,
            decoration: InputDecoration(
              labelText: label,
              prefixIcon: Icon(icon),
              border: myTextFieldBorder,
            ),
            onChanged: onChanged,
          ),
        ),
      ),
    );
  }
}