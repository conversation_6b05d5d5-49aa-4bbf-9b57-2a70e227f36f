import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../we2up_text.dart';

class BusinessFontSizeTile extends StatelessWidget {
  final String title;
  final double value;
  final Function(double) onChanged;

  const BusinessFontSizeTile({
    super.key,
    required this.title,
    required this.value,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(5.sp),
        child: SizedBox(
          width: 90.w,
          child: ListTile(
            title: We2upText(title),
            trailing: SizedBox(
              width: 50.w,
              child: Slider(
                value: value,
                onChanged: onChanged,
                divisions: 12,
                min: 8,
                max: 20,
                label: value.toInt().toString(),
              ),
            ),
          ),
        ),
      ),
    );
  }
}