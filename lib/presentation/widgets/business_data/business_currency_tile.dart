import 'package:currency_picker/currency_picker.dart';
import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

import '../we2up_text.dart';

class BusinessCurrencyTile extends StatelessWidget {
  final String title;
  final String subtitle;
  final Function(Currency) onCurrencySelected;

  const BusinessCurrencyTile({
    super.key,
    required this.title,
    required this.subtitle,
    required this.onCurrencySelected,
  });

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;

    return Card(
      child: Padding(
        padding: EdgeInsets.all(5.sp),
        child: SizedBox(
          width: 90.w,
          child: ListTile(
            title: We2upText(title),
            subtitle: We2upText(subtitle),
            trailing: OutlinedButton(
              onPressed: () {
                showCurrencyPicker(
                  context: context,
                  showFlag: true,
                  showCurrencyName: true,
                  showCurrencyCode: true,
                  onSelect: (Currency currency) {
                    onCurrencySelected(currency);
                  },
                );
              },
              child: We2upText(strings.choose),
            ),
          ),
        ),
      ),
    );
  }
}