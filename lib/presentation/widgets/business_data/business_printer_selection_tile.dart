import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:sizer/sizer.dart';

import '../we2up_text.dart';

class BusinessPrinterSelectionTile extends StatelessWidget {
  final String title;
  final String subtitle;
  final VoidCallback onSelectPrinter;

  const BusinessPrinterSelectionTile({
    super.key,
    required this.title,
    required this.subtitle,
    required this.onSelectPrinter,
  });

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;

    return Card(
      child: Padding(
        padding: EdgeInsets.all(5.sp),
        child: SizedBox(
          width: 90.w,
          child: ListTile(
            title: We2upText(title),
            subtitle: We2upText(subtitle),
            trailing: OutlinedButton(
              onPressed: onSelectPrinter,
              child: We2upText(strings.choose),
            ),
          ),
        ),
      ),
    );
  }
}