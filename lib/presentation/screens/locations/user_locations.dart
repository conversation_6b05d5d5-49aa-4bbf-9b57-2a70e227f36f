import 'package:conditional_builder_null_safety/conditional_builder_null_safety.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:gap/gap.dart';
import 'package:sizer/sizer.dart';
import 'package:we2up/bloc/location/location_cubit.dart';
import 'package:we2up/bloc/location/user_locations_pdf.dart';
import 'package:we2up/presentation/widgets/locations_layouts/user_location_info_card.dart';

import '../../widgets/location_filter_dialog.dart';
import '../../widgets/locations_dropdown_button.dart';
import '../../widgets/user_dropdown_button.dart';
import '../../widgets/we2up_text.dart';

class UserLocationsScreen extends StatelessWidget {
  const UserLocationsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    final cubit = context.read<LocationCubit>();
    return Scaffold(
      appBar: AppBar(
        title: We2upText(strings.users_locations),
        actions: [
          IconButton(
            onPressed: () => showDialog(
              context: context,
              builder: (_) => BlocProvider.value(
                value: cubit,
                child: const LocationFilterDialog(),
              ),
            ),
            icon: const Icon(Icons.filter_list_alt),
          )
        ],
      ),
      body: BlocBuilder<LocationCubit, LocationState>(
        builder: (context, state) {
          return Padding(
            padding: EdgeInsets.symmetric(horizontal: 10.sp),
            child: Column(
              children: [
                Row(
                  children: [
                    We2upText(strings.branch),
                    Gap(8.sp),
                    Expanded(
                      child: BusinessLocationDropdown(
                        isExpanded: true,
                        onChanged: cubit.changeSelectedLocation,
                        value: cubit.selectedLocation,
                        isFilter: true,
                      ),
                    ),
                  ],
                ),
                Gap(8.sp),
                Row(
                  children: [
                    We2upText("${strings.user_name}:"),
                    Gap(8.sp),
                    Expanded(
                      child: UsersDropdownButton(
                        user: cubit.filterUser,
                        onChanged: cubit.updateFilterUser,
                        controller: cubit.filterUserController,
                      ),
                    ),
                  ],
                ),
                Gap(8.sp),
                Column(
                  children: [
                    SizedBox(
                      width: 50.w,
                      child: OutlinedButton.icon(
                        icon: const Icon(Icons.save_outlined),
                        label: We2upText(strings.search),
                        onPressed: () => cubit.fetchUserLocationsInfo(context),
                      ),
                    ),
                    Gap(8.sp),
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton(
                            onPressed: cubit.userLocationsInfo.isNotEmpty
                                ? () => generateUserLocationPdf(
                                      context,
                                      userLocationsInfo:
                                          cubit.userLocationsInfo,
                                    )
                                : null,
                            child: We2upText(strings.print),
                          ),
                        ),
                        Gap(8.sp),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: cubit.userLocationsInfo.isNotEmpty
                                ? () => generateUserLocationPdf(
                                      context,
                                      userLocationsInfo:
                                          cubit.userLocationsInfo,
                                      share: true,
                                    )
                                : null,
                            child: We2upText(strings.share),
                          ),
                        ),
                      ],
                    )
                  ],
                ),
                const Divider(),
                Expanded(
                  child: ConditionalBuilder(
                    condition: state is! UserLocationsInfoLoading,
                    builder: (_) {
                      if (cubit.userLocationsInfo.isEmpty) {
                        return Center(child: We2upText(strings.no_search_results));
                      }
                      return ListView.separated(
                        itemCount: cubit.userLocationsInfo.length,
                        itemBuilder: (_, index) => UserLocationInfoCard(
                          userLocationInfo: cubit.userLocationsInfo[index],
                        ),
                        separatorBuilder: (_, __) =>
                            Divider(endIndent: 20.w, indent: 20.w),
                      );
                    },
                    fallback: (_) =>
                        const Center(child: CircularProgressIndicator()),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
