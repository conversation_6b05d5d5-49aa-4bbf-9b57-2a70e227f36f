import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:go_router/go_router.dart';
import 'package:sizer/sizer.dart';
import 'package:we2up/presentation/widgets/locations_layouts/branch_location_range.dart';
import 'package:we2up/presentation/widgets/locations_layouts/search_location_range.dart';

import '../../../bloc/location/location_cubit.dart';
import '../../../utils/route_constants.dart';
import '../../widgets/locations_layouts/closest_location_range.dart';
import '../../widgets/locations_layouts/contact_location_range.dart';
import '../../widgets/we2up_text.dart';

class LocationSettings extends StatelessWidget {
  const LocationSettings({super.key});

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    return Scaffold(
      appBar: AppBar(title: We2upText(strings.location_settings)),
      body: BlocBuilder<LocationCubit, LocationState>(
        buildWhen: (_, current) => current is LocationRangesValuesUpdated,
        builder: (context, state) {
          return ListView(
            padding: EdgeInsets.symmetric(horizontal: 10.sp),
            children: [
              ListTile(
                visualDensity: VisualDensity.comfortable,
                trailing: const Icon(Icons.location_history),
                title: We2upText(strings.change_branch_location),
                onTap: () => context.push(
                  changeBranchLocation,
                  extra: context.read<LocationCubit>(),
                ),
              ),
              const Divider(),
              const BranchLocationRange(),
              const Divider(),
              const ContactLocationRange(),
              const Divider(),
              const SearchLocationRange(),
              const Divider(),
              const ClosestLocationRange(),
            ],
          );
        },
      ),
    );
  }
}
