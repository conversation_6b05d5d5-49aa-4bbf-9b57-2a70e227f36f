import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:go_router/go_router.dart';
import 'package:we2up/bloc/location/location_cubit.dart';
import 'package:we2up/data/db/db_manager.dart';

import '../../../utils/route_constants.dart';
import '../../widgets/we2up_text.dart';

class LocationsScreen extends StatelessWidget {
  const LocationsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    final cubit = context.read<LocationCubit>();
    return Scaffold(
      appBar: AppBar(title: We2upText(strings.location)),
      body: BlocConsumer<LocationCubit, LocationState>(
        listener: (context, state) {
          if (state is RangedDataLoading) {
            EasyLoading.show(status: strings.loading);
          } else if (state is RangedDataLoaded) {
            EasyLoading.dismiss();
          }
        },
        builder: (context, state) {
          return ListView(
            children: [
              ListTile(
                enabled: canAccessLocationUserPlaces(),
                visualDensity: VisualDensity.comfortable,
                title: We2upText(strings.users_locations),
                trailing: const Icon(Icons.people),
                onTap: () => context.push(userLocationScreen, extra: cubit),
              ),
              const Divider(),
              ListTile(
                enabled: canAccessLocationWalkingLine(),
                visualDensity: VisualDensity.comfortable,
                title: We2upText(strings.itinerary),
                trailing: const Icon(Icons.route),
                onTap: () => context.push(itinerary, extra: cubit),
              ),
              const Divider(),
              ListTile(
                enabled: canAccessLocationSearch(),
                visualDensity: VisualDensity.comfortable,
                title: We2upText(strings.location_search),
                trailing: const Icon(Icons.search),
                onTap: () => context.push(locationSearchScreen, extra: cubit),
              ),
              const Divider(),
              ListTile(
                enabled: canAccessLocationSetting(),
                visualDensity: VisualDensity.comfortable,
                title: We2upText(strings.location_settings),
                trailing: const Icon(Icons.settings_outlined),
                onTap: () => context.push(locationSettings, extra: cubit),
              ),
            ],
          );
        },
      ),
    );
  }
}
