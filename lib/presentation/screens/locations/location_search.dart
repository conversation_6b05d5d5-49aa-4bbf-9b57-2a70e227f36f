import 'package:conditional_builder_null_safety/conditional_builder_null_safety.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:gap/gap.dart';
import 'package:sizer/sizer.dart';
import 'package:we2up/bloc/location/location_cubit.dart';

import '../../../utils/we2up_constants.dart';
import '../../widgets/contact_details_card.dart';
import '../../widgets/location_range_dropdown_button.dart';
import '../../widgets/we2up_text.dart';

class LocationSearch extends StatelessWidget {
  const LocationSearch({super.key});

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    return Scaffold(
      appBar: AppBar(title: We2upText(strings.location_search)),
      body: BlocBuilder<LocationCubit, LocationState>(
        builder: (context, state) {
          final cubit = context.read<LocationCubit>();
          return Padding(
            padding: EdgeInsets.symmetric(horizontal: 8.sp),
            child: Column(
              children: [
                ListTile(
                  title: We2upText(strings.selected_location),
                  trailing: const Icon(Icons.edit_location_alt),
                  onTap: () async {
                    final selectedLocation =
                        await getLocationWithPermission(context);
                    cubit.updateSelectedLocation(selectedLocation);
                  },
                ),
                Gap(8.sp),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 15.sp),
                  child: Row(
                    children: [
                      We2upText("${strings.location_state}: "),
                      Gap(8.sp),
                      Expanded(
                        child: LocationRangeDropdownButton(
                          onChanged: cubit.updateLocationRange,
                          locationRange: cubit.locationRange,
                        ),
                      ),
                    ],
                  ),
                ),
                Gap(8.sp),
                SizedBox(
                  width: 50.w,
                  child: OutlinedButton.icon(
                    icon: const Icon(Icons.save_outlined),
                    label: We2upText(strings.search),
                    onPressed: cubit.position != null
                        ? cubit.fetchSearchContacts
                        : null,
                  ),
                ),
                const Divider(),
                Expanded(
                  child: ConditionalBuilder(
                    condition: state is! SearchContactLoading,
                    builder: (_) {
                      if (cubit.searchContacts.isEmpty) {
                        return Center(child: We2upText(strings.no_search_results));
                      }
                      return ListView.separated(
                        itemCount: cubit.searchContacts.length,
                        itemBuilder: (_, index) => ContactDetailsCard(
                          contact: cubit.searchContacts[index],
                          isLocation: true,
                        ),
                        separatorBuilder: (_, __) =>
                            Divider(endIndent: 20.w, indent: 20.w),
                      );
                    },
                    fallback: (_) =>
                        const Center(child: CircularProgressIndicator()),
                  ),
                )
              ],
            ),
          );
        },
      ),
    );
  }
}
