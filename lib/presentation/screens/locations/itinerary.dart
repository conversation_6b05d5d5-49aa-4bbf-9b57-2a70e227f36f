import 'package:conditional_builder_null_safety/conditional_builder_null_safety.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:gap/gap.dart';
import 'package:sizer/sizer.dart';
import 'package:we2up/bloc/location/location_cubit.dart';
import 'package:we2up/presentation/widgets/location_range_dropdown_button.dart';
import 'package:we2up/presentation/widgets/locations_layouts/itinerary_card.dart';
import 'package:we2up/presentation/widgets/my_loading_indicator.dart';

import '../../../bloc/location/pdf_function.dart';
import '../../widgets/contacts_dropdown_button.dart';
import '../../widgets/location_filter_dialog.dart';
import '../../widgets/locations_dropdown_button.dart';
import '../../widgets/user_dropdown_button.dart';
import '../../widgets/we2up_text.dart';

class ItineraryScreen extends StatelessWidget {
  const ItineraryScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    final cubit = context.read<LocationCubit>();
    cubit.itineraryController.addListener(() {
      if (cubit.itineraryController.position.pixels ==
          cubit.itineraryController.position.maxScrollExtent) {
        cubit.getTransactions(context);
      }
    });
    return Scaffold(
      appBar: AppBar(
        title: We2upText(strings.itinerary),
        actions: [
          IconButton(
            onPressed: () => showDialog(
              context: context,
              builder: (_) => BlocProvider.value(
                value: cubit,
                child: const LocationFilterDialog(),
              ),
            ),
            icon: const Icon(Icons.filter_list_alt),
          )
        ],
      ),
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 8.sp),
        child: Column(
          children: [
            Row(
              children: [
                We2upText(strings.branch),
                Gap(8.sp),
                Expanded(
                  child: BlocBuilder<LocationCubit, LocationState>(
                    buildWhen: (_, c) => c is BusinessLocationChanged,
                    builder: (context, state) {
                      return BusinessLocationDropdown(
                        isExpanded: true,
                        onChanged: cubit.changeSelectedLocation,
                        value: cubit.selectedLocation,
                        isFilter: true,
                      );
                    },
                  ),
                ),
              ],
            ),
            Gap(8.sp),
            Row(
              children: [
                We2upText("${strings.user_name}:"),
                Gap(8.sp),
                Expanded(
                  child: BlocBuilder<LocationCubit, LocationState>(
                    buildWhen: (_, c) => c is FilterUserUpdated,
                    builder: (context, state) {
                      return UsersDropdownButton(
                        user: cubit.filterUser,
                        onChanged: cubit.updateFilterUser,
                        controller: cubit.filterUserController,
                      );
                    },
                  ),
                ),
              ],
            ),
            Gap(8.sp),
            Row(
              children: [
                We2upText(strings.customer_name),
                Gap(8.sp),
                Expanded(
                  child: BlocBuilder<LocationCubit, LocationState>(
                    buildWhen: (_, c) => c is FilterContactUpdated,
                    builder: (context, state) {
                      return ContactsDropdownButton(
                        onChanged: cubit.updateFilterContact,
                        contact: cubit.filterContact,
                        isInLocation: true,
                      );
                    },
                  ),
                ),
              ],
            ),
            Gap(8.sp),
            Row(
              children: [
                We2upText("${strings.location_state}: "),
                Gap(8.sp),
                Expanded(
                  child: BlocBuilder<LocationCubit, LocationState>(
                    buildWhen: (_, c) => c is FilterRangeUpdated,
                    builder: (context, state) {
                      return LocationRangeDropdownButton(
                        onChanged: cubit.updateLocationRange,
                        locationRange: cubit.locationRange,
                      );
                    },
                  ),
                ),
              ],
            ),
            Gap(8.sp),
            SizedBox(
              width: 50.w,
              child: OutlinedButton.icon(
                icon: const Icon(Icons.save_outlined),
                label: We2upText(strings.search),
                onPressed: () async {
                  EasyLoading.showInfo(strings.loading);
                  await cubit.getTransactions(context, isNewSearch: true);
                  EasyLoading.dismiss();
                },
              ),
            ),
            Gap(8.sp),
            Row(
              children: [
                Expanded(
                  child: BlocBuilder<LocationCubit, LocationState>(
                    buildWhen: (_, c) => c is TransactionsLoaded,
                    builder: (context, state) {
                      return ElevatedButton(
                        onPressed: cubit.transactions.isNotEmpty
                            ? () => generatePdfTable(
                                  context,
                                  rows: cubit.transactions,
                                )
                            : null,
                        child: We2upText(strings.print),
                      );
                    },
                  ),
                ),
                Gap(8.sp),
                Expanded(
                  child: BlocBuilder<LocationCubit, LocationState>(
                    buildWhen: (_, c) => c is TransactionsLoaded,
                    builder: (context, state) {
                      return ElevatedButton(
                        onPressed: cubit.transactions.isNotEmpty
                            ? () => generatePdfTable(
                                  context,
                                  rows: cubit.transactions,
                                  share: true,
                                )
                            : null,
                        child: We2upText(strings.share),
                      );
                    },
                  ),
                ),
              ],
            ),
            Expanded(
              child: BlocBuilder<LocationCubit, LocationState>(
                buildWhen: (_, current) => current is TransactionsLoaded,
                builder: (context, state) {
                  if (state is TransactionsLoaded) {
                    return ConditionalBuilder(
                      condition: state.transactions.isNotEmpty,
                      builder: (_) => ListView.builder(
                        controller: cubit.itineraryController,
                        itemCount: state.transactions.length,
                        itemBuilder: (context, index) {
                          return ItineraryCard(
                            transaction: state.transactions[index],
                          );
                        },
                      ),
                      fallback: (context) {
                        if (state is NewTransactionsLoading) {
                          return const MyLoadingIndicator();
                        }
                        return Center(
                          child: We2upText(strings.no_transactions_in_range),
                        );
                      },
                    );
                  }
                  return const SizedBox();
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
