import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:maps_launcher/maps_launcher.dart';
import 'package:sizer/sizer.dart';
import 'package:we2up/bloc/location/location_cubit.dart';

import '../../../utils/we2up_constants.dart';
import '../../widgets/locations_dropdown_button.dart';
import '../../widgets/we2up_text.dart';

class ChangeBranchLocation extends StatelessWidget {
  const ChangeBranchLocation({super.key});

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    return Scaffold(
      appBar: AppBar(title: We2upText(strings.change_branch_location)),
      body: <PERSON><PERSON><PERSON>er<LocationCubit, LocationState>(
        builder: (context, state) {
          final cubit = context.read<LocationCubit>();
          return ListView(
            padding: EdgeInsets.symmetric(horizontal: 10.sp),
            children: [
              Row(
                children: [
                  We2upText("${strings.branch_location}:"),
                  Gap(8.sp),
                  Expanded(
                    child: BusinessLocationDropdown(
                      isExpanded: true,
                      onChanged: cubit.changeSelectedLocation,
                      value: cubit.selectedLocation,
                    ),
                  ),
                ],
              ),
              Gap(8.sp),
              OutlinedButton.icon(
                icon: const Icon(Icons.location_on_outlined),
                label: We2upText(strings.current_branch_location),
                onPressed: cubit.selectedLocation != null
                    ? () {
                        final locationInfo =
                            cubit.selectedLocation!.locationInfo;
                        locationInfo != null
                            ? MapsLauncher.launchCoordinates(
                                locationInfo.latitude,
                                locationInfo.longitude,
                              )
                            : EasyLoading.showInfo(
                                strings.branch_has_no_location,
                              );
                      }
                    : null,
              ),
              const Divider(),
              ListTile(
                title: We2upText(strings.selected_location),
                trailing: const Icon(Icons.edit_location_alt),
                onTap: () async {
                  final selectedLocation =
                      await getLocationWithPermission(context);
                  cubit.updateSelectedLocation(selectedLocation);
                },
              ),
              Gap(8.sp),
              OutlinedButton.icon(
                icon: const Icon(Icons.save_outlined),
                label: We2upText(strings.save),
                onPressed:
                    cubit.position != null && cubit.selectedLocation != null
                        ? () => cubit
                            .updateBusinessLocation()
                            .then((value) => context.pop())
                        : null,
              ),
            ],
          );
        },
      ),
    );
  }
}
