import 'package:conditional_builder_null_safety/conditional_builder_null_safety.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:go_router/go_router.dart';
import 'package:sizer/sizer.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/bloc/crm/crm_cubit.dart';
import 'package:we2up/presentation/widgets/follow_up_card.dart';
import 'package:we2up/utils/we2up_constants.dart';

import '../../data/db/db_manager.dart';
import '../../utils/route_constants.dart';
import '../widgets/crm_filter.dart';
import '../widgets/we2up_text.dart';

class FollowUpsPage extends StatelessWidget {
  const FollowUpsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    final cubit = context.read<CrmCubit>();
    return GestureDetector(
      onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
      child: Scaffold(
        appBar: AppBar(
          title: We2upText(strings.follow_ups),
          centerTitle: true,
          actions: [
            IconButton(
              onPressed: () => showDialog(
                context: context,
                builder: (_) => BlocProvider.value(
                  value: cubit,
                  child: const FollowUpFilter(),
                ),
              ),
              icon: const Icon(Icons.filter_list_alt),
            ),
          ],
        ),
        body: Padding(
          padding: EdgeInsets.symmetric(horizontal: 3.sp),
          child: BlocConsumer<CrmCubit, CrmState>(
            listener: (context, state) {
              if (state is NewFollowUpsLoading ||
                  state is EditFollowUpsLoading) {
                EasyLoading.show(status: strings.loading);
              } else {
                EasyLoading.dismiss();
              }
            },
            buildWhen: (previous, current) => current is FollowUpsLoaded,
            builder: (context, state) {
              if (state is FollowUpsLoaded) {
                return ConditionalBuilder(
                  condition: state.followups.isNotEmpty,
                  builder: (context) => ListView.builder(
                    controller: cubit.scrollController,
                    itemCount: state.followups.length,
                    itemBuilder: (context, index) {
                      return FollowUpDetailsCard(
                        followUp: state.followups[index],
                      );
                    },
                  ),
                  fallback: (context) => Center(
                    child: We2upText(strings.no_transactions_in_range),
                  ),
                );
              }
              return Center(child: We2upText(strings.loading));
            },
          ),
        ),
        floatingActionButton: loginData.isAdmin
            ? FloatingActionButton(
                onPressed: () async => await checkShiftLocationAccess(
                  context,
                  () => context.push(
                    addEditFollowUp,
                    extra: (null, cubit),
                  ),
                ),
                child: const Icon(Icons.add),
              )
            : null,
      ),
    );
  }
}
