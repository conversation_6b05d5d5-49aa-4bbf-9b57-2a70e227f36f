import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sizer/sizer.dart';
import 'package:we2up/bloc/contacts/contacts_cubit.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/presentation/widgets/show_total.dart';

import '../widgets/contact_due_card.dart';
import '../widgets/filter_contacts_dialog.dart';
import '../widgets/we2up_text.dart';

class ContactsDues extends StatelessWidget {
  const ContactsDues({super.key, this.supplier = false});

  final bool supplier;

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    final cubit = ContactsCubit.get(context);
    cubit.resetContactsFilters();
    return GestureDetector(
      onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
      child: <PERSON><PERSON><PERSON>er<ContactsCubit, ContactsState>(
        builder: (context, state) {
          return Scaffold(
            appBar: AppBar(
              title: We2upText(
                supplier ? strings.suppliers_dues : strings.customers_dues,
              ),
              centerTitle: true,
              actions: [
                IconButton(
                  onPressed: () => showDialog(
                    context: context,
                    builder: (_) => BlocProvider.value(
                      value: cubit,
                      child: const FilterContactsDialog(dues: true),
                    ),
                  ),
                  icon: const Icon(Icons.filter_list_alt),
                )
              ],
            ),
            body: Padding(
              padding: EdgeInsets.symmetric(horizontal: 10.sp),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Expanded(
                    child: ListView.separated(
                      itemBuilder: (context, index) {
                        return ContactDueCard(
                          contact: cubit.filteredContacts(
                            filter: supplier ? "supplier" : "customer",
                          )[index],
                          supplier: supplier,
                        );
                      },
                      itemCount: cubit
                          .filteredContacts(
                            filter: supplier ? "supplier" : "customer",
                          )
                          .length,
                      separatorBuilder: (context, index) => const Divider(),
                    ),
                  ),
                  ShowTotal(
                    amountText: strings.total_dues,
                    amountValue: cubit.totalDues(
                      filter: supplier ? "supplier" : "customer",
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
