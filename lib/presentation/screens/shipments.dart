import 'package:conditional_builder_null_safety/conditional_builder_null_safety.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/bloc/products/products_cubit.dart';

import '../widgets/sale_filter_dialog.dart';
import '../widgets/sales_invoice_card.dart';
import '../widgets/show_three_totals.dart';
import '../widgets/we2up_text.dart';

class Shipments extends StatefulWidget {
  const Shipments({super.key});

  @override
  State<Shipments> createState() => _ShipmentsState();
}

class _ShipmentsState extends State<Shipments> {
  late final ScrollController scrollController;

  @override
  void initState() {
    ProductsCubit.get(context).resetSalesFilters();
    ProductsCubit.get(context).getFilteredSales(isNewSearch: true, refresh: true);
    scrollController = ScrollController();
    scrollController.addListener(() {
      if (scrollController.position.pixels ==
          scrollController.position.maxScrollExtent) {
        ProductsCubit.get(context).getFilteredSales();
      }
    });
    super.initState();
  }

  @override
  void didChangeDependencies() {
    ProductsCubit.get(context).resetSalesFilters();
    ProductsCubit.get(context).resetShipmentStatus();
    super.didChangeDependencies();
  }

  @override
  void dispose() {
    scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    return GestureDetector(
      onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
      child: Scaffold(
        appBar: AppBar(
          title: We2upText(strings.shipments),
          centerTitle: true,
          actions: [
            IconButton(
              onPressed: () => showDialog(
                context: context,
                builder: (_) => const SaleFilterDialog(shipment: true),
              ),
              icon: const Icon(Icons.filter_list_alt),
            ),
          ],
        ),
        body: BlocConsumer<ProductsCubit, ProductsState>(
          listener: (context, state) {
            if (state is NewSalesLoading || state is ShipmentStatusLoading) {
              EasyLoading.show(status: strings.loading);
            } else if(state is ShipmentStatusLoaded){
              EasyLoading.showSuccess("Shipment sent");
            } else {
              EasyLoading.dismiss();
            }
          },
          buildWhen: (previous, current) => current is SalesLoaded,
          builder: (context, state) {
            if (state is SalesLoaded) {
              final sales = state.sales.where((sell) => sell.isQuotation == 0);
              return ConditionalBuilder(
                condition: sales.isNotEmpty,
                builder: (context) {
                  return Column(
                    children: [
                      Expanded(
                        child: ListView.builder(
                          controller: scrollController,
                          itemCount: sales.length,
                          itemBuilder: (context, index) {
                            return SalesInvoiceCard(
                              sell: sales.elementAt(index),
                              shipment: true,
                            );
                          },
                        ),
                      ),
                      ShowThreeTotals(
                        totalText: "${strings.total_bills}:",
                        paidText: "${strings.total_paid}:",
                        remainingText: "${strings.total_dues}:",
                        total: state.calculations.$1,
                        paid: state.calculations.$2,
                        remaining: state.calculations.$3,
                      ),
                    ],
                  );
                },
                fallback: (context) => Center(
                  child: We2upText(strings.no_transactions_in_range),
                ),
              );
            }
            return Center(child: We2upText(strings.loading));
          },
        ),
      ),
    );
  }
}
