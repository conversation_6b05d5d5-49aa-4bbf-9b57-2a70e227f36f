import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_offline/flutter_offline.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:sizer/sizer.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/bloc/products/products_cubit.dart';
import 'package:we2up/data/db/db_manager.dart';
import 'package:we2up/utils/route_constants.dart';

import '../../bloc/single_product/single_product_cubit.dart';
import '../../utils/we2up_constants.dart';
import '../style_constants.dart';
import '../widgets/branch_opening_stock_card.dart';
import '../widgets/branches_multi_selector.dart';
import '../widgets/np_brand_dropdown_button.dart';
import '../widgets/np_category_dropdown_button.dart';
import '../widgets/np_expiry_dropdown_button.dart';
import '../widgets/np_tax_dropdown_button.dart';
import '../widgets/np_tax_type_dropdown_button.dart';
import '../widgets/np_unit_dropdown_button.dart';
import '../widgets/price_group_multi_selector.dart';
import '../widgets/price_groups_DSP.dart';
import '../widgets/we2up_text.dart';

class AddProduct extends StatelessWidget {
  const AddProduct({super.key});

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    final productCubit = ProductsCubit.get(context);
    final GlobalKey<FormState> formKey = GlobalKey<FormState>();
    final bool fromPurchase = previousRouteName(context) == purchaseInvoice;
    return GestureDetector(
      onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
      child: Scaffold(
        appBar:
            AppBar(title: We2upText(strings.new_product), centerTitle: true),
        body: Padding(
          padding: EdgeInsets.symmetric(horizontal: 15.sp),
          child: Form(
            key: formKey,
            child: BlocBuilder<SingleProductCubit, SingleProductState>(
              builder: (context, state) {
                final cubit = context.read<SingleProductCubit>();
                return ListView(
                  shrinkWrap: true,
                  children: [
                    if (cubit.pickedImage != null)
                      SizedBox(
                        height: 40.h,
                        child: Image.file(
                          File(cubit.pickedImage!.path),
                          fit: BoxFit.cover,
                        ),
                      ),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Expanded(
                          flex: 4,
                          child: OutlinedButton(
                            onPressed: cubit.pickImage,
                            child: Text(strings.choose_image),
                          ),
                        ),
                        if (cubit.pickedImage != null)
                          Expanded(
                            child: IconButton(
                              onPressed: cubit.removeImage,
                              icon: const Icon(
                                Icons.cancel_outlined,
                                color: Colors.red,
                              ),
                            ),
                          ),
                      ],
                    ),
                    verticalSpacer,
                    CheckboxListTile(
                      enabled: !fromPurchase,
                      title: We2upText(strings.enable_stock),
                      value: cubit.singleProductEnableStock,
                      onChanged: (_) => cubit.changeNPEnableStock(),
                      shape: RoundedRectangleBorder(
                        side: BorderSide(
                          color:
                              Theme.of(context).colorScheme.secondaryContainer,
                          width: 2,
                        ),
                        borderRadius: BorderRadius.circular(10.sp),
                      ),
                    ),
                    if (cubit.singleProductEnableStock) verticalSpacer,
                    if (cubit.singleProductEnableStock)
                      NewProductNumberTextField(
                        controller: cubit.singleProductAlertQuantity,
                        labelText: strings.alert_quantity,
                        icon: Icons.add_alert,
                        isNumber: true,
                      ),
                    const Divider(),
                    NewProductNumberTextField(
                      controller: cubit.singleProductName,
                      labelText: strings.product_name,
                      icon: Icons.text_format_outlined,
                      validate: true,
                      important: true,
                      isNumber: false,
                    ),
                    verticalSpacer,
                    Row(
                      children: [
                        Expanded(
                          child: NewProductNumberTextField(
                            controller: cubit.singleProductSKU,
                            labelText: strings.sku,
                            icon: Icons.qr_code,
                            validate: true,
                            validator: (v) {
                              if (v == null || v.isEmpty) {
                                return null;
                              } else if (productsBox.values
                                  .any((p) => p.sku == v)) {
                                return strings.duplicate_sku;
                              }
                              return null;
                            },
                          ),
                        ),
                        IconButton(
                          onPressed: () => cubit.scanProductBarcode(context),
                          icon: const Icon(Icons.document_scanner),
                        ),
                      ],
                    ),
                    verticalSpacer,
                    TextFormField(
                      controller: cubit.singleProductDescription,
                      decoration: InputDecoration(
                        labelText: strings.product_description,
                        prefixIcon: const Icon(Icons.description),
                        border: myTextFieldBorder,
                      ),
                    ),
                    verticalSpacer,
                    NewProductNumberTextField(
                      controller: cubit.singleProductDPP,
                      labelText: strings.product_single_dpp,
                      icon: Icons.price_change,
                      validate: cubit.singleProductEnableStock,
                      important: cubit.singleProductEnableStock,
                    ),
                    verticalSpacer,
                    NewProductNumberTextField(
                      controller: cubit.singleProductDSP,
                      labelText: strings.product_single_dsp,
                      icon: Icons.monetization_on,
                      validate: true,
                      important: true,
                    ),
                    verticalSpacer,
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        We2upText(strings.priceGroups),
                        Gap(8.sp),
                        Expanded(
                          child: PriceGroupsMultiSelect(
                            selectedPriceGroups: cubit.selectedPriceGroups,
                            onConfirmed: cubit.updateNPPriceGroupList,
                          ),
                        ),
                      ],
                    ),
                    Visibility(
                      visible: cubit.selectedPriceGroups.isNotEmpty,
                      child: Column(
                        children: [
                          verticalSpacer,
                          const PriceGroupDSP(),
                        ],
                      ),
                    ),
                    verticalSpacer,
                    NewProductNumberTextField(
                      controller: cubit.singleProductWeight,
                      labelText: strings.product_weight,
                      icon: Icons.propane_tank,
                    ),
                    verticalSpacer,
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        We2upText(strings.unit),
                        SizedBox(
                          width: 50.w,
                          child: NPUnitDropdownButton(
                            isExpanded: true,
                            onChanged: (u) => cubit.changeNPUnit(u!),
                            unit: cubit.singleProductUnit,
                          ),
                        )
                      ],
                    ),
                    verticalSpacer,
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        We2upText(strings.category),
                        SizedBox(
                          width: 50.w,
                          child: NPCategoryDropdownButton(
                            isExpanded: true,
                            onChanged: cubit.changeNPCategory,
                            category: cubit.singleProductCategory,
                          ),
                        )
                      ],
                    ),
                    verticalSpacer,
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        We2upText(strings.brand),
                        SizedBox(
                          width: 50.w,
                          child: NPBrandDropdownButton(
                            isExpanded: true,
                            onChanged: (b) => cubit.changeNPBrand(b),
                            unit: cubit.singleProductBrand,
                          ),
                        )
                      ],
                    ),
                    verticalSpacer,
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        We2upText(strings.tax_type),
                        SizedBox(
                          width: 50.w,
                          child: NPTaxTypeDropdownButton(
                            isExpanded: true,
                            onChanged: (tt) => cubit.changeNPTaxType(tt!),
                            taxType: cubit.singleProductTaxType,
                          ),
                        )
                      ],
                    ),
                    verticalSpacer,
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        We2upText(strings.tax.replaceFirst(":", "")),
                        SizedBox(
                          width: 50.w,
                          child: NPTaxRateDropdownButton(
                            isExpanded: true,
                            onChanged: cubit.changeNPTax,
                            taxRate: cubit.singleProductTax,
                          ),
                        )
                      ],
                    ),
                    const Divider(),
                    NewProductNumberTextField(
                      controller: cubit.singleProductExpiryPeriod,
                      labelText: strings.expiry_period,
                      icon: Icons.running_with_errors,
                    ),
                    verticalSpacer,
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        We2upText(strings.expiry_period),
                        SizedBox(
                          width: 50.w,
                          child: NPExpiryPeriodDropdownButton(
                            isExpanded: true,
                            onChanged: (ep) =>
                                cubit.changeNPExpiryPeriodType(ep),
                            expiryPeriod: cubit.expiryPeriodType,
                          ),
                        )
                      ],
                    ),
                    const Divider(),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        We2upText(strings.productLocations),
                        Gap(8.sp),
                        Expanded(
                          child: BranchesMultiSelect(
                            selectedBranches: cubit.selectedBranches,
                            onConfirmed: cubit.updateNPBranchesList,
                          ),
                        ),
                      ],
                    ),
                    verticalSpacer,
                    Visibility(
                      visible: cubit.singleProductEnableStock,
                      child: const BranchOpeningStock(),
                    ),
                    OfflineBuilder(
                      connectivityBuilder: (
                          BuildContext context,
                          dynamic connectivity,
                          Widget child,
                          ) {
                        bool connected;

                        if (connectivity is List<ConnectivityResult>) {
                          connected = !connectivity.contains(ConnectivityResult.none);
                        } else if (connectivity is ConnectivityResult) {
                          connected = connectivity != ConnectivityResult.none;
                        } else {
                          connected = false;
                        }

                        return ElevatedButton.icon(
                          icon: connected
                              ? const SizedBox()
                              : Icon(
                            Icons.wifi_off,
                            color: Theme.of(context).colorScheme.error,
                          ),
                          onPressed: connected
                              ? () {
                            if (formKey.currentState!.validate()) {
                              cubit.createNewProduct(fromPurchase: fromPurchase).then((value) {
                                if (value != -1) {
                                  productCubit.initSingleProduct(
                                    value,
                                    fromPurchase: fromPurchase,
                                  );
                                  if (context.mounted) context.pop();
                                }
                              });
                            }
                          }
                              : null,
                          label: We2upText(strings.new_product_button),
                        );
                      },
                      child: const SizedBox(),
                    ),
                    verticalSpacer,
                  ],
                );
              },
            ),
          ),
        ),
      ),
    );
  }
}
