import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:go_router/go_router.dart';
import 'package:responsive_builder/responsive_builder.dart';
import 'package:sizer/sizer.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/data/db/db_manager.dart';

import '../../bloc/auth/auth_cubit.dart';
import '../../utils/route_constants.dart';
import '../../utils/we2up_constants.dart';
import '../style_constants.dart';
import '../widgets/login_layouts/login_button.dart';
import '../widgets/login_layouts/login_form_column.dart';
import '../widgets/login_layouts/we2up_image.dart';

class LoginScreen extends StatelessWidget {
  const LoginScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    final formKey = GlobalKey<FormState>();
    final oauthCubit = context.read<AuthCubit>();
    final usernameController = TextEditingController(
      text: oauthCubit.savedUsername(),
    );
    final passwordController = TextEditingController(
      text: oauthCubit.savedPassword(),
    );
    void handleLoginForm() {
      if (formKey.currentState!.validate()) {
        oauthCubit.login(
          context,
          username: usernameController.text,
          password: passwordController.text,
        );
      }
    }

    return GestureDetector(
      onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
      child: KeyboardListener(
        focusNode: FocusNode(),
        onKeyEvent: (KeyEvent event) {
          if (event is KeyDownEvent &&
              event.logicalKey == LogicalKeyboardKey.enter) {
            handleLoginForm();
          }
        },
        child: Form(
          key: formKey,
          child: BlocConsumer<AuthCubit, AuthState>(
            listener: (context, state) {
              if (state is OauthLoading) {
                EasyLoading.show(status: strings.loading);
              } else if (state is SyncingDataLoading) {
                EasyLoading.show(status: strings.update_start);
              } else if (state is OauthLoaded) {
                if (businessLocationsBox.values.isNotEmpty &&
                    canGoToHomeBasedOnShift()) {
                  EasyLoading.showSuccess(strings.welcome);
                  oauthCubit.saveCredentialsValues(
                    usernameController.text,
                    passwordController.text,
                  );
                  context.go(landingScreen);
                  Future.delayed(const Duration(milliseconds: 100)).then((_) {
                    usernameController.dispose();
                    passwordController.dispose();
                  });
                } else {
                  if (!canGoToHomeBasedOnShift()) {
                    EasyLoading.showError(strings.must_close_old_shifts);
                  } else {
                    EasyLoading.showError(strings.no_business_locations);
                  }
                }
              } else if (state is OauthError) {
                passwordController.clear();
                EasyLoading.showError(strings.wrongCredentials);
              } else {
                EasyLoading.dismiss();
              }
            },
            builder: (context, state) {
              return Scaffold(
                appBar: AppBar(),
                body: ScreenTypeLayout.builder(
                  mobile: (_) => ListView(
                    shrinkWrap: true,
                    padding: EdgeInsets.symmetric(horizontal: 15.sp),
                    children: [
                      const We2upImage(),
                      verticalSpacer,
                      LoginFormColumn(
                        usernameController: usernameController,
                        passwordController: passwordController,
                      ),
                      LoginButton(onPressed: handleLoginForm),
                      verticalSpacer,
                    ],
                  ),
                  desktop: (_) => Row(
                    children: [
                      const Expanded(child: We2upImage()),
                      Expanded(
                        child: Padding(
                          padding: EdgeInsets.symmetric(horizontal: 40.sp),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              LoginFormColumn(
                                usernameController: usernameController,
                                passwordController: passwordController,
                              ),
                              SizedBox(
                                width: 200.sp,
                                child: LoginButton(onPressed: handleLoginForm),
                              ),
                              verticalSpacer,
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}
