import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_offline/flutter_offline.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:sizer/sizer.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/bloc/products/products_cubit.dart';
import 'package:we2up/utils/route_constants.dart';
import 'package:we2up/utils/we2up_constants.dart';

import '../../bloc/contacts/contacts_cubit.dart';
import '../../data/db/db_manager.dart';
import '../../data/models/contact.dart';
import '../style_constants.dart';
import '../widgets/customer_group_dropdown.dart';
import '../widgets/pay_term_type_dropdown_button.dart';
import '../widgets/price_group_list_dropdown_button.dart';
import '../widgets/we2up_text.dart';

class AddEditContact extends StatefulWidget {
  const AddEditContact({super.key, this.contact});

  final Contact? contact;

  @override
  State<AddEditContact> createState() => _AddEditContactState();
}

class _AddEditContactState extends State<AddEditContact> {
  late ContactsCubit cCubit;
  late Contact? contact;
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    cCubit = ContactsCubit.get(context);
    contact = widget.contact;
    cCubit.resetContactsCubitValues();
    if (contact != null) {
      cCubit.initContactsCubitValues(contact!);
    }
    super.initState();
  }

  String? validateField(value, answer) {
    if (value == null || value.isEmpty) {
      return answer;
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
      child: BlocBuilder<ContactsCubit, ContactsState>(
        builder: (context, state) {
          final strings = AppLocalizations.of(context)!;
          final previousRoute = previousRouteName(context);
          if (previousRoute == purchaseInvoice) cCubit.isCustomer = false;
          return Scaffold(
            appBar: AppBar(
              title: We2upText(
                widget.contact != null
                    ? strings.edit_contact
                    : strings.add_contact,
              ),
              centerTitle: true,
            ),
            body: Padding(
              padding: EdgeInsets.symmetric(horizontal: 15.sp),
              child: Form(
                key: _formKey,
                child: Column(
                  children: [
                    Expanded(
                      child: ListView(
                        children: [
                          CheckboxListTile(
                            title: We2upText(strings.is_supplier),
                            enabled: widget.contact == null &&
                                canCreateSupplier() &&
                                canCreateCustomer() &&
                                (previousRoute != billOfSale &&
                                    previousRoute != purchaseInvoice),
                            value: canCreateSupplier() && canCreateCustomer()
                                ? !cCubit.isCustomer
                                : canCreateCustomer(),
                            onChanged: (_) => cCubit.changeType(),
                            shape: RoundedRectangleBorder(
                              side: BorderSide(
                                color: Theme.of(context)
                                    .colorScheme
                                    .secondaryContainer,
                                width: 2,
                              ),
                              borderRadius: BorderRadius.circular(10.sp),
                            ),
                          ),
                          Gap(8.sp),
                          ListTile(
                            title: We2upText(strings.selected_location),
                            trailing: const Icon(Icons.edit_location_alt),
                            subtitle: cCubit.position == null
                                ? null
                                : We2upText(
                                    "${cCubit.position!.longitude} , "
                                    "${cCubit.position!.latitude}",
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                            shape: RoundedRectangleBorder(
                              side: BorderSide(
                                color: Theme.of(context).colorScheme.tertiary,
                                width: 2,
                              ),
                              borderRadius: BorderRadius.circular(10.sp),
                            ),
                            onTap: () async {
                              final selectedLocation =
                                  await getLocationWithPermission(context);
                              cCubit.updateSelectedLocation(selectedLocation);
                            },
                          ),
                          Column(
                            children: [
                              verticalSpacer,
                              TextFormField(
                                controller: cCubit.contactCode,
                                validator: (v) {
                                  if (contactsBox.values.any(
                                    (element) =>
                                        element.contactId?.toLowerCase() ==
                                            v?.toLowerCase() &&
                                        element != contact,
                                  )) {
                                    return strings.code_already_used;
                                  }
                                  return null;
                                },
                                decoration: InputDecoration(
                                  labelText: cCubit.isCustomer
                                      ? strings.customer_code
                                      : strings.supplier_code,
                                  prefixIcon: const Icon(Icons.code_sharp),
                                  border: myTextFieldBorder,
                                ),
                              ),
                              verticalSpacer,
                              TextFormField(
                                controller:
                                    cCubit.supplierBusinessNameController,
                                validator: !cCubit.isCustomer
                                    ? (v) => validateField(
                                        v, strings.field_cannot_be_empty)
                                    : null,
                                decoration: InputDecoration(
                                  labelText: strings.supplier_name_note,
                                  prefixIcon: const Icon(
                                    Icons.drive_file_rename_outline,
                                  ),
                                  border: myTextFieldBorder,
                                ),
                              ),
                            ],
                          ),
                          const Divider(),
                          if (cCubit.isCustomer)
                            Column(
                              children: [
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    We2upText("${strings.price_group_list}:"),
                                    SizedBox(width: 8.sp),
                                    Expanded(
                                      child: Container(
                                        decoration: dropDownDecoration(context),
                                        child: PriceGroupListDropdown(
                                          onChanged:
                                              cCubit.changeCartPriceGroup,
                                          value: cCubit.selectedPriceGroup,
                                          isExpanded: true,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                verticalSpacer,
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    We2upText("${strings.customer_group}:"),
                                    SizedBox(width: 8.sp),
                                    Expanded(
                                      child: CustomerGroupDropdown(
                                        onChanged: cCubit.changeCustomerGroup,
                                        value: cCubit.selectedCustomerGroup,
                                        isExpanded: true,
                                      ),
                                    ),
                                  ],
                                ),
                                const Divider(),
                              ],
                            ),
                          TextFormField(
                            validator: (v) =>
                                validateField(v, strings.field_cannot_be_empty),
                            controller: cCubit.firstNameController,
                            decoration: InputDecoration(
                              labelText: strings.first_name,
                              prefixIcon: const Icon(Icons.person),
                              suffixIcon: importantIcon(context),
                              border: myTextFieldBorder,
                            ),
                          ),
                          verticalSpacer,
                          TextFormField(
                            controller: cCubit.lastNameController,
                            decoration: InputDecoration(
                              labelText: strings.last_name,
                              prefixIcon: const Icon(Icons.person),
                              border: myTextFieldBorder,
                            ),
                          ),
                          verticalSpacer,
                          TextFormField(
                            validator: (v) {
                              if (v == null || v.isEmpty) {
                                return strings.field_cannot_be_empty;
                              }
                              if (contactsBox.values.any(
                                (element) =>
                                    element.mobile == v && element != contact,
                              )) {
                                return strings.phoneNumberAlreadyUsed;
                              }
                              return null;
                            },
                            controller: cCubit.mobileNumberController,
                            keyboardType: TextInputType.number,
                            decoration: InputDecoration(
                              labelText: strings.mobile_number,
                              prefixIcon: const Icon(Icons.mobile_friendly),
                              suffixIcon: importantIcon(context),
                              border: myTextFieldBorder,
                            ),
                          ),
                          verticalSpacer,
                          TextFormField(
                            controller: cCubit.alternateNumberController,
                            keyboardType: TextInputType.number,
                            decoration: InputDecoration(
                              labelText: strings.alternate_mobile_number,
                              prefixIcon: const Icon(Icons.mobile_friendly),
                              border: myTextFieldBorder,
                            ),
                          ),
                          verticalSpacer,
                          TextFormField(
                            controller: cCubit.landlineNumberController,
                            keyboardType: TextInputType.number,
                            decoration: InputDecoration(
                              labelText: strings.landline_number,
                              prefixIcon: const Icon(Icons.mobile_friendly),
                              border: myTextFieldBorder,
                            ),
                          ),
                          verticalSpacer,
                          TextFormField(
                            controller: cCubit.emailController,
                            keyboardType: TextInputType.emailAddress,
                            decoration: InputDecoration(
                              labelText: strings.email_address,
                              prefixIcon: const Icon(Icons.email),
                              border: myTextFieldBorder,
                            ),
                          ),
                          verticalSpacer,
                          const Divider(),
                          verticalSpacer,
                          TextFormField(
                            controller: cCubit.taxNumberController,
                            keyboardType: TextInputType.number,
                            decoration: InputDecoration(
                              labelText: strings.tax_number,
                              prefixIcon: const Icon(Icons.food_bank_rounded),
                              border: myTextFieldBorder,
                            ),
                          ),
                          verticalSpacer,
                          TextFormField(
                            controller: cCubit.countryController,
                            decoration: InputDecoration(
                              labelText: strings.country,
                              prefixIcon: const Icon(Icons.flag),
                              border: myTextFieldBorder,
                            ),
                          ),
                          verticalSpacer,
                          TextFormField(
                            controller: cCubit.cityController,
                            decoration: InputDecoration(
                              labelText: strings.city,
                              prefixIcon: const Icon(Icons.pin_drop),
                              border: myTextFieldBorder,
                            ),
                          ),
                          verticalSpacer,
                          TextFormField(
                            controller: cCubit.zipCodeController,
                            keyboardType: TextInputType.number,
                            decoration: InputDecoration(
                              labelText: strings.zip_code,
                              prefixIcon: const Icon(Icons.pin_drop),
                              border: myTextFieldBorder,
                            ),
                          ),
                          verticalSpacer,
                          TextFormField(
                            controller: cCubit.shippingAddressController,
                            decoration: InputDecoration(
                              labelText: strings.shipping_address,
                              prefixIcon: const Icon(Icons.local_shipping),
                              border: myTextFieldBorder,
                            ),
                          ),
                          verticalSpacer,
                          const Divider(),
                          verticalSpacer,
                          PayTermTypeDropdownButton(
                            payTermType: cCubit.payTermType,
                            onChanged: (p) => cCubit.onPayTermTypeChanged(p),
                          ),
                          cCubit.payTermType != null
                              ? verticalSpacer
                              : const SizedBox(),
                          Visibility(
                            visible: cCubit.payTermType != null,
                            child: SizedBox(
                              width: 90.w,
                              height: 50.sp,
                              child: TextFormField(
                                controller: cCubit.payTermNumberController,
                                keyboardType: TextInputType.number,
                                decoration: InputDecoration(
                                  labelText: strings.pay_term_number,
                                  prefixIcon: const Icon(Icons.numbers),
                                  border: myTextFieldBorder,
                                ),
                              ),
                            ),
                          ),
                          verticalSpacer,
                          const Divider(),
                          verticalSpacer,
                          TextFormField(
                            enabled: widget.contact?.openingBalancePaid == null,
                            controller: cCubit.openingBalanceController,
                            keyboardType: TextInputType.number,
                            decoration: InputDecoration(
                              labelText: strings.opening_balance,
                              prefixIcon: const Icon(Icons.money),
                              border: myTextFieldBorder,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      width: double.infinity,
                      padding: EdgeInsets.all(10.sp),
                      child: OfflineBuilder(
                        connectivityBuilder: (
                          BuildContext context,
                          dynamic connectivity,
                          Widget child,
                        ) {
                          bool connected;
                          if (connectivity is List<ConnectivityResult>) {
                            connected =
                                !connectivity.contains(ConnectivityResult.none);
                          } else if (connectivity is ConnectivityResult) {
                            connected = connectivity != ConnectivityResult.none;
                          } else {
                            connected = false;
                          }
                          return ElevatedButton.icon(
                            icon: connected
                                ? const SizedBox()
                                : Icon(
                                    Icons.wifi_off,
                                    color: Theme.of(context).colorScheme.error,
                                  ),
                            onPressed: connected
                                ? () => sendContactToAPI(previousRoute)
                                : null,
                            label: We2upText(
                              widget.contact != null
                                  ? strings.edit_contact_button
                                  : strings.add_contact_button,
                            ),
                          );
                        },
                        child: const SizedBox(),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  void sendContactToAPI(previousRoute) {
    if (_formKey.currentState!.validate()) {
      cCubit.sendContactToAPI(contact?.id).then((v) {
        if (v != -1) {
          final pCubit = ProductsCubit.get(context);
          final c = contactsBox.get(v)!;
          cCubit.position = null;
          if (previousRoute == billOfSale) {
            pCubit.changeSelectedContact(c);
          } else if (previousRoute == purchaseInvoice) {
            pCubit.changeSelectedContact(c);
          }
          context.pop();
        }
      });
    }
  }
}
