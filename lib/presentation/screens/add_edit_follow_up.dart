import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:sizer/sizer.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/bloc/crm/crm_cubit.dart';
import 'package:we2up/data/models/follow_up.dart';
import 'package:we2up/utils/we2up_constants.dart';

import '../../data/db/db_manager.dart';
import '../style_constants.dart';
import '../widgets/contacts_dropdown_button.dart';
import '../widgets/crm_notify_type_dropdown_button.dart';
import '../widgets/crm_schedule_type_dropdown_button.dart';
import '../widgets/crm_status_dropdown_button.dart';
import '../widgets/user_dropdown_button.dart';
import '../widgets/we2up_text.dart';

class AddEditFollowUp extends StatefulWidget {
  const AddEditFollowUp({super.key, this.followup});

  final FollowUp? followup;

  @override
  State<AddEditFollowUp> createState() => _AddEditFollowUpState();
}

class _AddEditFollowUpState extends State<AddEditFollowUp> {
  late CrmCubit crmCubit;
  late FollowUp? followup;
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final bool isAdmin = loginData.isAdmin;

  @override
  void initState() {
    crmCubit = CrmCubit.get(context);
    followup = widget.followup;
    crmCubit.initFollowUpFields(followUp: followup);
    super.initState();
  }

  String? validateField(value, answer) {
    if (value == null || value.isEmpty) {
      return answer;
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
      child: BlocBuilder<CrmCubit, CrmState>(
        builder: (context, state) {
          final strings = AppLocalizations.of(context)!;
          final colorScheme = Theme.of(context).colorScheme;
          final String? offlineID =
              followup?.offline == true ? followup?.refNo : null;
          return Scaffold(
            appBar: AppBar(
              title: We2upText(
                widget.followup != null
                    ? strings.edit_follow_up
                    : strings.add_follow_up,
              ),
              centerTitle: true,
            ),
            body: Padding(
              padding: EdgeInsets.symmetric(horizontal: 10.sp),
              child: Form(
                key: _formKey,
                child: Column(
                  children: [
                    Expanded(
                      child: ListView(
                        children: [
                          verticalSpacer,
                          Row(
                            children: [
                              We2upText("${strings.user_name}:"),
                              Gap(8.sp),
                              Expanded(
                                child: UsersDropdownButton(
                                  user: crmCubit.user,
                                  onChanged: crmCubit.updateSelectedUser,
                                  controller:
                                      crmCubit.searchSelectedUserController,
                                ),
                              ),
                            ],
                          ),
                          const Divider(),
                          // title
                          SizedBox(
                            width: 90.w,
                            height: 50.sp,
                            child: TextFormField(
                              controller: crmCubit.titleController,
                              validator: (v) => validateField(
                                  v, strings.field_cannot_be_empty),
                              decoration: InputDecoration(
                                labelText: strings.title,
                                prefixIcon: const Icon(Icons.title),
                                border: myTextFieldBorder,
                              ),
                            ),
                          ),
                          verticalSpacer,
                          // agent_name
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              We2upText(strings.agent_name),
                              SizedBox(
                                width: 50.w,
                                child: ContactsDropdownButton(
                                  onChanged: crmCubit.customerContactChanged,
                                  contact: crmCubit.agentContact,
                                ),
                              )
                            ],
                          ),
                          verticalSpacer,
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              We2upText(strings.schedule_type),
                              SizedBox(
                                width: 50.w,
                                child: CrmScheduleTypeDropdownButton(
                                  followupType: crmCubit.followUpType,
                                  isExpanded: true,
                                  onChanged: (f) =>
                                      crmCubit.changeFollowUpType(f!),
                                ),
                              )
                            ],
                          ),
                          verticalSpacer,
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              We2upText(strings.status),
                              SizedBox(
                                width: 50.w,
                                child: CrmStatusDropdownButton(
                                  status: crmCubit.status,
                                  isExpanded: true,
                                  onChanged: (s) => crmCubit.changeStatus(s!),
                                ),
                              )
                            ],
                          ),
                          verticalSpacer,
                          // description
                          SizedBox(
                            width: 90.w,
                            height: 50.sp,
                            child: TextFormField(
                              controller: crmCubit.descriptionController,
                              decoration: InputDecoration(
                                labelText: strings.description,
                                prefixIcon: const Icon(Icons.description),
                                border: myTextFieldBorder,
                              ),
                            ),
                          ),
                          const Divider(),
                          CheckboxListTile(
                            enabled: isAdmin,
                            title: We2upText("${strings.notify_via} ${strings.sms}"),
                            value: crmCubit.notifyViaSMS,
                            onChanged: (_) => crmCubit.changeNotifyViaSMS(),
                            shape: RoundedRectangleBorder(
                              side: BorderSide(
                                color: colorScheme.secondaryContainer,
                                width: 2,
                              ),
                              borderRadius: BorderRadius.circular(10.sp),
                            ),
                          ),
                          verticalSpacer,
                          CheckboxListTile(
                            enabled: isAdmin,
                            title:
                                We2upText("${strings.notify_via} ${strings.email}"),
                            value: crmCubit.notifyViaEmail,
                            onChanged: (_) => crmCubit.changeNotifyViaEmail(),
                            shape: RoundedRectangleBorder(
                              side: BorderSide(
                                color: colorScheme.secondaryContainer,
                                width: 2,
                              ),
                              borderRadius: BorderRadius.circular(10.sp),
                            ),
                          ),
                          const Divider(),
                          SizedBox(
                            width: 90.w,
                            height: 50.sp,
                            child: TextFormField(
                              keyboardType: TextInputType.number,
                              controller: crmCubit.notifyBeforeController,
                              decoration: InputDecoration(
                                labelText: strings.notify_before,
                                prefixIcon:
                                    const Icon(Icons.edit_notifications),
                                border: myTextFieldBorder,
                              ),
                              inputFormatters: <TextInputFormatter>[
                                FilteringTextInputFormatter.digitsOnly,
                              ],
                            ),
                          ),
                          verticalSpacer,
                          CrmNotifyTypeDropdownButton(
                            notifyType: crmCubit.notifyType,
                            isExpanded: true,
                            onChanged: (n) => crmCubit.changeNotifyType(n!),
                          ),
                          const Divider(),
                          ListTile(
                            title: We2upText(strings.start_date_time),
                            trailing: const Icon(Icons.access_time_outlined),
                            onLongPress: () => crmCubit.changeStartDate(null),
                            onTap: () {
                              final currentDate = DateTime.now();
                              final oneYearAgo = currentDate.subtract(
                                const Duration(days: 60),
                              );

                              final selectedDate = showDatePicker(
                                context: context,
                                initialDate: crmCubit.startDate ?? currentDate,
                                firstDate: oneYearAgo,
                                lastDate: currentDate.add(
                                  const Duration(days: 60),
                                ),
                              );

                              selectedDate.then((pickedDate) {
                                if (pickedDate != null) {
                                  showTimePicker(
                                    context: context,
                                    initialTime: TimeOfDay.fromDateTime(
                                      crmCubit.startDate ?? currentDate,
                                    ),
                                  ).then((pickedTime) {
                                    if (pickedTime != null) {
                                      final combinedDate = DateTime(
                                        pickedDate.year,
                                        pickedDate.month,
                                        pickedDate.day,
                                        pickedTime.hour,
                                        pickedTime.minute,
                                      );
                                      crmCubit.changeStartDate(combinedDate);
                                    }
                                  });
                                }
                              });
                            },
                            subtitle: We2upText(
                              formatDate(crmCubit.startDate ?? DateTime.now()),
                            ),
                          ),
                          ListTile(
                            title: We2upText(strings.end_date_time),
                            trailing: const Icon(Icons.access_time_outlined),
                            onLongPress: () => crmCubit.changeStartDate(null),
                            onTap: () {
                              final currentDate = DateTime.now();
                              final oneYearAgo = currentDate.subtract(
                                const Duration(days: 60),
                              );

                              final selectedDate = showDatePicker(
                                context: context,
                                initialDate: crmCubit.endDate ?? currentDate,
                                firstDate: oneYearAgo,
                                lastDate: currentDate.add(
                                  const Duration(days: 60),
                                ),
                              );

                              selectedDate.then((pickedDate) {
                                if (pickedDate != null) {
                                  showTimePicker(
                                    context: context,
                                    initialTime: TimeOfDay.fromDateTime(
                                      crmCubit.endDate ?? currentDate,
                                    ),
                                  ).then((pickedTime) {
                                    if (pickedTime != null) {
                                      final combinedDate = DateTime(
                                        pickedDate.year,
                                        pickedDate.month,
                                        pickedDate.day,
                                        pickedTime.hour,
                                        pickedTime.minute,
                                      );
                                      crmCubit.changeEndDate(combinedDate);
                                    }
                                  });
                                }
                              });
                            },
                            subtitle: We2upText(
                              formatDate(crmCubit.endDate ?? DateTime.now()),
                            ),
                          ),
                          const Divider(),
                          // allow_notification
                          if (isAdmin)
                            CheckboxListTile(
                              title: We2upText(strings.allow_notification),
                              value: crmCubit.allowNotification,
                              onChanged: (_) =>
                                  crmCubit.changeAllowNotification(),
                              shape: RoundedRectangleBorder(
                                side: BorderSide(
                                  color: colorScheme.secondaryContainer,
                                  width: 2,
                                ),
                                borderRadius: BorderRadius.circular(10.sp),
                              ),
                            ),
                          const Divider(),
                          ElevatedButton(
                            onPressed: crmCubit.agentContact != null &&
                                    crmCubit.user != null
                                ? () {
                                    if (_formKey.currentState!.validate()) {
                                      crmCubit
                                          .sendFollowUp(
                                        followUpID: followup?.id,
                                        offlineID: offlineID,
                                      )
                                          .then((v) {
                                        if (v) context.pop();
                                      });
                                    }
                                  }
                                : null,
                            child: We2upText(
                              widget.followup != null
                                  ? strings.edit_follow_up
                                  : strings.add_follow_up,
                            ),
                          ),
                          verticalSpacer,
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
