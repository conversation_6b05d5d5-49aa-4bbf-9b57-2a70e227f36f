import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:go_router/go_router.dart';
import 'package:sizer/sizer.dart';
import 'package:we2up/bloc/contacts/contacts_cubit.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/data/db/db_manager.dart';
import 'package:we2up/data/models/contact.dart';
import 'package:we2up/utils/route_constants.dart';
import 'package:we2up/utils/we2up_constants.dart';

import '../widgets/contact_details_card.dart';
import '../widgets/filter_contacts_dialog.dart';
import '../widgets/we2up_text.dart';

class ContactsPage extends StatelessWidget {
  const ContactsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
      child: BlocBuilder<ContactsCubit, ContactsState>(
        builder: (context, state) {
          final cubit = ContactsCubit.get(context);
          final strings = AppLocalizations.of(context)!;
          return Scaffold(
            appBar: AppBar(
              title: We2upText(strings.contacts),
              centerTitle: true,
              actions: [
                IconButton(
                  onPressed: () => showDialog(
                    context: context,
                    builder: (_) => BlocProvider.value(
                      value: cubit,
                      child: const FilterContactsDialog(),
                    ),
                  ),
                  icon: const Icon(Icons.filter_list_alt),
                ),
                IconButton(
                  onPressed: () async {
                    EasyLoading.show(
                      indicator: const CircularProgressIndicator(),
                      status: "Loading..",
                    );
                    await fetchContactsAndStoreInHive(refresh: true);
                    EasyLoading.dismiss();
                    cubit.updateList();
                  },
                  icon: const Icon(Icons.refresh),
                ),
              ],
            ),
            body: Padding(
              padding: EdgeInsets.symmetric(horizontal: 10.sp),
              child: ListView(
                children: [
                  ...cubit.filteredContacts().map((Contact contact) {
                    return ContactDetailsCard(contact: contact);
                  }),
                  SizedBox(height: 60.sp),
                ],
              ),
            ),
            floatingActionButton:
                (canCreateSupplier() || canCreateCustomer()) &&
                        loginData.isContactsReady
                    ? FloatingActionButton(
                        onPressed: () async => await checkShiftLocationAccess(
                          context,
                          () => context.push(
                            addEditContact,
                            extra: (null, cubit, false),
                          ),
                        ),
                        child: const Icon(Icons.person_add_alt_1),
                      )
                    : null,
          );
        },
      ),
    );
  }
}
