import 'package:flutter/material.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'dart:typed_data';

class PDFViewPage extends StatelessWidget {
  final Uint8List pdfData;

  const PDFViewPage({super.key, required this.pdfData});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('PDF View'),
      ),
      body: PDFViewWidget(pdfData: pdfData),
    );
  }
}

class PDFViewWidget extends StatefulWidget {
  final Uint8List pdfData;

  const PDFViewWidget({super.key, required this.pdfData});

  @override
  State<PDFViewWidget> createState() => _PDFViewWidgetState();
}

class _PDFViewWidgetState extends State<PDFViewWidget> {
  bool _isReady = false;
  String _errorMessage = '';

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        PDFView(
          pdfData: widget.pdfData,
          onRender: (pages) {
            if (pages != null) {
              setState(() => _isReady = true);
            }
          },
          onError: (error) => setState(() => _errorMessage = error.toString()),
          onPageError: (page, error) {
            setState(() => _errorMessage = '$page: ${error.toString()}');
          },
        ),
        _errorMessage.isEmpty
            ? !_isReady
                ? const Center(child: CircularProgressIndicator())
                : const SizedBox()
            : Center(child: Text(_errorMessage))
      ],
    );
  }
}
