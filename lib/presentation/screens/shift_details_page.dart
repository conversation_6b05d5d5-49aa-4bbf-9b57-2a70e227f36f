import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:go_router/go_router.dart';
import 'package:sizer/sizer.dart';
import 'package:we2up/bloc/shifts/shifts_cubit.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/presentation/screens/shifts.dart';
import 'package:we2up/presentation/widgets/my_loading_indicator.dart';

import '../../data/db/db_manager.dart';
import '../../data/models/business_settings.dart';
import '../../data/models/login_data.dart';
import '../../utils/we2up_constants.dart';
import '../style_constants.dart';
import '../widgets/we2up_text.dart';

class ShiftDetailsPage extends StatelessWidget {
  const ShiftDetailsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    final cubit = context.read<ShiftsCubit>();
    return Scaffold(
      appBar: AppBar(title: Text(strings.shift_details)),
      body: Padding(
        padding: EdgeInsets.all(5.sp),
        child: BlocBuilder<ShiftsCubit, ShiftsState>(
          buildWhen: (_, current) =>
              current is ShiftDetailsLoading ||
              current is ShiftDetailsError ||
              current is ShiftDetailsLoaded,
          builder: (context, state) {
            if (state is ShiftDetailsLoading) {
              return const MyLoadingIndicator();
            } else if (state is ShiftDetailsError) {
              return const Center(child: Text("Error!!"));
            } else if (state is ShiftDetailsLoaded) {
              final payments = state.shiftDetails.payments.first;
              final transactions = state.shiftDetails.transactions.first;
              final notRelatedPayments =
                  state.shiftDetails.notRelatedPayments.first;
              final shiftSupplierPayment =
                  (double.parse(payments.totalPurchasePaid) +
                      double.parse(notRelatedPayments.totalSupplierPaid));
              final shiftCustomerCollection =
                  (double.parse(payments.totalSellPaid) +
                      double.parse(notRelatedPayments.totalCustomerPaid));
              final remainingCash = (shiftCustomerCollection +
                      double.parse(payments.totalExpenseRefundPaid) +
                      double.parse(payments.totalPurchaseReturnPaid)) -
                  (shiftSupplierPayment +
                      double.parse(payments.totalExpensePaid) +
                      double.parse(payments.totalSellReturnPaid));
              return ListView(
                shrinkWrap: true,
                children: [
                  ShiftTile(
                    shift: cubit.currentShift!,
                    cubit: cubit,
                    clickable: false,
                  ),
                  const Divider(),
                  // sales and purchases
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      Flexible(
                        child: Card(
                          surfaceTintColor:
                              Theme.of(context).colorScheme.surface,
                          shape: homeCardShape,
                          child: Padding(
                            padding: EdgeInsets.all(5.sp),
                            child: Column(
                              children: [
                                Center(
                                  child: We2upText(
                                    strings.sales,
                                    style: TextStyle(
                                      fontSize: 16.sp,
                                      color:
                                          Theme.of(context).colorScheme.primary,
                                    ),
                                  ),
                                ),
                                const Divider(),
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Expanded(
                                      child: We2upText(
                                        strings.total,
                                        maxLines: 2,
                                      ),
                                    ),
                                    We2upText(transactions.totalSell),
                                  ],
                                ),
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Expanded(
                                      child: We2upText(
                                        strings.paid_up,
                                        maxLines: 2,
                                      ),
                                    ),
                                    We2upText(payments.totalSellPaid),
                                  ],
                                ),
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Expanded(
                                      child: We2upText(
                                        strings.due,
                                        maxLines: 2,
                                      ),
                                    ),
                                    We2upText(
                                      "${double.parse(transactions.totalSell) - double.parse(payments.totalSellPaid)}",
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      Flexible(
                        child: Card(
                          surfaceTintColor:
                              Theme.of(context).colorScheme.surface,
                          shape: homeCardShape,
                          child: Padding(
                            padding: EdgeInsets.all(5.sp),
                            child: Column(
                              children: [
                                We2upText(
                                  strings.purchases,
                                  style: TextStyle(
                                    fontSize: 16.sp,
                                    color:
                                        Theme.of(context).colorScheme.primary,
                                  ),
                                ),
                                const Divider(),
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Expanded(
                                      child: We2upText(
                                        strings.total,
                                        maxLines: 2,
                                      ),
                                    ),
                                    We2upText(transactions.totalPurchase),
                                  ],
                                ),
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Expanded(
                                      child: We2upText(
                                        strings.paid_up,
                                        maxLines: 2,
                                      ),
                                    ),
                                    We2upText(payments.totalPurchasePaid),
                                  ],
                                ),
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Expanded(
                                      child: We2upText(
                                        strings.due,
                                        maxLines: 2,
                                      ),
                                    ),
                                    We2upText(
                                      "${double.parse(transactions.totalPurchase) - double.parse(payments.totalPurchasePaid)}",
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  // summary
                  Padding(
                    padding: EdgeInsets.all(10.sp),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        We2upText(
                          strings.rosary_summary,
                          style: TextStyle(
                            fontSize: 16.sp,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                        ),
                        const Divider(),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Flexible(
                              child: Column(
                                children: [
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Expanded(
                                        child: We2upText(
                                          strings.modern_payment,
                                          maxLines: 2,
                                        ),
                                      ),
                                      We2upText("$shiftSupplierPayment"),
                                    ],
                                  ),
                                  const Divider(),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      We2upText("${strings.expenses} :"),
                                      We2upText(payments.totalExpensePaid),
                                    ],
                                  ),
                                  const Divider(),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      We2upText("${strings.sales_returns} :"),
                                      We2upText(payments.totalSellReturnPaid),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(width: 15.sp),
                            Flexible(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.start,
                                mainAxisSize: MainAxisSize.max,
                                children: [
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Expanded(
                                        child: We2upText(
                                          strings.customer_collection,
                                          maxLines: 2,
                                        ),
                                      ),
                                      We2upText("$shiftCustomerCollection"),
                                    ],
                                  ),
                                  const Divider(),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      We2upText("${strings.revenues} :"),
                                      We2upText(
                                          payments.totalExpenseRefundPaid),
                                    ],
                                  ),
                                  const Divider(),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      We2upText(
                                          "${strings.purchase_returns} :"),
                                      We2upText(
                                          payments.totalPurchaseReturnPaid),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        const Divider(),
                        We2upText(
                          "${strings.cash_left} "
                          "$remainingCash ${currencySymbol()}",
                        ),
                      ],
                    ),
                  ),
                  const Divider(),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 10.sp),
                    child: FilledButton(
                      style: FilledButton.styleFrom(
                        backgroundColor:
                            Theme.of(context).colorScheme.errorContainer,
                      ),
                      onPressed: cubit.currentShift?.status == "close"
                          ? null
                          : () {
                              EasyLoading.show(status: strings.loading);
                              cubit.closeShift(remainingCash).then((success) {
                                if (success) {
                                  EasyLoading.dismiss();
                                  cubit.shiftUpdated();
                                  if (cubit.currentShift?.userId ==
                                      loginData.userId) {
                                    // Update the Hive box directly to ensure the ValueListenableBuilder is notified
                                    final updatedLoginData = LoginData(
                                      loginData.tokenType,
                                      loginData.expiresIn,
                                      loginData.accessToken,
                                      loginData.refreshToken,
                                      loginData.userId,
                                      loginData.clockedIn,
                                      loginData.clockedInTime,
                                      loginData.isAdmin,
                                      loginData.permissions,
                                      false, // cashRegistered = false
                                      null, // cashRegisteredTime = null
                                      null, // cashRegisteredId = null
                                      loginData.allowOverSelling,
                                      loginData.locationRequired,
                                      loginData.username,
                                      loginData.password,
                                      loginData.defaultAccount,
                                      loginData.inSync,
                                      loginData.permittedLocations,
                                      loginData.lastUpdateTime,
                                      loginData.isProductsReady,
                                      loginData.isContactsReady,
                                      loginData.commAgntAllowed,
                                      loginData.tablesAllowed,
                                      loginData.serviceStaffAllowed,
                                      loginData.transactionEditDays,
                                      loginData.isCommissionAgentRequired,
                                    );
                                    credentialsBox.put(loginDataKey, updatedLoginData);
                                  }
                                  if(context.mounted )context.pop();
                                } else {
                                  EasyLoading.showError("Error!!");
                                }
                              });
                            },
                      child: Text(
                        strings.close_shift,
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                    ),
                  ),
                ],
              );
            }
            return const SizedBox();
          },
        ),
      ),
    );
  }
}
