import 'package:flutter/material.dart';
import 'package:we2up/presentation/widgets/printer_alert_dialog.dart';


class LabelPrinterScreen extends StatelessWidget {
  const LabelPrinterScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("Label Printer"),
      ),
      body: const Column(
        children: [],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          showDialog(
            context: context,
            builder: (context) {
              return const PrinterAlertDialog();
            },
          );
        },
      ),
    );
  }
}
