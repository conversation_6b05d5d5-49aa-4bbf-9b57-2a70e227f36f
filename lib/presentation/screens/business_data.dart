import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:sizer/sizer.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/presentation/style_constants.dart';

import '../../bloc/business_settings/business_settings_cubit.dart';
import '../widgets/business_data/business_image_section.dart';
import '../widgets/business_data/business_info_section.dart';
import '../widgets/business_data/business_options_section.dart';
import '../widgets/business_data/print_discount_settings_section.dart';
import '../widgets/business_data/printer_settings_section.dart';
import '../widgets/we2up_text.dart';

class BusinessDataPage extends StatelessWidget {
  const BusinessDataPage({super.key});

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    return GestureDetector(
      onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
      child: Scaffold(
        appBar: AppBar(
          title: We2upText(strings.business_data),
          centerTitle: true,
        ),
        body: BlocBuilder<BusinessSettingsCubit, BusinessSettingsState>(
          builder: (context, state) {
            if (state.isLoading) {
              return const Center(child: CircularProgressIndicator());
            }

            if (state.error != null) {
              return Center(child: Text(state.error!));
            }

            return SingleChildScrollView(
              padding: EdgeInsets.symmetric(horizontal: 10.sp),
              child: Column(
                children: [
                  const BusinessImageSection(),
                  verticalSpacer,
                  const BusinessInfoSection(),
                  verticalSpacer,
                  const BusinessOptionsSection(),
                  verticalSpacer,
                  const PrinterSettingsSection(),
                  const PrintAndDiscountSettingsSection(),
                ],
              ),
            );
          },
        ),
      ),
    );
  }
}
