import 'package:conditional_builder_null_safety/conditional_builder_null_safety.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:go_router/go_router.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:responsive_grid/responsive_grid.dart';
import 'package:sizer/sizer.dart';
import 'package:we2up/bloc/products/products_cubit.dart';
import 'package:we2up/data/db/db_manager.dart';
import 'package:we2up/data/models/product.dart';
import 'package:we2up/presentation/widgets/product_card_item.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

import '../../utils/route_constants.dart';
import '../style_constants.dart';
import '../widgets/filter_dialog.dart';
import '../widgets/we2up_text.dart';

class ProductsPage extends StatelessWidget {
  const ProductsPage({super.key});

  @override
  Widget build(BuildContext context) {
    ProductsCubit cubit = ProductsCubit.get(context);
    final strings = AppLocalizations.of(context)!;
    return GestureDetector(
      onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
      child: Scaffold(
        appBar: PreferredSize(
          preferredSize: Size.fromHeight(80.sp),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            mainAxisSize: MainAxisSize.max,
            children: <Widget>[
              Expanded(
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 5.sp),
                  child: TextField(
                    controller: cubit.filterSearchController,
                    onChanged: (s) => cubit.updateSearchFilter(),
                    decoration: InputDecoration(
                      hintText: strings.search,
                      prefixIcon: const Icon(Icons.search),
                      suffixIcon: IconButton(
                        onPressed: () => showDialog(
                          context: context,
                          builder: (_) => const FilterDialog(),
                        ),
                        icon: Icon(
                          FontAwesomeIcons.sliders,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                      border: myTextFieldBorder,
                    ),
                  ),
                ),
              ),
              IconButton(
                icon: Icon(
                  Icons.document_scanner_outlined,
                  color: Theme.of(context).colorScheme.primary,
                ),
                onPressed: () => cubit.scanBarcode(context),
              ),
              Visibility(
                visible: canCreateProduct(),
                child: FutureBuilder(
                  future: InternetConnectionChecker.instance.hasConnection,
                  builder: (context, snapshot) {
                    return IconButton(
                      onPressed: snapshot.hasData &&
                              snapshot.data! &&
                              loginData.isProductsReady
                          ? () => context.push(
                                addProductPage,
                                extra: cubit.selectedLocation,
                              )
                          : null,
                      icon: Icon(
                        Icons.add_business_outlined,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
        body: BlocBuilder<ProductsCubit, ProductsState>(
          builder: (context, state) {
            final List<Product> productList =
                cubit.filteredProductList(isPurchase: true);
            return ConditionalBuilder(
              condition: productList.isNotEmpty,
              builder: (context) {
                return ConditionalBuilder(
                  condition: cubit.isList,
                  builder: (_) => ListView.builder(
                    itemCount: productList.length,
                    itemBuilder: (context, index) {
                      return ProductCard(id: productList.elementAt(index).id);
                    },
                  ),
                  fallback: (_) => ResponsiveGridList(
                    desiredItemWidth: 90.sp,
                    minSpacing: 8.sp,
                    children: productList.map((p) {
                      // return ProductGridCard(id: p.id);
                      return const SizedBox();
                    }).toList(),
                  ),
                );
              },
              fallback: (_) => Center(child: We2upText(strings.no_products)),
            );
          },
        ),
      ),
    );
  }
}
