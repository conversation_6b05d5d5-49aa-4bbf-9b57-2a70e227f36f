import 'package:conditional_builder_null_safety/conditional_builder_null_safety.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:sizer/sizer.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/bloc/expenses/expenses_cubit.dart';
import 'package:we2up/presentation/widgets/expense_filter_dialog.dart';

import '../../data/models/expense.dart';
import '../widgets/expense_details_card.dart';
import '../widgets/show_total.dart';
import '../widgets/we2up_text.dart';

class ExpensesPage extends StatefulWidget {
  const ExpensesPage({super.key});

  @override
  State<ExpensesPage> createState() => _ExpensesPageState();
}

class _ExpensesPageState extends State<ExpensesPage> {
  late final ExpensesCubit cubit;
  late final ScrollController expensesController;
  late final ScrollController revenuesController;

  @override
  void initState() {
    cubit = ExpensesCubit.get(context);
    expensesController = ScrollController();
    expensesController.addListener(() {
      if (expensesController.position.pixels ==
          expensesController.position.maxScrollExtent) {
        cubit.getFilteredExpenses();
      }
    });
    revenuesController = ScrollController();
    revenuesController.addListener(() {
      if (revenuesController.position.pixels ==
          revenuesController.position.maxScrollExtent) {
        cubit.getFilteredExpenses();
      }
    });
    cubit.getFilteredExpenses(isNewSearch: true, refresh: true);
    super.initState();
  }

  @override
  void dispose() {
    cubit.resetExpensesFilters();
    expensesController.dispose();
    revenuesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    return Scaffold(
      appBar: AppBar(
        title: We2upText(strings.expenses_and_revenues),
        centerTitle: true,
        actions: [
          IconButton(
            onPressed: () => showDialog(
              context: context,
              builder: (_) => BlocProvider.value(
                value: cubit,
                child: const ExpenseFilterDialog(),
              ),
            ),
            icon: const Icon(Icons.filter_list_alt),
          )
        ],
      ),
      body: DefaultTabController(
        length: 2,
        child: Column(
          children: [
            TabBar(
              tabs: [
                Tab(text: strings.expenses),
                Tab(text: strings.revenues),
              ],
            ),
            Expanded(
              child: BlocConsumer<ExpensesCubit, ExpensesState>(
                listener: (context, state) {
                  if (state is NewExpensesLoading || state is ExpenseLoading) {
                    EasyLoading.show(status: strings.loading);
                  } else {
                    EasyLoading.dismiss();
                  }
                },
                buildWhen: (previous, current) => current is ExpensesLoaded,
                builder: (context, state) {
                  if (state is ExpensesLoaded) {
                    return TabBarView(
                      children: [
                        ExpensesTab(
                          scrollController: expensesController,
                          expenses: state.expenses.where(
                            (expense) => expense.isRefund == 0,
                          ),
                          total: cubit
                              .currentSummary?.payments.first.totalExpensePaid,
                        ),
                        ExpensesTab(
                          isRevenue: true,
                          scrollController: revenuesController,
                          expenses: state.expenses.where(
                            (expense) => expense.isRefund == 1,
                          ),
                          total: cubit.currentSummary?.payments.first
                              .totalExpenseRefundPaid,
                        ),
                      ],
                    );
                  }
                  return Center(child: We2upText(strings.loading));
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class ExpensesTab extends StatelessWidget {
  const ExpensesTab({
    super.key,
    this.isRevenue = false,
    required this.scrollController,
    required this.expenses,
    this.total,
  });

  final bool isRevenue;
  final ScrollController scrollController;
  final Iterable<Expense> expenses;
  final String? total;

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    double totalAmount = total != null
        ? double.parse(total!)
        : expenses.fold(
            0,
            (sum, expense) => sum + double.parse(expense.finalTotal ?? "0"),
          );
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 3.sp),
      child: ConditionalBuilder(
        condition: expenses.isNotEmpty,
        builder: (context) => Column(
          children: [
            Expanded(
              child: ListView.builder(
                controller: scrollController,
                itemCount: expenses.length,
                itemBuilder: (context, index) => ExpenseDetailsCard(
                  expense: expenses.elementAt(index),
                ),
              ),
            ),
            ShowTotal(
              amountText:
                  isRevenue ? strings.total_revenues : strings.total_expenses,
              amountValue: totalAmount.toStringAsFixed(4),
            ),
          ],
        ),
        fallback: (context) => Center(
          child: We2upText(strings.no_transactions_in_range),
        ),
      ),
    );
  }
}
