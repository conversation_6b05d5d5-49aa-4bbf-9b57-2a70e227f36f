// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:go_router/go_router.dart';
// import 'package:we2up/bloc/landing/landing_cubit.dart';
// import 'package:we2up/data/models/base_url.dart';
// import 'package:we2up/utils/route_constants.dart';
// import 'package:webview_flutter/webview_flutter.dart';
//
// import '../../data/db/db_manager.dart';
// import '../../utils/we2up_constants.dart';
// import '../widgets/we2up_text.dart';
//
// class WebViewPage extends StatelessWidget {
//   const WebViewPage({super.key});
//
//   @override
//   Widget build(BuildContext context) {
//     final BaseUrl base = credentialsBox.get(baseUrl);
//     final controller = WebViewController()
//       ..setJavaScriptMode(JavaScriptMode.unrestricted)
//       ..setBackgroundColor(const Color(0x00000000))
//       ..setNavigationDelegate(
//         NavigationDelegate(
//           onProgress: (_) => LandingCubit.get(context).webViewLoading(),
//           onPageFinished: (_) => LandingCubit.get(context).webViewLoaded(),
//           onNavigationRequest: (NavigationRequest request) {
//             if (!request.url
//                 .startsWith(generateCustomUrl(base).toLowerCase())) {
//               return NavigationDecision.prevent;
//             }
//             return NavigationDecision.navigate;
//           },
//         ),
//       )
//       ..loadRequest(Uri.parse(webViewUrl()));
//     return PopScope(
//       canPop: false,
//       onPopInvoked: (didPop) async {
//         if (await controller.canGoBack()) controller.goBack();
//       },
//       child: Scaffold(
//         appBar: AppBar(
//           title: We2upText("${base.name}.WE2UP"),
//           centerTitle: true,
//           leading: IconButton(
//             onPressed: () => context.go(landingScreen),
//             icon: const Icon(Icons.arrow_back_rounded),
//           ),
//         ),
//         body: BlocBuilder<LandingCubit, LandingState>(
//           buildWhen: (previous, current) =>
//               previous is WebViewLoading || current is WebViewLoaded,
//           builder: (context, state) {
//             if (state is WebViewLoading) {
//               return const Center(child: CircularProgressIndicator());
//             }
//             return WebViewWidget(controller: controller);
//           },
//         ),
//       ),
//     );
//   }
// }
