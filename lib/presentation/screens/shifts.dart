import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:sizer/sizer.dart';
import 'package:we2up/bloc/shifts/shifts_cubit.dart';
import 'package:we2up/data/models/cash_register.dart';
import 'package:we2up/presentation/widgets/my_loading_indicator.dart';
import 'package:we2up/utils/route_constants.dart';
import 'package:we2up/utils/we2up_constants.dart';

import '../../data/db/db_manager.dart';
import '../widgets/date_range_dropdown.dart';
import '../widgets/date_range_list_tile.dart';
import '../widgets/user_dropdown_button.dart';
import '../widgets/we2up_text.dart';

class Shifts extends StatelessWidget {
  const Shifts({super.key});

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    final cubit = context.read<ShiftsCubit>();
    cubit.scrollController.addListener(() {
      if (cubit.scrollController.position.pixels ==
          cubit.scrollController.position.maxScrollExtent) {
        cubit.getShifts(isNewSearch: false);
      }
    });
    return Scaffold(
      appBar: AppBar(title: Text(strings.shifts)),
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 10.sp),
        child: ListView(
          controller: cubit.scrollController,
          children: [
            Row(
              children: [
                We2upText("${strings.date_range}:"),
                Gap(8.sp),
                Expanded(
                  child: BlocBuilder<ShiftsCubit, ShiftsState>(
                    buildWhen: (_, current) => current is DateRangeUpdated,
                    builder: (context, state) {
                      return DateRangeDropdown(
                        changeStartDate: cubit.updateStartDate,
                        changeEndDate: cubit.updateEndDate,
                        onChanged: cubit.updateDateRange,
                        range: cubit.range,
                        isLocation: true,
                      );
                    },
                  ),
                ),
              ],
            ),
            BlocBuilder<ShiftsCubit, ShiftsState>(
              buildWhen: (_, current) => current is StartDateUpdated,
              builder: (context, state) {
                return DateRangeListTile(
                  isStartDate: true,
                  changeDate: cubit.updateStartDate,
                  changeRange: cubit.updateDateRange,
                  date: cubit.startDate,
                );
              },
            ),
            BlocBuilder<ShiftsCubit, ShiftsState>(
              buildWhen: (_, current) => current is EndDateUpdated,
              builder: (context, state) {
                return DateRangeListTile(
                  isStartDate: false,
                  isLocation: true,
                  changeDate: cubit.updateEndDate,
                  changeRange: cubit.updateDateRange,
                  date: cubit.endDate,
                );
              },
            ),
            const Divider(),
            Row(
              children: [
                We2upText("${strings.user_name}:"),
                Gap(8.sp),
                Expanded(
                  child: BlocBuilder<ShiftsCubit, ShiftsState>(
                    buildWhen: (_, c) => c is FilterUserUpdated,
                    builder: (context, state) {
                      return UsersDropdownButton(
                        user: cubit.user,
                        onChanged: cubit.updateFilterUser,
                        controller: cubit.filterUserController,
                      );
                    },
                  ),
                ),
              ],
            ),
            Gap(8.sp),
            BlocListener<ShiftsCubit, ShiftsState>(
              listener: (context, state) {
                if (state is ShiftsNewLoading) {
                  EasyLoading.show(status: strings.loading);
                } else {
                  EasyLoading.dismiss();
                }
              },
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    width: 50.w,
                    child: OutlinedButton.icon(
                      icon: const Icon(Icons.save_outlined),
                      label: We2upText(strings.search),
                      onPressed: () async {
                        EasyLoading.show(status: strings.loading);
                        cubit.getShifts(isNewSearch: true);
                        EasyLoading.dismiss();
                      },
                    ),
                  ),
                ],
              ),
            ),
            const Divider(),
            BlocBuilder<ShiftsCubit, ShiftsState>(
              buildWhen: (_, c) => c is ShiftsLoaded,
              builder: (context, state) {
                return ListView.builder(
                  physics: const NeverScrollableScrollPhysics(),
                  shrinkWrap: true,
                  itemCount: cubit.totalShifts.length,
                  itemBuilder: (context, index) {
                    return ShiftTile(
                      shift: cubit.totalShifts[index],
                      cubit: cubit,
                    );
                  },
                );
              },
            ),
            BlocBuilder<ShiftsCubit, ShiftsState>(
              buildWhen: (previous, current) =>
                  current is ShiftsLoading || previous is ShiftsLoading,
              builder: (context, state) {
                return Visibility(
                  visible: state is ShiftsLoading,
                  child: const MyLoadingIndicator(),
                );
              },
            )
          ],
        ),
      ),
    );
  }
}

class ShiftTile extends StatelessWidget {
  const ShiftTile({
    super.key,
    required this.shift,
    required this.cubit,
    this.clickable = true,
  });

  final CashRegister shift;
  final ShiftsCubit cubit;
  final bool clickable;

  @override
  Widget build(BuildContext context) {
    DateTime? closedAt = DateTime.tryParse(shift.closedAt ?? "");
    return ListTile(
      title: We2upText(
        usersBox.get(shift.userId)?.username ??
            usersBox.get(shift.userId)?.firstName ??
            (shift.userId == loginData.userId
                ? loginData.username
                : shift.userId.toString()),
      ),
      subtitle: We2upText(formatDate(shift.createdAt)),
      leading: Icon(
        Icons.circle,
        color: shift.status == "close"
            ? Theme.of(context).colorScheme.errorContainer
            : Theme.of(context).colorScheme.secondaryContainer,
      ),
      trailing: We2upText(closedAt != null ? formatDate(closedAt) : "open"),
      onTap: clickable
          ? () {
              cubit.loadShiftDetails(shift);
              context.push(shiftDetailsPage, extra: cubit);
            }
          : null,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8.sp)),
    );
  }
}
