import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:gap/gap.dart';
import 'package:go_router/go_router.dart';
import 'package:sizer/sizer.dart';
import 'package:we2up/bloc/expenses/expenses_cubit.dart';
import 'package:we2up/presentation/widgets/locations_dropdown_button.dart';
import 'package:we2up/presentation/widgets/product_number_text_field.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

import '../style_constants.dart';
import '../widgets/contacts_dropdown_button.dart';
import '../widgets/expense_categories_dropdown_button.dart';
import '../widgets/expense_recur_interval_type_dropdown_button.dart';
import '../widgets/payment_account_dropdown_button.dart';
import '../widgets/payment_method_dropdown_button.dart';
import '../widgets/show_box.dart';
import '../widgets/tax_dropdown_button.dart';
import '../widgets/user_dropdown_button.dart';
import '../widgets/we2up_text.dart';

class AddEditExpensePage extends StatelessWidget {
  const AddEditExpensePage({super.key});

  @override
  Widget build(BuildContext context) {
    final ExpensesCubit eCubit = ExpensesCubit.get(context);
    return GestureDetector(
      onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
      child: BlocBuilder<ExpensesCubit, ExpensesState>(
        builder: (context, state) {
          final strings = AppLocalizations.of(context)!;
          return Scaffold(
            appBar: AppBar(
              title: We2upText(
                eCubit.editExpense != null
                    ? strings.edit_expense
                    : strings.new_expense,
              ),
              centerTitle: true,
            ),
            body: Padding(
              padding: EdgeInsets.symmetric(horizontal: 15.sp),
              child: ListView(
                children: [
                  CheckboxListTile(
                    title: We2upText(strings.is_refund_expense),
                    enabled: eCubit.editExpense == null,
                    value: eCubit.isRefund,
                    onChanged: (_) => eCubit.changeRefund(),
                    shape: RoundedRectangleBorder(
                      side: BorderSide(
                        color: Theme.of(context).colorScheme.secondaryContainer,
                        width: 2,
                      ),
                      borderRadius: BorderRadius.circular(10.sp),
                    ),
                  ),
                  verticalSpacer,
                  Row(
                    children: [
                      We2upText("${strings.location}:"),
                      SizedBox(width: 8.sp),
                      Expanded(
                        child: BusinessLocationDropdown(
                          isExpanded: true,
                          onChanged: (l) =>
                              eCubit.changeSelectedLocation(location: l!),
                          value: eCubit.selectedLocation,
                        ),
                      ),
                    ],
                  ),
                  verticalSpacer,
                  Row(
                    children: [
                      We2upText(strings.expense_category),
                      Gap(8.sp),
                      Expanded(
                        child: ExpenseCategoriesDropdownButton(
                          isExpanded: true,
                          onChanged: eCubit.changeSelectedExpenseCategory,
                          category: eCubit.expenseCategory,
                        ),
                      ),
                    ],
                  ),
                  const Divider(),
                  CheckboxListTile(
                    title: We2upText(strings.recurring_expense),
                    value: eCubit.isRecurring,
                    onChanged: (_) => eCubit.changeRecurring(),
                    shape: RoundedRectangleBorder(
                      side: BorderSide(
                        color: Theme.of(context).colorScheme.secondary,
                        width: 2,
                      ),
                      borderRadius: BorderRadius.circular(10.sp),
                    ),
                  ),
                  Visibility(
                    visible: eCubit.isRecurring,
                    child: Column(
                      children: [
                        verticalSpacer,
                        TextField(
                          controller: eCubit.recurIntervalController,
                          keyboardType: TextInputType.number,
                          decoration: InputDecoration(
                            hintText: strings.recur_interval_expense,
                            prefixIcon: const Icon(Icons.numbers_sharp),
                            border: myTextFieldBorder,
                          ),
                        ),
                        verticalSpacer,
                        ExpenseRecurIntervalTypeDropdownButton(
                          intervalType: eCubit.intervalType,
                          onChanged: eCubit.changeIntervalType,
                        ),
                      ],
                    ),
                  ),
                  const Divider(),
                  Row(
                    children: [
                      We2upText(strings.customer_name),
                      SizedBox(width: 8.sp),
                      Expanded(
                        child: ContactsDropdownButton(
                          onChanged: eCubit.customerContactChanged,
                          contact: eCubit.customerContact,
                        ),
                      )
                    ],
                  ),
                  SizedBox(height: 8.sp),
                  Row(
                    children: [
                      We2upText("${strings.user_name}:"),
                      Gap(8.sp),
                      Expanded(
                        child: UsersDropdownButton(
                          user: eCubit.user,
                          onChanged: eCubit.updateExpenseForUser,
                          controller: eCubit.expenseForUserController,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 8.sp),
                  TextField(
                    controller: eCubit.expenseNoteController,
                    decoration: InputDecoration(
                      hintText: strings.expense_note,
                      prefixIcon: const Icon(Icons.note_alt),
                      border: myTextFieldBorder,
                    ),
                  ),
                  const Divider(),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      We2upText(strings.expense_amount),
                      SizedBox(width: 8.sp),
                      ProductAmountTextField(
                        controller: eCubit.expenseAmountController,
                        hint: strings.expense_amount.replaceFirst(":", ""),
                        onChanged: (_) => eCubit.amountChanged(),
                      ),
                    ],
                  ),
                  verticalSpacer,
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        flex: 3,
                        child: We2upText(
                          strings.tax,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      SizedBox(width: 8.sp),
                      Expanded(
                        flex: 7,
                        child: TaxDropdownButton(
                          taxRate: eCubit.taxRate,
                          onChanged: eCubit.changeTaxRate,
                          isExpanded: true,
                        ),
                      ),
                    ],
                  ),
                  verticalSpacer,
                  Row(
                    children: [
                      We2upText(strings.payment_method),
                      SizedBox(width: 8.sp),
                      PaymentMethodDropdownButton(
                        isExpanded: true,
                        onChanged: (pMethod) =>
                            eCubit.paymentMethodChanged(pMethod!),
                        paymentMethod: eCubit.paymentMethod,
                      ),
                    ],
                  ),
                  verticalSpacer,
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      We2upText(strings.payment_account),
                      SizedBox(width: 8.sp),
                      PaymentAccountDropdownButton(
                        onChanged: eCubit.changePaymentAccount,
                        paymentAccount: eCubit.paymentAccount,
                        isExpanded: true,
                      ),
                    ],
                  ),
                  const Divider(),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      We2upText(strings.total_expense_amount),
                      ShowBox(text: eCubit.totalExpenseAmount(), width: 55),
                    ],
                  ),
                  const Divider(),
                  ElevatedButton(
                    onPressed:
                        (eCubit.expenseAmountController.text.isNotEmpty &&
                                eCubit.paymentAccount != null)
                            ? () {
                                if (eCubit.editExpense?.id == null) {
                                  EasyLoading.show(status: strings.loading);
                                }
                                eCubit
                                    .sendExpenseToAPI(
                                  expenseID: eCubit.editExpense?.id,
                                  offlineID: eCubit.offlineID,
                                )
                                    .then((value) {
                                  if (value && eCubit.editExpense != null) {
                                    context.pop();
                                  } else if (value) {
                                    eCubit.resetExpensesFilters();
                                    eCubit.resetExpenseData();
                                  }
                                });
                              }
                            : null,
                    child: We2upText(
                      eCubit.editExpense != null
                          ? strings.finish_edit
                          : strings.finish_payment,
                    ),
                  ),
                  verticalSpacer,
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
