import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:sizer/sizer.dart';
import 'package:we2up/bloc/contact_payment/contact_payment_cubit.dart';
import 'package:we2up/presentation/widgets/product_number_text_field.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

import '../../utils/route_constants.dart';
import '../../utils/we2up_constants.dart';
import '../style_constants.dart';
import '../widgets/contacts_dropdown_button.dart';
import '../widgets/payment_account_dropdown_button.dart';
import '../widgets/payment_method_dropdown_button.dart';
import '../widgets/print_bill.dart';
import '../widgets/we2up_text.dart';

class ContactPayment extends StatefulWidget {
  const ContactPayment({super.key, required this.isCustomer});

  final bool isCustomer;

  @override
  State<ContactPayment> createState() => _ContactPaymentState();
}

class _ContactPaymentState extends State<ContactPayment> {
  late ContactPaymentCubit cCubit;
  late bool isCustomer;

  @override
  void initState() {
    isCustomer = widget.isCustomer;
    cCubit = ContactPaymentCubit.get(context);
    super.initState();
  }

  @override
  void dispose() {
    cCubit.resetValues();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
      child: BlocBuilder<ContactPaymentCubit, ContactPaymentState>(
        builder: (context, state) {
          final strings = AppLocalizations.of(context)!;
          return Scaffold(
            appBar: AppBar(
              title: We2upText(
                isCustomer
                    ? strings.sale_collection_button
                    : strings.purchase_payment,
              ),
              centerTitle: true,
            ),
            body: Padding(
              padding: EdgeInsets.symmetric(horizontal: 10.sp),
              child: ListView(
                children: [
                  Row(
                    children: [
                      We2upText(
                        isCustomer
                            ? strings.customer_name
                            : strings.supplier_name,
                      ),
                      SizedBox(width: 8.sp),
                      Expanded(
                        child: ContactsDropdownButton(
                          onChanged: cCubit.changeCustomerContact,
                          filterType: isCustomer ? 'customer' : 'supplier',
                          contact: cCubit.customerContact,
                        ),
                      )
                    ],
                  ),
                  if (cCubit.customerContact != null) verticalSpacer,
                  Visibility(
                    visible: cCubit.customerContact != null,
                    child: Container(
                      width: double.infinity,
                      decoration: showBoxDecoration(context),
                      child: Column(
                        children: [
                          ListTile(
                            title: We2upText(
                              "${strings.name}:",
                              style: Theme.of(context).textTheme.titleMedium,
                            ),
                            trailing: We2upText(
                              cCubit.customerContact?.name ?? '',
                              style: Theme.of(context).textTheme.bodyLarge,
                            ),
                          ),
                          const Divider(),
                          ListTile(
                            title: We2upText(
                              "${strings.mobile}:",
                              style: Theme.of(context).textTheme.titleMedium,
                            ),
                            trailing: We2upText(
                              cCubit.customerContact?.mobile ?? 'NA',
                              style: Theme.of(context).textTheme.bodyLarge,
                            ),
                          ),
                          const Divider(),
                          ListTile(
                            title: We2upText(
                              "${strings.due_amount}:",
                              style: Theme.of(context).textTheme.titleMedium,
                            ),
                            trailing: We2upText(
                              cCubit.customerContact?.due ?? "0.0",
                              style: Theme.of(context).textTheme.bodyLarge,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const Divider(),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      We2upText(isCustomer
                          ? strings.collected_amount
                          : strings.paidAmount),
                      SizedBox(width: 8.sp),
                      ProductAmountTextField(
                        controller: cCubit.paymentAmountController,
                        width: 45,
                        hint: (isCustomer
                                ? strings.collected_amount
                                : strings.paidAmount)
                            .replaceFirst(":", ""),
                        onChanged: (_) => cCubit.updateValues(),
                      )
                    ],
                  ),
                  verticalSpacer,
                  Row(
                    children: [
                      We2upText(strings.payment_method),
                      SizedBox(width: 8.sp),
                      PaymentMethodDropdownButton(
                        isExpanded: true,
                        onChanged: (pMethod) =>
                            cCubit.paymentMethodChanged(pMethod!),
                        paymentMethod: cCubit.paymentMethod,
                      ),
                    ],
                  ),
                  verticalSpacer,
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      We2upText(strings.payment_account),
                      SizedBox(width: 8.sp),
                      PaymentAccountDropdownButton(
                        onChanged: cCubit.changePaymentAccount,
                        paymentAccount: cCubit.paymentAccount,
                        isExpanded: true,
                      ),
                    ],
                  ),
                  verticalSpacer,
                  SizedBox(
                    width: 90.w,
                    child: TextField(
                      controller: cCubit.paymentNoteController,
                      decoration: InputDecoration(
                        hintText: strings.employee_note,
                        prefixIcon: const Icon(Icons.note_alt),
                        border: myTextFieldBorder,
                      ),
                    ),
                  ),
                  if (isCustomer) verticalSpacer,
                  if (isCustomer) const PrintBillCheckBox(payment: true),
                ],
              ),
            ),
            bottomNavigationBar: Container(
              width: double.infinity,
              padding: EdgeInsets.all(10.sp),
              child: SizedBox(
                width: 90.w,
                child: ElevatedButton(
                  onPressed: cCubit.paymentAmountController.text.isNotEmpty &&
                          cCubit.paymentAccount != null &&
                          cCubit.customerContact != null
                      ? () =>
                          cCubit.sendContactPaymentToAPI(context).then((value) {
                            if (value &&
                                previousRouteName(context) == contactDues) {
                              context.pop();
                            }
                          })
                      : null,
                  child: We2upText(
                    isCustomer ? strings.collect_amount : strings.payAmount,
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
