import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:sizer/sizer.dart';
import 'package:we2up/bloc/products/products_cubit.dart';
import 'package:we2up/presentation/widgets/show_box.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

import '../../utils/we2up_constants.dart';
import '../style_constants.dart';
import '../widgets/add_payment_details_item.dart';
import '../widgets/bill_of_sale_three_sell_buttons.dart';
import '../widgets/comm_agents_panel.dart';
import '../widgets/print_bill.dart';
import '../widgets/product_number_text_field.dart';
import '../widgets/shipment_status_dropdown_button.dart';
import '../widgets/shipping_companies_dropdown_button.dart';
import '../widgets/tax_dropdown_button.dart';
import '../widgets/we2up_text.dart';

class PaymentPage extends StatelessWidget {
  const PaymentPage({super.key});

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    final now = DateTime.now();
    return GestureDetector(
      onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
      child: Scaffold(
        appBar: AppBar(
          title: We2upText(strings.payment),
          centerTitle: true,
        ),
        body: BlocBuilder<ProductsCubit, ProductsState>(
          builder: (context, state) {
            final cubit = ProductsCubit.get(context);
            final shippingFeesC = cubit.cartShippingFeesController;
            shippingFeesC.addListener(cubit.applyPaymentChanged);
            return WillPopScope(
              onWillPop: () async {
                cubit.resetPaymentDetailsList();
                return true;
              },
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 15.sp),
                child: ListView(
                  children: [
                    verticalSpacer,
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        We2upText(strings.date),
                        ShowBox(
                          text: formatDate(now),
                          width: 65,
                        ),
                      ],
                    ),
                    const Divider(),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(child: We2upText(strings.net_bill)),
                        ShowBox(
                          text: cubit
                              .getTotalPaymentFees(purchase: false)
                              .toString(),
                          color:
                              Theme.of(context).colorScheme.tertiaryContainer,
                          width: 45,
                        ),
                      ],
                    ),
                    const Divider(),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          flex: 3,
                          child: We2upText(
                            strings.tax,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        SizedBox(width: 8.sp),
                        Expanded(
                          flex: 7,
                          child: TaxDropdownButton(
                            isExpanded: true,
                            taxRate: cubit.cartTaxRate,
                            onChanged: cubit.cartTaxRateChanged,
                          ),
                        ),
                      ],
                    ),
                    verticalSpacer,
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        We2upText(strings.shipping_fee),
                        SizedBox(width: 8.sp),
                        ProductAmountTextField(
                          controller: shippingFeesC,
                          width: 45,
                          hint: strings.shipping_fee.replaceFirst(":", ""),
                        ),
                      ],
                    ),
                    verticalSpacer,
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        We2upText("${strings.shipping_companies}:"),
                        SizedBox(width: 8.sp),
                        Expanded(
                          child: ShippingCompaniesDropdown(
                            isExpanded: true,
                            onChanged: cubit.changeShippingCompany,
                            company: cubit.cartShippingCompany,
                          ),
                        ),
                      ],
                    ),
                    verticalSpacer,
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        We2upText(strings.shipment_status),
                        SizedBox(width: 8.sp),
                        Expanded(
                          child: ShipmentStatusDropdownButton(
                            onChanged: cubit.changeShippingStatus,
                            status: cubit.cartShippingStatus,
                            isExpanded: true,
                          ),
                        ),
                      ],
                    ),
                    Column(
                      children: [
                        verticalSpacer,
                        TextField(
                          controller: cubit.cartShippingDetailsController,
                          decoration: InputDecoration(
                            hintText: strings.shipping_details,
                            prefixIcon: const Icon(Icons.note_alt_rounded),
                            border: myTextFieldBorder,
                          ),
                        ),
                        verticalSpacer,
                        TextField(
                          controller: cubit.cartShippingAddressController,
                          decoration: InputDecoration(
                            hintText: strings.shipping_address,
                            prefixIcon: const Icon(Icons.local_shipping),
                            border: myTextFieldBorder,
                          ),
                        ),
                      ],
                    ),
                    verticalSpacer,
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        We2upText(AppLocalizations.of(context)!.discount),
                        SizedBox(width: 8.sp),
                        ProductAmountTextField(
                          controller: cubit.cartDiscountController,
                          onChanged: (d) => cubit.applyCartDiscount(),
                          hint: strings.discount.replaceFirst(":", ""),
                          maxAmount: cubit.cartDiscountTypePercentage
                              ? 100
                              : cubit.getCartTotalBeforeTax(purchase: false),
                          focusNode: cubit.discountFieldFocusNode,
                        ),
                        SizedBox(width: 8.sp),
                        Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10.sp),
                            border: Border.all(
                              color:
                                  Theme.of(context).colorScheme.inversePrimary,
                              width: 2,
                            ),
                          ),
                          padding: EdgeInsetsDirectional.only(start: 5.sp),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Text("%"),
                              Checkbox(
                                value: cubit.cartDiscountTypePercentage,
                                onChanged: (_) =>
                                    cubit.switchCartDiscountType(),
                              )
                            ],
                          ),
                        ),
                      ],
                    ),
                    const Divider(),
                    const CommAgentsColumn(),
                    const Divider(),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(child: We2upText(strings.net_bill)),
                        ShowBox(
                          text: cubit
                              .getTotalPaymentFees(purchase: false)
                              .toStringAsFixed(4),
                          color:
                              Theme.of(context).colorScheme.tertiaryContainer,
                          width: 45,
                        ),
                      ],
                    ),
                    const Divider(),
                    Column(
                      children: cubit.paymentDetailsList
                          .asMap()
                          .entries
                          .map<AddPaymentDetailsItem>((entry) {
                        int index = entry.key;
                        return AddPaymentDetailsItem(index: index);
                      }).toList(),
                    ),
                    SizedBox(
                      width: 90.w,
                      child: ElevatedButton.icon(
                        onPressed: () => cubit.addPaymentDetails(),
                        label: We2upText(strings.add_payment),
                        icon: const Icon(Icons.add),
                        style: addPaymentButtonStyle(context),
                      ),
                    ),
                    const Divider(),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Column(
                            children: [
                              We2upText(strings.total_payment),
                              SizedBox(height: 5.sp),
                              ShowBox(
                                text: cubit
                                    .getTotalPaymentFees(purchase: false)
                                    .toStringAsFixed(4),
                                width: 35,
                              ),
                              SizedBox(height: 5.sp),
                              We2upText(strings.balance),
                              SizedBox(height: 5.sp),
                              ShowBox(
                                text: "${cubit.cartCustomerContact?.balance}",
                                width: 35,
                              ),
                              SizedBox(height: 5.sp),
                              TextField(
                                controller: cubit.cartEmployeeNoteController,
                                decoration: InputDecoration(
                                  hintText: strings.employee_note,
                                  border: const OutlineInputBorder(
                                    borderRadius: BorderRadius.all(
                                      Radius.circular(7.0),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        Gap(5.w),
                        Expanded(
                          child: Column(
                            children: [
                              We2upText(strings.total_payable),
                              SizedBox(height: 5.sp),
                              ShowBox(
                                text: cubit.getTotalPayable().toString(),
                                width: 35,
                              ),
                              SizedBox(height: 5.sp),
                              We2upText(strings.remaining),
                              SizedBox(height: 5.sp),
                              ShowBox(
                                text: (cubit.getTotalPaymentFees(
                                            purchase: false) -
                                        cubit.getTotalPayable())
                                    .toStringAsFixed(4),
                                width: 35,
                              ),
                              SizedBox(height: 5.sp),
                              TextField(
                                controller: cubit.cartSaleNoteController,
                                decoration: InputDecoration(
                                  hintText: strings.sale_note,
                                  border: const OutlineInputBorder(
                                    borderRadius: BorderRadius.all(
                                      Radius.circular(7.0),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const Divider(),
                    const PrintBillCheckBox(),
                    const ThreeSellButtons(goHome: true),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
