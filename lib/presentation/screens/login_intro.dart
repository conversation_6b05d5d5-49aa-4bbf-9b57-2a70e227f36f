import 'package:flutter/material.dart';
import 'package:responsive_builder/responsive_builder.dart';
import 'package:sizer/sizer.dart';
import 'package:we2up/presentation/widgets/login_layouts/login_button.dart';

import '../style_constants.dart';
import '../widgets/login_intro_layouts/language_dropdown_button.dart';
import '../widgets/login_intro_layouts/whatsapp_contact_us.dart';

class LoginIntroScreen extends StatelessWidget {
  const LoginIntroScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final we2upImage = Image.asset("images/we2up_logo.jpg");
    return Scaffold(
      body: ScreenTypeLayout.builder(
        mobile: (_) => ListView(
          shrinkWrap: true,
          padding: EdgeInsets.symmetric(horizontal: 6.w),
          children: [
            SizedBox(width: double.infinity, height: 5.h),
            we2upImage,
            SizedBox(height: 5.h),
            const LanguageDropdownButton(),
            verticalSpacer,
            LoginButton(),
          ],
        ),
        desktop: (_) => Row(
          children: [
            we2upImage,
            Expanded(
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 40.sp),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const LanguageDropdownButton(drawer: true),
                    verticalSpacer,
                    SizedBox(
                      width: 200.sp,
                      child: LoginButton(),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: const WhatsAppContactUs(),
    );
  }
}
