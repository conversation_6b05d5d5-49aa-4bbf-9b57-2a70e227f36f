import 'package:conditional_builder_null_safety/conditional_builder_null_safety.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:sizer/sizer.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/bloc/products/products_cubit.dart';

import '../widgets/sale_filter_dialog.dart';
import '../widgets/sale_or_purchase_return_card.dart';
import '../widgets/show_total.dart';
import '../widgets/we2up_text.dart';

class SalesReturns extends StatefulWidget {
  const SalesReturns({super.key});

  @override
  State<SalesReturns> createState() => _SalesReturnsState();
}

class _SalesReturnsState extends State<SalesReturns> {
  late ProductsCubit cubit;
  late final ScrollController scrollController;

  @override
  void initState() {
    cubit = ProductsCubit.get(context);
    cubit.resetSalesFilters();
    cubit.getFilteredSales(isNewSearch: true, saleReturn: true, refresh: true);
    scrollController = ScrollController();
    scrollController.addListener(() {
      if (scrollController.position.pixels ==
          scrollController.position.maxScrollExtent) {
        cubit.getFilteredSales(saleReturn: true);
      }
    });
    super.initState();
  }

  @override
  void dispose() {
    cubit.resetSalesFilters();
    scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    return GestureDetector(
      onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
      child: Scaffold(
        appBar: AppBar(
          title: We2upText(strings.sales_returns),
          centerTitle: true,
          actions: [
            IconButton(
              onPressed: () => showDialog(
                context: context,
                builder: (_) => const SaleFilterDialog(isReturn: true),
              ),
              icon: const Icon(Icons.filter_list_alt),
            ),
          ],
        ),
        body: Padding(
          padding: EdgeInsets.symmetric(horizontal: 3.sp),
          child: BlocConsumer<ProductsCubit, ProductsState>(
            listener: (context, state) {
              if (state is NewSalesLoading) {
                EasyLoading.show(status: strings.loading);
              } else {
                EasyLoading.dismiss();
              }
            },
            buildWhen: (previous, current) => current is SalesReturnsLoaded,
            builder: (context, state) {
              if (state is SalesReturnsLoaded) {
                return ConditionalBuilder(
                  condition: state.salesReturns.isNotEmpty,
                  builder: (context) {
                    return Column(
                      children: [
                        Expanded(
                          child: ListView.builder(
                            controller: scrollController,
                            itemCount: state.salesReturns.length,
                            itemBuilder: (context, index) {
                              return SaleOrPurchaseReturnCard(
                                sReturn: state.salesReturns.elementAt(index),
                              );
                            },
                          ),
                        ),
                        ShowTotal(
                          amountText: strings.total_sell_return,
                          amountValue: state.calculations.$1.toStringAsFixed(4),
                        ),
                      ],
                    );
                  },
                  fallback: (context) => Center(
                    child: We2upText(strings.no_transactions_in_range),
                  ),
                );
              }
              return Center(child: We2upText(strings.loading));
            },
          ),
        ),
      ),
    );
  }
}
