import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:responsive_builder/responsive_builder.dart';
import 'package:sizer/sizer.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/bloc/products/products_cubit.dart';
import 'package:we2up/data/models/bill_of_sale_data.dart';
import 'package:we2up/data/models/product.dart';
import 'package:we2up/presentation/widgets/we2up_text.dart';

import '../../data/models/business_settings.dart';
import '../../utils/invoice_handler.dart';
import '../../utils/we2up_constants.dart';
import '../widgets/bill_of_sale_layouts/sale_details_section.dart';
import '../widgets/filter_dialog.dart';
import '../widgets/shop_products_listview.dart';

class BillOfSale extends StatefulWidget {
  const BillOfSale({super.key, required this.billOfSaleData});

  final BillData billOfSaleData;

  @override
  State<BillOfSale> createState() => _BillOfSaleState();
}

class _BillOfSaleState extends State<BillOfSale> {
  late ProductsCubit cubit;
  late BillData _billOfSaleData;

  @override
  void initState() {
    cubit = ProductsCubit.get(context);
    _billOfSaleData = widget.billOfSaleData;
    cubit.initAllProducts(billData: _billOfSaleData);
    cubit.initCart(
      billData: _billOfSaleData,
      isNewSell: _billOfSaleData.sell == null,
    );
    if (_billOfSaleData.sell == null) {
      cubit.resetPaymentDetailsList();
    }
    super.initState();
  }

  @override
  void dispose() {
    cubit.initAllProducts();
    cubit.initCart();
    cubit.resetPaymentDetailsList();
    cubit.resetSellsFilters();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return InvoiceHandler(
      focusNode: cubit.invoiceFocusNode,
      productsDropdownKey: cubit.productsDropdownKey,
      child: BlocBuilder<ProductsCubit, ProductsState>(
        builder: (context, state) {
          final strings = AppLocalizations.of(context)!;
          return Scaffold(
            appBar: AppBar(
              title: We2upText(getHeaderText(_billOfSaleData, strings)),
              centerTitle: true,
              actions: [
                IconButton(
                  onPressed: () => showDialog(
                    context: context,
                    builder: (_) => const FilterDialog(),
                  ),
                  icon: Icon(
                    Icons.filter_list_alt,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                )
              ],
            ),
            body: Padding(
              padding: EdgeInsets.symmetric(horizontal: 10.sp),
              child: ScreenTypeLayout.builder(
                mobile: (_) {
                  return SaleDetailsSection(billOfSaleData: _billOfSaleData);
                },
                desktop: (_) {
                  List<Product> products = cubit.filteredProductList(
                    isPurchase: cubit.currentBillData?.quotationPage ?? false,
                  );
                  
                  // Check if products panel should be shown
                  final shouldShowProductsPanel = !_billOfSaleData.returnSell && showShopProductsGridView();
                  
                  if (shouldShowProductsPanel) {
                    // Show both panels side by side
                    return Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: SaleDetailsSection(
                            billOfSaleData: _billOfSaleData,
                          ),
                        ),
                        Expanded(
                          child: ShopProductsGridView(products: products),
                        ),
                      ],
                    );
                  } else {
                    // Show only the sale details section
                    return SaleDetailsSection(
                      billOfSaleData: _billOfSaleData,
                    );
                  }
                },
              ),
            ),
          );
        },
      ),
    );
  }
}
