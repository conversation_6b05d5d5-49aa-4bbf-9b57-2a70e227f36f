import 'package:conditional_builder_null_safety/conditional_builder_null_safety.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:sizer/sizer.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/bloc/products/products_cubit.dart';

import '../widgets/purchase_filter_dialog.dart';
import '../widgets/purchase_invoice_card.dart';
import '../widgets/show_three_totals.dart';
import '../widgets/we2up_text.dart';

class NewPurchaseReturn extends StatefulWidget {
  const NewPurchaseReturn({super.key});

  @override
  State<NewPurchaseReturn> createState() => _NewPurchaseReturnState();
}

class _NewPurchaseReturnState extends State<NewPurchaseReturn> {
  late final ScrollController scrollController;

  @override
  void initState() {
    ProductsCubit.get(context).getFilteredPurchases(isNewSearch: true, refresh: true);
    scrollController = ScrollController();
    scrollController.addListener(() {
      if (scrollController.position.pixels ==
          scrollController.position.maxScrollExtent) {
        ProductsCubit.get(context).getFilteredPurchases();
      }
    });
    super.initState();
  }

  @override
  void dispose() {
    scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    return GestureDetector(
      onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
      child: Scaffold(
        appBar: AppBar(
          title: We2upText(strings.purchase_return),
          centerTitle: true,
          actions: [
            IconButton(
              onPressed: () => showDialog(
                context: context,
                builder: (_) => const PurchaseFilterDialog(),
              ),
              icon: const Icon(Icons.filter_list_alt),
            ),
          ],
        ),
        body: Padding(
          padding: EdgeInsets.symmetric(horizontal: 3.sp),
          child: BlocConsumer<ProductsCubit, ProductsState>(
            listener: (context, state) {
              if (state is NewPurchasesLoading ||
                  state is PurchaseReturnLoading) {
                EasyLoading.show(status: strings.loading);
              } else {
                EasyLoading.dismiss();
              }
            },
            buildWhen: (previous, current) => current is PurchasesLoaded,
            builder: (context, state) {
              if (state is PurchasesLoaded) {
                return ConditionalBuilder(
                  condition: state.purchases.isNotEmpty,
                  builder: (context) {
                    return Column(
                      children: [
                        Expanded(
                          child: ListView.builder(
                            controller: scrollController,
                            itemCount: state.purchases.length,
                            itemBuilder: (context, index) {
                              return PurchaseInvoiceCard(
                                purchase: state.purchases.elementAt(index),
                                isReturn: true,
                              );
                            },
                          ),
                        ),
                        ShowThreeTotals(
                          totalText: strings.total_purchases,
                          total: state.calculations.$1,
                          paidText: strings.total_report_paid,
                          paid: state.calculations.$2,
                          remainingText: "${strings.remaining}:",
                          remaining: state.calculations.$3,
                        ),
                      ],
                    );
                  },
                  fallback: (context) => Center(
                    child: We2upText(strings.no_transactions_in_range),
                  ),
                );
              }
              return Center(child: We2upText(strings.loading));
            },
          ),
        ),
      ),
    );
  }
}
