import 'package:conditional_builder_null_safety/conditional_builder_null_safety.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:sizer/sizer.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/bloc/products/products_cubit.dart';
import 'package:we2up/presentation/widgets/purchase_filter_dialog.dart';

import '../widgets/sale_or_purchase_return_card.dart';
import '../widgets/show_total.dart';
import '../widgets/we2up_text.dart';

class PurchasesReturnPage extends StatefulWidget {
  const PurchasesReturnPage({super.key});

  @override
  State<PurchasesReturnPage> createState() => _PurchasesReturnPageState();
}

class _PurchasesReturnPageState extends State<PurchasesReturnPage> {
  late final ProductsCubit cubit;
  late final ScrollController scrollController;

  @override
  void initState() {
    cubit = ProductsCubit.get(context);
    scrollController = ScrollController();
    scrollController.addListener(() {
      if (scrollController.position.pixels ==
          scrollController.position.maxScrollExtent) {
        cubit.getFilteredPurchases(purchaseReturn: true);
      }
    });
    cubit.getFilteredPurchases(
        purchaseReturn: true, isNewSearch: true, refresh: true);
    super.initState();
  }

  @override
  void dispose() {
    cubit.resetSalesFilters();
    cubit.resetPurchasesFilters();
    scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    return GestureDetector(
      onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
      child: Scaffold(
        appBar: AppBar(
          title: We2upText(strings.purchase_returns),
          centerTitle: true,
          actions: [
            IconButton(
              onPressed: () => showDialog(
                context: context,
                builder: (_) => const PurchaseFilterDialog(isReturn: true),
              ),
              icon: const Icon(Icons.filter_list_alt),
            ),
          ],
        ),
        body: Padding(
          padding: EdgeInsets.symmetric(horizontal: 3.sp),
          child: BlocConsumer<ProductsCubit, ProductsState>(
            listener: (context, state) {
              if (state is NewPurchasesLoading) {
                EasyLoading.show(status: strings.loading);
              } else {
                EasyLoading.dismiss();
              }
            },
            buildWhen: (previous, current) => current is PurchasesLoaded,
            builder: (context, state) {
              if (state is PurchasesLoaded) {
                return ConditionalBuilder(
                  condition: state.purchases.isNotEmpty,
                  builder: (context) => Column(
                    children: [
                      Expanded(
                        child: ListView.builder(
                          controller: scrollController,
                          itemCount: state.purchases.length,
                          itemBuilder: (context, index) {
                            return SaleOrPurchaseReturnCard(
                              pReturn: state.purchases.elementAt(index),
                            );
                          },
                        ),
                      ),
                      ShowTotal(
                        amountText: strings.total_purchase_return,
                        amountValue: state.calculations.$1.toStringAsFixed(4),
                      ),
                    ],
                  ),
                  fallback: (context) => Center(
                    child: We2upText(strings.no_transactions_in_range),
                  ),
                );
              }
              return Center(child: We2upText(strings.loading));
            },
          ),
        ),
      ),
    );
  }
}
