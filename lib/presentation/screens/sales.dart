import 'package:conditional_builder_null_safety/conditional_builder_null_safety.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:sizer/sizer.dart';
import 'package:we2up/bloc/products/products_cubit.dart';
import 'package:we2up/data/db/db_manager.dart';
import 'package:we2up/presentation/widgets/show_three_totals.dart';

import '../../data/models/sell.dart';
import '../widgets/sales_invoice_card.dart';
import '../widgets/we2up_text.dart';

class SalesPage extends StatelessWidget {
  const SalesPage({super.key});

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    return DefaultTabController(
      length: canListQuotations() ? 3 : 2,
      child: Builder(builder: (context) {
        TabController tabController = DefaultTabController.of(context);
        tabController.addListener(() {
          ProductsCubit.get(context).setCurrentSalesIndex(tabController.index);
        });
        return Scaffold(
          appBar: TabBar(
            tabs: [
              Tab(text: strings.recent_sales),
              Tab(text: strings.all_sales),
              if (canListQuotations()) Tab(text: strings.offer_prices),
            ],
          ),
          body: BlocConsumer<ProductsCubit, ProductsState>(
            listener: (context, state) {
              if (state is NewSalesLoading || state is SellLoading) {
                EasyLoading.show(status: strings.loading);
              } else {
                EasyLoading.dismiss();
              }
            },
            buildWhen: (previous, current) => current is SalesLoaded,
            builder: (context, state) {
              if (!isProductsAndContactsReady()) {
                return Padding(
                  padding: EdgeInsets.symmetric(horizontal: 15.sp),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      We2upText(
                        strings.loading_contacts_and_products_message,
                      ),
                      const LinearProgressIndicator(),
                    ],
                  ),
                );
              }
              return TabBarView(
                controller: tabController,
                children: [
                  ConditionalBuilder(
                    condition: state is SalesLoaded,
                    builder: (context) {
                      return SaleTab(
                        sales: (state as SalesLoaded).todaySales,
                        calculations: state.todayCalculations,
                      );
                    },
                    fallback: (context) =>
                        Center(child: We2upText(strings.loading)),
                  ),
                  ConditionalBuilder(
                    condition: state is SalesLoaded,
                    builder: (context) {
                      return SaleTab(
                        sales: (state as SalesLoaded)
                            .sales
                            .where((s) => s.isQuotation == 0),
                        calculations: state.calculations,
                      );
                    },
                    fallback: (context) =>
                        Center(child: We2upText(strings.loading)),
                  ),
                  if (canListQuotations())
                    ConditionalBuilder(
                      condition: state is SalesLoaded,
                      builder: (context) {
                        return SaleTab(
                          sales: (state as SalesLoaded)
                              .sales
                              .where((s) => s.isQuotation == 1),
                          calculations: state.quotationCalculations,
                        );
                      },
                      fallback: (context) =>
                          Center(child: We2upText(strings.loading)),
                    ),
                ],
              );
            },
          ),
        );
      }),
    );
  }
}

class SaleTab extends StatelessWidget {
  const SaleTab({super.key, required this.sales, required this.calculations});

  final Iterable<Sell> sales;
  final (double, double, double) calculations;

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    final scrollController = ScrollController();
    scrollController.addListener(() {
      if (scrollController.position.pixels ==
          scrollController.position.maxScrollExtent) {
        ProductsCubit.get(context).getFilteredSales();
      }
    });
    return Column(
      children: [
        Expanded(
          child: ConditionalBuilder(
            condition: sales.isNotEmpty,
            builder: (context) => ListView.builder(
              controller: scrollController,
              itemCount: sales.length,
              itemBuilder: (context, index) {
                return SalesInvoiceCard(
                  sell: sales.elementAt(index),
                );
              },
            ),
            fallback: (context) => Center(
              child: We2upText(strings.no_transactions_in_range),
            ),
          ),
        ),
        ShowThreeTotals(
          totalText: "${strings.total_bills}:",
          paidText: "${strings.total_paid}:",
          remainingText: "${strings.total_dues}:",
          total: calculations.$1,
          paid: calculations.$2,
          remaining: calculations.$3,
        ),
      ],
    );
  }
}
