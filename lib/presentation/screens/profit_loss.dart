import 'package:conditional_builder_null_safety/conditional_builder_null_safety.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:gap/gap.dart';
import 'package:sizer/sizer.dart';
import 'package:we2up/bloc/profit_loss/profit_loss_cubit.dart';

import '../../data/models/business_settings.dart';
import '../widgets/date_range_dropdown.dart';
import '../widgets/date_range_list_tile.dart';
import '../widgets/locations_dropdown_button.dart';
import '../widgets/user_dropdown_button.dart';
import '../widgets/we2up_text.dart';

class ProfitLossScreen extends StatelessWidget {
  const ProfitLossScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    final cubit = context.read<ProfitLossCubit>();
    return Scaffold(
      appBar: AppBar(title: Text(strings.profit_loss_report)),
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 10.sp),
        child: ListView(
          children: [
            Row(
              children: [
                We2upText(strings.branch),
                Gap(8.sp),
                Expanded(
                  child: BlocBuilder<ProfitLossCubit, ProfitLossState>(
                    buildWhen: (_, c) => c is BusinessLocationChanged,
                    builder: (context, state) {
                      return BusinessLocationDropdown(
                        isExpanded: true,
                        onChanged: cubit.changeSelectedLocation,
                        value: cubit.location,
                        isFilter: true,
                      );
                    },
                  ),
                ),
              ],
            ),
            const Divider(),
            Row(
              children: [
                We2upText("${strings.date_range}:"),
                Gap(8.sp),
                Expanded(
                  child: BlocBuilder<ProfitLossCubit, ProfitLossState>(
                    buildWhen: (_, current) => current is DateRangeUpdated,
                    builder: (context, state) {
                      return DateRangeDropdown(
                        changeStartDate: cubit.updateStartDate,
                        changeEndDate: cubit.updateEndDate,
                        onChanged: cubit.updateDateRange,
                        range: cubit.range,
                        isLocation: true,
                      );
                    },
                  ),
                ),
              ],
            ),
            BlocBuilder<ProfitLossCubit, ProfitLossState>(
              buildWhen: (_, current) => current is StartDateUpdated,
              builder: (context, state) {
                return DateRangeListTile(
                  isStartDate: true,
                  changeDate: cubit.updateStartDate,
                  changeRange: cubit.updateDateRange,
                  date: cubit.startDate,
                );
              },
            ),
            BlocBuilder<ProfitLossCubit, ProfitLossState>(
              buildWhen: (_, current) => current is EndDateUpdated,
              builder: (context, state) {
                return DateRangeListTile(
                  isStartDate: false,
                  isLocation: true,
                  changeDate: cubit.updateEndDate,
                  changeRange: cubit.updateDateRange,
                  date: cubit.endDate,
                );
              },
            ),
            const Divider(),
            Row(
              children: [
                We2upText("${strings.user_name}:"),
                Gap(8.sp),
                Expanded(
                  child: BlocBuilder<ProfitLossCubit, ProfitLossState>(
                    buildWhen: (_, c) => c is FilterUserUpdated,
                    builder: (context, state) {
                      return UsersDropdownButton(
                        user: cubit.user,
                        onChanged: cubit.updateFilterUser,
                        controller: cubit.filterUserController,
                      );
                    },
                  ),
                ),
              ],
            ),
            Gap(8.sp),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(
                  width: 50.w,
                  child: OutlinedButton.icon(
                    icon: const Icon(Icons.save_outlined),
                    label: We2upText(strings.search),
                    onPressed: () async {
                      EasyLoading.show(status: strings.loading);
                      await cubit.getProfitLossReport();
                      EasyLoading.dismiss();
                    },
                  ),
                ),
              ],
            ),
            const Divider(),
            BlocBuilder<ProfitLossCubit, ProfitLossState>(
              buildWhen: (_, c) => c is ProfitLossReportReady,
              builder: (context, state) {
                return ConditionalBuilder(
                    condition: cubit.report != null,
                    builder: (context) {
                      return Column(
                        children: [
                          ReportTile(
                            title: strings.opening_stock,
                            subtitle: strings.with_purchase_price,
                            trailing: cubit.report!.openingStock,
                          ),
                          ReportTile(
                            title: strings.closing_stock,
                            subtitle: strings.with_purchase_price,
                            trailing: cubit.report!.closingStock,
                          ),
                          ReportTile(
                            title: strings.total_sell,
                            subtitle: strings.not_including_tax_and_discount,
                            trailing: cubit.report!.totalSell,
                          ),
                          ReportTile(
                            title: strings.total_purchase,
                            subtitle: strings.not_including_tax_and_discount,
                            trailing: cubit.report!.totalPurchase,
                          ),
                          ReportTile(
                            title: strings.total_sell_shipping_charge,
                            trailing: cubit.report!.totalSellShippingCharge,
                          ),
                          ReportTile(
                            title: strings.total_purchase_shipping_charge,
                            trailing: cubit.report!.totalPurchaseShippingCharge,
                          ),
                          ReportTile(
                            title: strings.total_sell_discount,
                            trailing: cubit.report!.totalSellDiscount,
                          ),
                          ReportTile(
                            title: strings.total_purchase_discount,
                            trailing: cubit.report!.totalPurchaseDiscount,
                          ),
                          ReportTile(
                            title: strings.total_sell_return,
                            trailing: cubit.report!.totalSellReturn,
                          ),
                          ReportTile(
                            title: strings.total_purchase_return,
                            trailing: cubit.report!.totalPurchaseReturn,
                          ),
                          ReportTile(
                            title: strings.total_expense,
                            trailing: cubit.report!.totalExpense,
                          ),
                          ReportTile(
                            title: strings.total_recovered,
                            trailing: cubit.report!.totalRecovered,
                          ),
                          const Divider(),
                          ReportTile(
                            title: strings.net_profit,
                            trailing: cubit.report!.grossProfit,
                          ),
                          ReportTile(
                            title: strings.gross_profit,
                            trailing: cubit.report!.netProfit,
                          ),
                        ],
                      );
                    },
                    fallback: (context) => const SizedBox());
              },
            ),
          ],
        ),
      ),
    );
  }
}

class ReportTile extends StatelessWidget {
  const ReportTile({
    super.key,
    required this.title,
    required this.trailing,
    this.subtitle,
  });

  final String title;
  final double trailing;
  final String? subtitle;

  @override
  Widget build(BuildContext context) {
    return ListTile(
      title: We2upText(title),
      subtitle: subtitle != null ? We2upText(subtitle!) : null,
      trailing: We2upText(
        "${trailing.toStringAsFixed(4)} ${currencySymbol()}",
        style: Theme.of(context).textTheme.labelLarge,
      ),
    );
  }
}
