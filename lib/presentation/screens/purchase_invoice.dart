import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:responsive_builder/responsive_builder.dart';
import 'package:sizer/sizer.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/bloc/products/products_cubit.dart';
import 'package:we2up/utils/invoice_handler.dart';
import 'package:we2up/utils/route_constants.dart';

import '../../data/models/bill_of_sale_data.dart';
import '../../data/models/business_settings.dart';
import '../../data/models/product.dart';
import '../../utils/we2up_constants.dart';
import '../widgets/filter_dialog.dart';
import '../widgets/purchase_invoice_layouts/purchase_details_section.dart';
import '../widgets/shop_products_listview.dart';
import '../widgets/we2up_text.dart';

class PurchaseInvoice extends StatefulWidget {
  const PurchaseInvoice({super.key, required this.billOfSaleData});

  final BillData billOfSaleData;

  @override
  State<PurchaseInvoice> createState() => _PurchaseInvoiceState();
}

class _PurchaseInvoiceState extends State<PurchaseInvoice> {
  late ProductsCubit cubit;
  late BillData _billData;

  @override
  void initState() {
    cubit = ProductsCubit.get(context);
    _billData = widget.billOfSaleData;
    cubit.initAllProducts(billData: _billData);
    cubit.initCart(billData: _billData);
    if (_billData.purchase == null) {
      cubit.resetPaymentDetailsList();
    }
    super.initState();
  }

  @override
  void didChangeDependencies() {
    if (previousRouteName(context) == landingScreen) {
      cubit.resetPurchasesFilters();
    }
    cubit.resetPaymentDetailsList();
    super.didChangeDependencies();
  }

  @override
  void dispose() {
    cubit.initAllProducts();
    cubit.initCart();
    cubit.resetPaymentDetailsList();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return InvoiceHandler(
      focusNode: cubit.invoiceFocusNode,
      productsDropdownKey: cubit.productsDropdownKey,
      child: BlocBuilder<ProductsCubit, ProductsState>(
        builder: (context, state) {
          final strings = AppLocalizations.of(context)!;
          return Scaffold(
            appBar: AppBar(
              title: We2upText(
                _billData.pReturn
                    ? strings.invoice_return_details
                    : (_billData.purchase != null
                        ? strings.edit_purchase
                        : strings.receipt),
              ),
              centerTitle: true,
              actions: [
                IconButton(
                  onPressed: () => showDialog(
                    context: context,
                    builder: (_) => const FilterDialog(isPurchase: true),
                  ),
                  icon: Icon(
                    Icons.filter_list_alt,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                )
              ],
            ),
            body: Padding(
              padding: EdgeInsets.symmetric(horizontal: 10.sp),
              child: ScreenTypeLayout.builder(
                mobile: (_) => PurchaseDetailsSection(billData: _billData),
                desktop: (_) {
                  List<Product> products =
                      cubit.filteredProductList(isPurchase: true);
                  
                  // Check if products panel should be shown
                  final shouldShowProductsPanel = !_billData.pReturn && showShopProductsGridView();
                  
                  if (shouldShowProductsPanel) {
                    // Show both panels side by side
                    return Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: PurchaseDetailsSection(billData: _billData),
                        ),
                        Expanded(
                          child: ShopProductsGridView(
                            products: products,
                            isPurchase: true,
                          ),
                        ),
                      ],
                    );
                  } else {
                    // Show only the purchase details section
                    return PurchaseDetailsSection(billData: _billData);
                  }
                },
              ),
            ),
          );
        },
      ),
    );
  }
}
