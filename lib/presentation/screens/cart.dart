import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:sizer/sizer.dart';
import 'package:we2up/bloc/products/products_cubit.dart';
import 'package:we2up/presentation/widgets/product_card_item.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/utils/route_constants.dart';

import '../style_constants.dart';
import '../widgets/we2up_text.dart';

class Cart extends StatelessWidget {
  const Cart({super.key});

  @override
  Widget build(BuildContext context) {
    ProductsCubit cubit = ProductsCubit.get(context);
    final searchController = cubit.cartSearchController;
    searchController.addListener(cubit.updateSearchFilter);
    return GestureDetector(
      onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
      child: Scaffold(
        key: cubit.cartScaffold<PERSON>ey,
        appBar: AppBar(
          title: We2upText(AppLocalizations.of(context)!.cart),
          centerTitle: true,
          bottom: PreferredSize(
            preferredSize: Size.fromHeight(50.sp),
            child: Container(
              padding: EdgeInsets.symmetric(vertical: 5.sp),
              color: Theme.of(context).scaffoldBackgroundColor,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                mainAxisSize: MainAxisSize.max,
                children: <Widget>[
                  SizedBox(
                    width: 90.w,
                    child: Row(
                      children: [
                        Expanded(
                          child: TextField(
                            controller: searchController,
                            decoration: InputDecoration(
                              hintText: AppLocalizations.of(context)!.search,
                              prefixIcon: const Icon(Icons.search),
                              border: myTextFieldBorder,
                            ),
                          ),
                        ),
                        IconButton(
                          icon: Icon(
                            Icons.document_scanner_outlined,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                          onPressed: () => cubit.scanBarcode(context),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        body: BlocBuilder<ProductsCubit, ProductsState>(
          builder: (context, state) {
            return ListView.builder(
              // +2 for Divider and CartBottomSheet
              itemCount: cubit.cartFilteredProducts().length,
              itemBuilder: (context, index) {
                return ProductCard(
                  id: cubit.cartFilteredProducts().elementAt(index),
                );
              },
            );
          },
        ),
        bottomSheet: Container(
          color: Theme.of(context).colorScheme.surface,
          width: double.infinity,
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.sp, vertical: 4.sp),
            child: BlocBuilder<ProductsCubit, ProductsState>(
              builder: (context, state) {
                return ElevatedButton(
                  onPressed: cubit.cartProducts.isNotEmpty
                      ? () => context.push(chooseCustomer)
                      : null,
                  child:
                      We2upText(AppLocalizations.of(context)!.choose_customer),
                );
              },
            ),
          ),
        ),
      ),
    );
  }
}
