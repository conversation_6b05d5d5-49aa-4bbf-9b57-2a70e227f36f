import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:sizer/sizer.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/bloc/contacts/contacts_cubit.dart';
import 'package:we2up/bloc/products/products_cubit.dart';

import '../../data/db/db_manager.dart';
import '../../utils/route_constants.dart';
import '../../utils/we2up_constants.dart';
import '../style_constants.dart';
import '../widgets/contacts_dropdown_button.dart';
import '../widgets/we2up_text.dart';

class ChooseCustomer extends StatelessWidget {
  const ChooseCustomer({super.key});

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    return BlocBuilder<ProductsCubit, ProductsState>(
      builder: (context, state) {
        final cubit = ProductsCubit.get(context);
        return Scaffold(
          appBar: AppBar(
            title: We2upText(strings.customer),
            centerTitle: true,
          ),
          floatingActionButton: (canCreateSupplier() || canCreateCustomer())
              ? FloatingActionButton(
                  onPressed: () async => await checkShiftLocationAccess(
                    context,
                    () => context.push(
                      addEditContact,
                      extra: (null, ContactsCubit(), true),
                    ),
                  ),
                  child: const Icon(Icons.person_add_alt_1),
                )
              : null,
          body: Padding(
            padding: EdgeInsets.all(5.sp),
            child: ListView(
              children: [
                SizedBox(height: 8.sp),
                ContactsDropdownButton(
                  filterType: 'customer',
                  onChanged: cubit.changeSelectedContact,
                  contact: cubit.cartCustomerContact,
                ),
                SizedBox(height: 8.sp),
                Visibility(
                  visible: cubit.cartCustomerContact != null,
                  child: Container(
                    width: double.infinity,
                    padding: EdgeInsets.all(5.sp),
                    decoration: showBoxDecoration(context),
                    child: Column(
                      children: [
                        ListTile(
                          title: We2upText(
                            "${strings.name}:",
                            style: Theme.of(context).textTheme.titleMedium,
                          ),
                          trailing:
                              We2upText(cubit.cartCustomerContact?.name ?? ''),
                        ),
                        const Divider(),
                        ListTile(
                          title: We2upText(
                            "${strings.mobile}:",
                            style: Theme.of(context).textTheme.titleMedium,
                          ),
                          trailing: We2upText(
                              cubit.cartCustomerContact?.mobile ?? 'NA'),
                        ),
                        const Divider(),
                        ListTile(
                          title: We2upText(
                            "${strings.balance}:",
                            style: Theme.of(context).textTheme.titleMedium,
                          ),
                          trailing:
                              We2upText(cubit.cartCustomerContact?.due ?? ''),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          bottomNavigationBar: Container(
            width: double.infinity,
            padding: EdgeInsets.all(10.sp),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(
                  width: 90.w,
                  child: ElevatedButton.icon(
                    onPressed: cubit.cartCustomerContact == null
                        ? null
                        : () async {
                            await cubit
                                .sendSellToAPI(context, isQuotation: 1)
                                .then((value) {
                              if (value && context.mounted) {
                                context.go(landingScreen);
                              }
                            });
                          },
                    label: We2upText(strings.add_price_offer),
                    icon: const Icon(Icons.add),
                  ),
                ),
                SizedBox(height: 8.sp),
                SizedBox(
                  width: 90.w,
                  child: ElevatedButton(
                    onPressed: cubit.cartCustomerContact == null ||
                            cubit.cartProducts.every((id) =>
                                !(cubit.availableInStock(id) > 0 ||
                                    cubit.allowOverSelling(id))) ||
                            !canCreateSell()
                        ? null
                        : () => context.push(payment),
                    child: We2upText(strings.pay_and_continue),
                  ),
                )
              ],
            ),
          ),
        );
      },
    );
  }
}
