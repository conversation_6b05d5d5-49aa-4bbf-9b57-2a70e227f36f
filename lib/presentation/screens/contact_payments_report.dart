import 'package:conditional_builder_null_safety/conditional_builder_null_safety.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:gap/gap.dart';
import 'package:sizer/sizer.dart';
import 'package:we2up/bloc/payments_report/contact_payment_report_cubit.dart';
import 'package:we2up/data/db/db_manager.dart';
import 'package:we2up/presentation/widgets/show_total.dart';
import 'package:we2up/utils/we2up_constants.dart';

import '../../data/models/business_settings.dart';
import '../../data/models/contact_payment_model.dart';
import '../widgets/date_range_dropdown.dart';
import '../widgets/payment_report_filter_dialog.dart';
import '../widgets/we2up_text.dart';

class ContactPaymentReportScreen extends StatelessWidget {
  const ContactPaymentReportScreen({super.key, required this.supplier});

  final bool supplier;

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    final cubit = context.read<PaymentsReportCubit>();
    cubit.scrollController.addListener(() {
      if (cubit.scrollController.position.pixels ==
          cubit.scrollController.position.maxScrollExtent) {
        cubit.getPaymentsReport(supplier, false);
      }
    });
    return Scaffold(
      appBar: AppBar(
        title: We2upText(
          supplier ? strings.supplier_payments : strings.customer_collections,
        ),
        actions: [
          IconButton(
            onPressed: () => showDialog(
              context: context,
              builder: (context) => BlocProvider.value(
                value: cubit,
                child: PaymentReportFilterDialog(supplier),
              ),
            ),
            icon: const Icon(Icons.filter_alt_sharp),
          )
        ],
      ),
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 10.sp),
        child: Column(
          children: [
            Row(
              children: [
                We2upText("${strings.date_range}:"),
                Gap(8.sp),
                Expanded(
                  child: BlocBuilder<PaymentsReportCubit, PaymentsReportState>(
                    buildWhen: (_, c) => c is DateRangeUpdated,
                    builder: (context, state) {
                      return DateRangeDropdown(
                        changeStartDate: cubit.updateStartDate,
                        changeEndDate: cubit.updateEndDate,
                        onChanged: cubit.updateDateRange,
                        range: cubit.range,
                      );
                    },
                  ),
                ),
              ],
            ),
            Gap(8.sp),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                BlocListener<PaymentsReportCubit, PaymentsReportState>(
                  listener: (context, state) {
                    if (state is NewReportLoading) {
                      EasyLoading.show(status: strings.loading);
                    } else {
                      EasyLoading.dismiss();
                    }
                  },
                  child: SizedBox(
                    width: 50.w,
                    child: OutlinedButton.icon(
                      icon: const Icon(Icons.save_outlined),
                      label: We2upText(strings.search),
                      onPressed: () => cubit.getPaymentsReport(supplier, true),
                    ),
                  ),
                ),
              ],
            ),
            const Divider(),
            BlocBuilder<PaymentsReportCubit, PaymentsReportState>(
              buildWhen: (previous, current) => current is ReportLoaded,
              builder: (context, state) {
                if (state is ReportLoaded) {
                  return Expanded(
                    child: ConditionalBuilder(
                      condition: state.payments.isNotEmpty,
                      builder: (context) => Column(
                        children: [
                          Expanded(
                            child: ListView.builder(
                              controller: cubit.scrollController,
                              itemCount: state.payments.length,
                              itemBuilder: (context, index) {
                                return PaymentTile(state.payments[index]);
                              },
                            ),
                          ),
                          ShowTotal(
                            amountText: supplier
                                ? strings.sum_report_paid
                                : strings.sum_report_collected,
                            amountValue: state.totalAmount.toStringAsFixed(4),
                          )
                        ],
                      ),
                      fallback: (context) => Center(
                        child: We2upText(strings.no_transactions_in_range),
                      ),
                    ),
                  );
                }
                return const SizedBox();
              },
            ),
          ],
        ),
      ),
    );
  }
}

class PaymentTile extends StatelessWidget {
  const PaymentTile(this.payment, {super.key});

  final ContactPaymentModel payment;

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    bool supplier = contactsBox.get(payment.contactId)!.type == "supplier";
    return Card(
      child: Padding(
        padding: EdgeInsets.fromLTRB(10.sp, 0, 10.sp, 5.sp),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: We2upText(
                    '${strings.reference_number}: ${payment.refNo}',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                ),
                TextButton(
                  onPressed: null,
                  child: We2upText(payment.method ?? strings.nothing),
                ),
              ],
            ),
            We2upText(
              '${strings.transaction_date}: '
              '${formatDate(payment.createdAt)}',
            ),
            Gap(8.sp),
            We2upText(
              '${supplier ? strings.total_report_paid : strings.total_report_collected} '
              '${payment.amount} ${currencySymbol()}',
            ),
            Gap(8.sp),
            We2upText("${strings.user_name}: ${payment.firstName}"),
            Gap(8.sp),
            We2upText("${supplier ? strings.supplier_name : strings.customer_name} "
                "${payment.contactName}"),
          ],
        ),
      ),
    );
  }
}
