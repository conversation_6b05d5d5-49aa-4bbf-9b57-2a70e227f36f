import 'package:flutter/material.dart';
import 'package:responsive_builder/responsive_builder.dart';
import 'package:sizer/sizer.dart';
import '../widgets/home_layouts/action_buttons_gridview.dart';
import '../widgets/home_layouts/rosary_summary.dart';
import '../widgets/home_layouts/sales_purchases_summary.dart';

class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(5.sp),
      child: ScreenTypeLayout.builder(
        mobile: (_) => ListView(
          children: const [
            SalesAndPurchasesSummary(),
            <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(),
            ActionButtonsGridView(),
          ],
        ),
        desktop: (_) => const Row(
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    SalesAndPurchasesSummary(),
                    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(),
                  ],
                ),
              ),
            ),
            Expanded(
              child: SingleChildScrollView(child: ActionButtonsGridView()),
            ),
          ],
        ),
      ),
    );
  }
}
