import 'dart:developer';

import 'package:conditional_builder_null_safety/conditional_builder_null_safety.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_offline/flutter_offline.dart';
import 'package:go_router/go_router.dart';
import 'package:responsive_builder/responsive_builder.dart';
import 'package:we2up/bloc/landing/landing_cubit.dart';
import '../../bloc/products/products_cubit.dart';
import 'package:badges/badges.dart' as badges;
import '../../data/db/db_manager.dart';
import '../../data/models/business_settings.dart';
import '../../data/repository/api_repo.dart';
import '../../utils/route_constants.dart';
import '../../utils/we2up_constants.dart';
import '../widgets/landing_layouts/landing_bottom_bar.dart';
import '../widgets/landing_layouts/main_drawer.dart';
import '../widgets/locations_dropdown_button.dart';
import '../widgets/sale_filter_dialog.dart';
import '../widgets/we2up_text.dart';

class LandingScreen extends StatelessWidget {
  const LandingScreen({super.key});

  @override
  Widget build(BuildContext context) {
    LandingCubit cubit = LandingCubit.get(context);
    ProductsCubit productsCubit = ProductsCubit.get(context);
    return WillPopScope(
      onWillPop: () async {
        if (cubit.currentIndex != 0) {
          cubit.switchPage(0);
        } else {
          bool? shouldPop = await cubit.showExitConfirmationDialog(context);
          return shouldPop ?? false;
        }
        return false;
      },
      child: ResponsiveBuilder(builder: (context, sizingInformation) {
        return BlocBuilder<LandingCubit, LandingState>(
          builder: (context, state) {
            return Scaffold(
              drawer:
                  sizingInformation.deviceScreenType == DeviceScreenType.desktop
                      ? null
                      : const MainDrawer(),
              appBar: buildAppBar(cubit, productsCubit, context),
              body: Row(
                children: [
                  if (sizingInformation.deviceScreenType ==
                      DeviceScreenType.desktop)
                    const MainDrawer(),
                  Expanded(child: cubit.currentPage[cubit.currentIndex]),
                ],
              ),
              bottomNavigationBar: const LandingBottomBar(),
              floatingActionButton: kDebugMode
                  ? FloatingActionButton(
                      onPressed: () => log(
                        loginData.accessToken.toString(),
                      ),
                      child: const Icon(Icons.abc),
                    )
                  : null,
            );
          },
        );
      }),
    );
  }

  AppBar buildAppBar(
    LandingCubit cubit,
    ProductsCubit productsCubit,
    BuildContext context,
  ) {
    return AppBar(
      title: ConditionalBuilder(
        condition: cubit.currentIndex == cubit.productsPageIndex(),
        builder: (_) {
          return BlocBuilder<ProductsCubit, ProductsState>(
            builder: (context, state) {
              final p = ProductsCubit.get(context);
              return BusinessLocationDropdown(
                onChanged: (l) => p.changeSelectedLocation(location: l!),
                value: p.selectedLocation,
              );
            },
          );
        },
        fallback: (_) => We2upText("${credentialsBox.get(baseUrl).name}.WE2UP"),
      ),
      leading: cubit.currentIndex == 1
          ? BlocBuilder<ProductsCubit, ProductsState>(
              builder: (context, state) {
                return badges.Badge(
                  position: badges.BadgePosition.topEnd(top: 0, end: 3),
                  badgeAnimation: const badges.BadgeAnimation.slide(
                    disappearanceFadeAnimationDuration:
                        Duration(milliseconds: 100),
                    curve: Curves.easeInCubic,
                  ),
                  showBadge: productsCubit.cartProducts.isNotEmpty,
                  badgeStyle: badges.BadgeStyle(
                    badgeColor: Theme.of(context).colorScheme.outlineVariant,
                  ),
                  badgeContent: We2upText(
                    productsCubit.cartProducts.length.toString(),
                  ),
                  child: IconButton(
                    icon: const Icon(Icons.shopping_cart),
                    onPressed: productsCubit.cartProducts.isEmpty
                        ? null
                        : () async => await checkShiftLocationAccess(
                              context,
                              () => context.push(cartScreen),
                            ),
                  ),
                );
              },
            )
          : cubit.currentIndex == 2
              ? BlocBuilder<ProductsCubit, ProductsState>(
                  buildWhen: (_, c) => c is CurrentSalesIndexChanged,
                  builder: (context, state) {
                    if (state is CurrentSalesIndexChanged) {
                      return Visibility(
                        visible: state.index != 0,
                        child: IconButton(
                          onPressed: () => showDialog(
                            context: context,
                            builder: (_) => const SaleFilterDialog(),
                          ),
                          icon: const Icon(Icons.filter_list_alt),
                        ),
                      );
                    }
                    return const SizedBox();
                  },
                )
              : null,
      centerTitle: true,
      actions: [
        OfflineBuilder(
          connectivityBuilder: (
            BuildContext context,
            dynamic connectivity,
            Widget child,
          ) {
            bool connected;

            if (connectivity is List<ConnectivityResult>) {
              connected = !connectivity.contains(ConnectivityResult.none);
            } else if (connectivity is ConnectivityResult) {
              connected = connectivity != ConnectivityResult.none;
            } else {
              connected = false;
            }

            return Icon(
              connected ? Icons.wifi : Icons.wifi_off,
              color: connected ? Colors.green : Colors.red,
            );
          },
          child: const SizedBox(),
        ),
        if (!stopSyncingTransactions())
          OfflineBuilder(
            connectivityBuilder: (
              BuildContext context,
              dynamic connectivity,
              Widget child,
            ) {
              bool connected;

              if (connectivity is List<ConnectivityResult>) {
                connected = !connectivity.contains(ConnectivityResult.none);
              } else if (connectivity is ConnectivityResult) {
                connected = connectivity != ConnectivityResult.none;
              } else {
                connected = false;
              }

              return IconButton(
                onPressed: !isDataInSync() && connected
                    ? () => ApiRepository.get()
                        .syncOfflineDataWithAPI(context: context)
                    : null,
                icon: !isDataInSync()
                    ? Icon(
                        Icons.sync_problem,
                        color: Theme.of(context).colorScheme.error,
                      )
                    : const Icon(Icons.done_all),
              );
            },
            child: const SizedBox(),
          ),
      ],
    );
  }
}
