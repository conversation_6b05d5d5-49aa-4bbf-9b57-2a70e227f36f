import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

RoundedRectangleBorder homeCardShape = RoundedRectangleBorder(
  borderRadius: BorderRadius.all(
    Radius.circular(8.sp),
  ),
);

ShapeDecoration showBoxDecoration(context, {Color? color}) => ShapeDecoration(
      color: color,
      shape: RoundedRectangleBorder(
        side: BorderSide(
          width: 2,
          color: Theme.of(context).colorScheme.inversePrimary,
        ),
        borderRadius: BorderRadius.circular(15.sp),
      ),
    );

ShapeDecoration dropDownDecoration(context) => ShapeDecoration(
      shape: RoundedRectangleBorder(
        side: BorderSide(
          width: 2,
          color: Theme.of(context).colorScheme.inversePrimary,
        ),
        borderRadius: BorderRadius.circular(15.sp),
      ),
    );

// Container decoration for dropdown buttons with fixed height
BoxDecoration dropDownContainerDecoration(context) => BoxDecoration(
      border: Border.all(
        width: 2,
        color: Theme.of(context).colorScheme.inversePrimary,
      ),
      borderRadius: BorderRadius.circular(15.sp),
    );


InputBorder filterInputBorder(context) {
  return OutlineInputBorder(
    borderSide: BorderSide(
      width: 2,
      color: Theme.of(context).colorScheme.inversePrimary,
    ),
    borderRadius: BorderRadius.circular(15.sp),
  );
}

InputDecoration searchDropdownButtonDecoration(
  context, {
  String? hintText,
  bool isSearch = false,
}) =>
    InputDecoration(
      hintText: hintText,
      alignLabelWithHint: true,
      prefixIcon: isSearch ? const Icon(Icons.search) : null,
      border: OutlineInputBorder(
        borderSide: BorderSide(
          width: 1,
          color: Theme.of(context).colorScheme.inversePrimary,
        ),
        borderRadius: BorderRadius.circular(16.sp),
      ),
    );

InputDecoration contactSearchDropdownButtonDecoration({
  required BuildContext context,
  required String hintText,
}) =>
    InputDecoration(
      hintText: hintText,
      alignLabelWithHint: true,
      prefixIcon: const Icon(Icons.search),
      contentPadding: EdgeInsets.symmetric(horizontal: 5.sp),
      border: OutlineInputBorder(
        borderSide: BorderSide(
          width: 2,
          color: Theme.of(context).colorScheme.inversePrimary,
        ),
        borderRadius: BorderRadius.circular(15.sp),
      ),
    );

RoundedRectangleBorder activeCardBorder(context) => RoundedRectangleBorder(
      side: BorderSide(
        width: 1.sp,
        color: Theme.of(context).colorScheme.primary,
      ),
      borderRadius: BorderRadius.all(
        Radius.circular(16.sp),
      ),
    );

ButtonStyle addPaymentButtonStyle(context) => ElevatedButton.styleFrom(
      shape: BeveledRectangleBorder(
        borderRadius: BorderRadius.circular(7.sp),
        side: BorderSide(
          color: Theme.of(context).colorScheme.primary,
        ),
      ),
    );

OutlineInputBorder loginButtonInputBorder = OutlineInputBorder(
  borderRadius: BorderRadius.circular(15.sp),
);

Widget verticalSpacer = SizedBox(height: 8.sp);

OutlineInputBorder myTextFieldBorder = OutlineInputBorder(
  borderRadius: BorderRadius.all(
    Radius.circular(7.sp),
  ),
);

Icon importantIcon(BuildContext context) {
  return Icon(
    Icons.label_important,
    color: Theme.of(context).colorScheme.error,
  );
}
