part of 'shifts_cubit.dart';

@immutable
abstract class ShiftsState {}

class <PERSON>ftsI<PERSON>tial extends ShiftsState {}

class DateRange<PERSON>pdated extends ShiftsState {}

class StartDateUpdated extends ShiftsState {}

class EndDateUpdated extends ShiftsState {}

class FilterUserUpdated extends ShiftsState {}

class ShiftsNewLoading extends ShiftsState {}

class ShiftsLoading extends ShiftsState {}

class ShiftsLoaded extends ShiftsState {}

class ShiftDetailsLoading extends ShiftsState {}

class ShiftDetailsError extends ShiftsState {}

class ShiftDetailsLoaded extends ShiftsState {
  final ShiftDetails shiftDetails;

  ShiftDetailsLoaded({required this.shiftDetails});
}
