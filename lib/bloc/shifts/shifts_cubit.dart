import 'package:bloc/bloc.dart';
import 'package:flutter/material.dart';
import 'package:we2up/data/models/shift_details.dart';
import 'package:we2up/data/repository/api_repo.dart';

import '../../data/models/cash_register.dart';
import '../../data/models/cash_register_to_api.dart';
import '../../data/models/user_model.dart';
import '../../presentation/widgets/date_range_dropdown.dart';

part 'shifts_state.dart';

class ShiftsCubit extends Cubit<ShiftsState> {
  late ApiRepository apiRepo;

  ShiftsCubit() : super(ShiftsInitial()) {
    apiRepo = ApiRepository.get();
  }

  DateRange range = DateRange.today;

  DateTime? startDate = DateTime.now();

  DateTime? endDate = DateTime.now();

  UserModel? user;

  CashRegister? currentShift;

  TextEditingController filterUserController = TextEditingController();

  void loadShiftDetails(CashRegister shift) async {
    emit(ShiftDetailsLoading());
    currentShift = shift;
    ShiftDetails? shiftDetails = await apiRepo.fetchShiftDetails(shift.id);
    if (shiftDetails != null) {
      emit(ShiftDetailsLoaded(shiftDetails: shiftDetails));
    } else {
      emit(ShiftDetailsError());
    }
  }

  void updateDateRange(DateRange range) {
    this.range = range;
    emit(DateRangeUpdated());
  }

  void updateStartDate(DateTime? date) {
    startDate = date;
    emit(StartDateUpdated());
  }

  void updateEndDate(DateTime? date) {
    endDate = date;
    emit(EndDateUpdated());
  }

  void updateFilterUser(UserModel? user) {
    this.user = user;
    emit(FilterUserUpdated());
  }

  ScrollController scrollController = ScrollController();

  int currentPage = 1;
  List<CashRegister> totalShifts = [];

  void getShifts({bool isNewSearch = false}) async {
    if (state is ShiftsLoading) return;
    if (isNewSearch) {
      currentPage = 1;
      totalShifts = [];
    }
    if (isNewSearch) emit(ShiftsNewLoading());
    if (!isNewSearch) emit(ShiftsLoading());
    final shifts = await apiRepo.getShiftsList(
      page: currentPage,
      startDate: startDate,
      endDate: endDate,
      userId: user?.id,
    );
    totalShifts.addAll(shifts ?? []);
    currentPage++;
    // totalShifts.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    emit(ShiftsLoaded());
  }

  void shiftUpdated() => emit(ShiftsLoaded());

  Future<bool> closeShift(double closingAmount) async {
    final locationInfo = await apiRepo.getCurrentLocation();
    final closedCashRegister = await apiRepo.closeCashRegister(
      cashRegister: CashRegisterToAPI(
        id: currentShift?.id,
        locationInfo: locationInfo,
        createdAt: DateTime.now(),
        status: CashRegisterStatus.close,
        closingAmount: closingAmount,
        closingNote: "",
      ),
    );
    if (closedCashRegister != null && currentShift != null) {
      totalShifts[totalShifts.indexOf(currentShift!)] = closedCashRegister;
    }
    return closedCashRegister != null;
  }

  @override
  Future<void> close() {
    startDate = null;
    endDate = null;
    user = null;
    scrollController.dispose();
    filterUserController.dispose();
    return super.close();
  }
}
