import 'dart:async';

import 'package:cron/cron.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:we2up/data/models/expense.dart';
import 'package:we2up/data/models/purchase.dart';
import 'package:we2up/data/models/purchase_return.dart';
import 'package:we2up/data/models/sell.dart';
import 'package:we2up/data/models/sell_return.dart';
import '../../data/models/business_settings.dart';
import '../../data/models/contact_payment_model.dart';
import '../../data/models/shift_details.dart';
import '../../data/repository/api_repo.dart';
import '../../presentation/screens/home.dart';
import '../../presentation/screens/products.dart';
import '../../presentation/screens/sales.dart';
import '../../data/db/db_manager.dart';

part 'landing_state.dart';

class LandingCubit extends Cubit<LandingState> {
  late final ApiRepository apiRepository;
  late final Cron cron;
  late final PackageInfo packageInfo;

  LandingCubit(BuildContext context) : super(LandingInitial()) {
    apiRepository = ApiRepository.get();
    cron = Cron();
    initPackageInfo();

    cron.schedule(Schedule.parse('*/10 * * * *'), () async {
      if (!stopSyncingTransactions()) {
        final connected = await InternetConnectionChecker.instance.hasConnection;
        if (loginData.accessToken != null &&
            connected &&
            isProductsAndContactsReady()) {
          await apiRepository.syncOfflineDataWithAPI();
          final locationInfo = await apiRepository.getCurrentLocation();
          await apiRepository.updateCurrentLocation(info: locationInfo);
          await Future.wait([
            fetchContactsAndStoreInHive(refresh: true),
            fetchProductsAndStoreInHive(refresh: true),
          ]).then(
            (_) async {
              if (!context.mounted) return;
              await fetchCashRegistersAndStoreInHive(
                context,
                refresh: true,
              );
            },
          );
          updateLandingScreen();
        }
      }
    });
    updateTotalReturnsPayments();
    startButtonUpdateStream();
  }

  static LandingCubit get(context) => BlocProvider.of<LandingCubit>(context);

  void updateLandingScreen() => emit(LandingScreenUpdated());

  ShiftDetails? shiftDetails;

  Future<void> initPackageInfo() async {
    emit(AppVersionLoading());
    packageInfo = await PackageInfo.fromPlatform();
    emit(AppVersionReady());
  }

  void resetTotalReturns() {
    shiftDetails = null;
    emit(TotalReturnsUpdated());
  }

  void updateTotalReturnsPayments() async {
    if (loginData.cashRegisteredId != null && !stopSyncingTransactions()) {
      final totals = await apiRepository.fetchShiftDetails(
        loginData.cashRegisteredId!,
      );
      if (totals != null) shiftDetails = totals;
      emit(TotalReturnsUpdated());
    }
  }

  void startButtonUpdateStream({bool runImmediately = false}) {
    if (runImmediately) {
      emit(UpdateButtonStateChange(false));
    }

    Stream.periodic(const Duration(minutes: 5), (count) {
      return isWithinLast10Minutes();
    }).listen((isWithinTime) {
      emit(UpdateButtonStateChange(!isWithinTime));
    });
  }

  Future<bool?> showExitConfirmationDialog(BuildContext context) async {
    final strings = AppLocalizations.of(context)!;
    return showDialog<bool>(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text(strings.exit_app),
          content: Text(strings.exit_app_q),
          actions: <Widget>[
            TextButton(
              onPressed: () => context.pop(false),
              child: Text(strings.no),
            ),
            TextButton(
              onPressed: () => context.pop(true),
              child: Text(strings.yes),
            ),
          ],
        );
      },
    );
  }

  int currentIndex = 0;
  List<Widget> currentPage = [
    const HomePage(),
    if (canViewProduct()) ...[const ProductsPage()],
    if (canViewSales()) ...[const SalesPage()],
  ];

  int productsPageIndex() {
    if (canViewProduct()) {
      for (int i = 0; i < currentPage.length; i++) {
        if (canViewProduct() &&
            currentPage[i].runtimeType.toString() == 'ProductsPage') {
          return i;
        }
      }
    }
    return -1;
  }

  int salesPageIndex() {
    if (canViewSales()) {
      for (int i = 0; i < currentPage.length; i++) {
        if (canViewSales() &&
            currentPage[i].runtimeType.toString() == 'SalesPage') {
          return i;
        }
      }
    }
    return -1;
  }

  void switchPage(int i) {
    currentIndex = i;
    emit(PageSwitched());
  }

  String shiftSalesTotalNumber() {
    int totalNumber = sellsBox.values
        .where(
          (element) =>
              element.transactionDate.isAfter(cashRegisteredTime()) &&
              element.isQuotation == 0,
        )
        .length;

    return totalNumber.toString();
  }

  String shiftSalesTotalAmount() {
    // if (shiftDetails != null) {
    //   return shiftDetails!.transactions.first.totalSell;
    // }
    double totalAmount = 0.0;

    for (Sell sell in sellsBox.values.where(
      (element) =>
          element.transactionDate.isAfter(cashRegisteredTime()) &&
          element.isQuotation == 0,
    )) {
      totalAmount += double.tryParse(sell.finalTotal!) ?? 0;
    }

    return totalAmount.toStringAsFixed(2);
  }

  String shiftSalesPaidUp() {
    // if (shiftDetails != null) {
    //   return shiftDetails!.payments.first.totalSellPaid;
    // }
    double paidUp = 0.0;

    for (Sell sell in sellsBox.values.where(
      (element) => element.transactionDate.isAfter(cashRegisteredTime()),
    )) {
      for (PaymentLine paymentLine in sell.paymentLines ?? []) {
        paidUp += double.tryParse(paymentLine.amount!) ?? 0;
      }
    }

    return paidUp.toStringAsFixed(2);
  }

  String shiftSalesDue() {
    return (double.parse(shiftSalesTotalAmount()) -
            double.parse(shiftSalesPaidUp()))
        .toStringAsFixed(2);
  }

  String shiftSalesReturns() {
    // if (shiftDetails != null) {
    //   return shiftDetails!.transactions.first.totalSellReturn;
    // }
    double returns = 0.0;

    for (SellReturn sellReturn in sellsReturnsBox.values.where(
      (element) => element.transactionDate.isAfter(cashRegisteredTime()),
    )) {
      returns += double.tryParse(sellReturn.finalTotal!) ?? 0;
    }

    return returns.toStringAsFixed(2);
  }

  String shiftPurchasesReturns() {
    // if (shiftDetails != null) {
    //   return shiftDetails!.transactions.first.totalPurchaseReturn;
    // }
    double returns = 0.0;

    for (PurchaseReturn sellReturn in purchaseReturnsBox.values.where(
      (element) => element.transactionDate.isAfter(cashRegisteredTime()),
    )) {
      returns += double.tryParse(sellReturn.finalTotal!) ?? 0;
    }

    return returns.toStringAsFixed(2);
  }

  String shiftPurchasesTotalNumber() {
    int totalNumber = purchasesBox.values
        .where(
          (element) => element.transactionDate.isAfter(cashRegisteredTime()),
        )
        .length;

    return totalNumber.toString();
  }

  String shiftPurchasesTotalAmount() {
    // if (shiftDetails != null) {
    //   return shiftDetails!.transactions.first.totalPurchase;
    // }
    double totalAmount = 0.0;

    for (Purchase purchase in purchasesBox.values.where(
      (element) => element.transactionDate.isAfter(cashRegisteredTime()),
    )) {
      totalAmount += double.tryParse(purchase.finalTotal!) ?? 0;
    }

    return totalAmount.toStringAsFixed(2);
  }

  String shiftPurchasesPaidUp() {
    // if (shiftDetails != null) {
    //   return shiftDetails!.payments.first.totalPurchasePaid;
    // }
    double paidUp = 0.0;

    for (Purchase purchase in purchasesBox.values.where(
      (element) => element.transactionDate.isAfter(cashRegisteredTime()),
    )) {
      for (PaymentLine paymentLine in purchase.paymentLines ?? []) {
        paidUp += double.tryParse(paymentLine.amount!) ?? 0;
      }
    }

    return paidUp.toStringAsFixed(2);
  }

  String purchasesDue() {
    return (double.parse(shiftPurchasesTotalAmount()) -
            double.parse(shiftPurchasesPaidUp()))
        .toStringAsFixed(2);
  }

  String shiftSupplierPayment() {
    // if (shiftDetails != null) {
    //   return shiftDetails!.notRelatedPayments.first.totalSupplierPaid;
    // }
    double paid = 0.0;

    for (ContactPaymentModel payment in paymentsReportBox.values.where(
      (payment) =>
          payment.createdAt.isAfter(cashRegisteredTime()) &&
          payment.createdAt.isBefore(DateTime.now()) &&
          contactsBox.get(payment.contactId)?.type == "supplier" &&
          payment.transactionType != "purchase_return",
    )) {
      paid += payment.amount ?? 0;
    }

    for (Purchase purchase in purchasesBox.values.where((p) => p.offline)) {
      for (PaymentLine paymentLine in purchase.paymentLines ?? []) {
        paid += double.parse(paymentLine.amount ?? "0");
      }
    }

    return paid.toStringAsFixed(2);
  }

  String shiftCustomerCollection() {
    // if (shiftDetails != null) {
    //   return shiftDetails!.notRelatedPayments.first.totalCustomerPaid;
    // }
    double collected = 0.0;

    for (ContactPaymentModel payment in paymentsReportBox.values.where(
      (payment) =>
          payment.createdAt.isAfter(cashRegisteredTime()) &&
          payment.createdAt.isBefore(DateTime.now()) &&
          contactsBox.get(payment.contactId)?.type == "customer" &&
          payment.transactionType != "sell_return",
    )) {
      collected += payment.amount ?? 0;
    }

    // offline payments aren't converted to contact payments => id problem
    // this is the easiest solution in my opinion.
    for (Sell sell in sellsBox.values.where((s) => s.offline)) {
      for (PaymentLine paymentLine in sell.paymentLines ?? []) {
        collected += double.parse(paymentLine.amount ?? "0");
      }
    }

    return collected.toStringAsFixed(2);
  }

  String expensesAmounts({bool refund = false}) {
    // if (shiftDetails != null) {
    //   if (!refund) {
    //     return shiftDetails!.transactions.first.totalExpense;
    //   } else {
    //     return shiftDetails!.transactions.first.totalExpenseRefund;
    //   }
    // }
    double expenses = 0.0;

    for (Expense expense in expensesBox.values.where(
      (element) =>
          element.transactionDate.isAfter(cashRegisteredTime()) &&
          element.isRefund == (refund ? 1 : 0),
    )) {
      if (expense.payments != null && expense.payments!.isNotEmpty) {
        expenses += expense.payments![0].amount!;
      }
    }
    return expenses.toStringAsFixed(2);
  }

  void webViewLoading() => emit(WebViewLoading());

  void webViewLoaded() => emit(WebViewLoaded());

  String remainingCash() {
    // double salesCollected = double.parse(shiftSalesCollected());
    double customerCollected = double.parse(shiftCustomerCollection());
    // double purchasesPaid = double.parse(shiftPurchasesPaid());
    double supplierPaid = double.parse(shiftSupplierPayment());
    double expensesAmount = double.parse(expensesAmounts());
    double totalSReturns =
        double.parse(shiftDetails?.payments.first.totalSellReturnPaid ?? "0");
    double totalPReturns = double.parse(
        shiftDetails?.payments.first.totalPurchaseReturnPaid ?? "0");
    double revenues = double.parse(expensesAmounts(refund: true));
    double remainingCash = customerCollected +
        revenues +
        totalPReturns -
        expensesAmount -
        supplierPaid -
        totalSReturns;

    return remainingCash.toStringAsFixed(2);
  }
}
