part of 'landing_cubit.dart';

@immutable
abstract class LandingState {}

class <PERSON><PERSON><PERSON>tial extends LandingState {}

class LandingScreenUpdated extends LandingState {}

class AppVersionLoading extends LandingState {}

class AppVersionReady extends LandingState {}

class PageSwitched extends LandingState {}

class ConnectionSwitched extends LandingState {}

class WebViewLoading extends LandingState {}

class WebViewLoaded extends LandingState {}

class TotalReturnsUpdated extends LandingState {}

class UpdateButtonStateChange extends LandingState {
  final bool canUpdate;

  UpdateButtonStateChange(this.canUpdate);
}
