import 'package:bloc/bloc.dart';
import 'package:flutter/material.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:we2up/data/db/db_manager.dart';
import 'package:we2up/presentation/widgets/date_range_dropdown.dart';

import '../../data/models/contact.dart';
import '../../data/models/contact_payment_model.dart';
import '../../data/models/user_model.dart';
import '../../utils/we2up_constants.dart';

part 'contact_payment_report_state.dart';

class PaymentsReportCubit extends Cubit<PaymentsReportState> {
  PaymentsReportCubit() : super(ContactPaymentReportInitial());

  DateTime? startDate = DateTime.now();

  DateTime? endDate = DateTime.now();

  UserModel? user;

  Contact? contact;

  DateRange range = DateRange.today;

  void updateDateRange(DateRange range) {
    this.range = range;
    emit(DateRangeUpdated());
  }

  void updateStartDate(DateTime? date) {
    startDate = date;
    emit(StartDateUpdated());
  }

  void updateEndDate(DateTime? date) {
    endDate = date;
    emit(EndDateUpdated());
  }

  void updateFilterUser(UserModel? user) {
    this.user = user;
    emit(FilterUserUpdated());
  }

  void updateFilterContact(Contact? contact) {
    this.contact = contact;
    emit(FilterContactUpdated());
  }

  TextEditingController filterUserController = TextEditingController();

  ScrollController scrollController = ScrollController();

  int currentPage = 1;
  bool loadedFromAPI = false;
  int itemsPerPage = 20;

  void getPaymentsReport(bool supplier, bool isNewSearch) async {
    if (isNewSearch) {
      currentPage = 1;
      loadedFromAPI = false;
      emit(NewReportLoading());
    } else {
      emit(PaginatedReportLoading());
    }
    final connected = await InternetConnectionChecker.instance.hasConnection;
    if (connected && !loadedFromAPI) {
      await fetchContactPaymentReports(
        supplier: supplier,
        startDate: startDate,
        endDate: endDate,
      );
      loadedFromAPI = true;
    }
    List<ContactPaymentModel> allPayments = paymentsReportBox.values
        .where((p) =>
            contactsBox.get(p.contactId)?.type ==
            (supplier ? "supplier" : 'customer'))
        .where((payment) =>
            (startDate == null ||
                sameDayOrAfter(payment.createdAt, startDate!,
                    shift: range == DateRange.shift)) &&
            (endDate == null ||
                sameDayOrBefore(payment.createdAt, endDate!,
                    shift: range == DateRange.shift)))
        .where((payment) => user == null || user!.id == payment.userId)
        .where((payment) => contact == null || contact!.id == payment.contactId)
        .toList();

    allPayments.sort((a, b) => b.createdAt.compareTo(a.createdAt));

    int endIndex = currentPage * itemsPerPage;
    endIndex = endIndex > allPayments.length ? allPayments.length : endIndex;

    List<ContactPaymentModel> paginatedPayments =
        allPayments.sublist(0, endIndex);

    currentPage++; // Increment currentPage for the next paginated call
    double totalAmount = allPayments.fold(
        0, (amount, payment) => amount + (payment.amount ?? 0));
    emit(ReportLoaded(paginatedPayments, totalAmount));
  }

  void reset() {
    startDate = null;
    endDate = null;
    user = null;
    contact = null;
    filterUserController.text = '';
  }

  @override
  Future<void> close() {
    reset();
    filterUserController.dispose();
    scrollController.dispose();
    return super.close();
  }
}
