part of 'contact_payment_report_cubit.dart';

@immutable
abstract class PaymentsReportState {}

class ContactPaymentReportInitial extends PaymentsReportState {}

class StartDateUpdated extends PaymentsReportState {}

class EndDateUpdated extends PaymentsReportState {}

class DateRangeUpdated extends PaymentsReportState {}

class FilterUserUpdated extends PaymentsReportState {}

class FilterContactUpdated extends PaymentsReportState {}

class NewReportLoading extends PaymentsReportState {}

class PaginatedReportLoading extends PaymentsReportState {}

class ReportLoaded extends PaymentsReportState {
  final List<ContactPaymentModel> payments;
  final double totalAmount;

  ReportLoaded(this.payments, this.totalAmount);
}
