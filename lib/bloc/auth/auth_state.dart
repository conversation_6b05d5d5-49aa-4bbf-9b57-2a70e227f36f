part of 'auth_cubit.dart';

@immutable
abstract class AuthState {}

class AuthInitial extends AuthState {}

class OauthLoading extends AuthState {}

class ShowingDialog extends AuthState {}

class DialogDismiss extends AuthState {}

class OauthError extends AuthState {}

class OauthLoaded extends AuthState {}

class SyncingDataLoading extends AuthState {}

class OauthSignedOut extends AuthState {}

class BaseUrlChanged extends AuthState {}

class PasswordVisibilityChanged extends AuthState {}

class FetchingAPIDataLoadedSuccessfully extends AuthState {}

class SyncingDataDone extends AuthState {}

class SyncingDataFailed extends AuthState {}

class UsernameAndPassSaved extends AuthState {}

class MaterialAppRebuilt extends AuthState {}
