import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:go_router/go_router.dart';
import 'package:we2up/data/models/base_url.dart';
import 'package:we2up/data/repository/api_repo.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

import '../../data/db/db_manager.dart';
import '../../data/models/user_permissions.dart';
import '../../utils/we2up_constants.dart';

part 'auth_state.dart';

class AuthCubit extends Cubit<AuthState> {
  AuthCubit() : super(AuthInitial());

  final oldBaseUrl = credentialsBox.get(baseUrl, defaultValue: baseURLs[0]);

  BaseUrl? selectedBaseUrl;

  void changeBaseUrl(BaseUrl? url) {
    selectedBaseUrl = url;
    credentialsBox.put(baseUrl, url);
    emit(BaseUrlChanged());
  }

  bool obscureText = true;

  void changeVisibility() {
    obscureText = !obscureText;
    emit(PasswordVisibilityChanged());
  }

  void login(
    context, {
    required String username,
    required String password,
  }) async {
    final strings = AppLocalizations.of(context)!;
    final repo = ApiRepository.get();
    emit(OauthLoading());
    final bool? userHasToken = await repo.doesUserHaveToken(
      username: username,
      password: password,
    );
    if (userHasToken == null) {
      emit(OauthError());
      return;
    }
    bool canTokenContinue = true;
    if (!userHasToken) {
      emit(ShowingDialog());
      canTokenContinue = await tokenExistDialog(context) ?? false;
      emit(OauthLoading());
    }
    if (canTokenContinue) {
      final bool sameData = (username == oldUsername() &&
              password == oldPassword() &&
              (selectedBaseUrl == null || selectedBaseUrl == oldBaseUrl)) ||
          (oldUsername().isEmpty && oldPassword().isEmpty);
      bool canLogin = true;
      if (!sameData) {
        emit(ShowingDialog());
        canLogin = await canLoginDialog(context) ?? false;
        EasyLoading.show(status: strings.loading);
        if (canLogin) await dbReset();
        emit(DialogDismiss());
      }
      if (canLogin) {
        repo.login(username: username, password: password).then(
          (bool success) async {
            if (success) {
              emit(SyncingDataLoading());
              final userPermissions = await repo.getNewPermissions();
              // if same user, pass, baseurl
              if (sameData && oldPermissions() == userPermissions) {
                await fetchDataAndStoreAllInHive(context, refresh: true);
              } else {
                await fetchDataAndStoreAllInHive(context, refresh: false);
              }
              emit(OauthLoaded());
            } else {
              emit(OauthError());
            }
          },
        );
      }
    } else {
      emit(DialogDismiss());
    }
  }

  bool saveCredentials() => userAndPassBox.get("saveCredentials") ?? false;

  void changeSavingCredentials(bool? value) {
    userAndPassBox.put("saveCredentials", value);
    emit(UsernameAndPassSaved());
  }

  String savedUsername() {
    if (kDebugMode) {
      return saveCredentials()
          ? (userAndPassBox.get("username") ?? "testapp")
          : "testapp";
    } else {
      return saveCredentials() ? (userAndPassBox.get("username") ?? "") : "";
    }
  }

  String savedPassword() {
    if (kDebugMode) {
      return saveCredentials()
          ? (userAndPassBox.get("password") ?? "testapp")
          : "testapp";
    } else {
      return saveCredentials() ? (userAndPassBox.get("password") ?? "") : "";
    }
  }

  String oldUsername() => userAndPassBox.get("oldUsername") ?? "";

  String oldPassword() => userAndPassBox.get("oldPassword") ?? "";

  UserPermissions oldPermissions() =>
      userAndPassBox.get("oldPermissions") ??
      UserPermissions.fromPermissionsList(const []);

  void saveCredentialsValues(username, password) {
    userAndPassBox.put("oldUsername", username);
    userAndPassBox.put("oldPassword", password);
    if (saveCredentials()) {
      userAndPassBox.put("username", username);
      userAndPassBox.put("password", password);
      emit(UsernameAndPassSaved());
    } else {
      userAndPassBox.put("username", "");
      userAndPassBox.put("password", "");
      emit(UsernameAndPassSaved());
    }
  }
}

Future<bool?> canLoginDialog(context) async {
  final strings = AppLocalizations.of(context)!;
  return await showDialog(
    context: context,
    builder: (context) {
      return AlertDialog(
        title: Text(strings.can_login_title),
        content: Text(strings.can_login_content),
        actions: [
          ElevatedButton(
            onPressed: () => context.pop(true),
            child: Text(strings.okay),
          ),
          ElevatedButton(
            onPressed: () => context.pop(false),
            child: Text(strings.cancel),
          ),
        ],
      );
    },
  );
}

Future<bool?> tokenExistDialog(context) async {
  final strings = AppLocalizations.of(context)!;
  return await showDialog(
    context: context,
    builder: (context) {
      return AlertDialog(
        title: Text(strings.logout_confirmation),
        content: Text(strings.logout_confirmation_content),
        actions: [
          ElevatedButton(
            onPressed: () => context.pop(true),
            child: Text(strings.continue_text),
          ),
          ElevatedButton(
            onPressed: () => context.pop(false),
            child: Text(strings.cancel),
          ),
        ],
      );
    },
  );
}
