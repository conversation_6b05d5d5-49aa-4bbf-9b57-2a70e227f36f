part of 'profit_loss_cubit.dart';

@immutable
abstract class ProfitLossState {}

class ProfitLossInitial extends ProfitLossState {}

class BusinessLocationChanged extends ProfitLossState {}

class DateRangeUpdated extends ProfitLossState {}

class StartDateUpdated extends ProfitLossState {}

class EndDateUpdated extends ProfitLossState {}

class FilterUserUpdated extends ProfitLossState {}
class ProfitLossReportReady extends ProfitLossState {}
