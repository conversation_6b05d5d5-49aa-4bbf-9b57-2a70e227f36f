import 'package:bloc/bloc.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:we2up/data/models/profit_loss_params.dart';
import 'package:we2up/data/repository/api_repo.dart';

import '../../data/models/business_location.dart';
import '../../data/models/profit_loss_report.dart';
import '../../data/models/user_model.dart';
import '../../presentation/widgets/date_range_dropdown.dart';

part 'profit_loss_state.dart';

class ProfitLossCubit extends Cubit<ProfitLossState> {
  ProfitLossCubit() : super(ProfitLossInitial());

  DateTime? startDate = DateTime.now();

  DateTime? endDate = DateTime.now();

  UserModel? user;

  BusinessLocation? location;

  ProfitLossReport? report;

  DateRange range = DateRange.today;

  void updateDateRange(DateRange range) {
    this.range = range;
    emit(DateRangeUpdated());
  }

  void changeSelectedLocation(BusinessLocation? location) {
    this.location = location;
    emit(BusinessLocationChanged());
  }

  void updateStartDate(DateTime? date) {
    startDate = date;
    emit(StartDateUpdated());
  }

  void updateEndDate(DateTime? date) {
    endDate = date;
    emit(EndDateUpdated());
  }

  void updateFilterUser(UserModel? user) {
    this.user = user;
    emit(FilterUserUpdated());
  }

  TextEditingController filterUserController = TextEditingController();

  Future<void> getProfitLossReport() async {
    final params = ProfitLossParams(
      endDate:
          endDate != null ? DateFormat('yyyy-MM-dd').format(endDate!) : null,
      startDate: startDate != null
          ? DateFormat('yyyy-MM-dd').format(startDate!)
          : null,
      locationID: location?.id,
      userID: user?.id,
    );
    report = await ApiRepository.get().getProfitLossReport(params);
    emit(ProfitLossReportReady());
  }

  @override
  Future<void> close() {
    startDate = null;
    endDate = null;
    user = null;
    location = null;
    report = null;
    filterUserController.dispose();
    return super.close();
  }
}
