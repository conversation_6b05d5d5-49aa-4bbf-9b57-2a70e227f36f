part of 'single_product_cubit.dart';

@immutable
abstract class SingleProductState {}

class SingleProductInitial extends SingleProductState {}

class SingleProductEnableStockChanged extends SingleProductState {}

class SingleProductTaxTypeChanged extends SingleProductState {}

class SingleProductExpiryPeriodTypeChanged extends SingleProductState {}

class SingleProductBranchesListChanged extends SingleProductState {}

class SingleProductPriceGroupsListChanged extends SingleProductState {}

class SingleProductUnitChanged extends SingleProductState {}

class SingleProductBrandChanged extends SingleProductState {}

class SingleProductCategoryChanged extends SingleProductState {}

class SingleProductTaxRateChanged extends SingleProductState {}

class SingleProductCreatedSuccessfully extends SingleProductState {}

class SingleProductCreationFailed extends SingleProductState {}

class SingleProductImageSelected extends SingleProductState {}

class SingleProductImageRemoved extends SingleProductState {}
