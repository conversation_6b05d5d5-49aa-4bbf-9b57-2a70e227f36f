import 'dart:developer';
import 'dart:io';

import 'package:bloc/bloc.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:just_audio/just_audio.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:image_picker/image_picker.dart';
import 'package:multi_select_flutter/util/multi_select_item.dart';
import 'package:path_provider/path_provider.dart';
import 'package:we2up/data/repository/api_repo.dart';
import 'package:image/image.dart' as img;

import '../../data/db/db_manager.dart';
import '../../data/models/business_location.dart';
import '../../data/models/new_product_to_api.dart';
import '../../data/models/product.dart';
import '../../data/models/selected_branch_info.dart';
import '../../data/models/selected_price_group_info.dart';

part 'single_product_state.dart';

class SingleProductCubit extends Cubit<SingleProductState> {
  late final ApiRepository apiRepository;
  final BusinessLocation selectedLocation;

  SingleProductCubit(this.selectedLocation) : super(SingleProductInitial()) {
    apiRepository = ApiRepository.get();
  }

  bool singleProductEnableStock = true;

  void changeNPEnableStock() {
    singleProductEnableStock = !singleProductEnableStock;
    emit(SingleProductEnableStockChanged());
  }

  TextEditingController singleProductName = TextEditingController();

  TextEditingController singleProductAlertQuantity = TextEditingController();

  TextEditingController singleProductSKU = TextEditingController();

  TextEditingController singleProductDescription = TextEditingController();

  TextEditingController singleProductDPP = TextEditingController();

  TextEditingController singleProductDSP = TextEditingController();

  TextEditingController singleProductWeight = TextEditingController();

  TextEditingController singleProductExpiryPeriod = TextEditingController();

  Unit singleProductUnit =
      unitsBox.values.firstWhere((element) => element.baseUnitId == null);


  Future<void> scanProductBarcode(BuildContext context) async {
    final audioPlayer = AudioPlayer()..setAsset('sounds/scanner-beep-sound.mp3');
    final MobileScannerController cameraController = MobileScannerController();

    try {
      String? scannedBarcode = await showDialog<String>(
        context: context,
        builder: (BuildContext context) => AlertDialog(
          content: SizedBox(
            height: 300,
            width: 300,
            child: MobileScanner(
              controller: cameraController,
              onDetect: (capture) {
                final List<Barcode> barcodes = capture.barcodes;
                for (final barcode in barcodes) {
                  if (barcode.rawValue != null) {
                    Navigator.of(context).pop(barcode.rawValue);
                    break;
                  }
                }
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => context.pop(),
              child: const Text('Cancel'),
            ),
          ],
        ),
      );

      if (scannedBarcode == null) {
        debugPrint('Scan canceled');
      } else {
        singleProductSKU.text = scannedBarcode;
        await audioPlayer.play();
      }
    } catch (e) {
      log(e.toString());
      EasyLoading.showError('Scan Failed!');
    } finally {
      cameraController.dispose();
    }
  }

  String singleDppIncTax() {
    if (singleProductTax != null) {
      var dpp = double.parse(singleProductDPP.text);
      dpp = (dpp * (1 + (singleProductTax!.amount! / 100)));
      return dpp.toString();
    }
    return singleProductDPP.text;
  }

  String singleDspIncTax() {
    if (singleProductTax != null) {
      var dsp = double.parse(singleProductDSP.text);
      dsp = (dsp * (1 + (singleProductTax!.amount! / 100)));
      return dsp.toString();
    }
    return singleProductDSP.text;
  }

  void changeNPUnit(Unit unit) {
    singleProductUnit = unit;
    emit(SingleProductUnitChanged());
  }

  Brand? singleProductBrand;

  void changeNPBrand(Brand? brand) {
    singleProductBrand = brand;
    emit(SingleProductBrandChanged());
  }

  TaxRate? singleProductTax;

  void changeNPTax(TaxRate? tax) {
    singleProductTax = tax;
    emit(SingleProductTaxRateChanged());
  }

  ProductCategory? singleProductCategory;

  void changeNPCategory(ProductCategory? category) {
    singleProductCategory = category;
    emit(SingleProductCategoryChanged());
  }

  TaxType singleProductTaxType = TaxType.exclusive;

  void changeNPTaxType(TaxType taxType) {
    singleProductTaxType = taxType;
    emit(SingleProductTaxTypeChanged());
  }

  ExpiryPeriodType? expiryPeriodType;

  void changeNPExpiryPeriodType(ExpiryPeriodType? expiryPeriodType) {
    this.expiryPeriodType = expiryPeriodType;
    emit(SingleProductExpiryPeriodTypeChanged());
  }

  final branchesMultiSelectList = businessLocationsBox.values
      .map((b) => SelectedBranchInfo(
            businessLocation: b,
            quantity: TextEditingController(),
            purchasePrice: TextEditingController(),
          ))
      .map((e) => MultiSelectItem(e, e.businessLocation.name))
      .toList();

  SelectedBranchInfo defaultSelectedBranch() {
    return SelectedBranchInfo(
      businessLocation: selectedLocation,
      quantity: TextEditingController(),
      purchasePrice: TextEditingController(),
    );
  }

  List<SelectedBranchInfo> selectedBranches = [];

  void updateNPBranchesList(List<SelectedBranchInfo> list) {
    selectedBranches = list;
    emit(SingleProductBranchesListChanged());
  }

  final priceGroupsMultiSelectList = sellingPriceGroupsBox.values
      .map((p) => SelectedPriceGroupInfo(
          priceGroup: p, sellPrice: TextEditingController()))
      .map((e) => MultiSelectItem(e, e.priceGroup.name))
      .toList();

  List<SelectedPriceGroupInfo> selectedPriceGroups = [];

  void updateNPPriceGroupList(List<SelectedPriceGroupInfo> list) {
    selectedPriceGroups = list;
    emit(SingleProductPriceGroupsListChanged());
  }

  XFile? pickedImage;

  Future<void> pickImage() async {
    final imagePicker = ImagePicker();
    pickedImage = await imagePicker.pickImage(
      source: ImageSource.gallery,
    );
    emit(SingleProductImageSelected());
  }

  Future<void> removeImage() async {
    pickedImage = null;
    emit(SingleProductImageRemoved());
  }

  Future<int> createNewProduct({bool fromPurchase = false}) async {
    EasyLoading.show(status: 'Creating Product');

    final locationInfo = await apiRepository.getCurrentLocation();
    String? imagePath;
    if (pickedImage != null) {
      final String fileName = '${DateTime.now().millisecondsSinceEpoch}.jpg';
      final image =
          img.decodeImage(await File(pickedImage!.path).readAsBytes());
      final img.Image reducedImage =
          img.copyResize(image!, width: 400, maintainAspect: true);
      final tempPath = (await getTemporaryDirectory()).path;
      final File compressedImage = File("$tempPath/$fileName");
      await compressedImage
          .writeAsBytes(img.encodeJpg(reducedImage, quality: 35));
      imagePath = "$tempPath/$fileName";
    }

    final productToAPI = NewProductToAPI(
      name: singleProductName.text,
      alertQuantity: singleProductAlertQuantity.text,
      productDescription: singleProductDescription.text,
      sku: singleProductSKU.text.isNotEmpty ? singleProductSKU.text : null,
      weight: singleProductWeight.text,
      unitId: singleProductUnit,
      categoryId: singleProductCategory,
      enableStock: singleProductEnableStock,
      taxType: singleProductTaxType,
      tax: singleProductTax,
      brandId: singleProductBrand?.id.toString(),
      expiryPeriod: singleProductExpiryPeriod.text,
      expiryPeriodType: expiryPeriodType,
      singleDpp: singleProductDPP.text,
      singleDppIncTax: singleDppIncTax(),
      singleDspIncTax: singleDspIncTax(),
      singleDsp: singleProductDSP.text,
      locationInfo: locationInfo,
      productLocations: selectedBranches.isEmpty
          ? [selectedLocation.id]
          : selectedBranches.map((e) => e.businessLocation.id).toList(),
      openingStock: (selectedBranches.isEmpty
              ? [defaultSelectedBranch()]
              : selectedBranches)
          .map((e) => {
                "location_id": e.businessLocation.id,
                "quantity": e.quantity.text,
                "purchase_price": e.purchasePrice.text.isNotEmpty
                    ? e.purchasePrice.text
                    : singleProductDPP.text,
              })
          .toList(),
      imagePath: imagePath,
    );
    final value = await apiRepository.sendNewProduct(
      newProductToAPI: productToAPI,
      selectedPriceGroups: selectedPriceGroups,
    );
    if (value != -1) {
      EasyLoading.showSuccess('تم إضافة المنتج بنجاح');
      emit(SingleProductCreatedSuccessfully());
      return value;
    } else {
      EasyLoading.showError('Product Creation Failed');
      emit(SingleProductCreationFailed());
      return -1;
    }
  }

  @override
  Future<void> close() {
    singleProductName.dispose();
    singleProductAlertQuantity.dispose();
    singleProductDescription.dispose();
    singleProductDPP.dispose();
    singleProductDSP.dispose();
    singleProductWeight.dispose();
    singleProductSKU.dispose();
    singleProductExpiryPeriod.dispose();
    return super.close();
  }
}
