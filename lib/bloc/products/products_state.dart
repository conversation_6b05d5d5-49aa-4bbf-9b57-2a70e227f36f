part of 'products_cubit.dart';

@immutable
abstract class ProductsState {}

class ProductsInitial extends ProductsState {}

class ProductsLoading extends ProductsState {}

class ProductsUpdated extends ProductsState {}

class ProductsError extends ProductsState {}

class AddedToCart extends ProductsState {}

class RemovedFromCart extends ProductsState {}

class CartFieldIsNull extends ProductsState {}

class UnitsLoading extends ProductsState {}

class UnitsLoaded extends ProductsState {}

class UnitsError extends ProductsState {}

class CurrentUnitChanged extends ProductsState {}

class CurrentTaxRateChanged extends ProductsState {}

class PaymentMethodDetailsChanged extends ProductsState {}

class CartPaymentMethodChanged extends ProductsState {}

class CurrentSalesIndexChanged extends ProductsState {
  final int index;

  CurrentSalesIndexChanged(this.index);
}

class PaymentMethodReset extends ProductsState {}

class PaymentMethodPaymentAccountChanged extends ProductsState {}

class CartTaxRateChanged extends ProductsState {}

class PrintBillChanged extends ProductsState {}

class CustomerContactChanged extends ProductsState {}

class CartTableChanged extends ProductsState {}

class CartServiceStaffChanged extends ProductsState {}

class CartCommAgentChanged extends ProductsState {}

class CartShippingCompanyChanged extends ProductsState {}

class CartShippingStatusChanged extends ProductsState {}

class SellingPriceGroupChanged extends ProductsState {}

class NumberChanged extends ProductsState {}

class NumberIncreased extends ProductsState {}

class EditableChanged extends ProductsState {}

class SelectedVariationChanged extends ProductsState {}

class ProductDiscountTypeSwitched extends ProductsState {}

class ProductsViewChanged extends ProductsState {}

class LocationChanged extends ProductsState {}

class ProductDiscountChanged extends ProductsState {}

class PiecePriceChanged extends ProductsState {}

class ProductQuantityChanged extends ProductsState {}

class CartDiscountChanged extends ProductsState {}

class CartPaymentChanged extends ProductsState {}

class SellLoading extends ProductsState {}

class SellReturnLoading extends ProductsState {}

class SellReturnSuccess extends ProductsState {}

class SellSuccess extends ProductsState {}

class SellDeleteSuccess extends ProductsState {}

class SellFailure extends ProductsState {}

class CartDiscountTypeSwitched extends ProductsState {}

class SellReturnFailure extends ProductsState {}

class SellDeleteFailure extends ProductsState {}

class FilterUpdated extends ProductsState {}

class SalesFilterReset extends ProductsState {}

class SaleFilterAlphabeticalOrderChanged extends ProductsState {}

class SaleFilterInStockChanged extends ProductsState {}

class SaleFilterSubCategoryChanged extends ProductsState {}

class SaleFilterPaymentStatusChanged extends ProductsState {}

class SaleFilterCategoryChanged extends ProductsState {}

class SaleFilterBrandChanged extends ProductsState {}

class SaleFilterUpdated extends ProductsState {}

class FilterUserUpdated extends ProductsState {}

class PurchaseFilterUpdated extends ProductsState {}

class PurchaseFilterContactUpdated extends ProductsState {}

class PurchaseFilterBusinessLocationUpdated extends ProductsState {}

class PurchaseFilterProductUpdated extends ProductsState {}

class DateRangeUpdated extends ProductsState {}

class PurchaseLoading extends ProductsState {}

class PurchasesLoaded extends ProductsState {
  final List<dynamic> purchases;
  final (double, double, double) calculations;

  PurchasesLoaded({required this.purchases, required this.calculations});
}

class NewPurchasesLoading extends ProductsState {}

class PaginatedPurchasesLoading extends ProductsState {}

class SalesLoaded extends ProductsState {
  final List<Sell> sales;
  final (double, double, double) calculations;
  final Iterable<Sell> todaySales;
  final (double, double, double) todayCalculations;
  final (double, double, double) quotationCalculations;

  SalesLoaded({
    required this.sales,
    required this.calculations,
    required this.todaySales,
    required this.todayCalculations,
    required this.quotationCalculations,
  });
}

class SalesReturnsLoaded extends ProductsState {
  final List<SellReturn> salesReturns;
  final (double, double, double) calculations;

  SalesReturnsLoaded({
    required this.salesReturns,
    required this.calculations,
  });
}

class NewSalesLoading extends ProductsState {}

class PaginatedSalesLoading extends ProductsState {}

class PurchaseFailure extends ProductsState {}

class PurchaseReturnLoading extends ProductsState {}

class PurchaseReturnFailure extends ProductsState {}

class ShipmentStatusChanged extends ProductsState {}

class ShipmentStatusLoading extends ProductsState {}

class ShipmentStatusLoaded extends ProductsState {}

class ShipmentStatusUpdated extends ProductsState {}

class ShipmentStatusFailed extends ProductsState {}

class NewProductEnableStockChanged extends ProductsState {}

class NewProductTaxTypeChanged extends ProductsState {}

class NewProductExpiryPeriodTypeChanged extends ProductsState {}

class NewProductBranchesListChanged extends ProductsState {}

class NewProductPriceGroupsListChanged extends ProductsState {}

class NewProductUnitChanged extends ProductsState {}

class NewProductBrandChanged extends ProductsState {}

class NewProductCategoryChanged extends ProductsState {}

class NewProductTaxRateChanged extends ProductsState {}

class NewProductCreatedSuccessfully extends ProductsState {}

class NewProductCreationFailed extends ProductsState {}

class UpdatingDone extends ProductsState {}

class LoadingInitialProducts extends ProductsState {}

class LoadedProductsFromAPI extends ProductsState {}

class LoadedContactsFromAPI extends ProductsState {}

class TotalSummaryUpdated extends ProductsState {}

class BillCleared extends ProductsState {}

class PanelCycled extends ProductsState {}
