import 'dart:async';
import 'dart:developer';

import 'package:dropdown_search/dropdown_search.dart';
import 'package:expandable/expandable.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:just_audio/just_audio.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:intl/intl.dart';
import 'package:we2up/data/models/bill_of_sale_data.dart';
import 'package:we2up/data/models/business_location.dart';
import 'package:we2up/data/models/payment.dart';
import 'package:we2up/data/models/purchase.dart';
import 'package:we2up/data/models/purchase_return_to_api.dart';
import 'package:we2up/data/models/purchase_to_api.dart';
import 'package:we2up/data/models/sell_return.dart';
import 'package:we2up/data/models/sell_to_api.dart';
import 'package:we2up/data/models/service_model.dart';
import 'package:we2up/data/models/shipping_company.dart';
import 'package:we2up/data/models/table.dart';
import '../../data/models/business_settings.dart';
import '../../data/models/contact.dart';
import '../../data/models/payment_account.dart';
import '../../data/models/payment_details.dart';
import '../../data/models/payment_status.dart';
import '../../data/models/product_data.dart';
import '../../data/models/product.dart';
import '../../data/models/purchase_return.dart';
import '../../data/models/sell.dart';
import '../../data/models/sell_return_to_api.dart';
import '../../data/models/selling_price_group.dart';
import '../../data/models/shift_details.dart';
import '../../data/models/user_model.dart';
import '../../data/repository/api_repo.dart';
import '../../data/db/db_manager.dart';

import '../../presentation/widgets/date_range_dropdown.dart';
import '../../presentation/widgets/printer_alert_dialog.dart';
import '../../utils/we2up_constants.dart';
import 'pdf_function.dart';

part 'products_state.dart';

class ProductsCubit extends Cubit<ProductsState> {
  late final ApiRepository apiRepository;

  ProductsCubit() : super(ProductsInitial()) {
    apiRepository = ApiRepository.get();
    initAllProducts();
  }

  Future<void> initialLoadProductsAndContacts() async {
    final connected = await InternetConnectionChecker.instance.hasConnection;
    if (productsBox.values.isEmpty && connected) {
      emit(LoadingInitialProducts());

      final productsController = StreamController<bool>.broadcast();
      final contactsController = StreamController<bool>.broadcast();

      productsController.stream.listen((bool newDataSaved) {
        if (newDataSaved) {
          initNewProducts();
          emit(LoadedProductsFromAPI());
        }
      });

      contactsController.stream.listen((bool newDataSaved) {
        if (newDataSaved) emit(LoadedContactsFromAPI());
      });

      await Future.wait([
        fetchProductsAndStoreInHive(controller: productsController),
        fetchContactsAndStoreInHive(controller: contactsController),
      ]).then((_) async => await fetchTransactions(false));

      // Close the local stream controller when fetching products is done
      productsController.close();
      contactsController.close();
      loginData.isProductsReady = true;
      loginData.isContactsReady = true;
      loginData.save();
    }
  }

  static ProductsCubit get(context) => BlocProvider.of<ProductsCubit>(context);

  void updateProducts() => emit(UpdatingDone());

  final GlobalKey<ScaffoldState> cartScaffoldKey = GlobalKey<ScaffoldState>();

  Map<int, ProductData> allProducts = {};

  Set<int> cartProducts = {};

  TextEditingController cartSearchController = TextEditingController();

  void setCurrentSalesIndex(int index) => emit(CurrentSalesIndexChanged(index));

  Set<int> cartFilteredProducts() {
    Set<int> mySet = {};
    if (cartSearchController.text.isNotEmpty) {
      final regex = RegExp(cartSearchController.text, caseSensitive: false);
      mySet = cartProducts.where((int cartID) {
        return regex.hasMatch(allProducts[cartID]?.product.name ?? "");
      }).toSet();
    } else {
      mySet = cartProducts;
    }

    return mySet;
  }

  bool allowOverSelling(id) {
    final product = allProducts[id]?.product;
    // or if allowOverSelling is true, or if the list is empty
    return (loginData.allowOverSelling || product?.enableStock == 0);
  }

  List<PaymentDetails> paymentDetailsList = [
    PaymentDetails(
      invoiceController: TextEditingController(),
      paymentNoteController: TextEditingController(),
    ),
  ];

  void addPaymentDetails() {
    paymentDetailsList.add(
      PaymentDetails(
        invoiceController: TextEditingController(),
        paymentNoteController: TextEditingController(),
      ),
    );
    emit(PaymentMethodDetailsChanged());
  }

  void resetPaymentDetailsList() {
    paymentDetailsList = [
      PaymentDetails(
          invoiceController: TextEditingController(),
          paymentNoteController: TextEditingController()),
    ];
    emit(PaymentMethodReset());
  }

  bool printCart = true;

  void changePrintCart() {
    printCart = !printCart;
    emit(PrintBillChanged());
  }

  Variation? findDefaultVariation(Product product) {
    Variation? selectedVariation;

    for (var productVariation in product.productVariations) {
      for (var variation in productVariation.variations!) {
        if (variation.variationLocationDetails.any((locationDetails) =>
            locationDetails.locationId == selectedLocation.id &&
            locationDetails.qtyAvailable > 0)) {
          selectedVariation = variation;
          return selectedVariation;
        }
      }
    }

    if (product.productVariations.isNotEmpty &&
        product.productVariations[0].variations!.isNotEmpty) {
      selectedVariation = product.productVariations[0].variations![0];
    }

    return selectedVariation;
  }

  BillData? currentBillData;

  double totalReturnValue() {
    double total = 0;
    if (currentBillData != null && currentBillData!.returnSell) {
      for (SellLine sl in currentBillData!.sell!.sellLines!) {
        final price = double.tryParse(sl.unitPriceIncTax ?? "0") ?? 0;
        total += (double.tryParse(
                    allProducts[sl.productId]!.quantityController.text) ??
                0) *
            price;
      }
    } else if (currentBillData != null && currentBillData!.pReturn) {
      for (PurchaseLine pl in currentBillData!.purchase!.purchaseLines!) {
        final price = double.tryParse(pl.purchasePriceIncTax ?? "0") ?? 0;
        total += (double.tryParse(
                    allProducts[pl.productId]!.quantityController.text) ??
                0) *
            price;
      }
    }
    return total;
  }

  void initAllProducts({BillData? billData}) {
    allProducts = {};
    currentBillData = billData;
    final sell = billData?.sell;
    final purchase = billData?.purchase;
    getRangeSummary();
    for (Product product in productsBox.values.where(
      (product) =>
          product.productVariations.isNotEmpty && product.isInactive == 0,
    )) {
      final quantityController = TextEditingController();
      final piecePriceController = TextEditingController();
      final discountController = TextEditingController();
      final noteController = TextEditingController();
      bool isPercentage = false;

      Unit? unit;

      // Check if there is a sell
      if (sell != null) {
        // Try to find a sell line with the same product ID
        List<int> productIDs = [];
        for (var sellLine in sell.sellLines!) {
          productIDs.add(sellLine.productId!);
        }
        if (productIDs.contains(product.id)) {
          final sellLine = sell.sellLines!.firstWhere(
            (line) => line.productId == product.id,
          );
          unit = unitsBox.get(sellLine.subUnitId);
          quantityController.text =
              ((sellLine.quantity ?? 0) / (unit?.baseUnitMultiplier ?? 1))
                  .toString();
          discountController.text = sellLine.lineDiscountAmount.toString();
          noteController.text = sellLine.sellLineNote.toString();
          isPercentage = sellLine.lineDiscountType == "percentage";
        }
      }

      // Check if there is a purchase
      if (purchase != null) {
        // Try to find a purchase line with the same product ID
        List<int> productIDs = [];
        for (var sellLine in purchase.purchaseLines!) {
          productIDs.add(sellLine.productId!);
        }
        if (productIDs.contains(product.id)) {
          final purchaseLine = purchase.purchaseLines!.firstWhere(
            (line) => line.productId == product.id,
          );
          unit = unitsBox.get(purchaseLine.subUnitId);
          quantityController.text =
              ((purchaseLine.quantity ?? 0) / (unit?.baseUnitMultiplier ?? 1))
                  .toString();
          discountController.text = purchaseLine.discountPercent.toString();
        }
      }

      allProducts[product.id] = ProductData(
        product: product,
        noteController: noteController,
        quantityController: quantityController
          ..addListener(() {
            double? number = double.tryParse(quantityController.text);
            applyOnCart(id: product.id, number: number);
            emit(ProductQuantityChanged());
          }),
        discountController: discountController
          ..addListener(() => emit(ProductDiscountChanged())),
        customPiecePriceController: piecePriceController
          ..addListener(() => emit(PiecePriceChanged())),
        // can't be retrieved from the API as the API only uses main unit.
        unit: unit ?? product.unit,
        variation: findDefaultVariation(product),
        taxRate: product.productTax,
        isPercentage: isPercentage,
        isActive: false,
        editable: false,
      );
    }
  }

  void initNewProducts() {
    for (Product product in productsBox.values.where(
      (product) => product.productVariations.isNotEmpty,
    )) {
      if (!allProducts.containsKey(product.id)) {
        final quantityController = TextEditingController();
        final piecePriceController = TextEditingController();
        final discountController = TextEditingController();
        final noteController = TextEditingController();
        bool isPercentage = false;

        Unit? unit;

        allProducts[product.id] = ProductData(
          product: product,
          noteController: noteController,
          quantityController: quantityController
            ..addListener(() {
              double? number = double.tryParse(quantityController.text);
              applyOnCart(id: product.id, number: number);
              emit(ProductQuantityChanged());
            }),
          discountController: discountController
            ..addListener(() => emit(ProductDiscountChanged())),
          customPiecePriceController: piecePriceController
            ..addListener(() => emit(PiecePriceChanged())),
          // can't be retrieved from the API as the API only uses main unit.
          unit: unit ?? product.unit,
          variation: findDefaultVariation(product),
          taxRate: product.productTax,
          isPercentage: isPercentage,
          isActive: false,
          editable: false,
        );
      }
    }
  }

  ShiftDetails? currentSummary;

  Future<void> getRangeSummary() async {
    if (await InternetConnectionChecker.instance.hasConnection &&
        !stopSyncingTransactions()) {
      final summary = await apiRepository.fetchRangeSummaryDetails(
        startDate: _filterStartDate,
        endDate: _filterEndDate,
      );
      if (summary != null) currentSummary = summary;
      emit(TotalSummaryUpdated());
    } else {
      currentSummary = null;
      emit(TotalSummaryUpdated());
    }
  }

  void initSingleProduct(int id, {bool fromPurchase = false}) {
    final Product product = productsBox.get(id)!;
    final quantityController = TextEditingController();
    final piecePriceController = TextEditingController();
    final discountController = TextEditingController();
    final noteController = TextEditingController();
    bool isPercentage = false;

    allProducts[product.id] = ProductData(
      product: product,
      noteController: noteController,
      quantityController: quantityController
        ..addListener(() {
          double? number = double.tryParse(quantityController.text);
          applyOnCart(id: product.id, number: number);
          emit(ProductQuantityChanged());
        }),
      discountController: discountController
        ..addListener(() => emit(ProductDiscountChanged())),
      customPiecePriceController: piecePriceController
        ..addListener(() => emit(PiecePriceChanged())),
      unit: product.unit,
      variation: findDefaultVariation(product),
      taxRate: product.productTax,
      isPercentage: isPercentage,
      isActive: false,
      editable: false,
    );
    final selectedProduct = productsBox.get(id)!;
    final isProductInSelectedLocation = selectedProduct.productLocations
        .any((location) => location.id == selectedLocation.id);

    final isStockAvailable = product.enableStock == 1 &&
        (availableInStock(product.id) > 0 || fromPurchase);

    final canApplyOnCart =
        isStockAvailable || allowOverSelling(id) || product.enableStock == 0;

    if (isProductInSelectedLocation && canApplyOnCart) {
      applyOnCart(id: id, number: 1);
      changeCount(id: id, count: 1);
    }
  }

  bool isProductDiscountPercentage(int id) => allProducts[id]!.isPercentage;

  void switchProductDiscountType(int id) {
    allProducts[id]!.isPercentage = !allProducts[id]!.isPercentage;
    emit(ProductDiscountTypeSwitched());
  }

  Variation? getVariation(int id) => allProducts[id]!.variation;

  void updateVariation(int id, Variation? variation) {
    allProducts[id]!.variation = variation;
    emit(SelectedVariationChanged());
  }

  void toggleEdit(int id) {
    allProducts[id]!.editable = !allProducts[id]!.editable;
    emit(EditableChanged());
  }

  bool isList = true;

  void toggleProductsView() {
    isList = !isList;
    emit(ProductsViewChanged());
  }

  BusinessLocation selectedLocation = businessLocationsBox.values.first;

  void changeSelectedLocation({required BusinessLocation location}) {
    selectedLocation = location;
    initAllProducts();
    initCart();
    resetPaymentDetailsList();
    emit(LocationChanged());
  }

  TextEditingController filterSearchController = TextEditingController();

  TextEditingController customServicePriceController = TextEditingController();

  FocusNode invoiceFocusNode = FocusNode();
  final productsDropdownKey = GlobalKey<DropdownSearchState<Product?>>();

  // Simplified focus management system
  final FocusNode paymentFieldFocusNode = FocusNode();
  final FocusNode discountFieldFocusNode = FocusNode();
  
  // Global keys for direct field access
  final GlobalKey<FormFieldState> paymentFieldKey = GlobalKey<FormFieldState>();
  final GlobalKey<FormFieldState> discountFieldKey = GlobalKey<FormFieldState>();



  // Scroll controller and keys for panel scrolling
  final ScrollController billScrollController = ScrollController();
  final GlobalKey paymentPanelKey = GlobalKey();
  final GlobalKey shippingPanelKey = GlobalKey();

  void updateSearchFilter() => emit(FilterUpdated());

  Brand? filterBrand;

  void changeFilterBrand(Brand? brand) {
    filterBrand = brand;
    emit(SaleFilterBrandChanged());
  }

  ProductCategory? filterCategory;

  void changeFilterCategory(ProductCategory? category) {
    filterCategory = category;
    emit(SaleFilterCategoryChanged());
  }

  PaymentStatus? filterPaymentStatus;

  void changeFilterPaymentStatus(PaymentStatus? filterStatus) {
    filterPaymentStatus = filterStatus;
    emit(SaleFilterPaymentStatusChanged());
  }

  ProductCategory? filterSubCategory;

  void changeFilterSubCategory(ProductCategory? subCategory) {
    filterSubCategory = subCategory;
    emit(SaleFilterSubCategoryChanged());
  }

  bool filterInStock = true;

  void changeFilterInStock(bool value) {
    filterInStock = value;
    emit(SaleFilterInStockChanged());
  }

  bool? filterAlphabeticalOrder;

  Icon getSortIcon() {
    if (filterAlphabeticalOrder == true) {
      return const Icon(Icons.arrow_downward);
    } else if (filterAlphabeticalOrder == false) {
      return const Icon(Icons.arrow_upward);
    } else {
      return const Icon(Icons.sort);
    }
  }

  void changeFilterAlphabeticalOrder() {
    if (filterAlphabeticalOrder == null) {
      filterAlphabeticalOrder = true;
    } else if (filterAlphabeticalOrder == true) {
      filterAlphabeticalOrder = false;
    } else {
      filterAlphabeticalOrder = null;
    }
    emit(SaleFilterAlphabeticalOrderChanged());
  }

  void resetSellsFilters() {
    filterBrand = null;
    filterCategory = null;
    filterSubCategory = null;
    filterInStock = true;
    filterAlphabeticalOrder = null;
    cartPriceGroup = null;
    changeServiceStaff(serviceStaffBox.values.length == 1
        ? serviceStaffBox.values.first
        : null);
    changeTable(null);
    changeCommAgent(
        commAgentsBox.values.length == 1 ? commAgentsBox.values.first : null);
    cartShippingStatus = null;
    cartShippingCompany = null;
    saleFilterShipmentStatus = null;
    customServicePriceController.text = "";
    cartServiceModel = defaultServiceModel();
    invoiceProductsPanelController =
        ExpandableController(initialExpanded: false);
    customerDataPanelController = ExpandableController(initialExpanded: false);
    shippingAndTaxesPanelController =
        ExpandableController(initialExpanded: true);
    paymentDataPanelController = ExpandableController(initialExpanded: true);
    commAgentsPanelController = ExpandableController(initialExpanded: true);
    emit(SalesFilterReset());
  }

  // Keyboard shortcut methods
  void forceExpandPaymentDataPanel() {
    // Force close ALL panels first (including products panel)
    final allPanelControllers = [
      invoiceProductsPanelController,
      customerDataPanelController,
      shippingAndTaxesPanelController,
      paymentDataPanelController,
      commAgentsPanelController,
    ];

    for (final controller in allPanelControllers) {
      if (controller.expanded) {
        controller.toggle();
      }
    }

    // Wait a bit, then open products panel and ensure payment panel is collapsed to show the field
    Future.delayed(const Duration(milliseconds: 100), () {
      // Open products panel first
      if (!invoiceProductsPanelController.expanded) {
        invoiceProductsPanelController.toggle();
      }
      
      // Ensure payment panel is collapsed to show the payment field
      if (paymentDataPanelController.expanded) {
        paymentDataPanelController.toggle();
      }
    });

    Future.delayed(const Duration(milliseconds: 400), () {
      _scrollToPanel(paymentPanelKey);
    });

    emit(PanelCycled());
  }

  void forceExpandShippingAndTaxesPanel() {
    // Force close ALL panels first (including products panel)
    final allPanelControllers = [
      invoiceProductsPanelController,
      customerDataPanelController,
      shippingAndTaxesPanelController,
      paymentDataPanelController,
      commAgentsPanelController,
    ];

    for (final controller in allPanelControllers) {
      if (controller.expanded) {
        controller.toggle();
      }
    }

    // Wait a bit, then open products panel and ensure shipping panel is collapsed to show the field
    Future.delayed(const Duration(milliseconds: 100), () {
      // Open products panel first
      if (!invoiceProductsPanelController.expanded) {
        invoiceProductsPanelController.toggle();
      }
      
      // Ensure shipping panel is collapsed to show the discount field
      if (shippingAndTaxesPanelController.expanded) {
        shippingAndTaxesPanelController.toggle();
      }
    });

    Future.delayed(const Duration(milliseconds: 400), () {
      _scrollToPanel(shippingPanelKey);
    });

    emit(PanelCycled());
  }

  void _scrollToPanel(GlobalKey panelKey) {
    if (panelKey.currentContext != null) {
      Scrollable.ensureVisible(
        panelKey.currentContext!,
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
      );
    }
  }



  void expandPaymentDataPanel() {
    // Only expand if currently collapsed
    if (!paymentDataPanelController.expanded) {
      paymentDataPanelController.toggle();
    }
  }

  void expandShippingAndTaxesPanel() {
    // Only expand if currently collapsed
    if (!shippingAndTaxesPanelController.expanded) {
      shippingAndTaxesPanelController.toggle();
    }
  }

  void cycleThroughPanels() {
    // Define the order of panels to cycle through
    final panelControllers = [
      invoiceProductsPanelController,
      customerDataPanelController, 
      shippingAndTaxesPanelController,
      paymentDataPanelController,
      commAgentsPanelController,
    ];

    // Find which panel is currently open
    int currentOpenPanelIndex = -1;
    for (int i = 0; i < panelControllers.length; i++) {
      if (panelControllers[i].expanded) {
        currentOpenPanelIndex = i;
        break;
      }
    }

    // Close all panels first
    for (final controller in panelControllers) {
      if (controller.expanded) {
        controller.toggle();
      }
    }

    // Determine next panel to open
    int nextPanelIndex;
    if (currentOpenPanelIndex == -1) {
      // No panel was open, open the first one
      nextPanelIndex = 0;
    } else {
      // Open the next panel in sequence (cycle back to 0 if at end)
      nextPanelIndex = (currentOpenPanelIndex + 1) % panelControllers.length;
    }

    // Open the next panel
    panelControllers[nextPanelIndex].toggle();
    
    emit(PanelCycled());
  }

  void clearBillAndCart() {
    // Clear all cart products
    cartProducts.clear();
    
    // Reset all product data to initial state
    for (var productData in allProducts.values) {
      productData.isActive = false;
      productData.editable = false;
      productData.quantityController.clear();
      productData.discountController.clear();
      productData.customPiecePriceController.clear();
      productData.noteController.clear();
      productData.isPercentage = false;
      productData.unit = productData.product.unit;
      productData.variation = findDefaultVariation(productData.product);
    }
    
    // Clear cart data
    cartCustomerContact = null;
    cartDiscountController.clear();
    cartShippingDetailsController.clear();
    cartShippingAddressController.clear();
    cartShippingFeesController.clear();
    cartSaleNoteController.clear();
    cartEmployeeNoteController.clear();
    cartTaxRate = null;
    cartShippingCompany = null;
    cartShippingStatus = null;
    customServicePriceController.clear();
    
    // Reset payment details
    resetPaymentDetailsList();
    
    // Reset commission agent, service staff, and table
    commAgent = commAgentsBox.values.length == 1 ? commAgentsBox.values.first : null;
    serviceStaff = serviceStaffBox.values.length == 1 ? serviceStaffBox.values.first : null;
    table = null;
    
    // Reset service model
    cartServiceModel = defaultServiceModel();
    
    emit(BillCleared());
  }

  void deleteFromCart(int id) {
    allProducts[id]!.editable = false;
    allProducts[id]!.isActive = false;
    allProducts[id]!.variation = findDefaultVariation(allProducts[id]!.product);
    allProducts[id]!.quantityController.text = "";
    allProducts[id]!.isPercentage = false;
    allProducts[id]!.unit = allProducts[id]!.product.unit;
    cartProducts.remove(id);
    emit(RemovedFromCart());
  }

  List<Product> filteredProductList({bool isPurchase = false}) {
    final regex = RegExp(
      filterSearchController.text,
      caseSensitive: false,
      unicode: true,
    );
    List<Product> products = [];
    products = allProducts.values
        .map((productData) => productData.product)
        .where((product) {
      // Skip products marked as not for selling
      if (product.notForSelling == 1 && !isPurchase) {
        return false;
      }
      return true;
    }).where((product) {
      // Filtering locations
      return product.productLocations.any((location) =>
          location.id == selectedLocation.id && location.isActive == 1);
    }).where((product) {
      // Filtering brands
      if (filterBrand == null) {
        return true;
      } else {
        return product.brand == filterBrand;
      }
    }).where((product) {
      // Filtering in stock
      final vld =
          allProducts[product.id]?.variation?.variationLocationDetails ?? [];

      // Check if any element in the list meets the conditions
      bool hasAvailableVariation = vld.any(
        (vl) => vl.locationId == selectedLocation.id && vl.qtyAvailable > 0,
      );

      return hasAvailableVariation ||
          allowOverSelling(product.id) ||
          isPurchase;
      // if (isPurchase) {
      //   return product.enableStock != 0;
      // } else {
      //   if (filterInStock) {
      //     return (product.enableStock == 1 &&
      //             availableInStock(product.id) > 0) ||
      //         (!isPurchase && product.enableStock == 0) ||
      //         cartProducts.contains(product.id);
      //   } else {
      //     return !((product.enableStock == 1 &&
      //             availableInStock(product.id) > 0) ||
      //         (!isPurchase && product.enableStock == 0));
      //   }
      // }
    }).where((product) {
      // Filtering categories
      if (filterCategory == null) {
        return true;
      } else {
        return product.category?.id == filterCategory!.id;
      }
    }).where((product) {
      if (!isPurchase && product.type == "combo") {
        return hasComboVariationInOtherProducts(product);
      } else {
        return true;
      }
    }).where((product) {
      // Filtering by search term using regex
      return filterSearchController.text.isEmpty ||
          regex.hasMatch(product.name) ||
          regex.hasMatch(product.sku ?? "") ||
          regex.hasMatch(product.productDescription ?? "") ||
          regex.hasMatch(availableInStock(product.id).toString());
    }).toList();

    // Rearrange products by product name
    if (filterAlphabeticalOrder == true) {
      products.sort((a, b) => a.name.compareTo(b.name));
    } else if (filterAlphabeticalOrder == false) {
      products.sort((a, b) => b.name.compareTo(a.name));
    }

    return products;
  }

  bool editable(int id) => allProducts[id]!.editable;

  bool isCardActive(int id) => allProducts[id]!.isActive;

  num? maxInInvoice(int id) {
    if (allowOverSelling(id)) {
      return null;
    }

    final productData = allProducts[id];
    if (productData == null) {
      return null;
    }

    final product = productData.product;
    final baseUnitMultiplier = productData.unit.baseUnitMultiplier;

    num? maxInvoice;
    if (productData.unit.id ==
        (unitsBox.get(allProducts[product.id]?.unit.id)?.id)) {
      maxInvoice = product.maxInInvoice?.toDouble();
    } else {
      maxInvoice = (product.maxInInvoice ?? 1.0) / baseUnitMultiplier;
    }

    final availableStock = availableInStock(id);

    if (maxInvoice == null) {
      return availableStock;
    }

    return maxInvoice < availableStock ? maxInvoice : availableStock;
  }

  int? maxDiscount(int id) => allProducts[id]!.product.maxDiscount;

  num? actualMaxDiscount(int id) {
    return loginData.isAdmin
        ? null
        : isProductDiscountPercentage(id)
            ? maxDiscount(id) ??
                (1 - getPurchasePrice(id) / piecePrice(id)) * 100.0
            : piecePrice(id) - getPurchasePrice(id);
  }

  TextEditingController cartDiscountController = TextEditingController();

  TextEditingController cartShippingDetailsController = TextEditingController();

  TextEditingController cartShippingAddressController = TextEditingController();

  TextEditingController cartShippingFeesController = TextEditingController();

  TextEditingController cartSaleNoteController = TextEditingController();

  TextEditingController cartEmployeeNoteController = TextEditingController();

  TaxRate? cartTaxRate;

  ShippingCompany? cartShippingCompany;

  String? cartShippingStatus;

  Contact? cartCustomerContact;

  UserModel? commAgent =
      commAgentsBox.values.length == 1 ? commAgentsBox.values.first : null;
  UserModel? serviceStaff =
      serviceStaffBox.values.length == 1 ? serviceStaffBox.values.first : null;
  BusinessTable? table;

  double getTotalPayable() {
    double totalPayable = 0.0;

    for (var paymentDetails in paymentDetailsList) {
      String invoiceText = paymentDetails.invoiceController.text.trim();
      double invoiceAmount = double.tryParse(invoiceText) ?? 0.0;
      totalPayable += invoiceAmount;
    }

    return totalPayable;
  }

  List<Unit> getAssociatedUnits(int id) {
    try {
      Set<Unit> associatedUnits = {
        unitsBox.get(allProducts[id]!.product.unit.id)!
      };
      if (allProducts[id]!.product.subUnitIds != null) {
        for (var e in allProducts[id]!.product.subUnitIds!) {
          if (unitsBox.get(int.parse(e)) == null) {}
          associatedUnits.add(unitsBox.get(int.parse(e))!);
        }
      }
      return associatedUnits.toList();
    } catch (e, stack) {
      logAppError("Error getting Associated units for id: $id", e, stack);
      return [];
    }
  }

  double availableInStock(int id) {
    Product? product = allProducts[id]?.product;
    Unit unit = allProducts[id]!.unit;
    double totalAvailable = 0;
    var variation = allProducts[id]!.variation;
    if (variation != null) {
      for (var details in variation.variationLocationDetails) {
        if (details.locationId == selectedLocation.id) {
          totalAvailable = details.qtyAvailable;
          break;
        }
      }
    }
    totalAvailable /= unit.baseUnitMultiplier;
    return product?.enableStock == 0 ? double.infinity : totalAvailable;
  }

  double piecePrice(int id, {bool isPurchase = false, bool original = false}) {
    Unit unit = allProducts[id]!.unit;
    double taxPercent = allProducts[id]!.taxRate?.amount ?? 0;
    double price;

    // If the original price is requested, bypass the custom price logic
    if (!original &&
        allProducts[id]!.customPiecePriceController.text.isNotEmpty) {
      double customPrice =
          double.parse(allProducts[id]!.customPiecePriceController.text);

      // Use custom price for purchasing
      if (isPurchase) {
        return customPrice;
      }

      // Use custom price for selling, with rules
      if (!isPurchase) {
        if (canSellBelowPurchasePrice()) {
          return customPrice;
        } else {
          double defaultPurchasePrice =
              allProducts[id]!.variation?.defaultPurchasePrice ?? 0;
          return customPrice < defaultPurchasePrice
              ? defaultPurchasePrice
              : customPrice;
        }
      }
    }

    // Return original price based on the purchase or sell context
    if (isPurchase) {
      price = allProducts[id]!.variation?.defaultPurchasePrice ?? 0;
    } else {
      price = allProducts[id]!.variation?.defaultSellPrice ?? 0;

      // Apply price group logic if applicable (only for selling)
      if (cartPriceGroup != null) {
        var priceGroup = allProducts[id]!
            .variation!
            .sellingPriceGroups
            .where((e) => e?.priceGroupId == cartPriceGroup!.id)
            .firstOrNull;

        price = priceGroup != null && priceGroup.priceIncTax != 0
            ? priceGroup.priceIncTax * 100 / (100 + taxPercent)
            : price;
      }
    }

    // Multiply by base unit multiplier
    price *= unit.baseUnitMultiplier;
    return price;
  }

  bool hasPriceGroupPriceIncTax(id) {
    if (cartPriceGroup != null) {
      var priceGroup = allProducts[id]!
          .variation!
          .sellingPriceGroups
          .where((e) => e?.priceGroupId == cartPriceGroup!.id)
          .firstOrNull;

      return priceGroup != null && priceGroup.priceIncTax != 0;
    }
    return false;
  }

  double getPurchasePrice(int id) {
    return allProducts[id]!.variation?.defaultPurchasePrice ?? 0;
  }

  double piecePriceAfter(int id, {bool isPurchase = false}) {
    double piecePrice = this.piecePrice(id, isPurchase: isPurchase);
    double discount =
        double.tryParse(allProducts[id]!.discountController.text) ?? 0;
    double taxRatePercentage = allProducts[id]?.taxRate?.amount ?? 0;
    bool isPercentage = allProducts[id]!.isPercentage;
    double discountAmount = isPercentage ? 0 : discount;
    double discountPercentage = isPercentage ? (1 - (discount / 100)) : 1;
    double taxRateValue = taxRatePercentage / 100;
    return (piecePrice * discountPercentage - discountAmount) *
        (1 + taxRateValue);
  }

  double getPriceAfter(int id, {bool purchase = false}) {
    double n = double.tryParse(allProducts[id]!.quantityController.text) ?? 0;
    double price = piecePriceAfter(id, isPurchase: purchase) * n;
    return double.parse(price.toStringAsFixed(4));
  }

  String getImageUrl(int id) => allProducts[id]!.product.imageUrl;

  String? getCount(int id) {
    double? n = double.tryParse(allProducts[id]!.quantityController.text);
    return n?.toString() ?? "";
  }

  String getProductName(int id) => allProducts[id]!.product.name;

  Unit getCurrentUnit(int id) => allProducts[id]!.unit;

  TaxRate? getCurrentPTaxRate(int id) => allProducts[id]!.taxRate;

  void changeSelectedUnit({required Unit unit, required int id}) {
    allProducts[id]!.unit = unit;
    final quantity =
        double.tryParse(allProducts[id]!.quantityController.text) ?? 0.0;
    allProducts[id]!.quantityController.text =
        (quantity / unit.baseUnitMultiplier).toString();
    emit(CurrentUnitChanged());
  }

  void taxRateChanged({TaxRate? taxRate, required int id}) {
    allProducts[id]!.taxRate = taxRate;
    emit(CurrentTaxRateChanged());
  }

  void changeCartPaymentMethod({required String paymentMethod, int index = 0}) {
    paymentDetailsList[index].paymentMethod = paymentMethod;
    emit(CartPaymentMethodChanged());
  }

  void changeCartPaymentAccount(
      {PaymentAccount? paymentAccount, required int index}) {
    paymentDetailsList[index].paymentAccount = paymentAccount;
    emit(PaymentMethodPaymentAccountChanged());
  }

  void cartTaxRateChanged(TaxRate? taxRate) {
    cartTaxRate = taxRate;
    emit(CartTaxRateChanged());
  }

  void initCart({BillData? billData, bool isNewSell = false}) {
    final sell = billData?.sell;
    final purchase = billData?.purchase;
    cartProducts = {};
    paymentDetailsList = [];
    if (sell != null) {
      if (sell.sellLines!.isNotEmpty) {
        for (var sellLine in sell.sellLines!) {
          cartProducts.add(sellLine.productId!);
          // Add TAX to product
          allProducts[sellLine.productId]!.taxRate =
              taxRatesBox.get(sellLine.taxId);
          // get discount amount
          allProducts[sellLine.productId]!.discountController.text =
              sellLine.lineDiscountAmount ?? "";
          allProducts[sellLine.productId]!.noteController.text =
              sellLine.sellLineNote ?? "";
          // get discount type for this line
          allProducts[sellLine.productId]!.isPercentage =
              sellLine.lineDiscountType == "percentage" ? true : false;
          // check if custom price is used.
          if (piecePrice(sellLine.productId!) !=
              double.tryParse(sellLine.unitPrice ?? "0")) {
            allProducts[sellLine.productId]!.customPiecePriceController.text =
                sellLine.unitPrice ?? "0";
          }
        }
      }
      if (sell.paymentLines != null && sell.paymentLines!.isNotEmpty) {
        for (var payment in sell.paymentLines!) {
          paymentDetailsList.add(PaymentDetails.fromPayment(payment));
        }
      } else {
        resetPaymentDetailsList();
      }
      final priceGroupId = contactsBox.get(sell.contactId)?.priceGroupId;
      changeCartPriceGroup(sellingPriceGroupsBox.get(priceGroupId));
      changeServiceStaff(usersBox.get(sell.resWaiterId));
      changeTable(tablesBox.get(sell.resTableId));
      changeCommAgent(usersBox.get(sell.commissionAgent));
      changeShippingCompany(shippingCompaniesBox.get(sell.shippingCompanyId));
      changeShippingStatus(sell.shippingStatus?.name);
      selectedLocation = businessLocationsBox.get(sell.locationId)!;
      // TODO: customServicePriceController.text => the packing price.
    }

    if (purchase != null) {
      if (purchase.purchaseLines!.isNotEmpty) {
        for (var purchaseLine in purchase.purchaseLines!) {
          cartProducts.add(purchaseLine.productId!);
          // Add TAX to product
          allProducts[purchaseLine.productId]!.taxRate =
              taxRatesBox.get(purchaseLine.taxId);
          // check if custom price is used. // EDIT LATER
          // if (double.parse(purchaseLine.purchasePrice ?? "0") !=
          //     (allProducts[purchaseLine.productId]!
          //         .variation
          //         ?.defaultSellPrice)) {
          //   allProducts[purchaseLine.productId]!
          //       .customPiecePriceController
          //       .text = purchaseLine.purchasePrice ?? "";
          // }
          // get discount amount
          allProducts[purchaseLine.productId]!.discountController.text =
              purchaseLine.discountPercent ?? "";
          // type is always percent as per API!! EDIT LATER
          allProducts[purchaseLine.productId]!.isPercentage = false;
          if (piecePrice(purchaseLine.productId!, isPurchase: true) !=
              double.tryParse(purchaseLine.purchasePrice ?? "0")) {
            allProducts[purchaseLine.productId]!
                .customPiecePriceController
                .text = purchaseLine.purchasePrice ?? "0";
          }
        }
      }
      if (purchase.purchaseLines!.isNotEmpty) {
        for (var payment in purchase.paymentLines ?? []) {
          paymentDetailsList.add(PaymentDetails.fromPayment(payment));
        }
      } else {
        resetPaymentDetailsList();
      }
      for (final businessLocation in businessLocationsBox.values) {
        if (purchase.locationId == businessLocation.id) {
          selectedLocation = businessLocation;
          break;
        }
      }
      final priceGroupId = contactsBox.get(purchase.contactId)?.priceGroupId;
      changeCartPriceGroup(sellingPriceGroupsBox.get(priceGroupId));
      selectedLocation = businessLocationsBox.get(purchase.locationId)!;
    }

    cartEmployeeNoteController.text =
        sell?.staffNote ?? purchase?.staffNote ?? "";
    cartSaleNoteController.text =
        sell?.additionalNotes ?? purchase?.additionalNotes ?? "";
    // no shipping fees for purchase.
    cartShippingFeesController.text = sell?.shippingCharges ?? "";
    cartShippingDetailsController.text =
        sell?.shippingDetails ?? purchase?.shippingDetails ?? "";
    cartShippingAddressController.text =
        sell?.shippingAddress ?? purchase?.shippingAddress ?? "";
    cartTaxRate = taxRatesBox.get(sell?.taxId ?? purchase?.taxId);
    cartServiceModel = (sell != null || purchase != null)
        ? serviceModelBox
            .get(sell?.typesOfServiceId ?? purchase?.typesOfServiceId)
        : defaultServiceModel();
    cartDiscountController.text =
        sell?.discountAmount ?? purchase?.discountAmount ?? "";
    cartDiscountTypePercentage =
        (sell?.discountType ?? purchase?.discountType) == "percentage"
            ? true
            : false;

    if (isNewSell) {
      cartCustomerContact = contactsBox.values
          .where((contact) => contact.isDefault == 1)
          .firstOrNull;
    } else {
      cartCustomerContact =
          contactsBox.get(sell?.contactId ?? purchase?.contactId);
    }
  }

  void changeSelectedContact(Contact? contact) {
    cartCustomerContact = contact;
    cartPriceGroup = sellingPriceGroupsBox.get(contact?.priceGroupId);
    emit(CustomerContactChanged());
  }

  void changeTable(BusinessTable? table) {
    this.table = table;
    emit(CartTableChanged());
  }

  void changeServiceStaff(UserModel? serviceStaff) {
    this.serviceStaff = serviceStaff;
    emit(CartServiceStaffChanged());
  }

  void changeCommAgent(UserModel? commAgent) {
    this.commAgent = commAgent;
    emit(CartCommAgentChanged());
  }

  void changeShippingCompany(ShippingCompany? shippingCompany) {
    cartShippingCompany = shippingCompany;
    emit(CartShippingCompanyChanged());
  }

  void changeShippingStatus(String? shippingStatus) {
    cartShippingStatus = shippingStatus;
    emit(CartShippingCompanyChanged());
  }

  SellingPriceGroup? cartPriceGroup;

  void changeCartPriceGroup(SellingPriceGroup? cartPriceGroup) {
    this.cartPriceGroup = cartPriceGroup;
    emit(SellingPriceGroupChanged());
  }

  final audioPlayer = AudioPlayer()..setAsset('sounds/scanner-beep-sound.mp3');

  void changeCount({required int id, required double? count}) async {
    final currentQuantity =
        double.tryParse(allProducts[id]!.quantityController.text) ?? 0;
    try {
      if (count != null) {
        // Format the count value
        String formattedCount = count.toString();
        if (count == count.floorToDouble()) {
          // No decimal places, show as integer
          formattedCount = count.toInt().toString();
        } else {
          // Has decimal places, format accordingly and remove unnecessary trailing zeros
          formattedCount = count.toString();
        }

        allProducts[id]!.quantityController.text = formattedCount;
      } else {
        allProducts[id]!.quantityController.text = '';
      }

      emit(NumberChanged());

      if (currentQuantity <= (count ?? 0) && playSoundWhenAddingProduct()) {
        await audioPlayer.play();
      }
    } catch (e, stack) {
      logAppError("Error Adding Product to cart", e, stack);
    }
  }

  void addProductToCart(context, {Product? product}) async {
    applyOnCart(id: product!.id, number: 1);
    double newCount =
        (double.tryParse(allProducts[product.id]!.quantityController.text) ??
                0) +
            1;
    // Check if max quantity is available
    if (maxInInvoice(product.id) != null &&
        newCount <= maxInInvoice(product.id)!) {
      // Update only if max quantity allows
      changeCount(id: product.id, count: newCount);
      if (showPopupMenuToSelectNumber()) {
        await showScanQuantityDialog(context, id: product.id);
      }
    } else {
      changeCount(id: product.id, count: newCount);
      if (showPopupMenuToSelectNumber()) {
        await showScanQuantityDialog(context, id: product.id);
      }
    }
  }

  void applyCartDiscount() => emit(CartDiscountChanged());

  void applyPaymentChanged() => emit(CartPaymentChanged());

  void applyOnCart({required int id, required double? number}) {
    if (number == null) {
      emit(CartFieldIsNull());
    } else {
      cartProducts.add(id);
      allProducts[id]!.isActive = true;
      emit(AddedToCart());
    }
  }

  bool isInteger(double value) => value == value.toInt();

  void updateNumber(int id, double newValue) {
    String currentValueText = allProducts[id]!.quantityController.text;
    double currentValue = double.tryParse(currentValueText) ?? 0;

    if (isInteger(currentValue) && isInteger(newValue)) {
      // If both the current and new values are integers, update as int
      int intValue = newValue.toInt();
      allProducts[id]!.quantityController.text = intValue.toString();
      changeCount(id: id, count: intValue.toDouble());
    } else {
      // Otherwise, update as a double
      allProducts[id]!.quantityController.text = newValue.toString();
      changeCount(id: id, count: newValue);
    }
  }

  void decrementNumber(int id) {
    double currentValue =
        double.tryParse(allProducts[id]!.quantityController.text) ?? 0;
    double newValue = currentValue - 1;

    if (newValue >= 0) {
      updateNumber(id, newValue);
    }
  }

  void incrementNumber(int id) {
    double currentValue =
        double.tryParse(allProducts[id]!.quantityController.text) ?? 0;
    double newValue = currentValue + 1;

    double max = maxInInvoice(id)?.toDouble() ?? double.infinity;

    if (newValue <= max) {
      updateNumber(id, newValue);
    } else {
      updateNumber(id, max);
    }
  }

  double getCartTotalBeforeTax({required bool purchase}) {
    double total = 0.0;
    for (int id in cartProducts) {
      double price = getPriceAfter(id, purchase: purchase);
      total += price;
    }
    return double.parse(total.toStringAsFixed(4));
  }

  bool cartDiscountTypePercentage = false;

  void switchCartDiscountType() {
    cartDiscountTypePercentage = !cartDiscountTypePercentage;
    emit(CartDiscountTypeSwitched());
  }

  ServiceModel? cartServiceModel = defaultServiceModel();

  void updateCartServiceModel(ServiceModel? cartServiceModel) {
    this.cartServiceModel = cartServiceModel;
    emit(CartDiscountTypeSwitched());
  }

  double getCartTotalAfterTax({required bool purchase}) {
    double subTotal = getCartTotalBeforeTax(purchase: purchase);
    double discount = double.tryParse(cartDiscountController.text) ?? 0;
    double taxRatePercentage = cartTaxRate?.amount ?? 0;
    double discountPercentage =
        cartDiscountTypePercentage ? (1 - (discount / 100)) : 1;
    double discountAmount = cartDiscountTypePercentage ? 0 : discount;
    double taxRateValue = taxRatePercentage / 100;
    double totalAfter = 0;
    if (businessSettings.includeServiceInTaxes) {
      totalAfter = ((subTotal * discountPercentage - discountAmount) +
              (purchase ? 0 : calculatePackingCharge())) *
          (1 + taxRateValue);
    } else {
      totalAfter = ((subTotal * discountPercentage - discountAmount) *
              (1 + taxRateValue) +
          (purchase ? 0 : calculatePackingCharge()));
    }
    return double.parse(totalAfter.toStringAsFixed(4));
  }

  double getTotalPaymentFees({required bool purchase}) {
    double shipping = double.tryParse(cartShippingFeesController.text) ?? 0;
    return getCartTotalAfterTax(purchase: purchase) + shipping;
  }

  double getDueAfterInvoice({required bool purchase}) {
    double due = double.parse(cartCustomerContact?.due ?? "0");
    double paymentFees = getTotalPaymentFees(purchase: purchase);
    double paidAmount = paymentDetailsList.fold(0, (sum, paymentDetail) {
      String invoiceText = paymentDetail.invoiceController.text;
      double invoiceValue = double.tryParse(invoiceText) ?? 0.0;
      return sum + invoiceValue;
    });
    return due + paymentFees - paidAmount;
  }

  Future<void> scanBarcode(BuildContext context) async {
    final MobileScannerController cameraController = MobileScannerController();
    bool isProcessing = false;

    try {
      await showDialog(
        context: context,
        builder: (dialogContext) => AlertDialog(
          content: SizedBox(
            height: 300,
            width: 300,
            child: MobileScanner(
              controller: cameraController,
              onDetect: (capture) async {
                if (isProcessing) return;
                isProcessing = true;

                final List<Barcode> barcodes = capture.barcodes;
                for (final barcode in barcodes) {
                  if (barcode.rawValue == null) continue;

                  final String scannedBarcode = barcode.rawValue!;

                  final int? id = productsBox.values
                      .where((element) => element.sku == scannedBarcode)
                      .firstOrNull
                      ?.id;

                  if (id == null) {
                    EasyLoading.showError("المنتج غير موجود فى قاعدة البيانات");
                    if (dialogContext.mounted) dialogContext.pop();
                    return;
                  }

                  final controller = allProducts[id]!.quantityController;
                  double newValue = (double.tryParse(controller.text) ?? 0) + 1;
                  if (maxInInvoice(id) == null ||
                      newValue <= maxInInvoice(id)! ||
                      loginData.allowOverSelling ||
                      loginData.isAdmin) {
                    controller.text = newValue.toString();

                    // Pause the scanner
                    cameraController.stop();

                    // Show dialog and play sound
                    if (showPopupMenuToSelectNumber()) {
                      await showScanQuantityDialog(context, id: id);
                    } else {
                      changeCount(id: id, count: newValue);
                    }
                    await audioPlayer.play();

                    // Wait for 1 second
                    await Future.delayed(const Duration(seconds: 1));

                    // Resume the scanner
                    cameraController.start();
                  } else {
                    EasyLoading.showError(
                        "لا يمكن اضافة المنتج لانه يتخطي الحد الاقصي فى الفاتورة.");
                    dialogContext.pop();
                    return;
                  }
                }

                isProcessing = false;
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(dialogContext).pop();
              },
              child: const Text('Cancel'),
            ),
          ],
        ),
      );
    } catch (e) {
      log(e.toString());
      EasyLoading.showError('Scan Failed!');
    } finally {
      cameraController.dispose();
    }
  }

  List<ProductToAPI> _getCartProducts(bool isQuotation) {
    List<ProductToAPI> cartProductList = [];
    for (int productId in cartProducts) {
      ProductData? productData = allProducts[productId];
      if (productData != null &&
          filteredProductList(isPurchase: isQuotation ? true : false)
              .any((product) => product.id == productId) &&
          (double.tryParse(productData.quantityController.text) ?? 0) > 0) {
        cartProductList.add(
          ProductToAPI(
            productId: productId,
            variationId: productData.variation?.id,
            quantity: double.parse(productData.quantityController.text),
            unitPrice: piecePrice(productId),
            discountAmount:
                double.tryParse(productData.discountController.text) ?? 0,
            subUnitId: productData.unit.id,
            discountType: productData.isPercentage ? "percentage" : "fixed",
            taxRateId: productData.taxRate?.id,
            note: productData.noteController.text,
            unitWeight: productData.product.weight,
          ),
        );
      }
    }
    return cartProductList;
  }

  List<Payment> _getPayments({
    bool? cash,
    bool? credit,
    required bool purchase,
    required int paymentFor,
  }) {
    return paymentDetailsList
        .map(
          (paymentDetails) => Payment(
            paymentId: paymentDetails.paymentId,
            amount: credit != null && credit
                ? 0
                : cash != null && cash
                    ? getTotalPaymentFees(purchase: purchase)
                    : double.tryParse(paymentDetails.invoiceController.text) ??
                        0,
            method: paymentDetails.paymentMethod,
            accountId: paymentDetails.paymentAccount?.id,
            note: paymentDetails.paymentNoteController.text,
            paymentFor: paymentFor,
          ),
        )
        .toList();
  }

  Future<void> checkToPrint(context, {bool sReturn = false}) async {
    if (printCart && !sReturn) {
      List<Sell> sortedSells = sellsBox.values.toList();
      sortedSells
          .sort((a, b) => b.transactionDate.compareTo(a.transactionDate));
      await generatePdf(context, sell: sortedSells.firstOrNull);
    } else if (printCart && sReturn) {
      List<SellReturn> sortedSReturns = sellsReturnsBox.values.toList();
      sortedSReturns
          .sort((a, b) => b.transactionDate.compareTo(a.transactionDate));
      await generatePdf(context, sReturn: sortedSReturns.firstOrNull);
    }
    printCart = true;
  }

  double saleWeight() => cartProducts.fold(0.0, (weight, productId) {
        final productData = allProducts[productId]!;
        return weight +
            (double.tryParse(productData.product.weight ?? "0") ?? 0.0) *
                (double.tryParse(productData.quantityController.text) ?? 0.0) *
                productData.unit.baseUnitMultiplier;
      });

  double calculatePackingCharge() {
    if (customServicePriceController.text.trim().isNotEmpty) {
      return double.parse(customServicePriceController.text);
    } else if (cartServiceModel != null) {
      switch (cartServiceModel?.packingChargeType) {
        case PackingChargeType.weight:
          return cartServiceModel!.packingCharge * saleWeight();
        case PackingChargeType.fixed:
          return cartServiceModel!.packingCharge;
        case PackingChargeType.percentage:
          return cartServiceModel!.packingCharge *
              (getCartTotalAfterTax(purchase: false) / 100);
        default:
          return 0;
      }
    } else {
      return 0;
    }
  }

  void _updateProductsQuantities(List<dynamic> products) {
    for (var p in products) {
      if (p is! ProductToAPI && p is! PurchaseProductToAPI) continue;

      final int productId = p is ProductToAPI
          ? p.productId
          : (p as PurchaseProductToAPI).productId;
      final int? variationId = p is ProductToAPI
          ? p.variationId
          : (p as PurchaseProductToAPI).variationId;
      final num quantity = p is ProductToAPI
          ? p.quantity
          : num.parse((p as PurchaseProductToAPI).quantity ?? "0");

      final product = productsBox.get(productId);
      if (product == null) continue;

      final newQuantity = p is PurchaseProductToAPI
          ? availableInStock(product.id) + quantity
          : availableInStock(product.id) - quantity;

      final updatedProduct = product.copyWith(
        productVariations: product.productVariations.map((productVariation) {
          return productVariation.copyWith(
            variations: productVariation.variations?.map((variation) {
              if (variation.id != variationId) return variation;

              final updatedLocationDetails =
                  variation.variationLocationDetails.map((location) {
                if (location.locationId != selectedLocation.id) return location;

                return location.copyWith(qtyAvailable: newQuantity);
              }).toList();

              return variation.copyWith(
                variationLocationDetails: updatedLocationDetails,
              );
            }).toList(),
          );
        }).toList(),
      );

      productsBox.put(productId, updatedProduct);
    }
  }

  Future<bool> sendSellToAPI(
    context, {
    bool? cash,
    int? sellID,
    String? offlineID,
    int isQuotation = 0,
    bool credit = false,
  }) async {
    final now = DateTime.now();
    emit(SellLoading());

    final locationInfo = await apiRepository.getCurrentLocation();

    final productsList = _getCartProducts(isQuotation == 1);

    final sell = SellToAPI(
      invoiceNo: sellID == null
          ? "${loginData.userId}_${DateFormat('yMdHms').format(now)}"
          : sellsBox.get(sellID)!.invoiceNo!,
      locationId: selectedLocation.id,
      contactId: cartCustomerContact!.id,
      sellingPriceGroupId: cartPriceGroup?.id,
      taxRateId: cartTaxRate?.id,
      discountType: cartDiscountTypePercentage ? "percentage" : "fixed",
      shippingCharges: double.tryParse(cartShippingFeesController.text) ?? 0,
      discountAmount: double.tryParse(cartDiscountController.text) ?? 0,
      saleNote: cartSaleNoteController.text,
      staffNote: cartEmployeeNoteController.text,
      transactionDate: dateToString(now),
      products: productsList,
      payments: _getPayments(
        cash: cash,
        credit: credit,
        purchase: false,
        paymentFor: cartCustomerContact!.id,
      ),
      isQuotation: isQuotation,
      invoiceAmount: getTotalPaymentFees(purchase: false).toString(),
      offlineID: offlineID,
      status: isQuotation == 0 ? "final" : "draft",
      shippingStatus: cartShippingStatus,
      locationInfo: locationInfo,
      typesOfServiceId: cartServiceModel?.id,
      packingCharge: calculatePackingCharge(),
      packingChargeType: cartServiceModel?.name,
      commissionAgent: commAgent?.id,
      tableId: table?.id,
      serviceStaffId: serviceStaff?.id,
      shippingCompanyId: cartShippingCompany?.id,
      shippingDetails: cartShippingDetailsController.text,
      shippingAddress: cartShippingAddressController.text,
    );

    await apiRepository.sendProductSell(
      sell: sell,
      sellID: sellID,
    );

    EasyLoading.showSuccess('Sell Successful');
    _updateProductsQuantities(productsList);
    initAllProducts();
    resetSellsFilters();
    initCart(isNewSell: sellID == null);
    resetPaymentDetailsList();
    getFilteredSales(refresh: true);
    return true;
  }

  List<ReturnProductToAPI> _getCartReturnProducts(int sellID) {
    List<ReturnProductToAPI> cartReturnProductList = [];
    for (int productId in cartProducts) {
      ProductData? productData = allProducts[productId];
      SellLine sellLine = sellsBox
          .get(sellID)!
          .sellLines!
          .firstWhere((element) => element.productId! == productId);
      if (productData != null &&
          filteredProductList().any((product) => product.id == productId)) {
        cartReturnProductList.add(
          ReturnProductToAPI(
            sellLineId: sellLine.id!,
            quantity: double.tryParse(
                    allProducts[sellLine.productId]!.quantityController.text) ??
                0,
            unitPriceIncTax: double.tryParse(sellLine.unitPriceIncTax!) ??
                piecePriceAfter(productId),
          ),
        );
      }
    }
    return cartReturnProductList;
  }

  Future<bool> sendSellReturn({required int sellID, String? offlineID}) async {
    emit(SellReturnLoading());
    final locationInfo = await apiRepository.getCurrentLocation();

    final sellReturnToAPI = SellReturnToAPI(
      transactionId: sellID,
      transactionDate: dateToString(DateTime.now()),
      discountType: cartDiscountTypePercentage ? "percentage" : "fixed",
      discountAmount: double.tryParse(cartDiscountController.text) ?? 0,
      products: _getCartReturnProducts(sellID),
      locationInfo: locationInfo,
    );

    await apiRepository.sendSellReturn(
      sellReturnToAPI: sellReturnToAPI,
      offlineID: offlineID,
    );

    EasyLoading.showSuccess('Sell Return Successful');
    initAllProducts();
    resetSellsFilters();
    initCart();
    resetPaymentDetailsList();
    getFilteredSales(isNewSearch: true, refresh: true);
    emit(SellReturnSuccess());
    return true;
  }

  Future<void> deleteSell(
    context, {
    required sellID,
    bool offline = false,
  }) async {
    final strings = AppLocalizations.of(context)!;
    log("sellID: $sellID");
    log("offline: $offline");
    EasyLoading.show(status: strings.deleting_sell);
    bool? value = await ApiRepository.get().deleteSell(
      sellID: sellID,
      offline: offline,
    );

    if (value != null && value) {
      EasyLoading.showSuccess(strings.sell_deleted_successfully);
      initAllProducts();
      resetSellsFilters();
      initCart();
      resetPaymentDetailsList();
      getFilteredSales(refresh: true);
      emit(SellDeleteSuccess());
    } else {
      EasyLoading.showError(strings.deletion_failed);
      emit(SellDeleteFailure());
    }
  }

  DateRange range = DateRange.today;

  void updateDateRange(DateRange range) {
    this.range = range;
    emit(DateRangeUpdated());
  }

  DateTime? _filterStartDate = DateTime.now();

  void changeFilterStartDate(DateTime? date) {
    _filterStartDate = date;
    const sevenDays = Duration(days: 7);
    if (_filterStartDate != null && _filterEndDate != null) {
      if (!(sameDayOrAfter(_filterStartDate!, _filterEndDate!) &&
          sameDayOrBefore(_filterStartDate!.add(sevenDays), _filterEndDate!))) {
        // the end date is not in 7 days range of the start date.
        _filterEndDate = _filterStartDate!.add(sevenDays);
      }
    } else if (_filterStartDate != null && _filterEndDate == null) {
      _filterEndDate = _filterStartDate!.add(sevenDays);
    }
    emit(SaleFilterUpdated());
  }

  DateTime? _filterEndDate = DateTime.now();

  void changeFilterEndDate(DateTime? date) {
    _filterEndDate = date;
    emit(SaleFilterUpdated());
  }

  Contact? _saleFilterContact;

  void changeSaleFilterContact(Contact? contact) {
    _saleFilterContact = contact;
    emit(SaleFilterUpdated());
  }

  BusinessLocation? _saleFilterBusinessLocation;

  void changeSaleFilterBusinessLocation(BusinessLocation? location) {
    _saleFilterBusinessLocation = location;
    emit(SaleFilterUpdated());
  }

  Product? _saleFilterProduct;

  void changeSaleFilterProduct(Product? product) {
    _saleFilterProduct = product;
    emit(SaleFilterUpdated());
  }

  String? saleFilterShipmentStatus;

  void changeSaleFilterShipmentStatus(String? status) {
    saleFilterShipmentStatus = status;
    emit(SaleFilterUpdated());
  }

  TextEditingController userTextEditingController = TextEditingController();

  UserModel? filterUser;

  void updateSaleFilterUser(UserModel? user) {
    filterUser = user;
    emit(FilterUserUpdated());
  }

  DateTime? get saleFilterStartDate => _filterStartDate;

  DateTime? get saleFilterEndDate => _filterEndDate;

  Contact? get saleFilterContact => _saleFilterContact;

  Product? get saleFilterProduct => _saleFilterProduct;

  BusinessLocation? get saleFilterBusinessLocation =>
      _saleFilterBusinessLocation;

  TextEditingController invoiceRefFilterController = TextEditingController();

  void updateProductsCubit() => emit(ProductsUpdated());

  int currentSalePage = 1;
  int salesPerPage = 10;

  Future<void> getFilteredSales({
    bool saleReturn = false,
    bool isNewSearch = false,
    bool refresh = false,
  }) async {
    if (state is PaginatedSalesLoading) return;
    if (isNewSearch) {
      currentSalePage = 1;
      emit(NewSalesLoading());
    } else {
      emit(PaginatedSalesLoading());
    }

    final connected = await InternetConnectionChecker.instance.hasConnection;

    int expectedCount = currentSalePage * salesPerPage;

    Iterable<Sell> sales = sellsBox.values
        .where((sell) => sell.sellLines != null)
        .where((sell) =>
            filterUser != null ? sell.createdBy == filterUser!.id : true)
        .toList()
        .reversed;
    Iterable<SellReturn> salesReturns = sellsReturnsBox.values
        .where((sr) => sr.returnParentSell != null)
        .where(
            (sr) => filterUser != null ? sr.createdBy == filterUser!.id : true)
        .toList()
        .reversed;

    if (isNewSearch) await getRangeSummary();

    // Check if connected and not refreshing
    if (connected && !refresh && !stopSyncingTransactions()) {
      // Check if sales data needs to be fetched
      if ((sales.length < expectedCount && !saleReturn) ||
          // Check if sales return data needs to be fetched
          (salesReturns.length < expectedCount && saleReturn)) {
        // Fetch sales or sales returns based on the condition
        if (!saleReturn) {
          await fetchSellsAndStoreInHive(
            startDate: _filterStartDate,
            endDate: _filterEndDate,
            page: currentSalePage,
          );
          sales = sellsBox.values
              .where((sell) => sell.sellLines != null)
              .where((sell) =>
                  filterUser != null ? sell.createdBy == filterUser!.id : true)
              .toList()
              .reversed;
        } else {
          await fetchSellsReturnsAndStoreInHive(
            startDate: _filterStartDate,
            endDate: _filterEndDate,
            page: currentSalePage,
          );
          salesReturns = sellsReturnsBox.values
              .where((sr) => sr.returnParentSell != null)
              .where((sr) =>
                  filterUser != null ? sr.createdBy == filterUser!.id : true)
              .toList()
              .reversed;
        }
      }
    }

    // Apply filters based on your existing logic
    // filters start here
    if (_filterStartDate != null && _filterEndDate != null && !saleReturn) {
      sales = sales.where((sell) {
        final sellDate = sell.transactionDate;
        return sameDayOrAfter(sellDate, _filterStartDate!,
                shift: range == DateRange.shift) &&
            sameDayOrBefore(sellDate, _filterEndDate!,
                shift: range == DateRange.shift);
      }).toList();
    } else if (_filterStartDate != null && !saleReturn) {
      sales = sales.where((sell) {
        return sameDayOrAfter(sell.transactionDate, _filterStartDate!,
            shift: range == DateRange.shift);
      }).toList();
    } else if (_filterEndDate != null && !saleReturn) {
      sales = sales.where((sell) {
        return sameDayOrBefore(sell.transactionDate, _filterEndDate!,
            shift: range == DateRange.shift);
      }).toList();
    } else if (_filterStartDate != null &&
        _filterEndDate != null &&
        saleReturn) {
      salesReturns = salesReturns.where((sellReturn) {
        final sellDate = sellReturn.transactionDate;
        return sameDayOrAfter(sellDate, _filterStartDate!,
                shift: range == DateRange.shift) &&
            sameDayOrBefore(sellDate, _filterEndDate!,
                shift: range == DateRange.shift);
      }).toList();
    } else if (_filterStartDate != null && saleReturn) {
      salesReturns = salesReturns.where((sellReturn) {
        return sameDayOrAfter(sellReturn.transactionDate, _filterStartDate!,
            shift: range == DateRange.shift);
      }).toList();
    } else if (_filterEndDate != null && saleReturn) {
      salesReturns = salesReturns.where((sellReturn) {
        return sameDayOrBefore(sellReturn.transactionDate, _filterStartDate!,
            shift: range == DateRange.shift);
      }).toList();
    }

    if (saleFilterShipmentStatus != null && !saleReturn) {
      sales = sales.where((sell) {
        return sell.shippingStatus?.name == saleFilterShipmentStatus;
      }).toList();
    }

    if (_saleFilterBusinessLocation != null && !saleReturn) {
      // Filter by businessId
      sales = sales.where((sell) {
        return sell.locationId == _saleFilterBusinessLocation!.id;
      }).toList();
    } else if (_saleFilterBusinessLocation != null && saleReturn) {
      salesReturns = salesReturns.where((sellReturn) {
        return sellReturn.locationId == _saleFilterBusinessLocation!.id;
      }).toList();
    }

    if (filterPaymentStatus != null && !saleReturn) {
      // Filter by contact
      sales = sales.where((sell) {
        return sell.paymentStatus == filterPaymentStatus;
      }).toList();
    }

    if (_saleFilterContact != null && !saleReturn) {
      // Filter by contact
      sales = sales.where((sell) {
        return sell.contactId == _saleFilterContact!.id;
      }).toList();
    } else if (_saleFilterContact != null && saleReturn) {
      salesReturns = salesReturns.where((sellReturn) {
        return sellReturn.contactId == _saleFilterContact!.id;
      }).toList();
    }

    if (_saleFilterProduct != null && !saleReturn) {
      // Filter by product
      sales = sales.where((sell) {
        return sell.sellLines!.any((SellLine sellLine) =>
            sellLine.productId == _saleFilterProduct!.id);
      }).toList();
    } else if (_saleFilterProduct != null && saleReturn) {
      salesReturns = salesReturns.where((sellReturn) {
        return sellReturn.returnParentSell!.sellLines!.any(
            (SellLine sellLine) =>
                sellLine.productId == _saleFilterProduct!.id);
      }).toList();
    }

    if (invoiceRefFilterController.text.isNotEmpty && !saleReturn) {
      final regex =
          RegExp(invoiceRefFilterController.text, caseSensitive: false);
      sales = sales.where((sell) {
        return regex.hasMatch(sell.invoiceNo ?? "");
      }).toList();
    } else if (invoiceRefFilterController.text.isNotEmpty && saleReturn) {
      final regex =
          RegExp(invoiceRefFilterController.text, caseSensitive: false);
      salesReturns = salesReturns.where((sellReturn) {
        return regex.hasMatch(sellReturn.returnParentSell?.invoiceNo ?? "");
      }).toList();
    }
    // filters end here

    // Pagination
    List<dynamic> allList =
        !saleReturn ? sales.toList() : salesReturns.toList();
    int endIndex = currentSalePage * salesPerPage;
    endIndex = endIndex > allList.length ? allList.length : endIndex;

    List<dynamic> paginatedList = allList.sublist(0, endIndex);

    currentSalePage++; // Increment currentPage for the next paginated call

    final allCalculations = currentSummary != null
        ? totalBillsFromSummary(
            summary: currentSummary!, isSale: true, isReturn: saleReturn)
        : totalBills(
            sales:
                allList.where((s) => !saleReturn ? s.isQuotation == 0 : true),
          );

    final quotationCalculations = totalBills(
      sales: allList.where((s) => !saleReturn ? s.isQuotation == 1 : true),
    );

    final todaySells = sellsBox.values.where((sell) =>
        sell.transactionDate
            .isAfter(DateTime.now().subtract(const Duration(days: 1))) &&
        sell.isQuotation == 0);

    final todayCalculations = totalBills(sales: todaySells);
    if (!saleReturn) {
      emit(
        SalesLoaded(
          sales: paginatedList as List<Sell>,
          calculations: allCalculations,
          quotationCalculations: quotationCalculations,
          todayCalculations: todayCalculations,
          todaySales: todaySells.toList().reversed,
        ),
      );
    } else {
      emit(
        SalesReturnsLoaded(
          salesReturns: paginatedList as List<SellReturn>,
          calculations: allCalculations,
        ),
      );
    }
  }

  (double bills, double paid, double dues) totalBills(
      {required Iterable<dynamic> sales}) {
    double billAmount = 0;
    double amountPaid = 0;
    double dues = 0;
    for (var sell in sales) {
      billAmount += double.parse(sell.finalTotal ?? "0");
      if (sell.paymentLines != null) {
        for (PaymentLine paymentLine in sell.paymentLines!) {
          amountPaid += double.parse(paymentLine.amount!);
        }
      }
      dues = billAmount - amountPaid;
    }
    return (billAmount, amountPaid, dues);
  }

  (double bills, double paid, double dues) totalBillsFromSummary({
    required ShiftDetails summary,
    required bool isSale,
    required bool isReturn,
  }) {
    double billAmount = 0;
    double amountPaid = 0;
    double dues = 0;

    if (isSale) {
      if (!isReturn) {
        billAmount = double.parse(summary.transactions.first.totalSell);
        amountPaid = double.parse(summary.payments.first.totalSellPaid);
      } else {
        billAmount = double.parse(summary.transactions.first.totalSellReturn);
        amountPaid = double.parse(summary.payments.first.totalSellReturnPaid);
      }
    } else {
      if (!isReturn) {
        billAmount = double.parse(summary.transactions.first.totalPurchase);
        amountPaid = double.parse(summary.payments.first.totalPurchasePaid);
      } else {
        billAmount =
            double.parse(summary.transactions.first.totalPurchaseReturn);
        amountPaid =
            double.parse(summary.payments.first.totalPurchaseReturnPaid);
      }
    }
    dues = billAmount - amountPaid;
    return (billAmount, amountPaid, dues);
  }

  void resetSalesFilters() {
    _filterStartDate = DateTime.now();
    _filterEndDate = DateTime.now();
    _saleFilterContact = null;
    filterUser = null;
    _saleFilterProduct = null;
    _saleFilterBusinessLocation = null;
    filterPaymentStatus = null;
    cartPriceGroup = null;
    range = DateRange.today;
    invoiceRefFilterController.text = "";
    customServicePriceController.text = "";
    cartSearchController.text = "";
    saleFilterShipmentStatus = null;
    changeServiceStaff(serviceStaffBox.values.length == 1
        ? serviceStaffBox.values.first
        : null);
    changeTable(null);
    changeCommAgent(
        commAgentsBox.values.length == 1 ? commAgentsBox.values.first : null);
    cartShippingStatus = null;
    cartShippingCompany = null;
    emit(SalesFilterReset());
  }

  /// purchase management
  List<PurchaseProductToAPI> _getPurchaseProducts() {
    List<PurchaseProductToAPI> cartProductList = [];
    for (int productId in cartProducts) {
      ProductData? productData = allProducts[productId];
      if (productData != null &&
          filteredProductList(isPurchase: true)
              .any((product) => product.id == productId)) {
        int? variationId;
        variationId = productData.variation?.id;
        final unit = unitsBox.get(productData.unit.id);
        final isBaseUnit = unit?.baseUnitId == null;
        cartProductList.add(
          PurchaseProductToAPI(
            productId: productId,
            variationId: variationId,
            quantity: productData.quantityController.text,
            purchasePrice: piecePrice(productId, isPurchase: true).toString(),
            discountPercent: productData.discountController.text,
            productUnitId: isBaseUnit
                ? productData.unit.id.toString()
                : unit?.baseUnitId.toString() ?? productData.unit.id.toString(),
            subUnitId: isBaseUnit ? null : productData.unit.id.toString(),
            itemTax: productData.taxRate?.amount.toString(),
            defaultSellPrice: piecePrice(productId, original: true).toString(),
            purchasePriceIncTax:
                piecePriceAfter(productId, isPurchase: true).toString(),
          ),
        );
      }
    }
    return cartProductList;
  }

  Future<bool> sendPurchaseToAPI({
    bool? cash,
    bool credit = false,
    int? purchaseID,
    String? offlineID,
  }) async {
    final now = DateTime.now();
    emit(PurchaseLoading());
    final productsList = _getPurchaseProducts();
    final locationInfo = await apiRepository.getCurrentLocation();

    final purchase = PurchaseToAPI(
      contactId: cartCustomerContact!.id,
      status: "received",
      transactionDate: now,
      refNo: purchaseID != null ? purchasesBox.get(purchaseID)?.refNo : null,
      locationId: selectedLocation.id,
      totalBeforeTax: getCartTotalBeforeTax(purchase: true).toString(),
      finalTotal: getCartTotalAfterTax(purchase: true).toString(),
      payment: _getPayments(
        cash: cash,
        credit: credit,
        purchase: true,
        paymentFor: cartCustomerContact!.id,
      ),
      products: productsList,
      discountType: cartDiscountTypePercentage ? "percentage" : "fixed",
      shippingCharges: cartShippingFeesController.text,
      discountAmount: cartDiscountController.text,
      additionalNotes: cartEmployeeNoteController.text,
      taxId: cartTaxRate?.id.toString(),
      taxAmount: taxRatesBox.get(cartTaxRate?.id)?.amount.toString(),
      offlineID: offlineID,
      locationInfo: locationInfo,
      shippingDetails: cartShippingDetailsController.text,
    );

    await apiRepository.sendProductPurchase(
      purchase: purchase,
      purchaseID: purchaseID,
    );

    _updateProductsQuantities(productsList);
    initAllProducts();
    resetSellsFilters();
    initCart();
    resetPaymentDetailsList();
    getFilteredPurchases(isNewSearch: true, refresh: true);
    return true;
  }

  List<ProductPurchaseReturn> _getCartPurchaseReturnProducts(int purchaseID) {
    List<ProductPurchaseReturn> cartPurchaseReturnProductList = [];
    for (int productId in cartProducts) {
      ProductData? productData = allProducts[productId];
      PurchaseLine purchaseLine = purchasesBox
          .get(purchaseID)!
          .purchaseLines!
          .firstWhere((element) => element.productId! == productId);
      if (productData != null &&
          filteredProductList(isPurchase: true)
              .any((product) => product.id == productId)) {
        cartPurchaseReturnProductList.add(
          ProductPurchaseReturn(
            purchaseLineId: purchaseLine.id.toString(),
            returnedQuantity:
                allProducts[purchaseLine.productId]!.quantityController.text,
          ),
        );
      }
    }
    return cartPurchaseReturnProductList;
  }

  Future<bool> sendPurchaseReturn(BuildContext context, {required int purchaseID}) async {
    final strings = AppLocalizations.of(context)!;
    emit(PurchaseReturnLoading());
    final locationInfo = await apiRepository.getCurrentLocation();

    final purchaseReturnToAPI = PurchaseReturnToAPI(
      locationInfo: locationInfo,
      transactionId: purchaseID.toString(),
      taxId: purchasesBox.get(purchaseID)!.taxId,
      taxAmount: purchasesBox.get(purchaseID)!.taxAmount,
      products: _getCartPurchaseReturnProducts(purchaseID),
    );

    await apiRepository.sendPurchaseReturn(
      purchaseReturnToAPI: purchaseReturnToAPI,
    );

    EasyLoading.showSuccess(strings.purchase_return_added_successfully);
    initAllProducts();
    resetSellsFilters();
    initCart();
    resetPaymentDetailsList();
    getFilteredPurchases(isNewSearch: true, refresh: true);
    return true;
  }

  Future<void> deletePurchase(
    BuildContext context, {
    required dynamic purchaseID,
    bool offline = false,
  }) async {
    final strings = AppLocalizations.of(context)!;
    EasyLoading.show(status: strings.deleting_purchase);
    bool? value = await apiRepository.deletePurchase(
      purchaseID: purchaseID,
      offline: offline,
    );

    if (value != null && value) {
      EasyLoading.showSuccess(strings.purchase_deleted_successfully);
      initAllProducts();
      resetSellsFilters();
      initCart();
      resetPaymentDetailsList();
      getFilteredPurchases(refresh: true);
      emit(SellDeleteSuccess());
    } else {
      EasyLoading.showError(strings.deletion_failed);
      emit(SellDeleteFailure());
    }
  }

  Contact? _purchaseFilterContact;

  void changePurchaseFilterContact(Contact? contact) {
    _purchaseFilterContact = contact;
    emit(PurchaseFilterContactUpdated());
  }

  BusinessLocation? _purchaseFilterBusinessLocation;

  void changePurchaseFilterBusinessLocation(BusinessLocation? location) {
    _purchaseFilterBusinessLocation = location;
    emit(PurchaseFilterBusinessLocationUpdated());
  }

  Product? _purchaseFilterProduct;

  void changePurchaseFilterProduct(Product? product) {
    _purchaseFilterProduct = product;
    emit(PurchaseFilterProductUpdated());
  }

  Contact? get purchaseFilterContact => _purchaseFilterContact;

  Product? get purchaseFilterProduct => _purchaseFilterProduct;

  BusinessLocation? get purchaseFilterBusinessLocation =>
      _purchaseFilterBusinessLocation;

  TextEditingController purchaseRefFilterController = TextEditingController();

  int currentPage = 1;
  int itemsPerPage = 10;

  Future<void> getFilteredPurchases({
    bool purchaseReturn = false,
    bool isNewSearch = false,
    bool refresh = false,
  }) async {
    if (state is PaginatedPurchasesLoading) return;
    if (isNewSearch) {
      currentPage = 1;
      emit(NewPurchasesLoading());
    } else {
      emit(PaginatedPurchasesLoading());
    }

    final connected = await InternetConnectionChecker.instance.hasConnection;

    int expectedCount = currentPage * itemsPerPage;

    Iterable<Purchase> purchases = purchasesBox.values
        .where((purchase) => purchase.purchaseLines != null)
        .toList()
        .reversed;
    Iterable<PurchaseReturn> purchasesReturns = purchaseReturnsBox.values
        .where((pr) => pr.returnParentPurchase != null)
        .toList()
        .reversed;

    if (isNewSearch) await getRangeSummary();

    // Check if connected and not refreshing
    if (connected && !refresh && !stopSyncingTransactions()) {
      // Check if purchases data needs to be fetched
      if ((purchases.length < expectedCount && !purchaseReturn) ||
          // Check if purchases return data needs to be fetched
          (purchasesReturns.length < expectedCount && purchaseReturn)) {
        // Fetch sales or purchases returns based on the condition
        if (!purchaseReturn) {
          await fetchPurchasesAndStoreInHive(
            startDate: _filterStartDate,
            endDate: _filterEndDate,
            page: currentPage,
          );
          purchases = purchasesBox.values
              .where((purchase) => purchase.purchaseLines != null)
              .toList()
              .reversed;
        } else {
          await fetchPurchasesReturnsAndStoreInHive(
            startDate: _filterStartDate,
            endDate: _filterEndDate,
            page: currentPage,
          );
          purchasesReturns = purchaseReturnsBox.values
              .where((pr) => pr.returnParentPurchase != null)
              .toList()
              .reversed;
        }
      }
    }

    // Apply filters
    if (_filterStartDate != null && _filterEndDate != null && !purchaseReturn) {
      purchases = purchases.where((purchase) {
        final purchaseDate = purchase.transactionDate;
        return sameDayOrAfter(purchaseDate, _filterStartDate!,
                shift: range == DateRange.shift) &&
            sameDayOrBefore(purchaseDate, _filterEndDate!,
                shift: range == DateRange.shift);
      }).toList();
    } else if (_filterStartDate != null && !purchaseReturn) {
      purchases = purchases.where((purchase) {
        return sameDayOrAfter(purchase.transactionDate, _filterStartDate!,
            shift: range == DateRange.shift);
      }).toList();
    } else if (_filterEndDate != null && !purchaseReturn) {
      purchases = purchases.where((purchase) {
        return sameDayOrBefore(purchase.transactionDate, _filterEndDate!,
            shift: range == DateRange.shift);
      }).toList();
    } else if (_filterStartDate != null &&
        _filterEndDate != null &&
        purchaseReturn) {
      purchasesReturns = purchasesReturns.where((purchaseReturn) {
        final purchaseDate = purchaseReturn.transactionDate;
        return sameDayOrAfter(purchaseDate, _filterStartDate!,
                shift: range == DateRange.shift) &&
            sameDayOrBefore(purchaseDate, _filterEndDate!,
                shift: range == DateRange.shift);
      }).toList();
    } else if (_filterStartDate != null && purchaseReturn) {
      purchasesReturns = purchasesReturns.where((purchaseReturn) {
        return sameDayOrAfter(purchaseReturn.transactionDate, _filterStartDate!,
            shift: range == DateRange.shift);
      }).toList();
    } else if (_filterEndDate != null && purchaseReturn) {
      purchasesReturns = purchasesReturns.where((purchaseReturn) {
        return sameDayOrBefore(
            purchaseReturn.transactionDate, _filterStartDate!,
            shift: range == DateRange.shift);
      }).toList();
    }

    if (_purchaseFilterContact != null && !purchaseReturn) {
      // Filter by contact
      purchases = purchases.where((purchase) {
        return purchase.contactId == _purchaseFilterContact!.id;
      }).toList();
    } else if (_purchaseFilterContact != null && purchaseReturn) {
      purchasesReturns = purchasesReturns.where((purchaseReturn) {
        return purchaseReturn.contactId == _purchaseFilterContact!.id;
      }).toList();
    }

    if (_purchaseFilterBusinessLocation != null && !purchaseReturn) {
      // Filter by Location
      purchases = purchases.where((purchase) {
        return purchase.locationId == _purchaseFilterBusinessLocation!.id;
      }).toList();
    } else if (_purchaseFilterBusinessLocation != null && purchaseReturn) {
      purchasesReturns = purchasesReturns.where((purchaseReturn) {
        return purchaseReturn.locationId == _purchaseFilterBusinessLocation!.id;
      }).toList();
    }

    if (_purchaseFilterProduct != null && !purchaseReturn) {
      // Filter by product
      purchases = purchases.where((purchase) {
        return purchase.purchaseLines!.any((PurchaseLine purchaseLine) =>
            purchaseLine.productId == _purchaseFilterProduct!.id);
      }).toList();
    } else if (_purchaseFilterProduct != null && purchaseReturn) {
      purchasesReturns = purchasesReturns.where((purchaseReturn) {
        return purchaseReturn.purchaseLines!.any((PurchaseLine purchaseLine) =>
            purchaseLine.productId == _purchaseFilterProduct!.id);
      }).toList();
    }

    if (purchaseRefFilterController.text.isNotEmpty && !purchaseReturn) {
      final regex =
          RegExp(purchaseRefFilterController.text, caseSensitive: false);
      purchases = purchases.where((purchase) {
        return regex.hasMatch(purchase.refNo ?? "");
      }).toList();
    } else if (purchaseRefFilterController.text.isNotEmpty && purchaseReturn) {
      final regex =
          RegExp(purchaseRefFilterController.text, caseSensitive: false);
      purchasesReturns = purchasesReturns.where((pr) {
        return regex.hasMatch(pr.returnParentPurchase?.refNo ?? "");
      }).toList();
    }

    List<dynamic> allList =
        !purchaseReturn ? purchases.toList() : purchasesReturns.toList();

    // Apply filters based on your existing logic

    // Pagination
    int endIndex = currentPage * itemsPerPage;
    endIndex = endIndex > allList.length ? allList.length : endIndex;

    List<dynamic> paginatedList = allList.sublist(0, endIndex);

    currentPage++; // Increment currentPage for the next paginated call

    double totalAmount =
        allList.fold(0, (sum, p) => sum + double.parse(p.finalTotal ?? "0"));

    double totalPaid =
        allList.where((purchase) => purchase.paymentLines != null).fold(
      0,
      (sum, purchase) {
        return sum +
            purchase.paymentLines!.fold(
                0, (sum, payment) => sum + double.parse(payment.amount ?? "0"));
      },
    );

    double remaining = totalAmount - totalPaid;

    emit(
      PurchasesLoaded(
        purchases: paginatedList,
        calculations: currentSummary != null
            ? totalBillsFromSummary(
                summary: currentSummary!,
                isSale: false,
                isReturn: purchaseReturn,
              )
            : (totalAmount, totalPaid, remaining),
      ),
    );
  }

  void resetPurchasesFilters() {
    range = DateRange.today;
    _filterStartDate = DateTime.now();
    _filterEndDate = DateTime.now();
    _purchaseFilterContact = null;
    filterUser = null;
    _purchaseFilterProduct = null;
    purchaseRefFilterController.text = "";
    _purchaseFilterBusinessLocation = null;
    cartPriceGroup = null;
    invoiceProductsPanelController =
        ExpandableController(initialExpanded: false);
    customerDataPanelController = ExpandableController(initialExpanded: false);
    shippingAndTaxesPanelController =
        ExpandableController(initialExpanded: true);
    paymentDataPanelController = ExpandableController(initialExpanded: true);
    commAgentsPanelController = ExpandableController(initialExpanded: true);
    emit(PurchaseFilterUpdated());
  }

  /// purchase management END

  /// shipment management
  Map<int, String?>? shipmentsStatus = {};

  void changeShipmentStatus({required int id, String? status}) {
    shipmentsStatus![id] = status;
    emit(ShipmentStatusChanged());
  }

  void resetShipmentStatus() {
    shipmentsStatus = {};
    emit(ShipmentStatusChanged());
  }

  Future<void> sendShipmentStatusUpdate(int sellId) async {
    emit(ShipmentStatusLoading());
    final value = await apiRepository.updateShipmentStatus(
        sellID: sellId, status: shipmentsStatus![sellId]);
    if (value != null && value) {
      shipmentsStatus = {};
      getFilteredSales(isNewSearch: true, refresh: true);
      emit(ShipmentStatusLoaded());
    } else {
      EasyLoading.showError('Status Update Failed');
      emit(ShipmentStatusFailed());
    }
  }

  /// shipment management END

  /// Printer
  Future<void> generatePdf(
    BuildContext context, {
    Sell? sell,
    SellReturn? sReturn,
    Purchase? purchase,
    bool share = false,
    bool view = false,
  }) async {
    final strings = AppLocalizations.of(context)!;
    if (!share && defaultTargetPlatform != TargetPlatform.windows && defaultTargetPlatform != TargetPlatform.macOS) {
      if (getBluetoothDeviceAddress() != null) {
        return await showDialog(
          context: context,
          builder: (_) => PrinterAlertDialog(
            sell: sell,
            sReturn: sReturn,
            purchase: purchase,
          ),
        );
      } else {
        EasyLoading.showError(strings.choose_default_bluetooth_device);
      }
    } else {
      generatePdfFunction(
        context,
        sell: sell,
        share: share,
        sReturn: sReturn,
        purchase: purchase,
      );
    }
  }

  /// Printer END

  var invoiceProductsPanelController =
      ExpandableController(initialExpanded: false);
  var customerDataPanelController =
      ExpandableController(initialExpanded: false);
  var shippingAndTaxesPanelController =
      ExpandableController(initialExpanded: true);
  var commAgentsPanelController = ExpandableController(initialExpanded: true);
  var paymentDataPanelController = ExpandableController(initialExpanded: true);

  void resetAll() {
    selectedLocation = businessLocationsBox.values.first;
    initAllProducts();
    initCart();
    resetPaymentDetailsList();
    resetPurchasesFilters();
    resetSalesFilters();
    resetSellsFilters();
    resetShipmentStatus();
  }



  @override
  Future<void> close() {
    // Dispose of scroll controller
    billScrollController.dispose();
    // Dispose of focus nodes
    paymentFieldFocusNode.dispose();
    discountFieldFocusNode.dispose();
    return super.close();
  }
}
