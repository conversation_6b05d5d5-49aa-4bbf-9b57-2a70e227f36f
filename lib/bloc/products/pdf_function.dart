import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:printing/printing.dart';
import 'package:we2up/data/models/business_settings.dart';
import 'package:we2up/utils/we2up_constants.dart';
import '../../data/models/product.dart';
import '../../data/models/purchase.dart';
import '../../data/models/sell.dart';
import '../../data/db/db_manager.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;

import '../../data/models/sell_return.dart';
import '../../data/models/service_model.dart';
import '../../utils/thermal_printer_manager.dart';

(pw.TextDirection, bool) _direction(context) {
  return AppLocalizations.of(context)!.localeName == "ar"
      ? (pw.TextDirection.rtl, true)
      : (pw.TextDirection.ltr, false);
}

Future<Uint8List> generatePdfFunction(
  BuildContext context, {
  Sell? sell,
  Purchase? purchase,
  SellReturn? sReturn,
  bool share = false,
  bool view = false,
  bool warehouse = false,
  String? address,
}) async {
  final strings = AppLocalizations.of(context)!;
  final dir = _direction(context).$1;
  final bool isArabic = _direction(context).$2;
  final font = await fontFromAssetBundle(getCurrentFontPath());
  final connected = await InternetConnectionChecker.instance.hasConnection;
  String sanitizedFileName =
      'receipt ${(sell?.invoiceNo ?? purchase?.refNo ?? "").trim()}.pdf';
  final contact = contactsBox.get(
    sell?.contactId ?? purchase?.contactId ?? sReturn?.contactId,
  );

  final Iterable<SellLine>? sReturnSellLines = sReturn
      ?.returnParentSell!.sellLines
      ?.where((line) => double.parse(line.quantityReturned ?? "0.0") != 0);

  double invoiceTotalAmount() {
    var x = double.parse(
        sell?.finalTotal ?? purchase?.finalTotal ?? sReturn?.finalTotal ?? "0");
    return roundIfAbove075(x);
  }

  double totalPaid() {
    double x = 0.0;
    sell?.paymentLines?.forEach((paymentLine) {
      x += double.tryParse(paymentLine.amount ?? "0.0") ?? 0.0;
    });
    purchase?.paymentLines?.forEach((paymentLine) {
      x += double.tryParse(paymentLine.amount ?? "0.0") ?? 0.0;
    });
    x += double.tryParse(sReturn?.finalTotal ?? "0.0") ?? 0.0;
    return roundIfAbove075(x);
  }

  double getQuantity(dynamic sellLine, {isReturn = false}) {
    final double quantity = (isReturn
            ? double.tryParse(sellLine.quantityReturned ?? "0")
            : sellLine.quantity) ??
        0.0;
    final sellUnit = unitsBox.get(sellLine.subUnitId);
    final productUnit = productsBox.get(sellLine.productId)?.unit;
    final sameUnit = sellUnit == productUnit;
    return sameUnit
        ? quantity
        : quantity / (sellUnit?.baseUnitMultiplier ?? 1.0);
  }

  String invoiceQuantities() {
    double x = 0.0;
    sell?.sellLines?.forEach((sellLine) {
      x += getQuantity(sellLine);
    });
    purchase?.purchaseLines?.forEach((purchaseLine) {
      x += getQuantity(purchaseLine);
    });
    sReturnSellLines?.forEach((sellLine) {
      x += getQuantity(sellLine, isReturn: true);
    });
    return formatNumber(roundIfAbove075(x));
  }

  String sumWithoutTaxAndDiscount() {
    double x = 0.0;
    sell?.sellLines?.forEach((sellLine) {
      x += (double.parse(
              sellLine.unitPriceBeforeDiscount ?? sellLine.unitPrice ?? "0") *
          (sellLine.quantity ?? 0.0).toDouble());
    });
    purchase?.purchaseLines?.forEach((purchaseLine) {
      x += (double.parse(purchaseLine.purchasePrice ?? "0") *
          (purchaseLine.quantity ?? 0.0).toDouble());
    });
    sReturnSellLines?.forEach((sellLine) {
      x += (double.parse(
              sellLine.unitPriceBeforeDiscount ?? sellLine.unitPrice ?? "0") *
          (double.tryParse(sellLine.quantityReturned ?? "0") ?? 0).toDouble());
    });
    return formatNumber(x);
  }

  String sumAfterTaxAndDiscount() {
    double x = 0.0;
    sell?.sellLines?.forEach((sellLine) {
      x += double.parse(sellLine.unitPriceIncTax ?? "0") *
          (sellLine.quantity ?? 0);
    });
    purchase?.purchaseLines?.forEach((purchaseLine) {
      x += double.parse(purchaseLine.purchasePriceIncTax ?? "0") *
          (purchaseLine.quantity ?? 0);
    });
    sReturnSellLines?.forEach((sellLine) {
      x += double.parse(sellLine.unitPriceIncTax ?? "0") *
          (double.tryParse(sellLine.quantityReturned ?? "0") ?? 0);
    });
    return formatNumber(roundIfAbove075(x));
  }

  String getLineDiscountAmount(dynamic line, {bool returnPercentage = false}) {
    double x = 0;

    if (line is SellLine) {
      var discountAmount = double.parse(line.lineDiscountAmount ?? "0");
      var priceBefore = double.parse(line.unitPriceBeforeDiscount ?? "0");

      if (returnPercentage) {
        if (line.lineDiscountType == "percentage") {
          x += discountAmount;
        } else {
          // Convert flat discount amount to percentage
          x += (discountAmount / priceBefore) * 100;
        }
      } else {
        if (line.lineDiscountType == "percentage") {
          var amount = priceBefore * (discountAmount / 100);
          x += amount;
        } else {
          x += discountAmount;
        }
      }
    } else if (line is PurchaseLine) {
      var discountPercent = double.parse(line.discountPercent ?? "0");
      var priceBefore = double.parse(line.ppWithoutDiscount ?? "0");

      if (returnPercentage) {
        x += discountPercent;
      } else {
        var amount = priceBefore * (discountPercent / 100);
        x += amount;
      }
    }

    return formatNumber(roundIfAbove075(x));
  }

  double sumDiscounts() {
    double x = 0.0;
    sell?.sellLines?.forEach((sellLine) {
      var discountAmount = double.parse(sellLine.lineDiscountAmount ?? "0");
      var priceBefore = double.parse(sellLine.unitPriceBeforeDiscount ?? "0");
      if (sellLine.lineDiscountType == "percentage") {
        var amount = priceBefore * (discountAmount / 100);
        x += amount * (sellLine.quantity ?? 0);
      } else {
        x += discountAmount * (sellLine.quantity ?? 0);
      }
    });
    purchase?.purchaseLines?.forEach((purchaseLine) {
      x += (double.tryParse(purchaseLine.discountPercent ?? "0") ?? 0) *
          (purchaseLine.quantity ?? 0);
    });

    sReturnSellLines?.forEach((sellLine) {
      var discountAmount = double.parse(sellLine.lineDiscountAmount ?? "0");
      var priceBefore = double.parse(sellLine.unitPriceBeforeDiscount ?? "0");
      if (sellLine.lineDiscountType == "percentage") {
        var amount = priceBefore * (discountAmount / 100);
        x += amount * (double.tryParse(sellLine.quantityReturned ?? "0") ?? 0);
      } else {
        x += discountAmount *
            (double.tryParse(sellLine.quantityReturned ?? "0") ?? 0);
      }
    });

    return x;
  }

  double sumTaxes() {
    return (double.parse(sumWithoutTaxAndDiscount()) -
            sumDiscounts() -
            double.parse(sumAfterTaxAndDiscount())) *
        -1;
  }

  (bool, String) productTaxesSameType() {
    TaxRate? tax;
    bool allTaxesSameType = true;

    if (sell != null && sell.sellLines != null) {
      for (var sellLine in sell.sellLines!) {
        if (sellLine.taxId != null) {
          TaxRate? currentTax = taxRatesBox.get(sellLine.taxId);
          if (currentTax != null) {
            if (tax == null) {
              // First non-null tax found
              tax = currentTax;
            } else if (currentTax != tax) {
              // Taxes are not equal
              allTaxesSameType = false;
              break;
            }
          }
        }
      }
    } else if (purchase != null && purchase.purchaseLines != null) {
      for (var purchaseLine in purchase.purchaseLines!) {
        if (purchaseLine.taxId != null) {
          TaxRate? currentTax = taxRatesBox.get(purchaseLine.taxId);
          if (currentTax != null) {
            if (tax == null) {
              // First non-null tax found
              tax = currentTax;
            } else if (currentTax != tax) {
              // Taxes are not equal
              allTaxesSameType = false;
              break;
            }
          }
        }
      }
    } else if (sReturnSellLines != null) {
      for (var sellLine in sReturnSellLines) {
        if (sellLine.taxId != null) {
          TaxRate? currentTax = taxRatesBox.get(sellLine.taxId);
          if (currentTax != null) {
            if (tax == null) {
              // First non-null tax found
              tax = currentTax;
            } else if (currentTax != tax) {
              // Taxes are not equal
              allTaxesSameType = false;
              break;
            }
          }
        }
      }
    }

    if (allTaxesSameType && tax != null) {
      // All non-null taxes have the same type
      return (true, "(${tax.name!}) ");
    }

    // No tax or non-null taxes are not equal
    return (false, "");
  }

  double invoiceTaxes() {
    if (sell?.taxId != null || purchase?.taxId != null) {
      final x = taxRatesBox
          .get(
            sell?.taxId ?? purchase?.taxId ?? sReturn?.returnParentSell!.taxId,
          )!
          .amount!;
      return roundIfAbove075(x);
    } else {
      return 0;
    }
  }

  double invoiceShipping() {
    if (sell?.shippingCharges != null || purchase?.shippingCharges != null) {
      return double.tryParse(
              sell?.shippingCharges ?? purchase?.shippingCharges ?? '0') ??
          0;
    } else {
      return 0;
    }
  }

  double totalWeight() {
    double weight = 0;
    sell?.sellLines?.forEach((sellLine) {
      final w = productsBox.get(sellLine.productId)?.weight;
      weight += ((double.tryParse(w ?? "0") ?? 0) * ((sellLine.quantity ?? 0)));
    });
    purchase?.purchaseLines?.forEach((purchaseLine) {
      final w = productsBox.get(purchaseLine.productId)?.weight;
      weight +=
          ((double.tryParse(w ?? "0") ?? 0) * ((purchaseLine.quantity ?? 0)));
    });
    sReturnSellLines?.forEach((sellLine) {
      final w = productsBox.get(sellLine.productId)?.weight;
      weight += ((double.tryParse(w ?? "0") ?? 0) * ((sellLine.quantity ?? 0)));
    });
    return weight;
  }

  double invoiceService() {
    if (sell?.typesOfServiceId != null) {
      final serviceModel = serviceModelBox.get(sell!.typesOfServiceId);
      switch (serviceModel?.packingChargeType) {
        case PackingChargeType.weight:
          return serviceModel!.packingCharge * totalWeight();
        case PackingChargeType.fixed:
          return serviceModel!.packingCharge;
        case PackingChargeType.percentage:
          return serviceModel!.packingCharge *
              (double.parse(sumWithoutTaxAndDiscount()) / 100);
        default:
          return 0;
      }
    } else {
      return 0;
    }
  }

  double invoiceDiscount({bool returnPercentage = false}) {
    if (sell?.discountAmount != null) {
      var discountAmount = double.parse(sell!.discountAmount!);
      if (sell.discountType == "percentage") {
        if (returnPercentage) {
          return discountAmount;
        }
        var amount =
            double.parse(sumAfterTaxAndDiscount()) * (discountAmount / 100);
        return roundIfAbove075(amount);
      } else {
        if (returnPercentage) {
          // Convert flat discount amount to percentage
          var sumAfterTax = double.parse(sumAfterTaxAndDiscount());
          var discountPercent = (discountAmount / sumAfterTax) * 100;
          return roundIfAbove075(discountPercent);
        }
        return discountAmount;
      }
    } else if (purchase?.discountAmount != null) {
      var discountAmount =
          double.tryParse(purchase!.discountAmount ?? "0") ?? 0;
      if (purchase.discountType == "percentage") {
        if (returnPercentage) {
          return discountAmount;
        }
        var amount =
            double.parse(sumAfterTaxAndDiscount()) * (discountAmount / 100);
        return roundIfAbove075(amount);
      } else {
        if (returnPercentage) {
          // Convert flat discount amount to percentage
          var sumAfterTax = double.parse(sumAfterTaxAndDiscount());
          var discountPercent = (discountAmount / sumAfterTax) * 100;
          return roundIfAbove075(discountPercent);
        }
        return roundIfAbove075(discountAmount);
      }
    } else if (sReturn?.discountAmount != null) {
      var discountAmount = double.tryParse(sReturn!.discountAmount ?? "0") ?? 0;
      if (sReturn.discountType == "percentage") {
        if (returnPercentage) {
          return discountAmount;
        }
        var amount =
            double.parse(sumAfterTaxAndDiscount()) * (discountAmount / 100);
        return roundIfAbove075(amount);
      } else {
        if (returnPercentage) {
          // Convert flat discount amount to percentage
          var sumAfterTax = double.parse(sumAfterTaxAndDiscount());
          var discountPercent = (discountAmount / sumAfterTax) * 100;
          return roundIfAbove075(discountPercent);
        }
        return roundIfAbove075(discountAmount);
      }
    }
    return 0;
  }

  String calculateTaxAmount() {
    final total = double.parse(sumAfterTaxAndDiscount());
    final discountedTotal = (total - invoiceDiscount());
    final taxAmount = (discountedTotal * (invoiceTaxes() / 100));
    return formatNumber(roundIfAbove075(taxAmount));
  }

  double previousBalance() {
    if (sReturn != null) {
      return double.parse(contact?.due ?? "0") + totalPaid();
    }
    var x = double.parse(contact?.due ?? "0") -
        double.parse((sell?.finalTotal ?? purchase?.finalTotal) ?? "0") +
        totalPaid();
    return roundIfAbove075(x);
  }

  double invoiceTotalPayment() {
    if (sReturn != null) {
      return previousBalance() - totalPaid();
    }
    var x = previousBalance() +
        double.parse(sell?.finalTotal ?? purchase?.finalTotal ?? "0");
    return roundIfAbove075(x);
  }

  double remainingDue() {
    if (sReturn != null) {
      return invoiceTotalPayment() + totalPaid();
    }
    var x = invoiceTotalPayment() - totalPaid();
    return roundIfAbove075(x);
  }

  bool sellHasTaxes() {
    return sell?.sellLines?.any((sellLine) => sellLine.taxId != null) ??
        purchase?.purchaseLines
            ?.any((purchaseLine) => purchaseLine.taxId != null) ??
        sReturnSellLines?.any((sellLine) => sellLine.taxId != null) ??
        false;
  }

  bool sellHasDiscount() {
    return sell?.sellLines?.any((sellLine) =>
            double.parse(sellLine.lineDiscountAmount ?? "0") > 0) ??
        purchase?.purchaseLines?.any((purchaseLine) =>
            (double.tryParse(purchaseLine.discountPercent ?? "0") ?? 0) > 0) ??
        sReturnSellLines?.any((sellLine) =>
            double.parse(sellLine.lineDiscountAmount ?? "0") > 0) ??
        false;
  }

  final style = pw.TextStyle(
    font: font,
    fontSize: getCurrentFontSize().toDouble(),
    fontWeight: pw.FontWeight.bold,
  );
  final pdf = pw.Document(pageMode: PdfPageMode.fullscreen);

  pw.Text styledText(String text) => pw.Text(
        applyArabicDigitConversion(text),
        style: style,
        textAlign: pw.TextAlign.center,
      );

  pw.Text smallText(String text) => pw.Text(
        applyArabicDigitConversion(text),
        style: pw.TextStyle(
          font: font,
          fontSize: (getCurrentFontSize() - 2).toDouble(),
          fontWeight: pw.FontWeight.bold,
        ),
        textAlign: pw.TextAlign.center,
      );

  pw.Widget headerText(String text) => pw.Container(
        color: const PdfColor(0.85, 0.85, 0.85),
        child: pw.Center(
          child: pw.Text(
            applyArabicDigitConversion(text),
            textAlign: pw.TextAlign.center,
            style: style,
            maxLines: 1,
          ),
        ),
      );

  const myTableBorder = pw.TableBorder(
    left: pw.BorderSide(),
    right: pw.BorderSide(),
    top: pw.BorderSide(width: 1.3),
    bottom: pw.BorderSide(width: 1.3),
    horizontalInside: pw.BorderSide(),
    verticalInside: pw.BorderSide(),
  );

  PdfPageFormat myPageFormat({int? marginRight, int? marginLeft}) {
    if ((getCurrentPaperSize() == 'roll57' ||
            getCurrentPaperSize() == 'roll80') &&
        defaultTargetPlatform != TargetPlatform.windows && defaultTargetPlatform != TargetPlatform.macOS) {
      return PdfPageFormat.roll80.copyWith(
        marginLeft: 1,
        marginRight: 1,
        marginTop: 0,
      );
    } else if (getCurrentPaperSize() == 'a4') {
      return PdfPageFormat.a4;
    } else if (getCurrentPaperSize() == 'a5') {
      return PdfPageFormat.a5;
    } else {
      return PdfPageFormat.roll80.copyWith(
        marginRight: !share ? (marginRight ?? 5) * PdfPageFormat.mm : null,
        marginLeft: !share ? (marginRight ?? 5) * PdfPageFormat.mm : null,
      );
    }
  }

  int productTitleIndex() {
    if (sellHasDiscount() && sellHasTaxes()) {
      return 4;
    } else if (sellHasDiscount()) {
      return 3;
    } else if (sellHasTaxes()) {
      return 3;
    }
    return 2;
  }

  final dataList = [
    if (sell?.sellLines != null)
      ...sell!.sellLines!.map((e) {
        return [
          if (!warehouse)
            formatNumber((e.quantity ?? 0) *
                double.parse(e.unitPriceIncTax ?? e.unitPrice ?? "0")),
          if (sellHasDiscount() && !warehouse)
            getLineDiscountAmount(
              e,
              returnPercentage: !isDiscountOptionAmount(),
            ),
          if (sellHasTaxes() && !warehouse)
            formatNumber(taxRatesBox.get(e.taxId)?.amount ?? 0.0),
          if (!warehouse)
            formatNumber(double.parse(e.unitPriceIncTax ?? e.unitPrice ?? "0")),
          "${productsBox.get(e.productId)!.name}"
              "${e.sellLineNote != null ? "\n ${e.sellLineNote}" : ""}",
          if (showUnitInReceipt())
            unitsBox.get(e.subUnitId)?.actualName ??
                productsBox.get(e.productId)?.unit.actualName,
          formatNumber(getQuantity(e)),
        ];
      }),
    if (sReturnSellLines != null)
      ...sReturnSellLines.map((e) {
        return [
          formatNumber((double.tryParse(e.quantityReturned ?? "0") ?? 0) *
              double.parse(e.unitPriceIncTax ?? e.unitPrice ?? "0")),
          if (sellHasDiscount())
            getLineDiscountAmount(
              e,
              returnPercentage: !isDiscountOptionAmount(),
            ),
          if (sellHasTaxes())
            formatNumber(taxRatesBox.get(e.taxId)?.amount ?? 0),
          formatNumber(double.parse(e.unitPriceIncTax ?? e.unitPrice ?? "0")),
          productsBox.get(e.productId)!.name,
          if (showUnitInReceipt())
            unitsBox.get(e.subUnitId)?.actualName ??
                productsBox.get(e.productId)?.unit.actualName,
          formatNumber(getQuantity(e, isReturn: true)),
        ];
      }),
    if (purchase?.purchaseLines != null)
      ...purchase!.purchaseLines!.map((p) {
        return [
          formatNumber(p.quantity! * double.parse(p.purchasePrice!)),
          if (sellHasDiscount())
            double.tryParse(p.discountPercent ?? '0')?.toDouble() ?? 0.0,
          if (sellHasTaxes())
            (taxRatesBox.get(p.taxId)?.amount?.toInt() ?? 0).toString(),
          formatNumber(double.parse(p.purchasePrice!)),
          productsBox.get(p.productId)!.name,
          if (showUnitInReceipt())
            unitsBox.get(p.subUnitId)?.actualName ??
                productsBox.get(p.productId)?.unit.actualName,
          formatNumber(getQuantity(p)),
        ];
      }),
  ];

  final processedData =
      !getPrintSequence() ? dataList : dataList.reversed.toList();

  final dataWidgets = processedData.map((row) {
    if (!isArabic) {
      row = row.reversed.toList();
    }
    return row.asMap().entries.map((entry) {
      final cellText = entry.value;
      return pw.Text(
        applyArabicDigitConversion(cellText.toString()),
        style: style,
        textAlign: pw.TextAlign.center,
        // max lines for all row
        maxLines: entry.key == (row.length - 2) ? 4 : 1,
        textDirection: pw.TextDirection.rtl,
      );
    }).toList();
  }).toList();

  pdf.addPage(
    pw.Page(
      textDirection: dir,
      pageFormat: myPageFormat(marginRight: 10, marginLeft: 2),
      build: (cnx) {
        return pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.center,
          children: [
            pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.center,
              children: [
                if (getImagePath() != null)
                  pw.Image(
                    pw.MemoryImage(
                      File(getImagePath()!).readAsBytesSync(),
                    ),
                    width: 50,
                    height: 50,
                    fit: pw.BoxFit.fitWidth,
                  ),
                pw.SizedBox(width: 10),
                pw.Column(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  children: [
                    if (getBusinessName() != null)
                      styledText(getBusinessName()!),
                    pw.SizedBox(width: 10),
                    if (getBusinessMobileNumber() != null)
                      styledText(getBusinessMobileNumber()!),
                  ],
                ),
              ],
            ),
            pw.Divider(),
            pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.center,
              children: [
                pw.Container(
                  padding: const pw.EdgeInsets.symmetric(
                    vertical: 2,
                    horizontal: 10,
                  ),
                  decoration: pw.BoxDecoration(
                    color: const PdfColor(0.85, 0.85, 0.85),
                    borderRadius: pw.BorderRadius.circular(10),
                  ),
                  child: pw.Center(
                    child: pw.Text(
                      applyArabicDigitConversion(sReturn != null
                          ? strings.sell_return_receipt
                          : sell != null
                              ? sell.isQuotation == 1
                                  ? strings.offer_price
                                  : strings.bill_of_sale
                              : strings.receipt),
                      style: pw.TextStyle(
                        fontWeight: pw.FontWeight.bold,
                        font: font,
                        fontSize: (getCurrentFontSize() + 5).toDouble(),
                      ),
                    ),
                  ),
                ),
              ],
            ),
            pw.SizedBox(height: 5),
            pw.Row(
              children: [
                pw.Expanded(
                  child: styledText(
                    "${strings.date} "
                    "${formatDate(sell?.transactionDate ?? purchase?.transactionDate ?? sReturn?.transactionDate ?? DateTime.now())}",
                  ),
                ),
                pw.Expanded(
                  child: styledText(
                    "${sell != null ? strings.sell_id : strings.ref_no}: "
                    "${(sell?.invoiceNo ?? purchase?.refNo ?? sReturn?.returnParentSell!.invoiceNo ?? "")}",
                  ),
                ),
              ],
            ),
            pw.SizedBox(height: 5),
            pw.Row(
              children: [
                pw.Expanded(
                  child: pw.Center(
                    child: pw.RichText(
                      text: pw.TextSpan(
                        style: style,
                        children: [
                          pw.WidgetSpan(
                            child: pw.Container(
                              child: styledText("${strings.invoice_c_name}: "),
                            ),
                          ),
                          pw.WidgetSpan(
                            child: pw.Directionality(
                              textDirection: pw.TextDirection.rtl,
                              child: styledText("${contact?.name}"),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                pw.Expanded(
                  child: styledText(
                    "${strings.invoice_c_number}: ${contact?.mobile}",
                  ),
                ),
              ],
            ),
            pw.SizedBox(height: 5),
            pw.Row(
              children: [
                if (sell != null && sell.commissionAgent != null)
                  pw.Expanded(
                    child: smallText(
                      "${strings.commission_seller}: ${usersBox.get(sell.commissionAgent)?.firstName}",
                    ),
                  ),
                if (sell != null && sell.resWaiterId != null)
                  pw.Expanded(
                    child: smallText(
                      "${strings.service_staff}: ${usersBox.get(sell.resWaiterId)?.firstName}",
                    ),
                  ),
                if (sell != null && sell.resTableId != null)
                  pw.Expanded(
                    child: smallText(
                      "${strings.tables}: ${tablesBox.get(sell.resTableId)?.name}",
                    ),
                  ),
              ],
            ),
            if (contact?.addressLine1 != null) pw.SizedBox(height: 5),
            if (contact?.addressLine1 != null)
              pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.center,
                children: [
                  pw.Directionality(
                    textDirection: pw.TextDirection.rtl,
                    child: styledText("${strings.customer_address} "
                        "${contact?.addressLine1}"),
                  )
                ],
              ),
            pw.SizedBox(height: 5),
            pw.TableHelper.fromTextArray(
              context: cnx,
              cellAlignment: pw.Alignment.center,
              cellPadding: const pw.EdgeInsets.symmetric(horizontal: 1, vertical: 2),
              headerPadding: const pw.EdgeInsets.all(0),
              border: myTableBorder,
              columnWidths: {
                productTitleIndex(): const pw.FlexColumnWidth(2),
              },
              headers: (() {
                final headerList = [
                  if (!warehouse) strings.table_total,
                  if (sellHasDiscount() && !warehouse)
                    "${strings.table_discount}"
                        "${!isDiscountOptionAmount() ? "(%)" : ""}",
                  if (sellHasTaxes() && !warehouse) strings.table_tax,
                  if (!warehouse) strings.table_price,
                  strings.table_item,
                  if (showUnitInReceipt()) strings.unit,
                  strings.table_qty,
                ].map((e) => headerText(e)).toList();
                // Reverse headers if language is English
                if (!isArabic) {
                  return headerList.reversed.toList();
                }

                return headerList;
              })(),
              data: dataWidgets,
            ),
            if ((sumTaxes() > 0 || sumDiscounts() > 0) && !warehouse)
              pw.SizedBox(height: 5),
            if ((sumTaxes() > 0 || sumDiscounts() > 0) && !warehouse)
              pw.Wrap(
                alignment: pw.WrapAlignment.center,
                spacing: 8,
                children: [
                  pw.Row(
                    mainAxisSize: pw.MainAxisSize.min,
                    children: [
                      smallText("${strings.sum}: "),
                      smallText(sumWithoutTaxAndDiscount()),
                    ],
                  ),
                  pw.Row(
                    mainAxisSize: pw.MainAxisSize.min,
                    children: [
                      smallText("${strings.table_discount}: "),
                      smallText(
                        formatNumber(
                          sumDiscounts(
                              // returnPercentage: !isDiscountOptionAmount(),
                              ),
                        ),
                      ),
                    ],
                  ),
                  pw.Row(
                    mainAxisSize: pw.MainAxisSize.min,
                    children: [
                      smallText(
                        "${strings.table_tax}: "
                        "${productTaxesSameType().$1 ? productTaxesSameType().$2 : ""}",
                      ),
                      smallText(
                        formatNumber(sumTaxes()),
                      ),
                    ],
                  ),
                  pw.Row(
                    mainAxisSize: pw.MainAxisSize.min,
                    children: [
                      smallText(strings.total),
                      smallText(sumAfterTaxAndDiscount()),
                    ],
                  ),
                ],
              ),
            pw.SizedBox(height: 5),
            pw.Wrap(
              alignment: pw.WrapAlignment.center,
              spacing: 8,
              children: [
                pw.Row(
                  mainAxisSize: pw.MainAxisSize.min,
                  children: [
                    styledText(strings.invoice_number_of_items),
                    pw.SizedBox(width: 5),
                    pw.Container(
                      decoration: pw.BoxDecoration(
                        border: pw.Border.all(
                          color: PdfColors.black,
                          width: 1,
                        ),
                      ),
                      child: pw.Padding(
                        padding:
                            const pw.EdgeInsetsDirectional.fromSTEB(3, 0, 2, 0),
                        child: pw.Center(
                            child: styledText(
                          "${(sell?.sellLines ?? purchase?.purchaseLines ?? sReturnSellLines?.toList())?.length}",
                        )),
                      ),
                    ),
                    pw.SizedBox(),
                  ],
                ),
                if (totalWeight() != 0)
                  pw.Row(
                    mainAxisSize: pw.MainAxisSize.min,
                    children: [
                      pw.Text(strings.total_weight, style: style),
                      pw.SizedBox(width: 5),
                      pw.Container(
                        decoration: pw.BoxDecoration(
                          border: pw.Border.all(
                            color: PdfColors.black,
                            width: 1,
                          ),
                        ),
                        child: pw.Padding(
                          padding: const pw.EdgeInsetsDirectional.fromSTEB(
                              3, 0, 2, 0),
                          child: styledText(formatNumber(totalWeight())),
                        ),
                      ),
                      pw.SizedBox(),
                    ],
                  ),
                pw.Row(
                  mainAxisSize: pw.MainAxisSize.min,
                  children: [
                    styledText(strings.invoice_invoice_quantities),
                    pw.SizedBox(width: 5),
                    pw.Container(
                      decoration: pw.BoxDecoration(
                        border: pw.Border.all(
                          color: PdfColors.black,
                          width: 1,
                        ),
                      ),
                      child: pw.Padding(
                        padding:
                            const pw.EdgeInsetsDirectional.fromSTEB(3, 0, 2, 0),
                        child: styledText(invoiceQuantities()),
                      ),
                    ),
                    pw.SizedBox(),
                  ],
                ),
              ],
            ),
            if (!warehouse) pw.Divider(),
            if (!warehouse)
              pw.Center(
                child: pw.TableHelper.fromTextArray(
                  context: cnx,
                  cellAlignment: pw.Alignment.center,
                  cellPadding: const pw.EdgeInsets.all(0),
                  border: myTableBorder,
                  data: (() {
                    final dataArray = [
                      if (invoiceTaxes() != 0 ||
                          invoiceDiscount() != 0 ||
                          invoiceService() != 0 ||
                          invoiceShipping() != 0)
                        [
                          styledText(sumAfterTaxAndDiscount()),
                          styledText(strings.sum),
                        ],
                      if (invoiceDiscount() != 0)
                        [
                          styledText(
                            formatNumber(
                              invoiceDiscount(
                                returnPercentage: !isDiscountOptionAmount(),
                              ),
                            ),
                          ),
                          styledText("${strings.table_discount} "
                              "${!isDiscountOptionAmount() ? "(%)" : ""}"),
                        ],
                      if (invoiceTaxes() != 0)
                        [
                          styledText(calculateTaxAmount()),
                          pw.Directionality(
                            textDirection: pw.TextDirection.rtl,
                            child: styledText("${strings.table_tax} "
                                "(${isArabic ? "" : "%"}${taxRatesBox.get(sell?.taxId ?? purchase?.taxId)!.name!} "
                                " ${invoiceTaxes()}${!isArabic ? "" : "%"})"),
                          ),
                        ],
                      if (invoiceShipping() != 0)
                        [
                          styledText(formatNumber(invoiceShipping())),
                          styledText(
                              strings.shipping_fee.replaceFirst(":", "")),
                        ],
                      if (invoiceService() != 0)
                        [
                          styledText(formatNumber(invoiceService())),
                          pw.Directionality(
                            textDirection: pw.TextDirection.rtl,
                            child: styledText("${strings.service_price} "
                                "(${serviceModelBox.get(sell?.typesOfServiceId)?.name})"),
                          ),
                        ],
                      [
                        headerText(formatNumber(invoiceTotalAmount())),
                        headerText(strings.invoice_total_amount),
                      ],
                      if (previousBalance() != 0 &&
                          (sell?.isQuotation == 0 || sReturn != null))
                        [
                          styledText(formatNumber(previousBalance())),
                          styledText(strings.invoice_previous_balance),
                        ],
                      if (invoiceTotalPayment() != 0 &&
                          (sell?.isQuotation == 0 || sReturn != null))
                        [
                          headerText(formatNumber(invoiceTotalPayment())),
                          headerText(strings.invoice_total_payment),
                        ],
                      if (totalPaid() != 0 && sell?.isQuotation == 0)
                        [
                          styledText(formatNumber(totalPaid())),
                          styledText(strings.invoice_total_paid),
                        ],
                      if (totalPaid() != 0 &&
                          remainingDue() != 0 &&
                          sell?.isQuotation == 0)
                        [
                          styledText(formatNumber(remainingDue())),
                          styledText(strings.invoice_remaining_due),
                        ],
                    ];
                    // Reverse rows inside each array if language is English
                    if (!isArabic) {
                      return dataArray
                          .map((row) => row.reversed.toList())
                          .toList();
                    }

                    return dataArray;
                  })(),
                ),
              ),
            if (!warehouse) pw.Divider(),
            if (!warehouse)
              pw.Center(
                child: pw.BarcodeWidget(
                  height: 20,
                  width: 80,
                  color: PdfColor.fromHex("#000000"),
                  barcode: pw.Barcode.code128(),
                  data: sell?.invoiceNo ??
                      purchase?.refNo ??
                      sReturn?.returnParentSell!.invoiceNo ??
                      "",
                ),
              ),
            if (!warehouse) pw.SizedBox(height: 10),
            if (businessSettings.businessFooterText.isNotEmpty && !warehouse)
              styledText(businessSettings.businessFooterText),
            if (sell?.qrCode != null &&
                businessSettings.printQRCode &&
                !warehouse)
              pw.Center(
                child: pw.BarcodeWidget(
                  height: 60,
                  width: 60,
                  color: PdfColor.fromHex("#000000"),
                  barcode: pw.Barcode.qrCode(),
                  data: sell!.qrCode!,
                ),
              ),
            if (!connected ||
                (sell?.offline ?? false) ||
                (purchase?.offline ?? false))
              styledText("OFFLINE"),
          ],
        );
      },
    ),
  );

  if (!view) {
    !share
        ? () async {
            if (defaultTargetPlatform == TargetPlatform.windows || defaultTargetPlatform == TargetPlatform.macOS) {
              Printing.layoutPdf(
                onLayout: (PdfPageFormat format) => pdf.save(),
                format: myPageFormat(marginRight: 10, marginLeft: 2),
                usePrinterSettings: true,
                name: sanitizedFileName.replaceAll(RegExp(r'[/:*?"<>|]'), ''),
              );
            } else {
              final imageData = await ThermalPrinterManager.pdfToDecodedImage(
                await pdf.save(),
              );
              await ThermalPrinterManager.thermalPrint(imageData);
            }
          }()
        : Printing.sharePdf(
            bytes: await pdf.save(),
            filename: sanitizedFileName.replaceAll(RegExp(r'[/:*?"<>|]'), ''),
          );
  }

  return await pdf.save();
}
