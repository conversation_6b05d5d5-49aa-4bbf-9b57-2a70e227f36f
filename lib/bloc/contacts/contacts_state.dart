part of 'contacts_cubit.dart';

@immutable
abstract class ContactsState {}

class ContactsInitial extends ContactsState {}
class ContactTypeChanged extends ContactsState {}
class PayTermTypeChanged extends ContactsState {}
class ContactSentToAPISuccessfully extends ContactsState {}
class ContactSentToAPIFailed extends ContactsState {}
class BusinessFilterUpdated extends ContactsState {}
class NameFilterUpdated extends ContactsState {}
class NumberFilterUpdated extends ContactsState {}
class Contacts<PERSON>istUpdated extends ContactsState {}
class TypeFilterUpdated extends ContactsState {}
class SellingPriceGroupChanged extends ContactsState {}
class CustomerGroupChanged extends ContactsState {}
class CustomerContactChanged extends ContactsState {}
class FiltersReset extends ContactsState {}
class PositionChanged extends ContactsState {}
