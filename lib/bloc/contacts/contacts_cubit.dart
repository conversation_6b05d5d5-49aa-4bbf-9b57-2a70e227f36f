import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:we2up/data/models/contact_to_api.dart';
import 'package:we2up/data/db/db_manager.dart';
import 'package:we2up/data/models/customer_group.dart';
import 'package:we2up/data/models/location_info.dart';

import '../../data/models/contact.dart';
import '../../data/models/selling_price_group.dart';
import '../../data/repository/api_repo.dart';

part 'contacts_state.dart';

class ContactsCubit extends Cubit<ContactsState> {
  late final ApiRepository apiRepository;

  ContactsCubit() : super(ContactsInitial()) {
    apiRepository = ApiRepository.get();
  }

  static ContactsCubit get(context) => BlocProvider.of<ContactsCubit>(context);

  bool isCustomer = true;

  void changeType() {
    isCustomer = !isCustomer;
    emit(ContactTypeChanged());
  }

  LocationInfo? position;

  void updateSelectedLocation(LocationInfo? position) {
    if (position != null) {
      this.position = position;
      emit(PositionChanged());
    }
  }

  var supplierBusinessNameController = TextEditingController();

  var contactCode = TextEditingController();

  var firstNameController = TextEditingController();

  var lastNameController = TextEditingController();

  var mobileNumberController = TextEditingController();

  var landlineNumberController = TextEditingController();

  var taxNumberController = TextEditingController();

  var alternateNumberController = TextEditingController();

  var countryController = TextEditingController();

  var emailController = TextEditingController();

  var cityController = TextEditingController();

  var zipCodeController = TextEditingController();

  var shippingAddressController = TextEditingController();

  var openingBalanceController = TextEditingController();

  var payTermNumberController = TextEditingController();

  String? payTermType;

  void onPayTermTypeChanged(String? payTermType) {
    this.payTermType = payTermType;
    emit(PayTermTypeChanged());
  }

  void resetContactsCubitValues() {
    isCustomer = true;
    supplierBusinessNameController.text = "";
    contactCode.text = "";
    firstNameController.text = "";
    lastNameController.text = "";
    mobileNumberController.text = "";
    landlineNumberController.text = "";
    taxNumberController.text = "";
    alternateNumberController.text = "";
    countryController.text = "";
    emailController.text = "";
    cityController.text = "";
    zipCodeController.text = "";
    shippingAddressController.text = "";
    openingBalanceController.text = "";
    payTermNumberController.text = "";
    payTermType = null;
    position = null;
    selectedCustomerGroup = null;
    selectedPriceGroup = null;
  }

  void initContactsCubitValues(Contact contact) {
    isCustomer = contact.type == "customer";
    supplierBusinessNameController.text = contact.supplierBusinessName ?? "";
    contactCode.text = contact.contactId ?? "";
    firstNameController.text = contact.firstName ?? "";
    lastNameController.text = contact.lastName ?? "";
    mobileNumberController.text = contact.mobile ?? "";
    landlineNumberController.text = contact.landline ?? "";
    taxNumberController.text = contact.taxNumber ?? "";
    alternateNumberController.text = contact.alternateNumber ?? "";
    countryController.text = contact.country ?? "";
    emailController.text = contact.email ?? "";
    cityController.text = contact.city ?? "";
    zipCodeController.text = contact.zipCode ?? "";
    shippingAddressController.text = contact.shippingAddress ?? "";
    openingBalanceController.text = contact.openingBalance ?? "";
    payTermNumberController.text = contact.payTermNumber.toString();
    payTermType = contact.payTermType;
    position = contact.locationInfo;
    selectedPriceGroup = sellingPriceGroupsBox.get(contact.priceGroupId);
    selectedCustomerGroup = customerGroupBox.get(contact.customerGroupId);
  }

  Future<int> sendContactToAPI(int? contactID) async {
    EasyLoading.showSuccess('Sending Contact');

    final contactToAPI = ContactToAPI(
      locationPosition: position,
      type: isCustomer ? 'customer' : 'supplier',
      mobile: mobileNumberController.text,
      firstName: firstNameController.text,
      lastName: lastNameController.text,
      alternateNumber: alternateNumberController.text,
      country: countryController.text,
      city: cityController.text,
      email: emailController.text,
      landline: landlineNumberController.text,
      shippingAddress: shippingAddressController.text,
      taxNumber: taxNumberController.text,
      zipCode: zipCodeController.text,
      supplierBusinessName: supplierBusinessNameController.text,
      openingBalance: double.tryParse(openingBalanceController.text) ?? 0,
      customerGroupId: selectedCustomerGroup?.id.toString(),
      priceGroupId: selectedPriceGroup?.id,
      contactId: contactCode.text.isNotEmpty
          ? contactCode.text
          : contactsBox.get(contactID)?.contactId,
    );

    final value = await apiRepository.sendContactToAPI(
      contactToAPI: contactToAPI,
      contactID: contactID,
    );

    if (value != -1) {
      EasyLoading.showSuccess('Contact sent Successfully');
      emit(ContactSentToAPISuccessfully());
      resetContactsCubitValues();
      return value;
    } else {
      EasyLoading.showError('Contact sending Failed');
      emit(ContactSentToAPIFailed());
      return value;
    }
  }

  TextEditingController businessFilterController = TextEditingController();

  TextEditingController nameFilterController = TextEditingController();

  TextEditingController numberFilterController = TextEditingController();

  void businessListener() => emit(BusinessFilterUpdated());

  void nameListener() => emit(NameFilterUpdated());

  void numberListener() => emit(NumberFilterUpdated());

  void updateList() => emit(ContactsListUpdated());

  String? typeFilter;

  void changeContactTypeFilter(String? typeFilter) {
    this.typeFilter = typeFilter;
    emit(TypeFilterUpdated());
  }

  SellingPriceGroup? selectedPriceGroup;

  void changeCartPriceGroup(SellingPriceGroup? priceGroup) {
    selectedPriceGroup = priceGroup;
    emit(SellingPriceGroupChanged());
  }

  CustomerGroup? selectedCustomerGroup;

  void changeCustomerGroup(CustomerGroup? group) {
    selectedCustomerGroup = group;
    emit(CustomerGroupChanged());
  }

  List<Contact> filteredContacts({String? filter}) {
    // Get all contacts from the box
    Iterable<Contact> contacts = contactsBox.values.toList().reversed;

    if (!canViewSupplier()) {
      contacts =
          contacts.where((contact) => contact.type != "supplier").toList();
    }

    if (!canViewCustomer()) {
      contacts =
          contacts.where((contact) => contact.type != "customer").toList();
    }

    if (filter != null) {
      contacts = contacts.where((contact) {
        return contact.type.toLowerCase() == filter.toLowerCase() &&
            double.parse(contact.due ?? "0") != 0;
      }).toList();
    }

    // Apply filters
    if (typeFilter != null) {
      // Filter by Type
      contacts = contacts.where((contact) {
        return contact.type.toLowerCase() == typeFilter!.toLowerCase();
      }).toList();
    }

    if (businessFilterController.text.isNotEmpty) {
      final regex = RegExp(businessFilterController.text, caseSensitive: false);
      contacts = contacts.where((contact) {
        return regex.hasMatch(contact.supplierBusinessName ?? "");
      }).toList();
    }

    if (nameFilterController.text.isNotEmpty) {
      final regex = RegExp(nameFilterController.text, caseSensitive: false);
      contacts = contacts.where((contact) {
        return regex.hasMatch(contact.name ?? "");
      }).toList();
    }

    if (numberFilterController.text.isNotEmpty) {
      final regex = RegExp(numberFilterController.text, caseSensitive: false);
      contacts = contacts.where((contact) {
        return regex.hasMatch(contact.mobile ?? "");
      }).toList();
    }

    return contacts.toList();
  }

  String totalDues({String? filter}) {
    double dues = 0;
    for (Contact contact in filteredContacts(filter: filter)) {
      dues += double.parse(contact.due ?? "0");
    }
    return dues.toStringAsFixed(4);
  }

  Contact? selectedContact;

  TextEditingController contactCnt = TextEditingController();

  void changeCustomerContact(Contact? contact) {
    selectedContact = contact;
    emit(CustomerContactChanged());
  }

  void resetContactsFilters() {
    businessFilterController.text = '';
    nameFilterController.text = '';
    numberFilterController.text = '';
    typeFilter = null;
    position = null;
    emit(FiltersReset());
  }

  @override
  Future<void> close() {
    supplierBusinessNameController.dispose();
    firstNameController.dispose();
    lastNameController.dispose();
    mobileNumberController.dispose();
    landlineNumberController.dispose();
    taxNumberController.dispose();
    alternateNumberController.dispose();
    countryController.dispose();
    emailController.dispose();
    cityController.dispose();
    zipCodeController.dispose();
    shippingAddressController.dispose();
    openingBalanceController.dispose();
    payTermNumberController.dispose();
    businessFilterController.dispose();
    nameFilterController.dispose();
    numberFilterController.dispose();
    contactCnt.dispose();
    return super.close();
  }
}
