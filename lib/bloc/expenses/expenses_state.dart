part of 'expenses_cubit.dart';

@immutable
abstract class ExpensesState {}

class ExpensesInitial extends ExpensesState {}
class LocationChanged extends ExpensesState {}
class IsRefundChanged extends ExpensesState {}
class IsRecurringChanged extends ExpensesState {}
class ExpenseCategoryChanged extends ExpensesState {}
class CustomerContactChanged extends ExpensesState {}
class TaxRateChanged extends ExpensesState {}
class FilterUserUpdated extends ExpensesState {}
class RecurIntervalTypeChanged extends ExpensesState {}
class PaymentMethodChanged extends ExpensesState {}
class PaymentMethodDetailsChanged extends ExpensesState {}
class ExpenseAmountChanged extends ExpensesState {}
class ExpenseLoading extends ExpensesState {}
class ExpenseSentToAPIFailed extends ExpensesState {}
class EditExpenseDataLoaded extends ExpensesState {}
class ExpenseDataReset extends ExpensesState {}
class FieldsResetToOriginal extends ExpensesState {}
class ExpenseFilterResat extends ExpensesState {}
class FilterUpdated extends ExpensesState {}
class ExpenseFilterUpdated extends ExpensesState {}
class NewExpensesLoading extends ExpensesState {}
class PaginatedExpensesLoading extends ExpensesState {}
class TotalSummaryUpdated extends ExpensesState {}
class ExpensesLoaded extends ExpensesState {
  final Iterable<Expense> expenses;

  ExpensesLoaded({required this.expenses});
}
