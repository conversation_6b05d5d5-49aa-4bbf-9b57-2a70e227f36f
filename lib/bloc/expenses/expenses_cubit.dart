import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:we2up/data/models/expense_category.dart';
import 'package:we2up/data/models/expense_to_api.dart';
import 'package:we2up/data/models/payment.dart';

import '../../data/models/business_location.dart';
import '../../data/models/business_settings.dart';
import '../../data/models/contact.dart';
import '../../data/models/expense.dart';
import '../../data/models/payment_account.dart';
import '../../data/models/product.dart';
import '../../data/models/shift_details.dart';
import '../../data/models/user_model.dart';
import '../../data/repository/api_repo.dart';
import '../../data/db/db_manager.dart';
import '../../presentation/widgets/date_range_dropdown.dart';
import '../../utils/we2up_constants.dart';

part 'expenses_state.dart';

enum RecurIntervalType { days, months, years }

class ExpensesCubit extends Cubit<ExpensesState> {
  late final ApiRepository apiRepository;
  late final String? offlineID;
  Expense? editExpense;
  PaymentAccount? paymentAccount;

  BusinessLocation selectedLocation = businessLocationsBox.values.first;

  ExpensesCubit({this.editExpense}) : super(ExpensesInitial()) {
    paymentAccount = defaultPaymentAccount(selectedLocation);
    if (editExpense != null) setInitialDataForEdit(editExpense);
    offlineID = editExpense?.offline == true
        ? editExpense?.refNo
        : editExpense?.id.toString();
    apiRepository = ApiRepository.get();
  }

  static ExpensesCubit get(c) => BlocProvider.of<ExpensesCubit>(c);

  ShiftDetails? currentSummary;

  Future<void> getRangeSummary() async {
    if (await InternetConnectionChecker.instance.hasConnection) {
      final summary = await apiRepository.fetchRangeSummaryDetails(
        startDate: _eStartDate,
        endDate: _eEndDate,
      );
      if (summary != null) currentSummary = summary;
      emit(TotalSummaryUpdated());
    } else {
      currentSummary = null;
      emit(TotalSummaryUpdated());
    }
  }

  void changeSelectedLocation({required BusinessLocation location}) {
    selectedLocation = location;
    emit(LocationChanged());
  }

  bool isRefund = false;

  void changeRefund() {
    isRefund = !isRefund;
    emit(IsRefundChanged());
  }

  bool isRecurring = false;

  void changeRecurring() {
    isRecurring = !isRecurring;
    emit(IsRecurringChanged());
  }

  ExpenseCategory? expenseCategory;

  void changeSelectedExpenseCategory(ExpenseCategory? expenseCategory) {
    this.expenseCategory = expenseCategory;
    emit(ExpenseCategoryChanged());
  }

  Contact? customerContact;

  void customerContactChanged(Contact? contact) {
    customerContact = contact;
    emit(CustomerContactChanged());
  }

  TaxRate? taxRate;

  void changeTaxRate(TaxRate? taxRate) {
    this.taxRate = taxRate;
    emit(TaxRateChanged());
  }

  TextEditingController expenseForUserController = TextEditingController();

  UserModel? user;

  void updateExpenseForUser(UserModel? user) {
    this.user = user;
    emit(FilterUserUpdated());
  }

  RecurIntervalType intervalType = RecurIntervalType.days;

  void changeIntervalType(RecurIntervalType? type) {
    intervalType = type!;
    emit(RecurIntervalTypeChanged());
  }

  TextEditingController expenseAmountController = TextEditingController();

  TextEditingController expenseNoteController = TextEditingController();

  TextEditingController recurIntervalController = TextEditingController();

  void amountChanged() => emit(ExpenseAmountChanged());

  String paymentMethod = "cash";

  void paymentMethodChanged(String paymentMethod) {
    this.paymentMethod = paymentMethod;
    emit(PaymentMethodChanged());
  }

  void changePaymentAccount(PaymentAccount? paymentAccount) {
    this.paymentAccount = paymentAccount;
    emit(PaymentMethodDetailsChanged());
  }

  String totalExpenseAmount() {
    double expenseAmount = double.tryParse(expenseAmountController.text) ?? 0;
    double taxRatePercentage = (taxRate?.amount ?? 0) / 100;
    double amount = expenseAmount * (1 + taxRatePercentage);
    return double.parse(amount.toStringAsFixed(2)).toString();
  }

  void setInitialDataForEdit(Expense? editExpense) {
    this.editExpense = editExpense;
    selectedLocation = businessLocationsBox.get(editExpense?.locationId) ??
        businessLocationsBox.values.first;
    taxRate = taxRatesBox.get(editExpense?.taxId);
    expenseAmountController.text = editExpense?.totalBeforeTax ?? "";
    expenseNoteController.text = editExpense?.additionalNotes ?? "";
    recurIntervalController.text = editExpense?.recurInterval.toString() ?? "";
    intervalType = RecurIntervalType.values.firstWhere(
      (element) => element.name == editExpense?.recurIntervalType,
      orElse: () => RecurIntervalType.days,
    );
    customerContact = contactsBox.get(editExpense?.contactId);
    expenseCategory = expenseCategoriesBox.get(editExpense?.expenseCategoryId);
    if (editExpense != null &&
        editExpense.payments != null &&
        editExpense.payments!.isNotEmpty) {
      paymentMethod = editExpense.payments![0].method!;
    } else {
      paymentMethod = "cash";
    }

    isRefund = editExpense?.isRefund == 1;

    paymentAccount =
        paymentAccountsBox.get(editExpense?.payments?.first.accountId) ??
            defaultPaymentAccount(selectedLocation);

    emit(EditExpenseDataLoaded());
  }

  void resetExpenseData() {
    selectedLocation = businessLocationsBox.values.first;
    taxRate = null;
    expenseAmountController.text = "";
    expenseNoteController.text = "";
    recurIntervalController.text = "";
    customerContact = null;
    expenseCategory = null;
    paymentAccount = defaultPaymentAccount(selectedLocation);
    paymentMethod = "cash";
    isRefund = false;
    emit(FieldsResetToOriginal());
  }

  Future<bool> sendExpenseToAPI({int? expenseID, String? offlineID}) async {
    emit(ExpenseLoading());
    final locationInfo = await apiRepository.getCurrentLocation();

    final expenseToAPI = ExpenseToAPI(
      locationInfo: locationInfo,
      transactionDate: dateToString(DateTime.now()),
      isRefund: isRefund ? 1 : 0,
      taxRateId: taxRate?.id,
      locationId: selectedLocation.id,
      contactId: customerContact?.id,
      expenseFor: user?.id,
      finalTotal: double.parse(totalExpenseAmount()),
      additionalNotes: expenseNoteController.text,
      offlineID: offlineID,
      expenseCategoryId: expenseCategory?.id,
      isRecurring: isRecurring ? 1 : 0,
      recurInterval: recurIntervalController.text.isEmpty
          ? null
          : int.parse(recurIntervalController.text),
      recurIntervalType: isRecurring ? intervalType.name : null,
      payment: [
        Payment(
          amount: double.tryParse(expenseAmountController.text) ?? 0,
          method: paymentMethod,
          accountId: paymentAccount?.id,
          note: "",
        ),
      ],
    );

    await apiRepository.sendExpenseToAPI(
        expenseToAPI: expenseToAPI, expenseID: expenseID);

    EasyLoading.showSuccess('Expense sent Successfully');
    getFilteredExpenses(isNewSearch: true, refresh: true);
    return true;
  }

  DateRange range = DateRange.today;

  void updateDateRange(DateRange range) {
    this.range = range;
    emit(ExpenseFilterUpdated());
  }

  DateTime? _eStartDate = DateTime.now();

  void changeEStartDate(DateTime? date) {
    _eStartDate = date;
    const sevenDays = Duration(days: 7);
    if (_eStartDate != null && _eEndDate != null) {
      if (!(sameDayOrAfter(_eStartDate!, _eEndDate!) &&
          sameDayOrBefore(_eStartDate!.add(sevenDays), _eEndDate!))) {
        // the end date is not in 7 days range of the start date.
        _eEndDate = _eStartDate!.add(sevenDays);
      }
    } else if (_eStartDate != null && _eEndDate == null) {
      _eEndDate = _eStartDate!.add(sevenDays);
    }

    emit(ExpenseFilterUpdated());
  }

  DateTime? _eEndDate = DateTime.now();

  void changeEEndDate(DateTime? date) {
    _eEndDate = date;
    emit(ExpenseFilterUpdated());
  }

  Contact? _eFilterContact;

  void changeEFilterContact(Contact? contact) {
    _eFilterContact = contact;
    emit(ExpenseFilterUpdated());
  }

  ExpenseCategory? expenseFilterCategory;

  void changeEFilterCategory(ExpenseCategory? category) {
    expenseFilterCategory = category;
    emit(ExpenseFilterUpdated());
  }

  UserModel? _eFilterUser;

  TextEditingController filterUserController = TextEditingController();

  void changeEFilterUser(UserModel? user) {
    _eFilterUser = user;
    emit(ExpenseFilterUpdated());
  }

  DateTime? get expenseStartDate => _eStartDate;

  DateTime? get expenseEndDate => _eEndDate;

  Contact? get expenseFilterContact => _eFilterContact;

  UserModel? get expenseFilterUser => _eFilterUser;

  TextEditingController invoiceRefFilterController = TextEditingController();

  void invoiceRefFilterChanged() => emit(FilterUpdated());

  int currentPage = 1;
  int itemsPerPage = 20;

  Future<void> getFilteredExpenses(
      {bool isNewSearch = false, bool refresh = false}) async {
    if (isNewSearch) {
      currentPage = 1;
      emit(NewExpensesLoading());
    } else {
      emit(PaginatedExpensesLoading());
    }

    final connected = await InternetConnectionChecker.instance.hasConnection;

    if (isNewSearch) await getRangeSummary();

    // Fetch data from API if not loaded
    if (connected && !refresh && !stopSyncingTransactions()) {
      await fetchExpensesAndStoreInHive(
        startDate: _eStartDate,
        endDate: _eEndDate,
        page: currentPage,
      );
    }

    Iterable<Expense> expenses = expensesBox.values.toList().reversed;

    // Apply filters based on your existing logic
    if (_eStartDate != null && _eEndDate != null) {
      expenses = expenses.where((expense) {
        final expenseDate = expense.transactionDate;
        return sameDayOrAfter(expenseDate, _eStartDate!,
                shift: range == DateRange.shift) &&
            sameDayOrBefore(expenseDate, _eEndDate!,
                shift: range == DateRange.shift);
      }).toList();
    } else if (_eStartDate != null) {
      expenses = expenses.where((expense) {
        return sameDayOrAfter(expense.transactionDate, _eStartDate!,
            shift: range == DateRange.shift);
      }).toList();
    } else if (_eEndDate != null) {
      expenses = expenses.where((expense) {
        return sameDayOrBefore(expense.transactionDate, _eEndDate!,
            shift: range == DateRange.shift);
      }).toList();
    }

    if (_eFilterContact != null) {
      // Filter by contact
      expenses = expenses.where((e) {
        return e.contactId == _eFilterContact!.id;
      }).toList();
    }

    if (expenseFilterCategory != null) {
      // Filter by Category
      expenses = expenses.where((e) {
        return e.category?.name == expenseFilterCategory?.name;
      }).toList();
    }

    if (_eFilterUser != null) {
      // Filter by User
      expenses = expenses.where((e) {
        return e.expenseFor?['id'] == _eFilterUser!.id;
      }).toList();
    }

    if (invoiceRefFilterController.text.isNotEmpty) {
      final regex =
          RegExp(invoiceRefFilterController.text, caseSensitive: false);
      expenses = expenses.where((expense) {
        return regex.hasMatch(expense.refNo ?? "");
      }).toList();
    }
    // Pagination
    int endIndex = currentPage * itemsPerPage;
    endIndex = endIndex > expenses.length ? expenses.length : endIndex;

    List<Expense> paginatedExpenses = expenses.toList().sublist(0, endIndex);

    currentPage++; // Increment currentPage for the next paginated call
    emit(ExpensesLoaded(expenses: paginatedExpenses));
  }

  void resetExpensesFilters() {
    _eStartDate = DateTime.now();
    _eEndDate = DateTime.now();
    range = DateRange.today;
    _eFilterContact = null;
    expenseFilterCategory = null;
    _eFilterUser = null;
    expenseCategory = null;
    paymentAccount = defaultPaymentAccount(selectedLocation);
    user = null;
    invoiceRefFilterController.text = "";
    expenseForUserController.text = "";
    emit(ExpenseFilterResat());
  }

  @override
  Future<void> close() {
    expenseForUserController.dispose();
    expenseAmountController.dispose();
    expenseNoteController.dispose();
    recurIntervalController.dispose();
    filterUserController.dispose();
    invoiceRefFilterController.dispose();
    return super.close();
  }
}
