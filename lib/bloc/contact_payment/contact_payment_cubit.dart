import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:intl/intl.dart';
import 'package:we2up/data/models/payment.dart';
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, TargetPlatform;

import '../../data/models/contact.dart';
import '../../data/models/payment_account.dart';
import '../../data/repository/api_repo.dart';
import '../../data/db/db_manager.dart';
import '../../presentation/widgets/printer_alert_dialog.dart';
import 'package:we2up/bloc/contact_payment/payment_pdf.dart';

part 'contact_payment_state.dart';

class ContactPaymentCubit extends Cubit<ContactPaymentState> {
  late final ApiRepository apiRepository;

  ContactPaymentCubit() : super(ContactPaymentInitial()) {
    apiRepository = ApiRepository.get();
  }

  static ContactPaymentCubit get(c) => BlocProvider.of<ContactPaymentCubit>(c);

  TextEditingController paymentNoteController = TextEditingController();

  TextEditingController paymentAmountController = TextEditingController();

  Contact? customerContact;

  final FocusNode contactsFocusNode = FocusNode();

  void changeCustomerContact(Contact? contact) {
    customerContact = contact;
    emit(CustomerContactChanged());
  }

  String paymentMethod = "cash";

  void paymentMethodChanged(String paymentMethod) {
    this.paymentMethod = paymentMethod;
    emit(PaymentMethodChanged());
  }

  void resetValues() {
    customerContact = null;
    paymentAccount = defaultPaymentAccount(businessLocationsBox.values.first);
    paymentMethod = 'cash';
    paymentNoteController.text = '';
    paymentAmountController.text = '';
    printReceipt = true;
    emit(ValuesResetToOriginal());
  }

  void updateValues() => emit(ValuesUpdated());

  PaymentAccount? paymentAccount = defaultPaymentAccount(
    businessLocationsBox.values.first,
  );

  void changePaymentAccount(PaymentAccount? paymentAccount) {
    this.paymentAccount = paymentAccount;
    emit(PaymentMethodDetailsChanged());
  }

  bool printReceipt = true;

  void changePrintCart() {
    printReceipt = !printReceipt;
    emit(PrintBillChanged());
  }

  Future<bool> sendContactPaymentToAPI(context) async {
    EasyLoading.show(status: 'Payment Loading');
    final locationInfo = await apiRepository.getCurrentLocation();
    final online = await InternetConnectionChecker.instance.hasConnection;
    final now = DateTime.now();
    final double oldDue = double.parse(customerContact?.due ?? "0");
    final payment = Payment(
      amount: double.tryParse(paymentAmountController.text) ?? 0,
      method: paymentMethod,
      accountId: paymentAccount?.id,
      note: paymentNoteController.text,
      paymentFor: customerContact?.id,
      transactionDate: now,
      offline: !online,
      refNo: "${loginData.userId}${DateFormat('yMdHms').format(now)}",
    );

    await apiRepository.sendContactPaymentToAPI(
      paymentToAPI: payment,
      contactID: customerContact!.id,
      locationInfo: locationInfo,
    );

    EasyLoading.dismiss();
    emit(PaymentSentToAPISuccessfully());

    if (printReceipt && customerContact?.type == "customer") {
      if (defaultTargetPlatform == TargetPlatform.windows || defaultTargetPlatform == TargetPlatform.macOS) {
        // For Windows and macOS, directly generate PDF without printer dialog
        await generatePaymentPdf(
          context,
          payment,
          oldDue,
        );
      } else {
        // For other platforms, show printer dialog
        await showDialog(
          context: context,
          builder: (_) => PrinterAlertDialog(payment: payment, oldDue: oldDue),
        );
      }
    }

    EasyLoading.showSuccess('Payment sent Successfully');
    resetValues();
    return true;
  }
}
