part of 'contact_payment_cubit.dart';

@immutable
abstract class ContactPaymentState {}

class ContactPaymentInitial extends ContactPaymentState {}
class CustomerContactChanged extends ContactPaymentState {}
class PaymentMethodChanged extends ContactPaymentState {}
class ValuesResetToOriginal extends ContactPaymentState {}
class PaymentMethodDetailsChanged extends ContactPaymentState {}
class PrintBillChanged extends ContactPaymentState {}
class ValuesUpdated extends ContactPaymentState {}
class PaymentSentToAPISuccessfully extends ContactPaymentState {}
class PaymentSentToAPIFailed extends ContactPaymentState {}
