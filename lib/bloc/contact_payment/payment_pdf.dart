import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:printing/printing.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:we2up/utils/we2up_constants.dart';

import '../../data/db/db_manager.dart';
import '../../data/models/business_settings.dart';
import '../../data/models/payment.dart';
import '../../utils/thermal_printer_manager.dart';

pw.TextDirection _direction(context) {
  return AppLocalizations.of(context)!.localeName == "ar"
      ? pw.TextDirection.rtl
      : pw.TextDirection.ltr;
}

Future<void> generatePaymentPdf(
  BuildContext context,
  Payment payment,
  double oldDue,
) async {
  final strings = AppLocalizations.of(context)!;
  final dir = _direction(context);
  final font = await fontFromAssetBundle(getCurrentFontPath());
  final pdf = pw.Document(pageMode: PdfPageMode.fullscreen);

  final now = DateTime.now();
  final customer = contactsBox.get(payment.paymentFor)!;
  final style = pw.TextStyle(
    font: font,
    fontSize: getCurrentFontSize().toDouble(),
    fontWeight: pw.FontWeight.bold,
  );
  pw.Text styledText(String text) =>
      pw.Text(applyArabicDigitConversion(text), style: style);

  PdfPageFormat myPageFormat({int? marginRight, int? marginLeft}) {
    if ((getCurrentPaperSize() == 'roll57' ||
            getCurrentPaperSize() == 'roll80') &&
        defaultTargetPlatform != TargetPlatform.windows && defaultTargetPlatform != TargetPlatform.macOS) {
      return PdfPageFormat.roll80.copyWith(
        marginLeft: 1,
        marginRight: 1,
        marginTop: 0,
      );
    } else if (getCurrentPaperSize() == 'a4') {
      return PdfPageFormat.a4;
    } else if (getCurrentPaperSize() == 'a5') {
      return PdfPageFormat.a5;
    } else {
      return PdfPageFormat.roll80.copyWith(
        marginRight: (marginRight ?? 5) * PdfPageFormat.mm,
        marginLeft: (marginRight ?? 5) * PdfPageFormat.mm,
      );
    }
  }

  pdf.addPage(
    pw.Page(
      textDirection: dir,
      pageFormat: myPageFormat(),
      build: (cnx) {
        return pw.Padding(
          // change to 5 or 7.5 if not okay
          padding: const pw.EdgeInsets.only(right: 0),
          child: pw.Column(
            children: [
              pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.center,
                children: [
                  if (getImagePath() != null)
                    pw.Image(
                      pw.MemoryImage(
                        File(getImagePath()!).readAsBytesSync(),
                      ),
                      width: 50,
                      height: 50,
                      fit: pw.BoxFit.fitWidth,
                    ),
                  pw.SizedBox(width: 10),
                  pw.Column(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      if (getBusinessName() != null)
                        styledText(getBusinessName()!),
                      pw.SizedBox(width: 10),
                      if (getBusinessMobileNumber() != null)
                        styledText(getBusinessMobileNumber()!),
                    ],
                  ),
                ],
              ),
              pw.Divider(),
              pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.center,
                children: [
                  pw.Center(
                    child: pw.Text(
                      strings.receipt_of_cash,
                      style: pw.TextStyle(
                        fontWeight: pw.FontWeight.bold,
                        font: font,
                        fontSize:
                            (businessSettings.currentFontSize + 5).toDouble(),
                      ),
                    ),
                  ),
                  styledText(formatDate(now)),
                  pw.SizedBox(height: 8),
                  pw.RichText(
                    text: pw.TextSpan(
                      style: style,
                      children: [
                        pw.WidgetSpan(
                          child: pw.Container(
                            child: styledText("${strings.customer_name} "),
                          ),
                        ),
                        pw.WidgetSpan(
                          child: pw.Directionality(
                            textDirection: pw.TextDirection.rtl,
                            child: styledText(customer.name ?? "N/A"),
                          ),
                        ),
                      ],
                    ),
                  ),
                  pw.SizedBox(height: 8),
                  pw.RichText(
                    text: pw.TextSpan(
                      style: style,
                      children: [
                        pw.WidgetSpan(
                          child: pw.Container(
                            child: styledText("${strings.collector_name}: "),
                          ),
                        ),
                        pw.WidgetSpan(
                          child: pw.Directionality(
                            textDirection: pw.TextDirection.rtl,
                            child: styledText(loginData.username),
                          ),
                        ),
                      ],
                    ),
                  ),
                  pw.Divider(),
                  pw.Row(
                    children: [
                      pw.Expanded(child: styledText("${strings.due_amount}:")),
                      pw.Expanded(
                        child: styledText(
                          "  ${formatNumber(roundIfAbove075(oldDue))}",
                        ),
                      ),
                    ],
                  ),
                  pw.Container(
                    color: const PdfColor(0.85, 0.85, 0.85),
                    child: pw.Row(
                      children: [
                        pw.Expanded(
                            child: styledText("${strings.collected_amount} ")),
                        pw.Expanded(
                          child: styledText(
                            "  ${formatNumber(
                              roundIfAbove075(payment.amount!),
                            )}",
                          ),
                        ),
                      ],
                    ),
                  ),
                  pw.Row(
                    children: [
                      pw.Expanded(child: styledText("${strings.remaining}:  ")),
                      pw.Expanded(
                        child: styledText(
                          formatNumber(
                            roundIfAbove075(oldDue - payment.amount!),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        );
      },
    ),
  );

  if (defaultTargetPlatform == TargetPlatform.windows || defaultTargetPlatform == TargetPlatform.macOS) {
    Printing.layoutPdf(
      onLayout: (PdfPageFormat format) => pdf.save(),
      format: myPageFormat(marginRight: 10, marginLeft: 2),
      usePrinterSettings: true,
    );
  } else {
    final imageData = await ThermalPrinterManager.pdfToDecodedImage(
      await pdf.save(),
    );
    await ThermalPrinterManager.thermalPrint(imageData);
  }
}
