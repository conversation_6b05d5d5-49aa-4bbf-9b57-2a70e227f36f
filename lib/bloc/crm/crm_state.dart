part of 'crm_cubit.dart';

@immutable
abstract class CrmState {}

class CrmInitial extends CrmState {}
class AllowedNotificationsChanged extends CrmState {}
class NotifyViaEmailChanged extends CrmState {}
class NotifyViaSMSChanged extends CrmState {}
class FollowUpTypeChanged extends CrmState {}
class NotifyTypeChanged extends CrmState {}
class StatusChanged extends CrmState {}
class CustomerContactChanged extends CrmState {}
class StartDateChanged extends CrmState {}
class EndDateChanged extends CrmState {}
class FollowUpSentToAPIFailed extends CrmState {}
class ChangedStatusFilter extends CrmState {}
class FilterUserUpdated extends CrmState {}
class FollowUpFilterTitleChanged extends CrmState {}
class ChangedContactFilter extends CrmState {}
class ChangedDateFilter extends CrmState {}
class FiltersReset extends CrmState {}
class SelectedUserChanged extends CrmState {}
class NewFollowUpsLoading extends CrmState {}
class EditFollowUpsLoading extends CrmState {}
class PaginatedFollowUpsLoading extends CrmState {}
class FollowU<PERSON>Loaded extends CrmState {
  final List<FollowUp> followups;

  FollowUpsLoaded({required this.followups});
}
