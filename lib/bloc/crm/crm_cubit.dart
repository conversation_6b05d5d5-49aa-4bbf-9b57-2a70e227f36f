import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:we2up/data/models/follow_up.dart';
import 'package:we2up/data/models/follow_up_to_api.dart';
import 'package:we2up/data/db/db_manager.dart';
import '../../data/models/business_settings.dart';
import '../../data/models/contact.dart';
import '../../data/models/user_model.dart';
import '../../data/repository/api_repo.dart';
import '../../presentation/widgets/date_range_dropdown.dart';
import '../../utils/we2up_constants.dart';

part 'crm_state.dart';

class CrmCubit extends Cubit<CrmState> {
  late final ApiRepository apiRepository;

  CrmCubit() : super(CrmInitial()) {
    getFilteredFollowUps(isNewSearch: true, refresh: true);
    scrollController.addListener(() {
      if (scrollController.position.pixels ==
          scrollController.position.maxScrollExtent) {
        getFilteredFollowUps();
      }
    });
    apiRepository = ApiRepository.get();
  }

  static CrmCubit get(context) => BlocProvider.of<CrmCubit>(context);

  TextEditingController titleController = TextEditingController();

  TextEditingController descriptionController = TextEditingController();

  TextEditingController notifyBeforeController = TextEditingController();

  bool allowNotification = false;

  void changeAllowNotification() {
    allowNotification = !allowNotification;
    emit(AllowedNotificationsChanged());
  }

  bool notifyViaEmail = false;

  void changeNotifyViaEmail() {
    notifyViaEmail = !notifyViaEmail;
    emit(NotifyViaEmailChanged());
  }

  bool notifyViaSMS = false;

  void changeNotifyViaSMS() {
    notifyViaSMS = !notifyViaSMS;
    emit(NotifyViaSMSChanged());
  }

  FollowUpType followUpType = FollowUpType.call;

  void changeFollowUpType(FollowUpType followUpType) {
    this.followUpType = followUpType;
    emit(FollowUpTypeChanged());
  }

  NotifyType notifyType = NotifyType.minute;

  void changeNotifyType(NotifyType notifyType) {
    this.notifyType = notifyType;
    emit(NotifyTypeChanged());
  }

  Status status = Status.open;

  void changeStatus(Status status) {
    this.status = status;
    emit(StatusChanged());
  }

  Contact? agentContact;

  void customerContactChanged(Contact? contact) {
    agentContact = contact;
    emit(CustomerContactChanged());
  }

  DateTime? startDate;

  void changeStartDate(DateTime? startDate) {
    this.startDate = startDate;
    emit(StartDateChanged());
  }

  DateTime? endDate;

  void changeEndDate(DateTime? endDate) {
    this.endDate = endDate;
    emit(EndDateChanged());
  }

  TextEditingController searchSelectedUserController = TextEditingController();

  UserModel? user;

  void updateSelectedUser(UserModel? user) {
    this.user = user;
    emit(SelectedUserChanged());
  }

  Future<bool> sendFollowUp({int? followUpID, String? offlineID}) async {
    emit(EditFollowUpsLoading());
    final now = DateTime.now();
    final hourLater = now.add(const Duration(hours: 1));

    final locationInfo = await apiRepository.getCurrentLocation();

    final followUpToAPI = FollowUpToAPI(
      locationInfo: locationInfo,
      title: titleController.text,
      contactId: agentContact!.id,
      description: descriptionController.text,
      scheduleType: followUpType.name,
      userId: [user!.id],
      notifyBefore: int.tryParse(notifyBeforeController.text) ?? 0,
      notifyType: notifyType.name,
      status: status.name,
      notifyVia: {"mail": notifyViaEmail ? 1 : 0, "sms": notifyViaSMS ? 1 : 0},
      allowNotification: allowNotification,
      startDatetime: dateToString(startDate ?? now),
      endDatetime: dateToString(endDate ?? hourLater),
      offlineID: offlineID,
    );

    await apiRepository.sendFollowUp(
        followUpToAPI: followUpToAPI, followUpID: followUpID);

    EasyLoading.showSuccess('Follow-Up sent Successfully');
    getFilteredFollowUps(isNewSearch: true, refresh: true);
    return true;
  }

  void initFollowUpFields({FollowUp? followUp}) {
    user = usersBox.get(followUp?.users?.first.id);
    titleController.text = followUp?.title ?? "";
    descriptionController.text = followUp?.description ?? "";
    notifyBeforeController.text = followUp?.notifyBefore.toString() ?? "";
    allowNotification = followUp?.allowNotification == 1 ? true : false;
    notifyViaEmail = followUp?.notifyVia?['mail'] == 1 ? true : false;
    notifyViaSMS = followUp?.notifyVia?['sms'] == 1 ? true : false;
    followUpType = FollowUpType.values.firstWhere(
      (type) => type.toString() == 'FollowUpType.${followUp?.scheduleType}',
      orElse: () => FollowUpType.call,
    );
    notifyType = NotifyType.values.firstWhere(
      (type) => type.toString() == 'NotifyType.${followUp?.notifyType}',
      orElse: () => NotifyType.minute,
    );
    status = Status.values.firstWhere(
      (type) => type.toString() == 'Status.${followUp?.status}',
      orElse: () => Status.open,
    );
    agentContact = contactsBox.get(followUp?.contactId);
    startDate =
        followUp != null ? DateTime.parse(followUp.startDatetime ?? "") : null;
    endDate =
        followUp != null ? DateTime.parse(followUp.endDatetime ?? "") : null;
  }

  DateRange range = DateRange.today;

  void updateDateRange(DateRange range) {
    this.range = range;
    emit(ChangedDateFilter());
  }

  DateTime? _followUpStartDate = DateTime.now();

  void changeFilterStartDate(DateTime? date) {
    _followUpStartDate = date;
    const sevenDays = Duration(days: 7);
    if (_followUpStartDate != null && _followUpEndDate != null) {
      if (!(sameDayOrAfter(_followUpStartDate!, _followUpEndDate!) &&
          sameDayOrBefore(
              _followUpStartDate!.add(sevenDays), _followUpEndDate!))) {
        // the end date is not in 7 days range of the start date.
        _followUpEndDate = _followUpStartDate!.add(sevenDays);
      }
    } else if (_followUpStartDate != null && _followUpEndDate == null) {
      _followUpEndDate = _followUpStartDate!.add(sevenDays);
    }
    emit(ChangedDateFilter());
  }

  DateTime? _followUpEndDate = DateTime.now();

  void changeFilterEndDate(DateTime? date) {
    _followUpEndDate = date;
    emit(ChangedDateFilter());
  }

  Contact? _followUpFilterContact;

  void changeFollowUpFilterContact(Contact? contact) {
    _followUpFilterContact = contact;
    emit(ChangedContactFilter());
  }

  Status? _followUpFilterStatus;

  void changeFollowUpFilterStatus(Status? status) {
    _followUpFilterStatus = status;
    emit(ChangedStatusFilter());
  }

  TextEditingController userTextEditingController = TextEditingController();

  UserModel? filterUser;

  void updateCRMFilterUser(UserModel? user) {
    filterUser = user;
    emit(FilterUserUpdated());
  }

  void followUpFilterTitleChanged() => emit(FollowUpFilterTitleChanged());

  DateTime? get filterStartDate => _followUpStartDate;

  DateTime? get filterEndDate => _followUpEndDate;

  Contact? get followUpFilterContact => _followUpFilterContact;

  Status? get followUpFilterStatus => _followUpFilterStatus;

  TextEditingController followUpFilterTitle = TextEditingController();

  ScrollController scrollController = ScrollController();

  int currentPage = 1;
  bool loadedFromAPI = false;
  int itemsPerPage = 20;

  Future<void> getFilteredFollowUps(
      {bool isNewSearch = false, bool refresh = false}) async {
    if (isNewSearch) {
      currentPage = 1;
      loadedFromAPI = false;
      emit(NewFollowUpsLoading());
    } else {
      emit(PaginatedFollowUpsLoading());
    }

    final connected = await InternetConnectionChecker.instance.hasConnection;

    if (connected && !loadedFromAPI && !refresh && !stopSyncingTransactions()) {
      await fetchFollowUpsAndStoreInHive(
          startDate: _followUpStartDate, endDate: _followUpEndDate);
      loadedFromAPI = true;
    }

    List<FollowUp> followUps = followUpBox.values.toList().reversed.toList();

    // Apply filters based on your existing logic
    if (filterUser != null) {
      followUps = followUps
          .where(
            (crm) => crm.users != null
                ? crm.users!.any((element) => element.id == filterUser!.id)
                : false,
          )
          .toList();
    }

    if (followUpFilterTitle.text.isNotEmpty) {
      final regex = RegExp(followUpFilterTitle.text, caseSensitive: false);
      followUps = followUps.where((followUp) {
        return regex.hasMatch(followUp.title ?? "");
      }).toList();
    }

    if (_followUpStartDate != null && _followUpEndDate != null) {
      followUps = followUps.where((followUp) {
        final purchaseDate = DateTime.parse(followUp.startDatetime ?? "");
        return sameDayOrAfter(purchaseDate, _followUpStartDate!,
                shift: range == DateRange.shift) &&
            sameDayOrBefore(purchaseDate, _followUpEndDate!,
                shift: range == DateRange.shift);
      }).toList();
    } else if (_followUpStartDate != null) {
      followUps = followUps.where((followUp) {
        final purchaseDate = DateTime.parse(followUp.startDatetime ?? "");
        return sameDayOrAfter(purchaseDate, _followUpStartDate!,
            shift: range == DateRange.shift);
      }).toList();
    } else if (_followUpEndDate != null) {
      followUps = followUps.where((followUp) {
        final purchaseDate = DateTime.parse(followUp.startDatetime ?? "");
        return sameDayOrBefore(purchaseDate, _followUpEndDate!,
            shift: range == DateRange.shift);
      }).toList();
    }

    if (_followUpFilterContact != null) {
      // Filter by contact
      followUps = followUps.where((followUp) {
        return followUp.contactId == _followUpFilterContact!.id;
      }).toList();
    }

    if (_followUpFilterStatus != null) {
      // Filter by Status
      followUps = followUps.where((followUp) {
        return followUp.status == _followUpFilterStatus!.name;
      }).toList();
    }

    // Pagination
    int endIndex = currentPage * itemsPerPage;
    endIndex = endIndex > followUps.length ? followUps.length : endIndex;

    List<FollowUp> paginatedFollowUps = followUps.sublist(0, endIndex);

    currentPage++; // Increment currentPage for the next paginated call

    emit(FollowUpsLoaded(followups: paginatedFollowUps));
  }

  void resetFollowUpsFilters() {
    range = DateRange.today;
    _followUpStartDate = DateTime.now();
    filterUser = null;
    _followUpEndDate = DateTime.now();
    _followUpFilterContact = null;
    _followUpFilterStatus = null;
    followUpFilterTitle.text = '';
    searchSelectedUserController.text = '';
    user = null;
    emit(FiltersReset());
  }

  @override
  Future<void> close() {
    resetFollowUpsFilters();
    titleController.dispose();
    descriptionController.dispose();
    notifyBeforeController.dispose();
    followUpFilterTitle.dispose();
    searchSelectedUserController.dispose();
    scrollController.dispose();
    return super.close();
  }
}
