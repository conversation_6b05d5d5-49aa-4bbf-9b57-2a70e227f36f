import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:printing/printing.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:sizer/sizer.dart';
import 'package:we2up/data/models/location_info.dart';
import 'package:we2up/presentation/widgets/location_range_dropdown_button.dart';

import '../../data/models/location_transaction_row.dart';
import '../../utils/we2up_constants.dart';

pw.TextDirection _direction(context) {
  return AppLocalizations.of(context)!.localeName == "ar"
      ? pw.TextDirection.rtl
      : pw.TextDirection.ltr;
}

Future<void> generatePdfTable(
  BuildContext context, {
  bool share = false,
  required List<LocationTransaction> rows,
}) async {
  final strings = AppLocalizations.of(context)!;
  final dir = _direction(context);
  final columnWidths = {
    0: const pw.FlexColumnWidth(),
    1: const pw.FlexColumnWidth(),
    2: const pw.FlexColumnWidth(),
    3: const pw.FlexColumnWidth(),
    4: const pw.FlexColumnWidth(),
    5: const pw.FlexColumnWidth(),
    6: const pw.FlexColumnWidth(),
    7: const pw.FlexColumnWidth(),
    8: const pw.FlexColumnWidth(),
    9: const pw.FlexColumnWidth(),
  };
  final font = await fontFromAssetBundle('fonts/Amiri-Regular.ttf');
  final style = pw.TextStyle(font: font, fontSize: 9);
  final tableStyle = pw.TextStyle(font: font, fontSize: 9);
  String sanitizedFileName = '${strings.itinerary_report}.pdf';

  const int rowsPerPage = 17; // Number of rows per page

  pw.Text headersStyledText(String text) => pw.Text(
        text,
        style: style,
        maxLines: 2,
        textAlign: pw.TextAlign.center,
      );

  String getGoogleMapsUrl(LocationInfo locationInfo) {
    return 'https://www.google.com/maps?q='
        '${locationInfo.latitude},${locationInfo.longitude}';
  }

  pw.Widget rowText(String text) => pw.SizedBox(
        height: 28,
        child: pw.Center(
          child: pw.Text(
            text,
            style: tableStyle,
            maxLines: 2,
            textAlign: pw.TextAlign.center,
          ),
        ),
      );

  // Initialize total variables
  double totalAmountWithdrawn = 0.0;
  double totalAmountEntered = 0.0;

  // Calculate totals for all rows
  for (LocationTransaction row in rows) {
    totalAmountWithdrawn += double.parse(row.amountWithdrawn ?? '0');
    totalAmountEntered += double.parse(row.amountEntered ?? '0');
  }

  final List<List<LocationTransaction>> pages = [];
  for (int i = 0; i < rows.length + 3; i += rowsPerPage) {
    final List<LocationTransaction> pageRows = rows.sublist(
        i, i + rowsPerPage > rows.length ? rows.length : i + rowsPerPage);
    pages.add(pageRows);
  }

  final pdf = pw.Document(pageMode: PdfPageMode.fullscreen);

  Iterable<List<pw.Widget>> extraTable = [
    [
      rowText(strings.total.replaceAll(":", "")),
      rowText(strings.total.replaceAll(":", "")),
      rowText(""),
      rowText(""),
      rowText(""),
      rowText(""),
      rowText(""),
      rowText(""),
      rowText(""),
    ],
    [
      rowText(totalAmountWithdrawn.toStringAsFixed(4)),
      rowText(totalAmountEntered.toStringAsFixed(4)),
      rowText(""),
      rowText(""),
      rowText(""),
      rowText(""),
      rowText(""),
      rowText(""),
      rowText(""),
    ],
    [
      rowText(
        (totalAmountEntered - totalAmountWithdrawn).toStringAsFixed(4),
      ),
      rowText(""),
      rowText(""),
      rowText(""),
      rowText(""),
      rowText(""),
      rowText(""),
      rowText(""),
      rowText(""),
    ]
  ];

  for (int pageIndex = 0; pageIndex < pages.length; pageIndex++) {
    pdf.addPage(
      pw.Page(
        textDirection: dir,
        pageFormat: PdfPageFormat.a4,
        build: (cnx) {
          return pw.Column(
            children: [
              pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.center,
                children: [
                  pw.Center(
                    child: pw.Text(
                      strings.itinerary_report,
                      style: pw.TextStyle(
                        fontWeight: pw.FontWeight.bold,
                        font: font,
                      ),
                    ),
                  ),
                  pw.SizedBox(height: 8.sp),
                  pw.TableHelper.fromTextArray(
                    context: cnx,
                    headers: [
                      strings.amount_withdrawn,
                      strings.amount_entered,
                      strings.distance_from_contact,
                      strings.distance_from_company,
                      strings.location,
                      strings.closest_location,
                      strings.contact,
                      strings.transaction,
                      strings.user,
                      strings.date_and_time,
                    ].map((e) => headersStyledText(e)).toList(),
                    data: [
                      ...pages[pageIndex].map(
                        (e) => [
                          rowText(e.amountWithdrawn ?? "0"),
                          rowText(e.amountEntered ?? "0"),
                          rowText(
                            strings.translateLocationAccuracy(
                              e.distanceFromContact,
                            ),
                          ),
                          rowText(
                            strings.translateLocationAccuracy(
                              e.distanceFromCompany,
                            ),
                          ),
                          e.locationInfo != null
                              ? pw.UrlLink(
                                  child: rowText(strings.location),
                                  destination:
                                      getGoogleMapsUrl(e.locationInfo!),
                                )
                              : rowText(strings.nothing),
                          e.closestContact?.locationInfo != null
                              ? pw.UrlLink(
                                  child: rowText(
                                    e.closestContact?.name ?? "N/A",
                                  ),
                                  destination: getGoogleMapsUrl(
                                    e.closestContact!.locationInfo!,
                                  ),
                                )
                              : rowText(
                                  e.closestContact?.name ?? "N/A",
                                ),
                          rowText(e.contactName),
                          rowText(e.transactionType),
                          rowText(e.userName),
                          rowText(formatDate(e.dateTime)),
                        ],
                      ),
                      if (pageIndex == pages.length - 1) ...extraTable,
                    ],
                    cellStyle: tableStyle,
                    headerStyle: style.copyWith(fontWeight: pw.FontWeight.bold),
                    border: pw.TableBorder.all(),
                    columnWidths: columnWidths,
                  ),
                ],
              ),
            ],
          );
        },
      ),
    );
  }

  !share
      ? Printing.layoutPdf(onLayout: (PdfPageFormat format) => pdf.save())
      : Printing.sharePdf(
          bytes: await pdf.save(),
          filename: sanitizedFileName.replaceAll(RegExp(r'[/:*?"<>|]'), ''),
        );
}
