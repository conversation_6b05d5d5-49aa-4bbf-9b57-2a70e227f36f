import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:printing/printing.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:sizer/sizer.dart';
import 'package:we2up/data/models/location_info.dart';
import 'package:we2up/data/models/user_location_info.dart';
import 'package:we2up/presentation/widgets/location_range_dropdown_button.dart';

import '../../utils/we2up_constants.dart';

pw.TextDirection _direction(context) {
  return AppLocalizations.of(context)!.localeName == "ar"
      ? pw.TextDirection.rtl
      : pw.TextDirection.ltr;
}

Future<void> generateUserLocationPdf(
  BuildContext context, {
  bool share = false,
  required List<UserLocationInfo> userLocationsInfo,
}) async {
  final strings = AppLocalizations.of(context)!;
  final dir = _direction(context);
  final columnWidths = {
    0: const pw.FlexColumnWidth(),
    1: const pw.FlexColumnWidth(),
    2: const pw.FlexColumnWidth(),
    3: const pw.FlexColumnWidth(),
    4: const pw.FlexColumnWidth(),
    5: const pw.FlexColumnWidth(),
    6: const pw.FlexColumnWidth(),
    7: const pw.FlexColumnWidth(),
  };
  final font = await fontFromAssetBundle('fonts/Amiri-Regular.ttf');
  final style = pw.TextStyle(font: font, fontSize: 9);
  final tableStyle = pw.TextStyle(font: font, fontSize: 9);
  String sanitizedFileName = "${strings.users_locations}.pdf";

  const int rowsPerPage = 17; // Number of rows per page

  pw.Text headersStyledText(String text) => pw.Text(
        text,
        style: style,
        maxLines: 2,
        textAlign: pw.TextAlign.center,
      );

  String lastTransactionDateTime(userLocationInfo) {
    return userLocationInfo.lastTransactionDateTime != null
        ? formatDate(userLocationInfo.lastTransactionDateTime!)
        : strings.nothing;
  }

  String shiftOpeningDateTime(userLocationInfo) {
    return userLocationInfo.openShiftDateTime != null
        ? formatDate(userLocationInfo.openShiftDateTime!)
        : strings.nothing;
  }

  pw.Widget rowText(String text) => pw.SizedBox(
        height: 28,
        child: pw.Center(
          child: pw.Text(
            text,
            style: tableStyle,
            maxLines: 2,
            overflow: pw.TextOverflow.clip,
            textAlign: pw.TextAlign.center,
          ),
        ),
      );

  final pdf = pw.Document(pageMode: PdfPageMode.fullscreen);

  final List<List<UserLocationInfo>> pages = [];
  for (int i = 0; i < userLocationsInfo.length + 3; i += rowsPerPage) {
    final List<UserLocationInfo> pageRows = userLocationsInfo.sublist(
        i,
        i + rowsPerPage > userLocationsInfo.length
            ? userLocationsInfo.length
            : i + rowsPerPage);
    pages.add(pageRows);
  }

  for (int pageIndex = 0; pageIndex < pages.length; pageIndex++) {
    pdf.addPage(
      pw.Page(
        textDirection: dir,
        pageFormat: PdfPageFormat.a4,
        build: (cnx) {
          return pw.Column(children: [
            pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.center,
              children: [
                pw.Center(
                  child: pw.Text(
                    strings.users_locations,
                    style: pw.TextStyle(
                        fontWeight: pw.FontWeight.bold, font: font),
                  ),
                ),
                pw.SizedBox(height: 8.sp),
                pw.TableHelper.fromTextArray(
                  context: cnx,
                  headers: [
                    strings.closest_location,
                    strings.distance_from_company,
                    strings.date.replaceFirst(":", ""),
                    strings.last_transaction_location,
                    strings.distance_from_company,
                    strings.date.replaceFirst(":", ""),
                    strings.open_shift_location,
                    strings.user,
                  ].map((e) => headersStyledText(e)).toList(),
                  data: [
                    ...pages[pageIndex]
                        .map((e) => [
                              rowText(
                                  e.closestContact?.name ?? strings.nothing),
                              rowText(strings.translateLocationAccuracy(
                                  e.lastTransactionRange)),
                              rowText(lastTransactionDateTime(e)),
                              e.lastTransactionLocation != null
                                  ? pw.UrlLink(
                                      child: rowText(
                                          strings.last_transaction_location),
                                      destination: getGoogleMapsUrl(
                                          e.lastTransactionLocation!),
                                    )
                                  : rowText(strings.nothing),
                              rowText(strings.translateLocationAccuracy(
                                  e.shiftOpeningRange)),
                              rowText(shiftOpeningDateTime(e)),
                              e.openShiftLocation != null
                                  ? pw.UrlLink(
                                      child:
                                          rowText(strings.open_shift_location),
                                      destination: getGoogleMapsUrl(
                                          e.openShiftLocation!),
                                    )
                                  : rowText(strings.nothing),
                              rowText(e.username),
                            ])
                        ,
                  ],
                  cellStyle: tableStyle,
                  headerStyle: style.copyWith(fontWeight: pw.FontWeight.bold),
                  border: pw.TableBorder.all(),
                  columnWidths: columnWidths,
                ),
              ],
            ),
          ]);
        },
      ),
    );
  }

  !share
      ? Printing.layoutPdf(onLayout: (PdfPageFormat format) => pdf.save())
      : Printing.sharePdf(
          bytes: await pdf.save(),
          filename: sanitizedFileName.replaceAll(RegExp(r'[/:*?"<>|]'), ''),
        );
}

String getGoogleMapsUrl(LocationInfo locationInfo) {
  return 'https://www.google.com/maps?q='
      '${locationInfo.latitude},${locationInfo.longitude}';
}
