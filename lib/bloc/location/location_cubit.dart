import 'package:bloc/bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:geolocator/geolocator.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:we2up/data/models/business_location.dart';
import 'package:we2up/data/models/contact.dart';
import 'package:we2up/data/models/contact_payment_model.dart';
import 'package:we2up/data/models/expense.dart';
import 'package:we2up/data/models/purchase.dart';
import 'package:we2up/data/models/purchase_return.dart';
import 'package:we2up/data/models/sell.dart';
import 'package:we2up/data/models/sell_return.dart';
import 'package:we2up/data/models/user_model.dart';
import 'package:we2up/utils/location_range_constants.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

import '../../data/models/cash_register.dart';
import '../../data/models/location_info.dart';
import '../../data/models/location_transaction_row.dart';
import '../../data/models/user_location_info.dart';
import '../../data/repository/api_repo.dart';
import '../../presentation/widgets/date_range_dropdown.dart';
import '../../presentation/widgets/location_range_dropdown_button.dart';
import '../../data/db/db_manager.dart';
import '../../utils/we2up_constants.dart';

part 'location_state.dart';

class LocationCubit extends Cubit<LocationState> {
  LocationCubit() : super(LocationInitial());

  LocationInfo? position;

  void updateSelectedLocation(LocationInfo? position) {
    if (position != null) {
      this.position = position;
      emit(PositionChanged());
    }
  }

  ScrollController itineraryController = ScrollController();

  Future<bool> updateBusinessLocation() async {
    emit(UpdatingBusinessLocationLoading());

    EasyLoading.show(status: 'Updating');

    final value = await ApiRepository.get().updateBusinessLocation(
      position: position!,
      locationId: selectedLocation!.id,
    );

    if (value == true) {
      EasyLoading.showSuccess('Update Successfully');
      position = null;
      selectedLocation = null;
      emit(UpdatingBusinessLocationSuccess());
      return true;
    } else {
      EasyLoading.showError('Update Failed');
      emit(UpdatingBusinessLocationError());
      return false;
    }
  }

  Future<void> selectCurrentLocation() async {
    emit(CurrentPositionLoading());
    try {
      final currentLocation = await ApiRepository.get().getCurrentLocation();
      position = currentLocation!;
      emit(PositionChanged());
    } catch (e) {
      // position = locationInfoFromPosition(cairoLocation);
      emit(LocationReset());
      debugPrint('Error fetching current location: $e');
    }
  }

  BusinessLocation? selectedLocation;

  void changeSelectedLocation(BusinessLocation? location) {
    selectedLocation = location;
    emit(BusinessLocationChanged());
  }

  DateRange range = DateRange.today;

  void updateDateRange(DateRange range) {
    this.range = range;
    emit(DateRangeUpdated());
  }

  DateTime? startDate = DateTime.now();

  void updateStartDate(DateTime? date) {
    startDate = date;
    emit(StartDateUpdated());
  }

  DateTime? endDate = DateTime.now();

  void updateEndDate(DateTime? date) {
    endDate = date;
    emit(EndDateUpdated());
  }

  UserModel? filterUser;

  void updateFilterUser(UserModel? user) {
    filterUser = user;
    emit(FilterUserUpdated());
  }

  Contact? filterContact;

  void updateFilterContact(Contact? contact) {
    filterContact = contact;
    emit(FilterContactUpdated());
  }

  LocationRange locationRange = LocationRange.all;

  void updateLocationRange(LocationRange range) {
    locationRange = range;
    emit(FilterRangeUpdated());
  }

  TextEditingController filterUserController = TextEditingController();

  LocationRange contactLocationRange(
      {LocationInfo? source, LocationInfo? destination}) {
    return calculateLocationRange(
      calculateDistance(
        source,
        destination,
      ),
      getContactHighDefault(),
      getContactMedDefault(),
      getContactLowDefault(),
    );
  }

  LocationRange branchLocationRange(
      {LocationInfo? source, LocationInfo? destination}) {
    return calculateLocationRange(
      calculateDistance(
        source,
        destination,
      ),
      getBranchHighDefault(),
      getBranchMedDefault(),
      getBranchLowDefault(),
    );
  }

  Iterable<dynamic> filterList(Iterable<dynamic> list) {
    // Filter sales based on start and end dates
    return list.where((transaction) {
      late DateTime transactionDate;
      if (transaction is ContactPaymentModel) {
        transactionDate = transaction.createdAt;
      } else {
        transactionDate = transaction.transactionDate;
      }

      if (startDate != null && endDate != null) {
        // Filter between start and end dates (inclusive)
        return sameDayOrAfter(transactionDate, startDate!,
                shift: range == DateRange.shift) &&
            sameDayOrBefore(transactionDate, endDate!,
                shift: range == DateRange.shift);
      } else if (startDate != null) {
        // Filter after the start date
        return sameDayOrAfter(transactionDate, startDate!,
            shift: range == DateRange.shift);
      } else if (endDate != null) {
        // Filter before or on the end date
        return sameDayOrBefore(transactionDate, endDate!,
            shift: range == DateRange.shift);
      }
      // No date filtering
      return true;
    }).where((transaction) {
      late int createdBy;
      if (transaction is ContactPaymentModel) {
        createdBy = transaction.userId;
      } else {
        createdBy = transaction.createdBy;
      }
      if (filterUser != null) {
        return createdBy == filterUser!.id;
      }
      return true;
    }).where((transaction) {
      if (filterContact != null) {
        return transaction.contactId == filterContact!.id;
      }
      return true;
    }).where((transaction) {
      if (transaction is ContactPaymentModel) {
        return true;
      }
      if (selectedLocation != null) {
        return transaction.locationId == selectedLocation?.id;
      }
      return true;
    }).where((transaction) {
      if (transaction is ContactPaymentModel) {
        return true;
      }
      Contact? contact = contactsBox.get(transaction.contactId);
      final range = contactLocationRange(
        source: transaction.locationInfo,
        destination: contact?.locationInfo,
      );
      switch (locationRange) {
        case LocationRange.all:
          return true;
        case LocationRange.high:
          return range == LocationRange.high;
        case LocationRange.medium:
          return range == LocationRange.medium;
        case LocationRange.low:
          return range == LocationRange.low;
        case LocationRange.none:
          return range == LocationRange.none;
      }
    });
  }

  String calculateDistanceString(
      LocationInfo? source, LocationInfo? destination) {
    if (source != null && destination != null) {
      double distance = Geolocator.distanceBetween(
        source.latitude,
        source.longitude,
        destination.latitude,
        destination.longitude,
      );

      return distance > 1000
          ? '${(distance / 1000).toStringAsFixed(2)} km'
          : '${distance.toStringAsFixed(2)} m';
    } else {
      return 'N/A';
    }
  }

  double calculateDistance(LocationInfo? source, LocationInfo? destination) {
    if (source != null && destination != null) {
      double distance = Geolocator.distanceBetween(
        source.latitude,
        source.longitude,
        destination.latitude,
        destination.longitude,
      );

      return distance;
    } else {
      return 9999999999 * 99;
    }
  }

  Contact? closestContact(LocationInfo? source) {
    if (source == null || contactsBox.isEmpty) return null;

    double minDistance = double.infinity;
    Contact? closestContact;

    for (Contact contact in contactsBox.values) {
      LocationInfo? destination = contact.locationInfo;
      if (destination != null) {
        double distance = calculateDistance(source, destination);
        if (distance < minDistance) {
          minDistance = distance;
          closestContact = contact;
        }
      }
    }

    return closestContact;
  }

  LocationRange calculateLocationRange(
    double distance,
    double high,
    double med,
    double low,
  ) {
    LocationRange range = LocationRange.none;

    if (distance <= high * 1000) {
      range = LocationRange.high;
    } else if (distance <= med * 1000) {
      range = LocationRange.medium;
    } else if (distance <= low * 1000) {
      range = LocationRange.low;
    }

    return range;
  }

  LocationInfo? getOpenShiftLocationInfo(UserModel user) {
    // Fetch all open cash registers for the user

    List<CashRegister> openShiftLocations = cashRegistersBox.values
        .where((register) =>
            register.userId == user.id && register.status == "open")
        .toList();
    // Sort the openShiftLocations by date "updated_at" in descending order
    openShiftLocations.sort((a, b) => b.updatedAt.compareTo(a.updatedAt));

    // Get the locationInfo from the first element in the sorted list
    CashRegister? firstCashRegister =
        openShiftLocations.isNotEmpty ? openShiftLocations.first : null;

    // Return the locationInfo, or null if no open shift is found
    return firstCashRegister?.locationInfo;
  }

  DateTime? getOpenShiftDateTime(UserModel user) {
    // Fetch all open cash registers for the user

    List<CashRegister> openShiftLocations = cashRegistersBox.values
        .where((register) =>
            register.userId == user.id && register.status == "open")
        .toList();
    // Sort the openShiftLocations by date "updated_at" in descending order
    openShiftLocations.sort((a, b) => b.updatedAt.compareTo(a.updatedAt));

    // Get the locationInfo from the first element in the sorted list
    CashRegister? firstCashRegister =
        openShiftLocations.isNotEmpty ? openShiftLocations.first : null;

    // Return the locationInfo, or null if no open shift is found
    return firstCashRegister?.createdAt;
  }

  List<UserLocationInfo> userLocationsInfo = [];

  void fetchUserLocationsInfo(context) async {
    emit(UserLocationsInfoLoading());
    userLocationsInfo = [];
    await getTransactions(context);
    Iterable<UserModel> usersToProcess =
        filterUser != null ? [filterUser!] : usersBox.values;

    for (UserModel user in usersToProcess) {
      var lastTransaction = transactions
          .where((element) => element.userName == user.username)
          .firstOrNull;
      var lastTransactionLocation = lastTransaction?.locationInfo;
      userLocationsInfo.add(
        UserLocationInfo(
          username: user.username!,
          shiftOpeningRange: branchLocationRange(
            source: selectedLocation?.locationInfo,
            destination: getOpenShiftLocationInfo(user),
          ),
          openShiftDateTime: getOpenShiftDateTime(user),
          lastTransactionRange: branchLocationRange(
            source: selectedLocation?.locationInfo,
            destination: lastTransactionLocation,
          ),
          lastTransactionDateTime: lastTransaction?.dateTime,
          userLocation: user.locationInfo,
          openShiftLocation: getOpenShiftLocationInfo(user),
          lastTransactionLocation: lastTransactionLocation,
          closestContact: closestContact(lastTransactionLocation),
        ),
      );
    }
    emit(UserLocationsInfoLoaded());
  }

  List<Contact> searchContacts = [];

  void fetchSearchContacts() async {
    emit(SearchContactLoading());
    searchContacts = [];
    for (Contact contact in contactsBox.values.where((contact) {
      final range = contactLocationRange(
        source: position,
        destination: contact.locationInfo,
      );
      switch (locationRange) {
        case LocationRange.all:
          return true;
        case LocationRange.high:
          return range == LocationRange.high;
        case LocationRange.medium:
          return range == LocationRange.medium;
        case LocationRange.low:
          return range == LocationRange.low;
        case LocationRange.none:
          return range == LocationRange.none;
      }
    })) {
      searchContacts.add(contact);
    }
    searchContacts.sort((a, b) {
      LocationInfo? locationA = a.locationInfo;
      LocationInfo? locationB = b.locationInfo;
      double distanceA = calculateDistance(locationA, position);
      double distanceB = calculateDistance(locationB, position);
      return distanceA.compareTo(distanceB);
    });
    emit(SearchContactLoaded());
  }

  void updateRanges(TextEditingController controller, int defaultValue,
      String storageKey, bool reset) {
    if (reset) controller.text = (defaultValue / 1000).toString();

    locationRangeBox.put(
      storageKey,
      !reset ? ((int.tryParse(controller.text) ?? 0) * 1000) : defaultValue,
    );

    EasyLoading.showSuccess(reset ? "Reset" : "Saved");
  }

  TextEditingController branchHighController = TextEditingController();
  TextEditingController branchMidController = TextEditingController();
  TextEditingController branchLowController = TextEditingController();

  void updateBranchRanges({bool reset = false}) {
    updateRanges(branchHighController, 50, branchHigh, reset);
    updateRanges(branchMidController, 150, branchMed, reset);
    updateRanges(branchLowController, 1500, branchLow, reset);
    emit(LocationRangesValuesUpdated());
  }

  TextEditingController contactHighController = TextEditingController();
  TextEditingController contactMidController = TextEditingController();
  TextEditingController contactLowController = TextEditingController();

  void updateContactRanges({bool reset = false}) {
    updateRanges(contactHighController, 50, contactHigh, reset);
    updateRanges(contactMidController, 150, contactMed, reset);
    updateRanges(contactLowController, 1500, contactLow, reset);
    emit(LocationRangesValuesUpdated());
  }

  TextEditingController searchHighController = TextEditingController();
  TextEditingController searchMidController = TextEditingController();
  TextEditingController searchLowController = TextEditingController();

  void updateSearchRanges({bool reset = false}) {
    updateRanges(searchHighController, 50, searchHigh, reset);
    updateRanges(searchMidController, 150, searchMed, reset);
    updateRanges(searchLowController, 1500, searchLow, reset);
    emit(LocationRangesValuesUpdated());
  }

  TextEditingController closestRangeController = TextEditingController();

  void updateClosestRange() {
    updateRanges(closestRangeController, 500, closestRange, false);
    emit(LocationRangesValuesUpdated());
  }

  Future<void> getRangedData() async {
    emit(RangedDataLoading());
    if (await InternetConnectionChecker.instance.hasConnection) {
      await fetchSellsAndStoreInHive(
          startDate: startDate, endDate: endDate, summary: true);
      await fetchSellsReturnsAndStoreInHive(
          startDate: startDate, endDate: endDate, summary: true);
      await fetchPurchasesAndStoreInHive(
          startDate: startDate, endDate: endDate, summary: true);
      await fetchPurchasesReturnsAndStoreInHive(
        startDate: startDate,
        endDate: endDate,
        summary: true,
      );
      await fetchExpensesAndStoreInHive(
        startDate: startDate,
        endDate: endDate,
        summary: true,
      );
      await fetchContactPaymentReports(startDate: startDate, endDate: endDate);
    }
    emit(RangedDataLoaded());
  }

  List<LocationTransaction> transactions = [];

  int currentPage = 1;
  int perPage = 20;

  Future<void> getTransactions(context, {bool isNewSearch = false}) async {
    if (isNewSearch) {
      currentPage = 1;
      emit(NewTransactionsLoading());
    } else {
      emit(PaginatedTransactionsLoading());
    }
    final strings = AppLocalizations.of(context)!;
    List<LocationTransaction> rows = [];
    // sells
    for (Sell sell in filterList(sellsBox.values)) {
      Contact contact = contactsBox.get(sell.contactId)!;
      var business = businessLocationsBox.get(sell.locationId);
      LocationRange rangeFromCompany = branchLocationRange(
        source: sell.locationInfo,
        destination: business?.locationInfo,
      );
      LocationRange rangeFromContact = contactLocationRange(
        source: sell.locationInfo,
        destination: contact.locationInfo,
      );
      double distance = calculateDistance(
        sell.locationInfo,
        contact.locationInfo,
      );
      LocationRange contactRange = calculateLocationRange(
        distance,
        getContactHighDefault(),
        getContactMedDefault(),
        getContactLowDefault(),
      );
      rows.add(
        LocationTransaction(
          dateTime: sell.transactionDate,
          userName: usersBox.get(sell.createdBy)?.username ?? "N/A",
          transactionType: strings.sell,
          contactName: contact.name ?? "N/A",
          locationInfo: sell.locationInfo,
          locationRange: contactRange,
          distanceFromCompany: rangeFromCompany,
          distanceFromContact: rangeFromContact,
          amountEntered: sell.finalTotal,
          closestContact: closestContact(sell.locationInfo),
        ),
      );
    }
    // sells returns
    for (SellReturn sellReturn in filterList(sellsReturnsBox.values)) {
      Contact contact = contactsBox.get(sellReturn.contactId)!;
      var business = businessLocationsBox.get(sellReturn.locationId);
      LocationRange rangeFromCompany = branchLocationRange(
        source: sellReturn.locationInfo,
        destination: business?.locationInfo,
      );
      LocationRange rangeFromContact = contactLocationRange(
        source: sellReturn.locationInfo,
        destination: contact.locationInfo,
      );
      double distance = calculateDistance(
        sellReturn.locationInfo,
        contact.locationInfo,
      );
      LocationRange contactRange = calculateLocationRange(
        distance,
        getContactHighDefault(),
        getContactMedDefault(),
        getContactLowDefault(),
      );
      rows.add(
        LocationTransaction(
          dateTime: sellReturn.transactionDate,
          userName: usersBox.get(sellReturn.createdBy)?.username ?? "N/A",
          transactionType: strings.sale_return,
          contactName: contact.name ?? "N/A",
          locationInfo: sellReturn.locationInfo,
          locationRange: contactRange,
          distanceFromCompany: rangeFromCompany,
          distanceFromContact: rangeFromContact,
          amountWithdrawn: sellReturn.finalTotal,
          closestContact: closestContact(sellReturn.locationInfo),
        ),
      );
    }
    // expenses
    for (Expense expense in filterList(expensesBox.values)) {
      Contact? contact = contactsBox.get(expense.contactId);
      var business = businessLocationsBox.get(expense.locationId);
      LocationRange rangeFromCompany = branchLocationRange(
        source: expense.locationInfo,
        destination: business?.locationInfo,
      );
      LocationRange rangeFromContact = contactLocationRange(
        source: expense.locationInfo,
        destination: contact?.locationInfo,
      );
      double distance = calculateDistance(
        expense.locationInfo,
        contact?.locationInfo,
      );
      LocationRange contactRange = calculateLocationRange(
        distance,
        getContactHighDefault(),
        getContactMedDefault(),
        getContactLowDefault(),
      );
      rows.add(
        LocationTransaction(
          dateTime: expense.transactionDate,
          userName: usersBox.get(expense.createdBy)?.username ?? "N/A",
          transactionType:
              expense.isRefund == 1 ? strings.revenue : strings.expense,
          contactName: contact?.name ?? "N/A",
          locationInfo: expense.locationInfo,
          locationRange: contactRange,
          distanceFromCompany: rangeFromCompany,
          distanceFromContact: rangeFromContact,
          amountWithdrawn: expense.isRefund != 1 ? expense.finalTotal : null,
          amountEntered: expense.isRefund == 1 ? expense.finalTotal : null,
          closestContact: closestContact(expense.locationInfo),
        ),
      );
    }
    // purchase
    for (Purchase purchase in filterList(purchasesBox.values)) {
      Contact contact = contactsBox.get(purchase.contactId)!;
      var business = businessLocationsBox.get(purchase.locationId);
      LocationRange rangeFromCompany = branchLocationRange(
        source: purchase.locationInfo,
        destination: business?.locationInfo,
      );
      LocationRange rangeFromContact = contactLocationRange(
        source: purchase.locationInfo,
        destination: contact.locationInfo,
      );
      double distance = calculateDistance(
        purchase.locationInfo,
        contact.locationInfo,
      );
      LocationRange contactRange = calculateLocationRange(
        distance,
        getContactHighDefault(),
        getContactMedDefault(),
        getContactLowDefault(),
      );
      rows.add(
        LocationTransaction(
          dateTime: purchase.transactionDate,
          userName: usersBox.get(purchase.createdBy)?.username ?? "N/A",
          transactionType: strings.purchase,
          contactName: contact.name ?? "N/A",
          locationInfo: purchase.locationInfo,
          locationRange: contactRange,
          distanceFromCompany: rangeFromCompany,
          distanceFromContact: rangeFromContact,
          amountWithdrawn: purchase.finalTotal,
          closestContact: closestContact(purchase.locationInfo),
        ),
      );
    }
    // purchase returns
    for (PurchaseReturn purchaseReturn
        in filterList(purchaseReturnsBox.values)) {
      Contact contact = contactsBox.get(purchaseReturn.contactId)!;
      var business = businessLocationsBox.get(purchaseReturn.locationId);
      LocationRange rangeFromCompany = branchLocationRange(
        source: purchaseReturn.locationInfo,
        destination: business?.locationInfo,
      );
      LocationRange rangeFromContact = contactLocationRange(
        source: purchaseReturn.locationInfo,
        destination: contact.locationInfo,
      );
      double distance = calculateDistance(
        purchaseReturn.locationInfo,
        contact.locationInfo,
      );
      LocationRange contactRange = calculateLocationRange(
        distance,
        getContactHighDefault(),
        getContactMedDefault(),
        getContactLowDefault(),
      );
      rows.add(
        LocationTransaction(
          dateTime: purchaseReturn.transactionDate,
          userName: usersBox.get(purchaseReturn.createdBy)?.username ?? "N/A",
          transactionType: strings.purchase_return,
          contactName: contact.name ?? "N/A",
          locationInfo: purchaseReturn.locationInfo,
          locationRange: contactRange,
          distanceFromCompany: rangeFromCompany,
          distanceFromContact: rangeFromContact,
          amountEntered: purchaseReturn.finalTotal,
          closestContact: closestContact(purchaseReturn.locationInfo),
        ),
      );
    }
    // payments
    for (ContactPaymentModel payment in filterList(paymentsReportBox.values)) {
      Contact contact = contactsBox.get(payment.contactId)!;
      LocationInfo? paymentLocation = payment.locationInfo ??
          sellsBox.values
              .where(
                  (s) => s.paymentLines?.any((p) => p.id == payment.id) == true)
              .firstOrNull
              ?.locationInfo ??
          purchasesBox.values
              .where(
                  (s) => s.paymentLines?.any((p) => p.id == payment.id) == true)
              .firstOrNull
              ?.locationInfo;
      LocationRange rangeFromCompany = branchLocationRange(
        source: paymentLocation,
        destination: selectedLocation?.locationInfo,
      );
      LocationRange rangeFromContact = contactLocationRange(
        source: paymentLocation,
        destination: contact.locationInfo,
      );
      rows.add(
        LocationTransaction(
          dateTime: payment.createdAt,
          userName: usersBox.get(payment.userId)?.username ?? "N/A",
          transactionType: payment.transactionType == "sell_return"
              ? strings.sale_return_payment
              : payment.transactionType == "purchase_return"
                  ? strings.purchase_return_payment
                  : contact.type == "supplier"
                      ? strings.purchase_payment
                      : strings.sale_collection_button,
          contactName: contact.name ?? "N/A",
          distanceFromCompany: rangeFromCompany,
          distanceFromContact: rangeFromContact,
          amountEntered: contact.type != "supplier" ||
                  payment.transactionType == "purchase_return"
              ? payment.amount?.toStringAsFixed(4) ?? "0"
              : null,
          amountWithdrawn: contact.type == "supplier" ||
                  payment.transactionType == "sell_return"
              ? payment.amount?.toStringAsFixed(4) ?? "0"
              : null,
          locationInfo: paymentLocation,
        ),
      );
    }

    rows.sort((a, b) => a.dateTime.compareTo(b.dateTime));

    transactions = rows;
    // Pagination
    int endIndex = currentPage * perPage;
    endIndex = endIndex > transactions.length ? transactions.length : endIndex;
    List<LocationTransaction> paginatedList = transactions.sublist(0, endIndex);
    currentPage++; // Increment currentPage for the next paginated call
    emit(TransactionsLoaded(paginatedList));
  }

  @override
  Future<void> close() {
    selectedLocation = null;
    startDate = DateTime.now();
    endDate = DateTime.now();
    filterUser = null;
    filterContact = null;
    locationRange = LocationRange.all;
    position = null;
    userLocationsInfo = [];
    searchContacts = [];
    transactions = [];
    branchHighController.dispose();
    branchMidController.dispose();
    branchLowController.dispose();
    contactHighController.dispose();
    contactMidController.dispose();
    contactLowController.dispose();
    searchHighController.dispose();
    searchMidController.dispose();
    searchLowController.dispose();
    closestRangeController.dispose();
    itineraryController.dispose();
    return super.close();
  }
}
