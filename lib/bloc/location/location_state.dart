part of 'location_cubit.dart';

@immutable
abstract class LocationState {}

class LocationInitial extends LocationState {}

class LocationReset extends LocationState {}

class PositionChanged extends LocationState {}

class CurrentPositionLoading extends LocationState {}

class StartDateUpdated extends LocationState {}

class DateRangeUpdated extends LocationState {}

class EndDateUpdated extends LocationState {}

class FilterUserUpdated extends LocationState {}

class FilterContactUpdated extends LocationState {}

class FilterRangeUpdated extends LocationState {}

class LocationFiltersReset extends LocationState {}

class BusinessLocationChanged extends LocationState {}

class UpdatingBusinessLocationLoading extends LocationState {}

class UpdatingBusinessLocationSuccess extends LocationState {}

class UpdatingBusinessLocationError extends LocationState {}

class LocationRangesValuesUpdated extends LocationState {}

class UserLocationsInfoLoading extends LocationState {}

class UserLocationsInfoLoaded extends LocationState {}

class SearchContactLoading extends LocationState {}

class SearchContactLoaded extends LocationState {}

class NewTransactionsLoading extends LocationState {}

class PaginatedTransactionsLoading extends LocationState {}

class TransactionsLoaded extends LocationState {
  final List<LocationTransaction> transactions;

  TransactionsLoaded(this.transactions);
}

class RangedDataLoading extends LocationState {}

class RangedDataLoaded extends LocationState {}
