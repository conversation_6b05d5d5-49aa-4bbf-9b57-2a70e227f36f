import 'package:bloc/bloc.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

import '../../data/db/db_manager.dart';
import '../../data/models/app_currency.dart';
import '../../data/models/business_settings.dart';
import '../../utils/we2up_constants.dart';

part 'business_settings_state.dart';

part 'business_settings_cubit.freezed.dart';

class BusinessSettingsCubit extends Cubit<BusinessSettingsState> {
  BusinessSettingsCubit()
      : super(BusinessSettingsState(settings: BusinessSettings.initial())) {
    loadSettings();
  }

  Future<void> loadSettings() async {
    try {
      emit(state.copyWith(isLoading: true));

      // Load the entire BusinessSettings object from the box
      final BusinessSettings settings = businessDetailsBox.get(
        businessSettingsDataKey,
        defaultValue: BusinessSettings.initial(),
      );

      emit(state.copyWith(settings: settings, isLoading: false));
    } catch (e) {
      emit(state.copyWith(error: e.toString(), isLoading: false));
    }
  }

  Future<void> updateSettings(BusinessSettings settings) async {
    try {
      // emit(state.copyWith(isLoading: true));

      // Store the entire BusinessSettings object in the box
      await businessDetailsBox.put(businessSettingsDataKey, settings);

      emit(state.copyWith(settings: settings, isLoading: false));
    } catch (e) {
      emit(state.copyWith(error: e.toString(), isLoading: false));
    }
  }

  void updateBusinessName(String value) {
    final newSettings = state.settings.copyWith(businessName: value);
    updateSettings(newSettings);
  }

  void updateBusinessMobileNumber(String value) {
    final newSettings = state.settings.copyWith(businessMobileNumber: value);
    updateSettings(newSettings);
  }

  void updateBusinessFooterText(String value) {
    final newSettings = state.settings.copyWith(businessFooterText: value);
    updateSettings(newSettings);
  }

  void updateImagePath(String? value) {
    final newSettings = state.settings.copyWith(imagePath: value);
    updateSettings(newSettings);
  }

  void updateIncludeServiceInTaxes(bool value) {
    final newSettings = state.settings.copyWith(includeServiceInTaxes: value);
    updateSettings(newSettings);
  }

  void updateCurrency(AppCurrency value) {
    final newSettings = state.settings.copyWith(currency: value);
    updateSettings(newSettings);
  }

  void updatePlaySound(bool value) {
    final newSettings =
        state.settings.copyWith(playSoundWhenAddingProduct: value);
    updateSettings(newSettings);
  }

  void updateUseArabicNumbers(bool value) {
    final newSettings = state.settings.copyWith(useArabicNumbers: value);
    updateSettings(newSettings);
  }

  void updatePaperSize(String value) {
    final newSettings = state.settings.copyWith(currentPaperSize: value);
    updateSettings(newSettings);
  }

  void updateFont(String value) {
    final newSettings = state.settings.copyWith(currentFont: value);
    updateSettings(newSettings);
  }

  void updateFontSize(int value) {
    final newSettings = state.settings.copyWith(currentFontSize: value);
    updateSettings(newSettings);
  }

  void updatePrintQRCode(bool value) {
    final newSettings = state.settings.copyWith(printQRCode: value);
    updateSettings(newSettings);
  }

  void updatePrintSequence(bool value) {
    final newSettings =
        state.settings.copyWith(printSequenceFirstAddedFirst: value);
    updateSettings(newSettings);
  }

  void updateShowUnit(bool value) {
    final newSettings = state.settings.copyWith(showUnit: value);
    updateSettings(newSettings);
  }

  void updateDiscountOption(bool value) {
    final newSettings = state.settings.copyWith(isDiscountOptionAmount: value);
    updateSettings(newSettings);
  }

  void updateStopSyncing(bool value) {
    final newSettings = state.settings.copyWith(stopSyncing: value);
    updateSettings(newSettings);
  }

  void updateShowShopProductsGridView(bool value) {
    final newSettings =
        state.settings.copyWith(showShopProductsGridView: value);
    updateSettings(newSettings);
  }

  void updateShowPopupMenuToSelectNumber(bool value) {
    final newSettings =
        state.settings.copyWith(showPopupMenuToSelectNumber: value);
    updateSettings(newSettings);
  }
}
