// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'business_settings_cubit.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$BusinessSettingsState {
  BusinessSettings get settings => throw _privateConstructorUsedError;
  bool get isLoading => throw _privateConstructorUsedError;
  String? get error => throw _privateConstructorUsedError;

  /// Create a copy of BusinessSettingsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BusinessSettingsStateCopyWith<BusinessSettingsState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BusinessSettingsStateCopyWith<$Res> {
  factory $BusinessSettingsStateCopyWith(BusinessSettingsState value,
          $Res Function(BusinessSettingsState) then) =
      _$BusinessSettingsStateCopyWithImpl<$Res, BusinessSettingsState>;
  @useResult
  $Res call({BusinessSettings settings, bool isLoading, String? error});

  $BusinessSettingsCopyWith<$Res> get settings;
}

/// @nodoc
class _$BusinessSettingsStateCopyWithImpl<$Res,
        $Val extends BusinessSettingsState>
    implements $BusinessSettingsStateCopyWith<$Res> {
  _$BusinessSettingsStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BusinessSettingsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? settings = null,
    Object? isLoading = null,
    Object? error = freezed,
  }) {
    return _then(_value.copyWith(
      settings: null == settings
          ? _value.settings
          : settings // ignore: cast_nullable_to_non_nullable
              as BusinessSettings,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }

  /// Create a copy of BusinessSettingsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $BusinessSettingsCopyWith<$Res> get settings {
    return $BusinessSettingsCopyWith<$Res>(_value.settings, (value) {
      return _then(_value.copyWith(settings: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$BusinessSettingsStateImplCopyWith<$Res>
    implements $BusinessSettingsStateCopyWith<$Res> {
  factory _$$BusinessSettingsStateImplCopyWith(
          _$BusinessSettingsStateImpl value,
          $Res Function(_$BusinessSettingsStateImpl) then) =
      __$$BusinessSettingsStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({BusinessSettings settings, bool isLoading, String? error});

  @override
  $BusinessSettingsCopyWith<$Res> get settings;
}

/// @nodoc
class __$$BusinessSettingsStateImplCopyWithImpl<$Res>
    extends _$BusinessSettingsStateCopyWithImpl<$Res,
        _$BusinessSettingsStateImpl>
    implements _$$BusinessSettingsStateImplCopyWith<$Res> {
  __$$BusinessSettingsStateImplCopyWithImpl(_$BusinessSettingsStateImpl _value,
      $Res Function(_$BusinessSettingsStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of BusinessSettingsState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? settings = null,
    Object? isLoading = null,
    Object? error = freezed,
  }) {
    return _then(_$BusinessSettingsStateImpl(
      settings: null == settings
          ? _value.settings
          : settings // ignore: cast_nullable_to_non_nullable
              as BusinessSettings,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      error: freezed == error
          ? _value.error
          : error // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$BusinessSettingsStateImpl implements _BusinessSettingsState {
  const _$BusinessSettingsStateImpl(
      {required this.settings, this.isLoading = false, this.error});

  @override
  final BusinessSettings settings;
  @override
  @JsonKey()
  final bool isLoading;
  @override
  final String? error;

  @override
  String toString() {
    return 'BusinessSettingsState(settings: $settings, isLoading: $isLoading, error: $error)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BusinessSettingsStateImpl &&
            (identical(other.settings, settings) ||
                other.settings == settings) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.error, error) || other.error == error));
  }

  @override
  int get hashCode => Object.hash(runtimeType, settings, isLoading, error);

  /// Create a copy of BusinessSettingsState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BusinessSettingsStateImplCopyWith<_$BusinessSettingsStateImpl>
      get copyWith => __$$BusinessSettingsStateImplCopyWithImpl<
          _$BusinessSettingsStateImpl>(this, _$identity);
}

abstract class _BusinessSettingsState implements BusinessSettingsState {
  const factory _BusinessSettingsState(
      {required final BusinessSettings settings,
      final bool isLoading,
      final String? error}) = _$BusinessSettingsStateImpl;

  @override
  BusinessSettings get settings;
  @override
  bool get isLoading;
  @override
  String? get error;

  /// Create a copy of BusinessSettingsState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BusinessSettingsStateImplCopyWith<_$BusinessSettingsStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
