// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'service_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class ServiceModelAdapter extends TypeAdapter<ServiceModel> {
  @override
  final int typeId = 58;

  @override
  ServiceModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ServiceModel(
      id: fields[0] as int,
      name: fields[1] as String,
      description: fields[2] as String?,
      businessId: fields[3] as int,
      locationPriceGroup: (fields[4] as Map?)?.cast<String, dynamic>(),
      packingCharge: fields[5] as double,
      packingChargeType: fields[6] as PackingChargeType,
      enableCustomFields: fields[7] as int,
      createdAt: fields[8] as DateTime,
      updatedAt: fields[9] as DateTime,
    );
  }

  @override
  void write(BinaryWriter writer, ServiceModel obj) {
    writer
      ..writeByte(10)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.description)
      ..writeByte(3)
      ..write(obj.businessId)
      ..writeByte(4)
      ..write(obj.locationPriceGroup)
      ..writeByte(5)
      ..write(obj.packingCharge)
      ..writeByte(6)
      ..write(obj.packingChargeType)
      ..writeByte(7)
      ..write(obj.enableCustomFields)
      ..writeByte(8)
      ..write(obj.createdAt)
      ..writeByte(9)
      ..write(obj.updatedAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ServiceModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class PackingChargeTypeAdapter extends TypeAdapter<PackingChargeType> {
  @override
  final int typeId = 59;

  @override
  PackingChargeType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return PackingChargeType.fixed;
      case 1:
        return PackingChargeType.weight;
      case 2:
        return PackingChargeType.percentage;
      default:
        return PackingChargeType.fixed;
    }
  }

  @override
  void write(BinaryWriter writer, PackingChargeType obj) {
    switch (obj) {
      case PackingChargeType.fixed:
        writer.writeByte(0);
        break;
      case PackingChargeType.weight:
        writer.writeByte(1);
        break;
      case PackingChargeType.percentage:
        writer.writeByte(2);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PackingChargeTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ServiceModel _$ServiceModelFromJson(Map<String, dynamic> json) => ServiceModel(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      description: json['description'] as String?,
      businessId: (json['business_id'] as num).toInt(),
      locationPriceGroup: json['location_price_group'] as Map<String, dynamic>?,
      packingCharge: double.parse(json['packing_charge'] as String),
      packingChargeType:
          _$PackingChargeTypeFromJson(json['packing_charge_type'] as String),
      enableCustomFields: (json['enable_custom_fields'] as num).toInt(),
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$ServiceModelToJson(ServiceModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'business_id': instance.businessId,
      'location_price_group': instance.locationPriceGroup,
      'packing_charge': instance.packingCharge,
      'packing_charge_type':
          _$PackingChargeTypeEnumMap[instance.packingChargeType]!,
      'enable_custom_fields': instance.enableCustomFields,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
    };

const _$PackingChargeTypeEnumMap = {
  PackingChargeType.fixed: 'fixed',
  PackingChargeType.weight: 'weight',
  PackingChargeType.percentage: 'percentage',
};
