// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'customer_group.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class CustomerGroupAdapter extends TypeAdapter<CustomerGroup> {
  @override
  final int typeId = 57;

  @override
  CustomerGroup read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return CustomerGroup(
      name: fields[0] as String?,
      amount: fields[1] as double?,
      sellingPriceGroup: fields[2] as String?,
      id: fields[3] as int,
      priceCalculationType: fields[4] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, CustomerGroup obj) {
    writer
      ..writeByte(5)
      ..writeByte(0)
      ..write(obj.name)
      ..writeByte(1)
      ..write(obj.amount)
      ..writeByte(2)
      ..write(obj.sellingPriceGroup)
      ..writeByte(3)
      ..write(obj.id)
      ..writeByte(4)
      ..write(obj.priceCalculationType);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CustomerGroupAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CustomerGroup _$CustomerGroupFromJson(Map<String, dynamic> json) =>
    CustomerGroup(
      name: json['name'] as String?,
      amount: (json['amount'] as num?)?.toDouble(),
      sellingPriceGroup: json['selling_price_group'] as String?,
      id: (json['id'] as num).toInt(),
      priceCalculationType: json['price_calculation_type'] as String?,
    );

Map<String, dynamic> _$CustomerGroupToJson(CustomerGroup instance) =>
    <String, dynamic>{
      'name': instance.name,
      'amount': instance.amount,
      'selling_price_group': instance.sellingPriceGroup,
      'id': instance.id,
      'price_calculation_type': instance.priceCalculationType,
    };
