import 'package:equatable/equatable.dart';
import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';

import '../../utils/we2up_constants.dart';
import 'location_info.dart';

part 'contact_to_api.g.dart';

@JsonSerializable(explicitToJson: true)
@HiveType(typeId: 25)
class ContactToAPI extends Equatable {
  @HiveField(0)
  @Json<PERSON>ey(name: 'type')
  final String type;

  @HiveField(1)
  @JsonKey(name: 'supplier_business_name')
  final String? supplierBusinessName;

  @HiveField(2)
  @<PERSON>son<PERSON><PERSON>(name: 'prefix')
  final String? prefix;

  @HiveField(3)
  @Json<PERSON>ey(name: 'first_name')
  final String? firstName;

  @HiveField(4)
  @JsonKey(name: 'middle_name')
  final String? middleName;

  @HiveField(5)
  @JsonKey(name: 'last_name')
  final String? lastName;

  @HiveField(6)
  @<PERSON>son<PERSON>ey(name: 'tax_number')
  final String? taxNumber;

  @HiveField(7)
  @Json<PERSON>ey(name: 'pay_term_number')
  final int? payTermNumber;

  @HiveField(8)
  @JsonKey(name: 'pay_term_type')
  final String? payTermType;

  @HiveField(9)
  @JsonKey(name: 'mobile')
  final String mobile;

  @HiveField(10)
  @JsonKey(name: 'landline')
  final String? landline;

  @HiveField(11)
  @JsonKey(name: 'alternate_number')
  final String? alternateNumber;

  @HiveField(12)
  @JsonKey(name: 'address_line_1')
  final String? addressLine1;

  @HiveField(13)
  @JsonKey(name: 'address_line_2')
  final String? addressLine2;

  @HiveField(14)
  @JsonKey(name: 'city')
  final String? city;

  @HiveField(15)
  @JsonKey(name: 'state')
  final String? state;

  @HiveField(16)
  @JsonKey(name: 'country')
  final String? country;

  @HiveField(17)
  @JsonKey(name: 'zip_code')
  final String? zipCode;

  @HiveField(18)
  @JsonKey(name: 'customer_group_id')
  final String? customerGroupId;

  @HiveField(19)
  @JsonKey(name: 'contact_id')
  final String? contactId;

  @HiveField(20)
  @JsonKey(name: 'dob')
  final String? dob;

  @HiveField(21)
  @JsonKey(name: 'custom_field1')
  final String? customField1;

  @HiveField(22)
  @JsonKey(name: 'custom_field2')
  final String? customField2;

  @HiveField(23)
  @JsonKey(name: 'custom_field3')
  final String? customField3;

  @HiveField(24)
  @JsonKey(name: 'custom_field4')
  final String? customField4;

  @HiveField(25)
  @JsonKey(name: 'email')
  final String? email;

  @HiveField(26)
  @JsonKey(name: 'shipping_address')
  final String? shippingAddress;

  @HiveField(27)
  @JsonKey(name: 'position')
  final String? position;

  @HiveField(28)
  @JsonKey(name: 'opening_balance')
  final double openingBalance;

  @HiveField(29)
  @JsonKey(name: 'source_id')
  final int? sourceId;

  @HiveField(30)
  @JsonKey(name: 'life_stage_id')
  final int? lifeStageId;

  @HiveField(31)
  @JsonKey(name: 'assigned_to')
  final List<dynamic> assignedTo;

  @HiveField(32)
  @JsonKey(name: 'location_info', toJson: locationInfoToJson)
  final LocationInfo? locationPosition;

  @HiveField(33)
  @JsonKey(name: 'price_group_id')
  final int? priceGroupId;

  const ContactToAPI({
    required this.type,
    this.supplierBusinessName,
    this.prefix,
    this.firstName,
    this.middleName,
    this.lastName,
    this.taxNumber,
    this.payTermNumber,
    this.payTermType,
    required this.mobile,
    this.landline,
    this.alternateNumber,
    this.addressLine1,
    this.addressLine2,
    this.city,
    this.state,
    this.country,
    this.zipCode,
    this.customerGroupId,
    this.contactId,
    this.dob,
    this.customField1,
    this.customField2,
    this.customField3,
    this.customField4,
    this.email,
    this.shippingAddress,
    this.position,
    this.openingBalance = 0,
    this.sourceId,
    this.lifeStageId,
    this.assignedTo = const [],
    this.locationPosition,
    this.priceGroupId,
  });

  factory ContactToAPI.fromJson(Map<String, dynamic> json) =>
      _$ContactToAPIFromJson(json);

  Map<String, dynamic> toJson() => _$ContactToAPIToJson(this);

  @override
  List<Object?> get props => [
        type,
        supplierBusinessName,
        prefix,
        firstName,
        middleName,
        lastName,
        taxNumber,
        payTermNumber,
        payTermType,
        mobile,
        landline,
        alternateNumber,
        addressLine1,
        addressLine2,
        city,
        state,
        country,
        zipCode,
        customerGroupId,
        contactId,
        dob,
        customField1,
        customField2,
        customField3,
        customField4,
        email,
        shippingAddress,
        position,
        openingBalance,
        sourceId,
        lifeStageId,
        assignedTo,
      ];
}
