// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'expense.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class ExpenseAdapter extends TypeAdapter<Expense> {
  @override
  final int typeId = 18;

  @override
  Expense read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Expense(
      id: fields[0] as int?,
      businessId: fields[1] as int?,
      locationId: fields[2] as int?,
      paymentStatus: fields[3] as String?,
      refNo: fields[4] as String?,
      transactionDate: fields[5] as DateTime,
      totalBeforeTax: fields[6] as String?,
      taxId: fields[7] as int?,
      taxAmount: fields[8] as String?,
      finalTotal: fields[9] as String?,
      expenseCategoryId: fields[10] as int?,
      document: fields[11] as String?,
      createdBy: fields[12] as int?,
      createdAt: fields[13] as DateTime?,
      updatedAt: fields[14] as DateTime?,
      expenseFor: (fields[15] as Map?)?.cast<String, dynamic>(),
      isRecurring: fields[16] as int?,
      recurInterval: fields[17] as int?,
      recurIntervalType: fields[18] as String?,
      recurRepetitions: fields[19] as int?,
      recurStoppedOn: fields[20] as DateTime?,
      recurParentId: fields[21] as int?,
      isRefund: fields[22] as int,
      additionalNotes: fields[23] as String?,
      payments: (fields[24] as List?)?.cast<Payment>(),
      offline: fields[25] as bool,
      expenseToAPI: fields[26] as ExpenseToAPI?,
      contactId: fields[27] as int?,
      locationInfo: fields[28] as LocationInfo?,
      category: fields[29] as ExpenseCategory?,
    );
  }

  @override
  void write(BinaryWriter writer, Expense obj) {
    writer
      ..writeByte(30)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.businessId)
      ..writeByte(2)
      ..write(obj.locationId)
      ..writeByte(3)
      ..write(obj.paymentStatus)
      ..writeByte(4)
      ..write(obj.refNo)
      ..writeByte(5)
      ..write(obj.transactionDate)
      ..writeByte(6)
      ..write(obj.totalBeforeTax)
      ..writeByte(7)
      ..write(obj.taxId)
      ..writeByte(8)
      ..write(obj.taxAmount)
      ..writeByte(9)
      ..write(obj.finalTotal)
      ..writeByte(10)
      ..write(obj.expenseCategoryId)
      ..writeByte(11)
      ..write(obj.document)
      ..writeByte(12)
      ..write(obj.createdBy)
      ..writeByte(13)
      ..write(obj.createdAt)
      ..writeByte(14)
      ..write(obj.updatedAt)
      ..writeByte(15)
      ..write(obj.expenseFor)
      ..writeByte(16)
      ..write(obj.isRecurring)
      ..writeByte(17)
      ..write(obj.recurInterval)
      ..writeByte(18)
      ..write(obj.recurIntervalType)
      ..writeByte(19)
      ..write(obj.recurRepetitions)
      ..writeByte(20)
      ..write(obj.recurStoppedOn)
      ..writeByte(21)
      ..write(obj.recurParentId)
      ..writeByte(22)
      ..write(obj.isRefund)
      ..writeByte(23)
      ..write(obj.additionalNotes)
      ..writeByte(24)
      ..write(obj.payments)
      ..writeByte(25)
      ..write(obj.offline)
      ..writeByte(26)
      ..write(obj.expenseToAPI)
      ..writeByte(27)
      ..write(obj.contactId)
      ..writeByte(28)
      ..write(obj.locationInfo)
      ..writeByte(29)
      ..write(obj.category);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ExpenseAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Expense _$ExpenseFromJson(Map<String, dynamic> json) => Expense(
      id: (json['id'] as num?)?.toInt(),
      businessId: (json['business_id'] as num?)?.toInt(),
      locationId: (json['location_id'] as num?)?.toInt(),
      paymentStatus: json['payment_status'] as String?,
      refNo: json['ref_no'] as String?,
      transactionDate: DateTime.parse(json['transaction_date'] as String),
      totalBeforeTax: json['total_before_tax'] as String?,
      taxId: (json['tax_id'] as num?)?.toInt(),
      taxAmount: json['tax_amount'] as String?,
      finalTotal: json['final_total'] as String?,
      expenseCategoryId: (json['expense_category_id'] as num?)?.toInt(),
      document: json['document'] as String?,
      createdBy: (json['created_by'] as num?)?.toInt(),
      createdAt: json['created_at'] == null
          ? null
          : DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] == null
          ? null
          : DateTime.parse(json['updated_at'] as String),
      expenseFor: Expense._expenseForFromJson(json['expense_for']),
      isRecurring: (json['is_recurring'] as num?)?.toInt(),
      recurInterval: (json['recur_interval'] as num?)?.toInt(),
      recurIntervalType: json['recur_interval_type'] as String?,
      recurRepetitions: (json['recur_repetitions'] as num?)?.toInt(),
      recurStoppedOn: json['recur_stopped_on'] == null
          ? null
          : DateTime.parse(json['recur_stopped_on'] as String),
      recurParentId: (json['recur_parent_id'] as num?)?.toInt(),
      isRefund: (json['isRefund'] as num).toInt(),
      additionalNotes: json['additional_notes'] as String?,
      payments: (json['payments'] as List<dynamic>?)
          ?.map((e) => Payment.fromJson(e as Map<String, dynamic>))
          .toList(),
      contactId: (json['contact_id'] as num?)?.toInt(),
      locationInfo: locationInfoFromListJson(json['location_info'] as List?),
      category: Expense._categoryForFromJson(json['category']),
    );

Map<String, dynamic> _$ExpenseToJson(Expense instance) => <String, dynamic>{
      'id': instance.id,
      'business_id': instance.businessId,
      'location_id': instance.locationId,
      'payment_status': instance.paymentStatus,
      'ref_no': instance.refNo,
      'transaction_date': dateToString(instance.transactionDate),
      'total_before_tax': instance.totalBeforeTax,
      'tax_id': instance.taxId,
      'tax_amount': instance.taxAmount,
      'final_total': instance.finalTotal,
      'expense_category_id': instance.expenseCategoryId,
      'document': instance.document,
      'created_by': instance.createdBy,
      'created_at': instance.createdAt?.toIso8601String(),
      'updated_at': instance.updatedAt?.toIso8601String(),
      'expense_for': instance.expenseFor,
      'is_recurring': instance.isRecurring,
      'recur_interval': instance.recurInterval,
      'recur_interval_type': instance.recurIntervalType,
      'recur_repetitions': instance.recurRepetitions,
      'recur_stopped_on': instance.recurStoppedOn?.toIso8601String(),
      'recur_parent_id': instance.recurParentId,
      'isRefund': instance.isRefund,
      'additional_notes': instance.additionalNotes,
      'payments': Expense.paymentListToJson(instance.payments),
      'contact_id': instance.contactId,
      'location_info': instance.locationInfo,
      'category': instance.category,
    };
