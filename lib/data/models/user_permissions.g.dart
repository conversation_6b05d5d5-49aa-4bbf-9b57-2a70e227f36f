// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_permissions.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class UserPermissionsAdapter extends TypeAdapter<UserPermissions> {
  @override
  final int typeId = 49;

  @override
  UserPermissions read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return UserPermissions(
      supplierView: fields[0] as bool,
      supplierViewOwn: fields[1] as bool,
      supplierCreate: fields[2] as bool,
      supplierUpdate: fields[3] as bool,
      supplierDelete: fields[4] as bool,
      customerView: fields[5] as bool,
      customerGroupView: fields[6] as bool,
      customerShow: fields[7] as bool,
      customerViewOwn: fields[8] as bool,
      customerCreate: fields[9] as bool,
      customerUpdate: fields[10] as bool,
      customerDelete: fields[11] as bool,
      customerCashback: fields[12] as bool,
      productView: fields[13] as bool,
      productCreate: fields[14] as bool,
      productUpdateAllPrices: fields[15] as bool,
      productUpdate: fields[16] as bool,
      productDelete: fields[17] as bool,
      productOpeningStock: fields[18] as bool,
      stockCompares: fields[19] as bool,
      viewPurchasePrice: fields[20] as bool,
      purchaseView: fields[21] as bool,
      purchaseCreate: fields[22] as bool,
      purchaseUpdate: fields[23] as bool,
      purchaseDelete: fields[24] as bool,
      purchasePaymentCreate: fields[25] as bool,
      purchasePaymentEdit: fields[26] as bool,
      purchasePaymentDelete: fields[27] as bool,
      purchaseUpdateStatus: fields[28] as bool,
      viewOwnPurchase: fields[29] as bool,
      purchaseReturnView: fields[30] as bool,
      purchaseReturnCreate: fields[31] as bool,
      salesSellAgel: fields[32] as bool,
      salesPayCard: fields[33] as bool,
      salesMultiPayWays: fields[34] as bool,
      salesSellInCash: fields[35] as bool,
      salesLessThanPurchasePrice: fields[36] as bool,
      salesShow: fields[37] as bool,
      listQuotations: fields[38] as bool,
      salesPriceOffer: fields[39] as bool,
      salesShowCurrentStockInPos: fields[40] as bool,
      salesShowComboDetails: fields[41] as bool,
      salesShowPurchasePriceInPos: fields[42] as bool,
      todaySellsTotalShow: fields[43] as bool,
      sellCreate: fields[44] as bool,
      sellUpdate: fields[45] as bool,
      sellDelete: fields[46] as bool,
      viewOwnSellOnly: fields[47] as bool,
      sellPaymentCreate: fields[48] as bool,
      sellPaymentEdit: fields[49] as bool,
      sellPaymentDelete: fields[50] as bool,
      editProductPriceFromPosScreen: fields[51] as bool,
      editProductDiscountFromPosScreen: fields[52] as bool,
      discountAccess: fields[53] as bool,
      accessShipping: fields[54] as bool,
      accessSellReturn: fields[55] as bool,
      customerBalanceDueInPos: fields[56] as bool,
      allExpenseAccess: fields[57] as bool,
      viewOwnExpense: fields[58] as bool,
      expensesView: fields[59] as bool,
      expenseCategories: fields[60] as bool,
      expenseCreate: fields[61] as bool,
      expenseEdit: fields[62] as bool,
      expenseDelete: fields[63] as bool,
      viewCashRegister: fields[64] as bool,
      closeCashRegister: fields[65] as bool,
      accessDefaultSellingPrice: fields[66] as bool,
      crmShow: fields[67] as bool,
      crmAccessAllSchedule: fields[68] as bool,
      crmAccessOwnSchedule: fields[69] as bool,
      essentialsCrudAllAttendance: fields[70] as bool,
      permissions: (fields[71] as List).cast<String>(),
      locationSearch: fields[74] as bool,
      locationSetting: fields[75] as bool,
      locationUserPlaces: fields[72] as bool,
      locationWalkingLine: fields[73] as bool,
      customersPaymentsReportView: fields[78] as bool,
      profitLossReportView: fields[80] as bool,
      purchasePaymentReportView: fields[76] as bool,
      sellPaymentReportView: fields[77] as bool,
      suppliersPaymentsReportView: fields[79] as bool,
    );
  }

  @override
  void write(BinaryWriter writer, UserPermissions obj) {
    writer
      ..writeByte(81)
      ..writeByte(0)
      ..write(obj.supplierView)
      ..writeByte(1)
      ..write(obj.supplierViewOwn)
      ..writeByte(2)
      ..write(obj.supplierCreate)
      ..writeByte(3)
      ..write(obj.supplierUpdate)
      ..writeByte(4)
      ..write(obj.supplierDelete)
      ..writeByte(5)
      ..write(obj.customerView)
      ..writeByte(6)
      ..write(obj.customerGroupView)
      ..writeByte(7)
      ..write(obj.customerShow)
      ..writeByte(8)
      ..write(obj.customerViewOwn)
      ..writeByte(9)
      ..write(obj.customerCreate)
      ..writeByte(10)
      ..write(obj.customerUpdate)
      ..writeByte(11)
      ..write(obj.customerDelete)
      ..writeByte(12)
      ..write(obj.customerCashback)
      ..writeByte(13)
      ..write(obj.productView)
      ..writeByte(14)
      ..write(obj.productCreate)
      ..writeByte(15)
      ..write(obj.productUpdateAllPrices)
      ..writeByte(16)
      ..write(obj.productUpdate)
      ..writeByte(17)
      ..write(obj.productDelete)
      ..writeByte(18)
      ..write(obj.productOpeningStock)
      ..writeByte(19)
      ..write(obj.stockCompares)
      ..writeByte(20)
      ..write(obj.viewPurchasePrice)
      ..writeByte(21)
      ..write(obj.purchaseView)
      ..writeByte(22)
      ..write(obj.purchaseCreate)
      ..writeByte(23)
      ..write(obj.purchaseUpdate)
      ..writeByte(24)
      ..write(obj.purchaseDelete)
      ..writeByte(25)
      ..write(obj.purchasePaymentCreate)
      ..writeByte(26)
      ..write(obj.purchasePaymentEdit)
      ..writeByte(27)
      ..write(obj.purchasePaymentDelete)
      ..writeByte(28)
      ..write(obj.purchaseUpdateStatus)
      ..writeByte(29)
      ..write(obj.viewOwnPurchase)
      ..writeByte(30)
      ..write(obj.purchaseReturnView)
      ..writeByte(31)
      ..write(obj.purchaseReturnCreate)
      ..writeByte(32)
      ..write(obj.salesSellAgel)
      ..writeByte(33)
      ..write(obj.salesPayCard)
      ..writeByte(34)
      ..write(obj.salesMultiPayWays)
      ..writeByte(35)
      ..write(obj.salesSellInCash)
      ..writeByte(36)
      ..write(obj.salesLessThanPurchasePrice)
      ..writeByte(37)
      ..write(obj.salesShow)
      ..writeByte(38)
      ..write(obj.listQuotations)
      ..writeByte(39)
      ..write(obj.salesPriceOffer)
      ..writeByte(40)
      ..write(obj.salesShowCurrentStockInPos)
      ..writeByte(41)
      ..write(obj.salesShowComboDetails)
      ..writeByte(42)
      ..write(obj.salesShowPurchasePriceInPos)
      ..writeByte(43)
      ..write(obj.todaySellsTotalShow)
      ..writeByte(44)
      ..write(obj.sellCreate)
      ..writeByte(45)
      ..write(obj.sellUpdate)
      ..writeByte(46)
      ..write(obj.sellDelete)
      ..writeByte(47)
      ..write(obj.viewOwnSellOnly)
      ..writeByte(48)
      ..write(obj.sellPaymentCreate)
      ..writeByte(49)
      ..write(obj.sellPaymentEdit)
      ..writeByte(50)
      ..write(obj.sellPaymentDelete)
      ..writeByte(51)
      ..write(obj.editProductPriceFromPosScreen)
      ..writeByte(52)
      ..write(obj.editProductDiscountFromPosScreen)
      ..writeByte(53)
      ..write(obj.discountAccess)
      ..writeByte(54)
      ..write(obj.accessShipping)
      ..writeByte(55)
      ..write(obj.accessSellReturn)
      ..writeByte(56)
      ..write(obj.customerBalanceDueInPos)
      ..writeByte(57)
      ..write(obj.allExpenseAccess)
      ..writeByte(58)
      ..write(obj.viewOwnExpense)
      ..writeByte(59)
      ..write(obj.expensesView)
      ..writeByte(60)
      ..write(obj.expenseCategories)
      ..writeByte(61)
      ..write(obj.expenseCreate)
      ..writeByte(62)
      ..write(obj.expenseEdit)
      ..writeByte(63)
      ..write(obj.expenseDelete)
      ..writeByte(64)
      ..write(obj.viewCashRegister)
      ..writeByte(65)
      ..write(obj.closeCashRegister)
      ..writeByte(66)
      ..write(obj.accessDefaultSellingPrice)
      ..writeByte(67)
      ..write(obj.crmShow)
      ..writeByte(68)
      ..write(obj.crmAccessAllSchedule)
      ..writeByte(69)
      ..write(obj.crmAccessOwnSchedule)
      ..writeByte(70)
      ..write(obj.essentialsCrudAllAttendance)
      ..writeByte(71)
      ..write(obj.permissions)
      ..writeByte(72)
      ..write(obj.locationUserPlaces)
      ..writeByte(73)
      ..write(obj.locationWalkingLine)
      ..writeByte(74)
      ..write(obj.locationSearch)
      ..writeByte(75)
      ..write(obj.locationSetting)
      ..writeByte(76)
      ..write(obj.purchasePaymentReportView)
      ..writeByte(77)
      ..write(obj.sellPaymentReportView)
      ..writeByte(78)
      ..write(obj.customersPaymentsReportView)
      ..writeByte(79)
      ..write(obj.suppliersPaymentsReportView)
      ..writeByte(80)
      ..write(obj.profitLossReportView);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserPermissionsAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
