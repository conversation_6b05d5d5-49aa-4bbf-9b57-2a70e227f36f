import 'package:json_annotation/json_annotation.dart';

part 'items_to_delete.g.dart';

@JsonSerializable(createToJson: false)
class ItemsToDelete {
  @JsonKey(name: 'sell')
  List<int> sell;

  @<PERSON>sonKey(name: 'sell_return')
  List<int> sellReturn;

  @<PERSON>son<PERSON>ey(name: 'purchase')
  List<int> purchase;

  @<PERSON>sonKey(name: 'purchase_return')
  List<int> purchaseReturn;

  @<PERSON>sonKey(name: 'expense')
  List<int> expense;

  @JsonKey(name: 'product')
  List<int> product;

  @JsonKey(name: 'contact')
  List<int> contact;

  @<PERSON>son<PERSON>ey(name: 'payment')
  List<int> payment;

  ItemsToDelete({
    required this.sell,
    required this.sellReturn,
    required this.purchase,
    required this.purchaseReturn,
    required this.expense,
    required this.product,
    required this.contact,
    required this.payment,
  });

  factory ItemsToDelete.fromJson(Map<String, dynamic> json) =>
      _$ItemsToDeleteFromJson(json);
}