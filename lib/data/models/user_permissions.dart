import 'package:equatable/equatable.dart';
import 'package:hive/hive.dart';

part 'user_permissions.g.dart';

@HiveType(typeId: 49)
class UserPermissions extends Equatable {
  bool hasSellingPriceGroupPermission(int groupId) {
    final permissionKey = 'selling_price_group.$groupId';
    return permissions.contains(permissionKey);
  }

  /// عرض جميع الموردين
  @HiveField(0)
  final bool supplierView;

  /// عرض تفاصيل المورد الخاص
  @HiveField(1)
  final bool supplierViewOwn;

  /// إضافة مورد جديد
  @HiveField(2)
  final bool supplierCreate;

  /// تعديل بيانات المورد
  @HiveField(3)
  final bool supplierUpdate;

  /// حذف مورد
  @HiveField(4)
  final bool supplierDelete;

  /// عرض جميع العملاء
  @HiveField(5)
  final bool customerView;

  /// عرض مجموعات العملاء
  @HiveField(6)
  final bool customerGroupView;

  /// عرض تفاصيل العميل
  @HiveField(7)
  final bool customerShow;

  /// عرض تفاصيل العميل الخاص
  @HiveField(8)
  final bool customerViewOwn;

  /// إضافة عميل جديد
  @HiveField(9)
  final bool customerCreate;

  /// تعديل بيانات العميل
  @HiveField(10)
  final bool customerUpdate;

  /// حذف عميل
  @HiveField(11)
  final bool customerDelete;

  /// منح خصم نقدي للعملاء
  @HiveField(12)
  final bool customerCashback;

  /// عرض تفاصيل المنتج
  @HiveField(13)
  final bool productView;

  /// إضافة منتج جديد
  @HiveField(14)
  final bool productCreate;

  /// تعديل أسعار جميع المنتجات
  @HiveField(15)
  final bool productUpdateAllPrices;

  /// تحديث تفاصيل المنتج
  @HiveField(16)
  final bool productUpdate;

  /// حذف منتج
  @HiveField(17)
  final bool productDelete;

  /// إضافة مخزون افتتاحي للمنتج
  @HiveField(18)
  final bool productOpeningStock;

  /// مقارنة مستويات المخزون
  @HiveField(19)
  final bool stockCompares;

  /// عرض سعر الشراء
  @HiveField(20)
  final bool viewPurchasePrice;

  /// عرض جميع عمليات الشراء
  @HiveField(21)
  final bool purchaseView;

  /// إضافة عملية شراء جديدة وتحديث المخزون
  @HiveField(22)
  final bool purchaseCreate;

  /// تحديث عمليات الشراء والمخزون التالف
  @HiveField(23)
  final bool purchaseUpdate;

  /// حذف عمليات الشراء والمخزون التالف
  @HiveField(24)
  final bool purchaseDelete;

  /// إضافة دفع شراء
  @HiveField(25)
  final bool purchasePaymentCreate;

  /// تعديل دفع الشراء
  @HiveField(26)
  final bool purchasePaymentEdit;

  /// حذف دفع الشراء
  @HiveField(27)
  final bool purchasePaymentDelete;

  /// تحديث حالة عمليات الشراء
  @HiveField(28)
  final bool purchaseUpdateStatus;

  /// عرض عمليات الشراء الخاصة
  @HiveField(29)
  final bool viewOwnPurchase;

  /// عرض مرتجعات المشتريات
  @HiveField(30)
  final bool purchaseReturnView;

  /// إضافة مرتجع المشتريات
  @HiveField(31)
  final bool purchaseReturnCreate;

  /// بيع بآجل
  @HiveField(32)
  final bool salesSellAgel;

  /// قبول دفع ببطاقة
  @HiveField(33)
  final bool salesPayCard;

  /// قبول دفع بوسائل متعددة
  @HiveField(34)
  final bool salesMultiPayWays;

  /// بيع نقدي
  @HiveField(35)
  final bool salesSellInCash;

  /// بيع المنتجات بأقل من سعر الشراء
  @HiveField(36)
  final bool salesLessThanPurchasePrice;

  /// عرض تفاصيل المبيعات
  @HiveField(37)
  final bool salesShow;

  /// عرض قائمة بيانات الأسعار
  @HiveField(38)
  final bool listQuotations;

  /// إعداد عرض السعر خلال عملية البيع
  @HiveField(39)
  final bool salesPriceOffer;

  /// عرض المخزون الحالي في نقطة البيع
  @HiveField(40)
  final bool salesShowCurrentStockInPos;

  /// عرض تفاصيل مكونات المنتج الكومبو
  @HiveField(41)
  final bool salesShowComboDetails;

  /// عرض سعر الشراء في نقطة البيع
  @HiveField(42)
  final bool salesShowPurchasePriceInPos;

  /// عرض الإجمالي اليومي للمبيعات
  @HiveField(43)
  final bool todaySellsTotalShow;

  /// إضافة عملية بيع في نقطة البيع
  @HiveField(44)
  final bool sellCreate;

  /// تحديث عمليات بيع في نقطة البيع
  @HiveField(45)
  final bool sellUpdate;

  /// حذف عمليات بيع في نقطة البيع
  @HiveField(46)
  final bool sellDelete;

  /// عرض عمليات بيع الخاصة
  @HiveField(47)
  final bool viewOwnSellOnly;

  /// إضافة دفع بيع
  @HiveField(48)
  final bool sellPaymentCreate;

  /// تعديل دفع بيع
  @HiveField(49)
  final bool sellPaymentEdit;

  /// حذف دفع بيع
  @HiveField(50)
  final bool sellPaymentDelete;

  /// تعديل سعر المنتج من شاشة نقطة البيع
  @HiveField(51)
  final bool editProductPriceFromPosScreen;

  /// تعديل خصم المنتج من شاشة نقطة البيع
  @HiveField(52)
  final bool editProductDiscountFromPosScreen;

  /// إضافة وتعديل وحذف الخصومات
  @HiveField(53)
  final bool discountAccess;

  /// الوصول إلى وظائف الشحن
  @HiveField(54)
  final bool accessShipping;

  /// الوصول إلى وظائف إرجاع المبيعات
  @HiveField(55)
  final bool accessSellReturn;

  /// عرض رصيد العميل المستحق في نقطة البيع
  @HiveField(56)
  final bool customerBalanceDueInPos;

  /// الوصول إلى جميع وظائف المصروفات
  @HiveField(57)
  final bool allExpenseAccess;

  /// عرض مصروفات الشخص الخاص
  @HiveField(58)
  final bool viewOwnExpense;

  /// عرض جميع المصروفات
  @HiveField(59)
  final bool expensesView;

  /// إدارة فئات المصروفات
  @HiveField(60)
  final bool expenseCategories;

  /// إضافة مصروف
  @HiveField(61)
  final bool expenseCreate;

  /// تعديل مصروف
  @HiveField(62)
  final bool expenseEdit;

  /// حذف مصروف
  @HiveField(63)
  final bool expenseDelete;

  /// عرض تفاصيل الدرج النقدي
  @HiveField(64)
  final bool viewCashRegister;

  /// إغلاق الدرج النقدي
  @HiveField(65)
  final bool closeCashRegister;

  /// الوصول إلى سعر البيع الافتراضي
  @HiveField(66)
  final bool accessDefaultSellingPrice;

  /// عرض تفاصيل إدارة العلاقات مع العملاء
  @HiveField(67)
  final bool crmShow;

  /// الوصول إلى جميع جداول إدارة العلاقات مع العملاء
  @HiveField(68)
  final bool crmAccessAllSchedule;

  /// الوصول إلى جدول إدارة العلاقات مع العملاء الخاص
  @HiveField(69)
  final bool crmAccessOwnSchedule;

  /// عمليات إضافة وتحديث وحذف جميع الحضور
  @HiveField(70)
  final bool essentialsCrudAllAttendance;

  @HiveField(71)
  final List<String> permissions;

  /// الوصول لصفحة مواقع المستخدمين
  @HiveField(72)
  final bool locationUserPlaces;

  /// الوصول لصفحة خط السير
  @HiveField(73)
  final bool locationWalkingLine;

  /// الوصول لصفحة بحث الموقع
  @HiveField(74)
  final bool locationSearch;

  /// الوصول لاعدادات الموقع
  @HiveField(75)
  final bool locationSetting;

  ///تقرير مدفوعات المشتريات
  @HiveField(76)
  final bool purchasePaymentReportView;

  ///تقرير مدفوعات المبيعات
  @HiveField(77)
  final bool sellPaymentReportView;

  ///تقرير تحصيلات نقدية
  @HiveField(78)
  final bool customersPaymentsReportView;

  ///تقرير دفعات الموردين
  @HiveField(79)
  final bool suppliersPaymentsReportView;

  ///عرض تقرير الربح /الخسارة
  @HiveField(80)
  final bool profitLossReportView;

  const UserPermissions({
    required this.supplierView,
    required this.supplierViewOwn,
    required this.supplierCreate,
    required this.supplierUpdate,
    required this.supplierDelete,
    required this.customerView,
    required this.customerGroupView,
    required this.customerShow,
    required this.customerViewOwn,
    required this.customerCreate,
    required this.customerUpdate,
    required this.customerDelete,
    required this.customerCashback,
    required this.productView,
    required this.productCreate,
    required this.productUpdateAllPrices,
    required this.productUpdate,
    required this.productDelete,
    required this.productOpeningStock,
    required this.stockCompares,
    required this.viewPurchasePrice,
    required this.purchaseView,
    required this.purchaseCreate,
    required this.purchaseUpdate,
    required this.purchaseDelete,
    required this.purchasePaymentCreate,
    required this.purchasePaymentEdit,
    required this.purchasePaymentDelete,
    required this.purchaseUpdateStatus,
    required this.viewOwnPurchase,
    required this.purchaseReturnView,
    required this.purchaseReturnCreate,
    required this.salesSellAgel,
    required this.salesPayCard,
    required this.salesMultiPayWays,
    required this.salesSellInCash,
    required this.salesLessThanPurchasePrice,
    required this.salesShow,
    required this.listQuotations,
    required this.salesPriceOffer,
    required this.salesShowCurrentStockInPos,
    required this.salesShowComboDetails,
    required this.salesShowPurchasePriceInPos,
    required this.todaySellsTotalShow,
    required this.sellCreate,
    required this.sellUpdate,
    required this.sellDelete,
    required this.viewOwnSellOnly,
    required this.sellPaymentCreate,
    required this.sellPaymentEdit,
    required this.sellPaymentDelete,
    required this.editProductPriceFromPosScreen,
    required this.editProductDiscountFromPosScreen,
    required this.discountAccess,
    required this.accessShipping,
    required this.accessSellReturn,
    required this.customerBalanceDueInPos,
    required this.allExpenseAccess,
    required this.viewOwnExpense,
    required this.expensesView,
    required this.expenseCategories,
    required this.expenseCreate,
    required this.expenseEdit,
    required this.expenseDelete,
    required this.viewCashRegister,
    required this.closeCashRegister,
    required this.accessDefaultSellingPrice,
    required this.crmShow,
    required this.crmAccessAllSchedule,
    required this.crmAccessOwnSchedule,
    required this.essentialsCrudAllAttendance,
    required this.permissions,
    required this.locationSearch,
    required this.locationSetting,
    required this.locationUserPlaces,
    required this.locationWalkingLine,
    required this.customersPaymentsReportView,
    required this.profitLossReportView,
    required this.purchasePaymentReportView,
    required this.sellPaymentReportView,
    required this.suppliersPaymentsReportView,
  });

  /// Constructor to initialize UserPermissions based on a list of permissions
  UserPermissions.fromPermissionsList(this.permissions)
      : supplierView = permissions.contains("supplier.view"),
        supplierViewOwn = permissions.contains("supplier.view_own"),
        supplierCreate = permissions.contains("supplier.create"),
        supplierUpdate = permissions.contains("supplier.update"),
        supplierDelete = permissions.contains("supplier.delete"),
        customerView = permissions.contains("customer.view"),
        customerGroupView = permissions.contains("customergroup.view"),
        customerShow = permissions.contains("customer.show"),
        customerViewOwn = permissions.contains("customer.view_own"),
        customerCreate = permissions.contains("customer.create"),
        customerUpdate = permissions.contains("customer.update"),
        customerDelete = permissions.contains("customer.delete"),
        customerCashback = permissions.contains("customer.cashback"),
        productView = permissions.contains("product.view"),
        productCreate = permissions.contains("product.create"),
        productUpdateAllPrices =
            permissions.contains("product.update_all_prices"),
        productUpdate = permissions.contains("product.update"),
        productDelete = permissions.contains("product.delete"),
        productOpeningStock = permissions.contains("product.opening_stock"),
        stockCompares = permissions.contains("stcok_compares"),
        viewPurchasePrice = permissions.contains("view_purchase_price"),
        purchaseView = permissions.contains("purchase.view"),
        purchaseCreate = permissions.contains("purchase.create"),
        purchaseUpdate = permissions.contains("purchase.update"),
        purchaseDelete = permissions.contains("purchase.delete"),
        purchasePaymentCreate = permissions.contains("purchase_payment.create"),
        purchasePaymentEdit = permissions.contains("purchase_payment.edit"),
        purchasePaymentDelete = permissions.contains("purchase_payment.delete"),
        purchaseUpdateStatus = permissions.contains("purchase.update_status"),
        viewOwnPurchase = permissions.contains("view_own_purchase"),
        purchaseReturnView = permissions.contains("purchase_return.view"),
        purchaseReturnCreate = permissions.contains("purchase_return.create"),
        salesSellAgel = permissions.contains("sales.sell_agel"),
        salesPayCard = permissions.contains("sales.pay_card"),
        salesMultiPayWays = permissions.contains("sales.multi_pay_ways"),
        salesSellInCash = permissions.contains("sales.sell_in_cash"),
        salesLessThanPurchasePrice =
            permissions.contains("sales.less_than_purchase_price"),
        salesShow = permissions.contains("sales.show"),
        listQuotations = permissions.contains("list_quotations"),
        salesPriceOffer = permissions.contains("sales.price_offer"),
        salesShowCurrentStockInPos =
            permissions.contains("sales.show_current_stock_in_pos"),
        salesShowComboDetails =
            permissions.contains("sales.show_combo_details"),
        salesShowPurchasePriceInPos =
            permissions.contains("sales.show_purchase_price_in_pos"),
        todaySellsTotalShow = permissions.contains("today_sells_total.show"),
        sellCreate = permissions.contains("sell.create"),
        sellUpdate = permissions.contains("sell.update"),
        sellDelete = permissions.contains("sell.delete"),
        viewOwnSellOnly = permissions.contains("view_own_sell_only"),
        sellPaymentCreate = permissions.contains("sell_payment.create"),
        sellPaymentEdit = permissions.contains("sell_payment.edit"),
        sellPaymentDelete = permissions.contains("sell_payment.delete"),
        editProductPriceFromPosScreen =
            permissions.contains("edit_product_price_from_pos_screen"),
        editProductDiscountFromPosScreen =
            permissions.contains("edit_product_discount_from_pos_screen"),
        discountAccess = permissions.contains("discount.access"),
        accessShipping = permissions.contains("access_shipping"),
        accessSellReturn = permissions.contains("access_sell_return"),
        customerBalanceDueInPos =
            permissions.contains("customer_balance_due_in_pos"),
        allExpenseAccess = permissions.contains("all_expense.access"),
        viewOwnExpense = permissions.contains("view_own_expense"),
        expensesView = permissions.contains("expenses.view"),
        expenseCategories = permissions.contains("expense.categories"),
        expenseCreate = permissions.contains("expense.create"),
        expenseEdit = permissions.contains("expense.edit"),
        expenseDelete = permissions.contains("expense.delete"),
        viewCashRegister = permissions.contains("view_cash_register"),
        closeCashRegister = permissions.contains("close_cash_register"),
        accessDefaultSellingPrice =
            permissions.contains("access_default_selling_price"),
        crmShow = permissions.contains("crm.show"),
        crmAccessAllSchedule = permissions.contains("crm.access_all_schedule"),
        crmAccessOwnSchedule = permissions.contains("crm.access_own_schedule"),
        locationUserPlaces = permissions.contains("location.user_places"),
        locationWalkingLine = permissions.contains("location.walking_line"),
        locationSearch = permissions.contains("location.search"),
        locationSetting = permissions.contains("location.setting"),
        purchasePaymentReportView =
            permissions.contains("purchase_payment_report.view"),
        sellPaymentReportView =
            permissions.contains("sell_payment_report.view"),
        customersPaymentsReportView =
            permissions.contains("customers_payments_report.view"),
        suppliersPaymentsReportView =
            permissions.contains("suppliers_payments_report.view"),
        profitLossReportView = permissions.contains("profit_loss_report.view"),
        essentialsCrudAllAttendance =
            permissions.contains("essentials.crud_all_attendance");

  @override
  List<Object?> get props => [
        supplierView,
        supplierViewOwn,
        supplierCreate,
        supplierUpdate,
        supplierDelete,
        customerView,
        customerGroupView,
        customerShow,
        customerViewOwn,
        customerCreate,
        customerUpdate,
        customerDelete,
        customerCashback,
        productView,
        productCreate,
        productUpdateAllPrices,
        productUpdate,
        productDelete,
        productOpeningStock,
        stockCompares,
        viewPurchasePrice,
        purchaseView,
        purchaseCreate,
        purchaseUpdate,
        purchaseDelete,
        purchasePaymentCreate,
        purchasePaymentEdit,
        purchasePaymentDelete,
        purchaseUpdateStatus,
        viewOwnPurchase,
        purchaseReturnView,
        purchaseReturnCreate,
        salesSellAgel,
        salesPayCard,
        salesMultiPayWays,
        salesSellInCash,
        salesLessThanPurchasePrice,
        salesShow,
        listQuotations,
        salesPriceOffer,
        salesShowCurrentStockInPos,
        salesShowComboDetails,
        salesShowPurchasePriceInPos,
        todaySellsTotalShow,
        sellCreate,
        sellUpdate,
        sellDelete,
        viewOwnSellOnly,
        sellPaymentCreate,
        sellPaymentEdit,
        sellPaymentDelete,
        editProductPriceFromPosScreen,
        editProductDiscountFromPosScreen,
        discountAccess,
        accessShipping,
        accessSellReturn,
        customerBalanceDueInPos,
        allExpenseAccess,
        viewOwnExpense,
        expensesView,
        expenseCategories,
        expenseCreate,
        expenseEdit,
        expenseDelete,
        viewCashRegister,
        closeCashRegister,
        accessDefaultSellingPrice,
        crmShow,
        crmAccessAllSchedule,
        crmAccessOwnSchedule,
        essentialsCrudAllAttendance,
        permissions,
        locationUserPlaces,
        locationWalkingLine,
        locationSearch,
        locationSetting,
        purchasePaymentReportView,
        sellPaymentReportView,
        customersPaymentsReportView,
        suppliersPaymentsReportView,
        profitLossReportView,
      ];
}
