// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'purchase.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class PurchaseAdapter extends TypeAdapter<Purchase> {
  @override
  final int typeId = 34;

  @override
  Purchase read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Purchase(
      id: fields[0] as int?,
      businessId: fields[1] as int?,
      locationId: fields[2] as int?,
      resTableId: fields[3] as dynamic,
      resWaiterId: fields[4] as dynamic,
      resOrderStatus: fields[5] as dynamic,
      type: fields[6] as String?,
      subType: fields[7] as dynamic,
      status: fields[8] as String?,
      subStatus: fields[9] as dynamic,
      isQuotation: fields[10] as int?,
      paymentStatus: fields[11] as String?,
      adjustmentType: fields[12] as dynamic,
      contactId: fields[13] as int?,
      customerGroupId: fields[14] as dynamic,
      invoiceNo: fields[15] as dynamic,
      refNo: fields[16] as dynamic,
      source: fields[17] as dynamic,
      subscriptionNo: fields[18] as dynamic,
      subscriptionRepeatOn: fields[19] as dynamic,
      transactionDate: fields[20] as DateTime,
      totalBeforeTax: fields[21] as String?,
      taxId: fields[22] as dynamic,
      taxAmount: fields[23] as String?,
      discountType: fields[24] as String?,
      discountAmount: fields[25] as String?,
      rpRedeemed: fields[26] as int?,
      rpRedeemedAmount: fields[27] as String?,
      shippingDetails: fields[28] as dynamic,
      shippingAddress: fields[29] as dynamic,
      deliveryDate: fields[30] as dynamic,
      shippingStatus: fields[31] as dynamic,
      deliveredTo: fields[32] as dynamic,
      shippingCharges: fields[33] as String?,
      shippingCustomField1: fields[34] as dynamic,
      shippingCustomField2: fields[35] as dynamic,
      shippingCustomField3: fields[36] as dynamic,
      shippingCustomField4: fields[37] as dynamic,
      shippingCustomField5: fields[38] as dynamic,
      additionalNotes: fields[39] as dynamic,
      staffNote: fields[40] as dynamic,
      isExport: fields[41] as int?,
      exportCustomFieldsInfo: fields[42] as dynamic,
      roundOffAmount: fields[43] as String?,
      additionalExpenseKey1: fields[44] as String?,
      additionalExpenseValue1: fields[45] as String?,
      additionalExpenseKey2: fields[46] as String?,
      additionalExpenseValue2: fields[47] as String?,
      additionalExpenseKey3: fields[48] as String?,
      additionalExpenseValue3: fields[49] as String?,
      additionalExpenseKey4: fields[50] as String?,
      additionalExpenseValue4: fields[51] as String?,
      finalTotal: fields[52] as String?,
      expenseCategoryId: fields[53] as dynamic,
      expenseFor: fields[54] as dynamic,
      commissionAgent: fields[55] as dynamic,
      document: fields[56] as dynamic,
      isDirectSale: fields[57] as int?,
      isSuspend: fields[58] as int?,
      exchangeRate: fields[59] as String?,
      totalAmountRecovered: fields[60] as dynamic,
      transferParentId: fields[61] as dynamic,
      returnParentId: fields[62] as dynamic,
      openingStockProductId: fields[63] as dynamic,
      createdBy: fields[64] as int?,
      purchaseRequisitionIds: fields[65] as dynamic,
      crmIsOrderRequest: fields[66] as int?,
      preferPaymentMethod: fields[67] as dynamic,
      preferPaymentAccount: fields[68] as dynamic,
      salesOrderIds: fields[69] as dynamic,
      purchaseOrderIds: fields[70] as dynamic,
      customField1: fields[71] as dynamic,
      customField2: fields[72] as dynamic,
      customField3: fields[73] as dynamic,
      customField4: fields[74] as dynamic,
      mfgParentProductionPurchaseId: fields[75] as dynamic,
      mfgWastedUnits: fields[76] as dynamic,
      mfgProductionCost: fields[77] as String?,
      mfgIsFinal: fields[78] as int?,
      repairCompletedOn: fields[79] as dynamic,
      repairWarrantyId: fields[80] as dynamic,
      repairBrandId: fields[81] as dynamic,
      repairStatusId: fields[82] as dynamic,
      repairModelId: fields[83] as dynamic,
      repairJobSheetId: fields[84] as dynamic,
      repairDefects: fields[85] as dynamic,
      repairSerialNo: fields[86] as dynamic,
      repairChecklist: fields[87] as dynamic,
      repairSecurityPwd: fields[88] as dynamic,
      repairSecurityPattern: fields[89] as dynamic,
      repairDueDate: fields[90] as dynamic,
      repairDeviceId: fields[91] as dynamic,
      repairUpdatesNotif: fields[92] as int?,
      essentialsDuration: fields[93] as String?,
      essentialsDurationUnit: fields[94] as dynamic,
      essentialsAmountPerUnitDuration: fields[95] as String?,
      essentialsAllowances: fields[96] as dynamic,
      essentialsDeductions: fields[97] as dynamic,
      woocommerceOrderId: fields[98] as dynamic,
      importBatch: fields[99] as dynamic,
      importTime: fields[100] as dynamic,
      typesOfServiceId: fields[101] as dynamic,
      packingCharge: fields[102] as dynamic,
      packingChargeType: fields[103] as dynamic,
      serviceCustomField1: fields[104] as dynamic,
      serviceCustomField2: fields[105] as dynamic,
      serviceCustomField3: fields[106] as dynamic,
      serviceCustomField4: fields[107] as dynamic,
      serviceCustomField5: fields[108] as dynamic,
      serviceCustomField6: fields[109] as dynamic,
      isCreatedFromApi: fields[110] as int?,
      rpEarned: fields[111] as int?,
      orderAddresses: fields[112] as dynamic,
      isRecurring: fields[113] as int?,
      recurInterval: fields[114] as dynamic,
      recurIntervalType: fields[115] as dynamic,
      recurRepetitions: fields[116] as dynamic,
      recurStoppedOn: fields[117] as dynamic,
      recurParentId: fields[118] as dynamic,
      invoiceToken: fields[119] as String?,
      payTermNumber: fields[120] as dynamic,
      payTermType: fields[121] as dynamic,
      pjtProjectId: fields[122] as dynamic,
      pjtTitle: fields[123] as dynamic,
      sellingPriceGroupId: fields[124] as dynamic,
      createdAt: fields[125] as String?,
      updatedAt: fields[126] as String?,
      endDate: fields[127] as dynamic,
      shippingCompanyId: fields[128] as dynamic,
      packageCount: fields[129] as dynamic,
      paymobOrderId: fields[130] as dynamic,
      prevBalance: fields[131] as dynamic,
      subCounter: fields[132] as dynamic,
      customerId: fields[133] as dynamic,
      supplierId: fields[134] as dynamic,
      sellId: fields[135] as dynamic,
      purchaseId: fields[136] as dynamic,
      etaSubmissionId: fields[137] as dynamic,
      etaUuid: fields[138] as dynamic,
      etaLongId: fields[139] as dynamic,
      etaHashKey: fields[140] as dynamic,
      etaStatus: fields[141] as dynamic,
      etaNotes: fields[142] as dynamic,
      invoiceSchemeId: fields[143] as dynamic,
      customStatus: fields[144] as dynamic,
      isReservation: fields[145] as dynamic,
      reviewStatus: fields[146] as dynamic,
      reviewDetails: fields[147] as dynamic,
      settlementPurchaseId: fields[148] as dynamic,
      settlementSellId: fields[149] as dynamic,
      invoiceLayoutId: fields[150] as dynamic,
      invoiceCommision: fields[151] as dynamic,
      commisionAsLeader: fields[152] as String?,
      leaderId: fields[153] as dynamic,
      purchaseLines: (fields[154] as List?)?.cast<PurchaseLine>(),
      paymentLines: (fields[155] as List?)?.cast<PaymentLine>(),
      offline: fields[156] as bool,
      purchaseToAPI: fields[157] as PurchaseToAPI?,
      locationInfo: fields[158] as LocationInfo?,
    );
  }

  @override
  void write(BinaryWriter writer, Purchase obj) {
    writer
      ..writeByte(159)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.businessId)
      ..writeByte(2)
      ..write(obj.locationId)
      ..writeByte(3)
      ..write(obj.resTableId)
      ..writeByte(4)
      ..write(obj.resWaiterId)
      ..writeByte(5)
      ..write(obj.resOrderStatus)
      ..writeByte(6)
      ..write(obj.type)
      ..writeByte(7)
      ..write(obj.subType)
      ..writeByte(8)
      ..write(obj.status)
      ..writeByte(9)
      ..write(obj.subStatus)
      ..writeByte(10)
      ..write(obj.isQuotation)
      ..writeByte(11)
      ..write(obj.paymentStatus)
      ..writeByte(12)
      ..write(obj.adjustmentType)
      ..writeByte(13)
      ..write(obj.contactId)
      ..writeByte(14)
      ..write(obj.customerGroupId)
      ..writeByte(15)
      ..write(obj.invoiceNo)
      ..writeByte(16)
      ..write(obj.refNo)
      ..writeByte(17)
      ..write(obj.source)
      ..writeByte(18)
      ..write(obj.subscriptionNo)
      ..writeByte(19)
      ..write(obj.subscriptionRepeatOn)
      ..writeByte(20)
      ..write(obj.transactionDate)
      ..writeByte(21)
      ..write(obj.totalBeforeTax)
      ..writeByte(22)
      ..write(obj.taxId)
      ..writeByte(23)
      ..write(obj.taxAmount)
      ..writeByte(24)
      ..write(obj.discountType)
      ..writeByte(25)
      ..write(obj.discountAmount)
      ..writeByte(26)
      ..write(obj.rpRedeemed)
      ..writeByte(27)
      ..write(obj.rpRedeemedAmount)
      ..writeByte(28)
      ..write(obj.shippingDetails)
      ..writeByte(29)
      ..write(obj.shippingAddress)
      ..writeByte(30)
      ..write(obj.deliveryDate)
      ..writeByte(31)
      ..write(obj.shippingStatus)
      ..writeByte(32)
      ..write(obj.deliveredTo)
      ..writeByte(33)
      ..write(obj.shippingCharges)
      ..writeByte(34)
      ..write(obj.shippingCustomField1)
      ..writeByte(35)
      ..write(obj.shippingCustomField2)
      ..writeByte(36)
      ..write(obj.shippingCustomField3)
      ..writeByte(37)
      ..write(obj.shippingCustomField4)
      ..writeByte(38)
      ..write(obj.shippingCustomField5)
      ..writeByte(39)
      ..write(obj.additionalNotes)
      ..writeByte(40)
      ..write(obj.staffNote)
      ..writeByte(41)
      ..write(obj.isExport)
      ..writeByte(42)
      ..write(obj.exportCustomFieldsInfo)
      ..writeByte(43)
      ..write(obj.roundOffAmount)
      ..writeByte(44)
      ..write(obj.additionalExpenseKey1)
      ..writeByte(45)
      ..write(obj.additionalExpenseValue1)
      ..writeByte(46)
      ..write(obj.additionalExpenseKey2)
      ..writeByte(47)
      ..write(obj.additionalExpenseValue2)
      ..writeByte(48)
      ..write(obj.additionalExpenseKey3)
      ..writeByte(49)
      ..write(obj.additionalExpenseValue3)
      ..writeByte(50)
      ..write(obj.additionalExpenseKey4)
      ..writeByte(51)
      ..write(obj.additionalExpenseValue4)
      ..writeByte(52)
      ..write(obj.finalTotal)
      ..writeByte(53)
      ..write(obj.expenseCategoryId)
      ..writeByte(54)
      ..write(obj.expenseFor)
      ..writeByte(55)
      ..write(obj.commissionAgent)
      ..writeByte(56)
      ..write(obj.document)
      ..writeByte(57)
      ..write(obj.isDirectSale)
      ..writeByte(58)
      ..write(obj.isSuspend)
      ..writeByte(59)
      ..write(obj.exchangeRate)
      ..writeByte(60)
      ..write(obj.totalAmountRecovered)
      ..writeByte(61)
      ..write(obj.transferParentId)
      ..writeByte(62)
      ..write(obj.returnParentId)
      ..writeByte(63)
      ..write(obj.openingStockProductId)
      ..writeByte(64)
      ..write(obj.createdBy)
      ..writeByte(65)
      ..write(obj.purchaseRequisitionIds)
      ..writeByte(66)
      ..write(obj.crmIsOrderRequest)
      ..writeByte(67)
      ..write(obj.preferPaymentMethod)
      ..writeByte(68)
      ..write(obj.preferPaymentAccount)
      ..writeByte(69)
      ..write(obj.salesOrderIds)
      ..writeByte(70)
      ..write(obj.purchaseOrderIds)
      ..writeByte(71)
      ..write(obj.customField1)
      ..writeByte(72)
      ..write(obj.customField2)
      ..writeByte(73)
      ..write(obj.customField3)
      ..writeByte(74)
      ..write(obj.customField4)
      ..writeByte(75)
      ..write(obj.mfgParentProductionPurchaseId)
      ..writeByte(76)
      ..write(obj.mfgWastedUnits)
      ..writeByte(77)
      ..write(obj.mfgProductionCost)
      ..writeByte(78)
      ..write(obj.mfgIsFinal)
      ..writeByte(79)
      ..write(obj.repairCompletedOn)
      ..writeByte(80)
      ..write(obj.repairWarrantyId)
      ..writeByte(81)
      ..write(obj.repairBrandId)
      ..writeByte(82)
      ..write(obj.repairStatusId)
      ..writeByte(83)
      ..write(obj.repairModelId)
      ..writeByte(84)
      ..write(obj.repairJobSheetId)
      ..writeByte(85)
      ..write(obj.repairDefects)
      ..writeByte(86)
      ..write(obj.repairSerialNo)
      ..writeByte(87)
      ..write(obj.repairChecklist)
      ..writeByte(88)
      ..write(obj.repairSecurityPwd)
      ..writeByte(89)
      ..write(obj.repairSecurityPattern)
      ..writeByte(90)
      ..write(obj.repairDueDate)
      ..writeByte(91)
      ..write(obj.repairDeviceId)
      ..writeByte(92)
      ..write(obj.repairUpdatesNotif)
      ..writeByte(93)
      ..write(obj.essentialsDuration)
      ..writeByte(94)
      ..write(obj.essentialsDurationUnit)
      ..writeByte(95)
      ..write(obj.essentialsAmountPerUnitDuration)
      ..writeByte(96)
      ..write(obj.essentialsAllowances)
      ..writeByte(97)
      ..write(obj.essentialsDeductions)
      ..writeByte(98)
      ..write(obj.woocommerceOrderId)
      ..writeByte(99)
      ..write(obj.importBatch)
      ..writeByte(100)
      ..write(obj.importTime)
      ..writeByte(101)
      ..write(obj.typesOfServiceId)
      ..writeByte(102)
      ..write(obj.packingCharge)
      ..writeByte(103)
      ..write(obj.packingChargeType)
      ..writeByte(104)
      ..write(obj.serviceCustomField1)
      ..writeByte(105)
      ..write(obj.serviceCustomField2)
      ..writeByte(106)
      ..write(obj.serviceCustomField3)
      ..writeByte(107)
      ..write(obj.serviceCustomField4)
      ..writeByte(108)
      ..write(obj.serviceCustomField5)
      ..writeByte(109)
      ..write(obj.serviceCustomField6)
      ..writeByte(110)
      ..write(obj.isCreatedFromApi)
      ..writeByte(111)
      ..write(obj.rpEarned)
      ..writeByte(112)
      ..write(obj.orderAddresses)
      ..writeByte(113)
      ..write(obj.isRecurring)
      ..writeByte(114)
      ..write(obj.recurInterval)
      ..writeByte(115)
      ..write(obj.recurIntervalType)
      ..writeByte(116)
      ..write(obj.recurRepetitions)
      ..writeByte(117)
      ..write(obj.recurStoppedOn)
      ..writeByte(118)
      ..write(obj.recurParentId)
      ..writeByte(119)
      ..write(obj.invoiceToken)
      ..writeByte(120)
      ..write(obj.payTermNumber)
      ..writeByte(121)
      ..write(obj.payTermType)
      ..writeByte(122)
      ..write(obj.pjtProjectId)
      ..writeByte(123)
      ..write(obj.pjtTitle)
      ..writeByte(124)
      ..write(obj.sellingPriceGroupId)
      ..writeByte(125)
      ..write(obj.createdAt)
      ..writeByte(126)
      ..write(obj.updatedAt)
      ..writeByte(127)
      ..write(obj.endDate)
      ..writeByte(128)
      ..write(obj.shippingCompanyId)
      ..writeByte(129)
      ..write(obj.packageCount)
      ..writeByte(130)
      ..write(obj.paymobOrderId)
      ..writeByte(131)
      ..write(obj.prevBalance)
      ..writeByte(132)
      ..write(obj.subCounter)
      ..writeByte(133)
      ..write(obj.customerId)
      ..writeByte(134)
      ..write(obj.supplierId)
      ..writeByte(135)
      ..write(obj.sellId)
      ..writeByte(136)
      ..write(obj.purchaseId)
      ..writeByte(137)
      ..write(obj.etaSubmissionId)
      ..writeByte(138)
      ..write(obj.etaUuid)
      ..writeByte(139)
      ..write(obj.etaLongId)
      ..writeByte(140)
      ..write(obj.etaHashKey)
      ..writeByte(141)
      ..write(obj.etaStatus)
      ..writeByte(142)
      ..write(obj.etaNotes)
      ..writeByte(143)
      ..write(obj.invoiceSchemeId)
      ..writeByte(144)
      ..write(obj.customStatus)
      ..writeByte(145)
      ..write(obj.isReservation)
      ..writeByte(146)
      ..write(obj.reviewStatus)
      ..writeByte(147)
      ..write(obj.reviewDetails)
      ..writeByte(148)
      ..write(obj.settlementPurchaseId)
      ..writeByte(149)
      ..write(obj.settlementSellId)
      ..writeByte(150)
      ..write(obj.invoiceLayoutId)
      ..writeByte(151)
      ..write(obj.invoiceCommision)
      ..writeByte(152)
      ..write(obj.commisionAsLeader)
      ..writeByte(153)
      ..write(obj.leaderId)
      ..writeByte(154)
      ..write(obj.purchaseLines)
      ..writeByte(155)
      ..write(obj.paymentLines)
      ..writeByte(156)
      ..write(obj.offline)
      ..writeByte(157)
      ..write(obj.purchaseToAPI)
      ..writeByte(158)
      ..write(obj.locationInfo);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PurchaseAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class PurchaseLineAdapter extends TypeAdapter<PurchaseLine> {
  @override
  final int typeId = 35;

  @override
  PurchaseLine read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return PurchaseLine(
      id: fields[0] as int?,
      transactionId: fields[1] as int?,
      productId: fields[2] as int?,
      variationId: fields[3] as int?,
      quantity: fields[4] as double?,
      secondaryUnitQuantity: fields[5] as String?,
      ppWithoutDiscount: fields[6] as String?,
      discountPercent: fields[7] as String?,
      purchasePrice: fields[8] as String?,
      purchasePriceIncTax: fields[9] as String?,
      itemTax: fields[10] as String?,
      taxId: fields[11] as dynamic,
      purchaseOrderLineId: fields[12] as dynamic,
      quantitySold: fields[13] as String?,
      quantityAdjusted: fields[14] as String?,
      quantityReturned: fields[15] as String?,
      poQuantityPurchased: fields[16] as String?,
      mfgQuantityUsed: fields[17] as String?,
      mfgDate: fields[18] as dynamic,
      expDate: fields[19] as dynamic,
      lotNumber: fields[20] as dynamic,
      subUnitId: fields[21] as int?,
      createdAt: fields[22] as String?,
      updatedAt: fields[23] as String?,
      packagesCounter: fields[24] as int?,
      serialNumber: fields[25] as dynamic,
      bonusQuantity: fields[26] as String?,
      isBonus: fields[27] as int?,
      x: fields[28] as dynamic,
      y: fields[29] as dynamic,
      z: fields[30] as dynamic,
      num: fields[31] as dynamic,
    );
  }

  @override
  void write(BinaryWriter writer, PurchaseLine obj) {
    writer
      ..writeByte(32)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.transactionId)
      ..writeByte(2)
      ..write(obj.productId)
      ..writeByte(3)
      ..write(obj.variationId)
      ..writeByte(4)
      ..write(obj.quantity)
      ..writeByte(5)
      ..write(obj.secondaryUnitQuantity)
      ..writeByte(6)
      ..write(obj.ppWithoutDiscount)
      ..writeByte(7)
      ..write(obj.discountPercent)
      ..writeByte(8)
      ..write(obj.purchasePrice)
      ..writeByte(9)
      ..write(obj.purchasePriceIncTax)
      ..writeByte(10)
      ..write(obj.itemTax)
      ..writeByte(11)
      ..write(obj.taxId)
      ..writeByte(12)
      ..write(obj.purchaseOrderLineId)
      ..writeByte(13)
      ..write(obj.quantitySold)
      ..writeByte(14)
      ..write(obj.quantityAdjusted)
      ..writeByte(15)
      ..write(obj.quantityReturned)
      ..writeByte(16)
      ..write(obj.poQuantityPurchased)
      ..writeByte(17)
      ..write(obj.mfgQuantityUsed)
      ..writeByte(18)
      ..write(obj.mfgDate)
      ..writeByte(19)
      ..write(obj.expDate)
      ..writeByte(20)
      ..write(obj.lotNumber)
      ..writeByte(21)
      ..write(obj.subUnitId)
      ..writeByte(22)
      ..write(obj.createdAt)
      ..writeByte(23)
      ..write(obj.updatedAt)
      ..writeByte(24)
      ..write(obj.packagesCounter)
      ..writeByte(25)
      ..write(obj.serialNumber)
      ..writeByte(26)
      ..write(obj.bonusQuantity)
      ..writeByte(27)
      ..write(obj.isBonus)
      ..writeByte(28)
      ..write(obj.x)
      ..writeByte(29)
      ..write(obj.y)
      ..writeByte(30)
      ..write(obj.z)
      ..writeByte(31)
      ..write(obj.num);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PurchaseLineAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Purchase _$PurchaseFromJson(Map<String, dynamic> json) => Purchase(
      id: (json['id'] as num?)?.toInt(),
      businessId: (json['business_id'] as num?)?.toInt(),
      locationId: (json['location_id'] as num?)?.toInt(),
      resTableId: json['res_table_id'],
      resWaiterId: json['res_waiter_id'],
      resOrderStatus: json['res_order_status'],
      type: json['type'] as String?,
      subType: json['sub_type'],
      status: json['status'] as String?,
      subStatus: json['sub_status'],
      isQuotation: (json['is_quotation'] as num?)?.toInt(),
      paymentStatus: json['payment_status'] as String?,
      adjustmentType: json['adjustment_type'],
      contactId: (json['contact_id'] as num?)?.toInt(),
      customerGroupId: json['customer_group_id'],
      invoiceNo: json['invoice_no'],
      refNo: json['ref_no'],
      source: json['source'],
      subscriptionNo: json['subscription_no'],
      subscriptionRepeatOn: json['subscription_repeat_on'],
      transactionDate: DateTime.parse(json['transaction_date'] as String),
      totalBeforeTax: json['total_before_tax'] as String?,
      taxId: json['tax_id'],
      taxAmount: json['tax_amount'] as String?,
      discountType: json['discount_type'] as String?,
      discountAmount: json['discount_amount'] as String?,
      rpRedeemed: (json['rp_redeemed'] as num?)?.toInt(),
      rpRedeemedAmount: json['rp_redeemed_amount'] as String?,
      shippingDetails: json['shipping_details'],
      shippingAddress: json['shipping_address'],
      deliveryDate: json['delivery_date'],
      shippingStatus: json['shipping_status'],
      deliveredTo: json['delivered_to'],
      shippingCharges: json['shipping_charges'] as String?,
      shippingCustomField1: json['shipping_custom_field_1'],
      shippingCustomField2: json['shipping_custom_field_2'],
      shippingCustomField3: json['shipping_custom_field_3'],
      shippingCustomField4: json['shipping_custom_field_4'],
      shippingCustomField5: json['shipping_custom_field_5'],
      additionalNotes: json['additional_notes'],
      staffNote: json['staff_note'],
      isExport: (json['is_export'] as num?)?.toInt(),
      exportCustomFieldsInfo: json['export_custom_fields_info'],
      roundOffAmount: json['round_off_amount'] as String?,
      additionalExpenseKey1: json['additional_expense_key_1'] as String?,
      additionalExpenseValue1: json['additional_expense_value_1'] as String?,
      additionalExpenseKey2: json['additional_expense_key_2'] as String?,
      additionalExpenseValue2: json['additional_expense_value_2'] as String?,
      additionalExpenseKey3: json['additional_expense_key_3'] as String?,
      additionalExpenseValue3: json['additional_expense_value_3'] as String?,
      additionalExpenseKey4: json['additional_expense_key_4'] as String?,
      additionalExpenseValue4: json['additional_expense_value_4'] as String?,
      finalTotal: json['final_total'] as String?,
      expenseCategoryId: json['expense_category_id'],
      expenseFor: json['expense_for'],
      commissionAgent: json['commission_agent'],
      document: json['document'],
      isDirectSale: (json['is_direct_sale'] as num?)?.toInt(),
      isSuspend: (json['is_suspend'] as num?)?.toInt(),
      exchangeRate: json['exchange_rate'] as String?,
      totalAmountRecovered: json['total_amount_recovered'],
      transferParentId: json['transfer_parent_id'],
      returnParentId: json['return_parent_id'],
      openingStockProductId: json['opening_stock_product_id'],
      createdBy: (json['created_by'] as num?)?.toInt(),
      purchaseRequisitionIds: json['purchase_requisition_ids'],
      crmIsOrderRequest: (json['crm_is_order_request'] as num?)?.toInt(),
      preferPaymentMethod: json['prefer_payment_method'],
      preferPaymentAccount: json['prefer_payment_account'],
      salesOrderIds: json['sales_order_ids'],
      purchaseOrderIds: json['purchase_order_ids'],
      customField1: json['custom_field_1'],
      customField2: json['custom_field_2'],
      customField3: json['custom_field_3'],
      customField4: json['custom_field_4'],
      mfgParentProductionPurchaseId: json['mfg_parent_production_purchase_id'],
      mfgWastedUnits: json['mfg_wasted_units'],
      mfgProductionCost: json['mfg_production_cost'] as String?,
      mfgIsFinal: (json['mfg_is_final'] as num?)?.toInt(),
      repairCompletedOn: json['repair_completed_on'],
      repairWarrantyId: json['repair_warranty_id'],
      repairBrandId: json['repair_brand_id'],
      repairStatusId: json['repair_status_id'],
      repairModelId: json['repair_model_id'],
      repairJobSheetId: json['repair_job_sheet_id'],
      repairDefects: json['repair_defects'],
      repairSerialNo: json['repair_serial_no'],
      repairChecklist: json['repair_checklist'],
      repairSecurityPwd: json['repair_security_pwd'],
      repairSecurityPattern: json['repair_security_pattern'],
      repairDueDate: json['repair_due_date'],
      repairDeviceId: json['repair_device_id'],
      repairUpdatesNotif: (json['repair_updates_notif'] as num?)?.toInt(),
      essentialsDuration: json['essentials_duration'] as String?,
      essentialsDurationUnit: json['essentials_duration_unit'],
      essentialsAmountPerUnitDuration:
          json['essentials_amount_per_unit_duration'] as String?,
      essentialsAllowances: json['essentials_allowances'],
      essentialsDeductions: json['essentials_deductions'],
      woocommerceOrderId: json['woocommerce_order_id'],
      importBatch: json['import_batch'],
      importTime: json['import_time'],
      typesOfServiceId: json['types_of_service_id'],
      packingCharge: json['packing_charge'],
      packingChargeType: json['packing_charge_type'],
      serviceCustomField1: json['service_custom_field_1'],
      serviceCustomField2: json['service_custom_field_2'],
      serviceCustomField3: json['service_custom_field_3'],
      serviceCustomField4: json['service_custom_field_4'],
      serviceCustomField5: json['service_custom_field_5'],
      serviceCustomField6: json['service_custom_field_6'],
      isCreatedFromApi: (json['is_created_from_api'] as num?)?.toInt(),
      rpEarned: (json['rp_earned'] as num?)?.toInt(),
      orderAddresses: json['order_addresses'],
      isRecurring: (json['is_recurring'] as num?)?.toInt(),
      recurInterval: json['recur_interval'],
      recurIntervalType: json['recur_interval_type'],
      recurRepetitions: json['recur_repetitions'],
      recurStoppedOn: json['recur_stopped_on'],
      recurParentId: json['recur_parent_id'],
      invoiceToken: json['invoice_token'] as String?,
      payTermNumber: json['pay_term_number'],
      payTermType: json['pay_term_type'],
      pjtProjectId: json['pjt_project_id'],
      pjtTitle: json['pjt_title'],
      sellingPriceGroupId: json['selling_price_group_id'],
      createdAt: json['created_at'] as String?,
      updatedAt: json['updated_at'] as String?,
      endDate: json['end_date'],
      shippingCompanyId: json['shipping_company_id'],
      packageCount: json['package_count'],
      paymobOrderId: json['paymob_order_id'],
      prevBalance: json['prev_balance'],
      subCounter: json['sub_counter'],
      customerId: json['customer_id'],
      supplierId: json['supplier_id'],
      sellId: json['sell_id'],
      purchaseId: json['purchase_id'],
      etaSubmissionId: json['eta_submissionId'],
      etaUuid: json['eta_uuid'],
      etaLongId: json['eta_longId'],
      etaHashKey: json['eta_hashKey'],
      etaStatus: json['eta_status'],
      etaNotes: json['eta_notes'],
      invoiceSchemeId: json['invoice_scheme_id'],
      customStatus: json['custom_status'],
      isReservation: json['is_reservation'],
      reviewStatus: json['review_status'],
      reviewDetails: json['review_details'],
      settlementPurchaseId: json['settlement_purchase_id'],
      settlementSellId: json['settlement_sell_id'],
      invoiceLayoutId: json['invoice_layout_id'],
      invoiceCommision: json['invoice_commision'],
      commisionAsLeader: json['commision_as_leader'] as String?,
      leaderId: json['leader_id'],
      purchaseLines:
          Purchase._purchaseLinesFromJsonList(json['purchase_lines'] as List?),
      paymentLines:
          Purchase._paymentLinesFromJsonList(json['payment_lines'] as List?),
      locationInfo: locationInfoFromListJson(json['location_info'] as List?),
    );

Map<String, dynamic> _$PurchaseToJson(Purchase instance) => <String, dynamic>{
      'id': instance.id,
      'business_id': instance.businessId,
      'location_id': instance.locationId,
      'res_table_id': instance.resTableId,
      'res_waiter_id': instance.resWaiterId,
      'res_order_status': instance.resOrderStatus,
      'type': instance.type,
      'sub_type': instance.subType,
      'status': instance.status,
      'sub_status': instance.subStatus,
      'is_quotation': instance.isQuotation,
      'payment_status': instance.paymentStatus,
      'adjustment_type': instance.adjustmentType,
      'contact_id': instance.contactId,
      'customer_group_id': instance.customerGroupId,
      'invoice_no': instance.invoiceNo,
      'ref_no': instance.refNo,
      'source': instance.source,
      'subscription_no': instance.subscriptionNo,
      'subscription_repeat_on': instance.subscriptionRepeatOn,
      'transaction_date': dateToString(instance.transactionDate),
      'total_before_tax': instance.totalBeforeTax,
      'tax_id': instance.taxId,
      'tax_amount': instance.taxAmount,
      'discount_type': instance.discountType,
      'discount_amount': instance.discountAmount,
      'rp_redeemed': instance.rpRedeemed,
      'rp_redeemed_amount': instance.rpRedeemedAmount,
      'shipping_details': instance.shippingDetails,
      'shipping_address': instance.shippingAddress,
      'delivery_date': instance.deliveryDate,
      'shipping_status': instance.shippingStatus,
      'delivered_to': instance.deliveredTo,
      'shipping_charges': instance.shippingCharges,
      'shipping_custom_field_1': instance.shippingCustomField1,
      'shipping_custom_field_2': instance.shippingCustomField2,
      'shipping_custom_field_3': instance.shippingCustomField3,
      'shipping_custom_field_4': instance.shippingCustomField4,
      'shipping_custom_field_5': instance.shippingCustomField5,
      'additional_notes': instance.additionalNotes,
      'staff_note': instance.staffNote,
      'is_export': instance.isExport,
      'export_custom_fields_info': instance.exportCustomFieldsInfo,
      'round_off_amount': instance.roundOffAmount,
      'additional_expense_key_1': instance.additionalExpenseKey1,
      'additional_expense_value_1': instance.additionalExpenseValue1,
      'additional_expense_key_2': instance.additionalExpenseKey2,
      'additional_expense_value_2': instance.additionalExpenseValue2,
      'additional_expense_key_3': instance.additionalExpenseKey3,
      'additional_expense_value_3': instance.additionalExpenseValue3,
      'additional_expense_key_4': instance.additionalExpenseKey4,
      'additional_expense_value_4': instance.additionalExpenseValue4,
      'final_total': instance.finalTotal,
      'expense_category_id': instance.expenseCategoryId,
      'expense_for': instance.expenseFor,
      'commission_agent': instance.commissionAgent,
      'document': instance.document,
      'is_direct_sale': instance.isDirectSale,
      'is_suspend': instance.isSuspend,
      'exchange_rate': instance.exchangeRate,
      'total_amount_recovered': instance.totalAmountRecovered,
      'transfer_parent_id': instance.transferParentId,
      'return_parent_id': instance.returnParentId,
      'opening_stock_product_id': instance.openingStockProductId,
      'created_by': instance.createdBy,
      'purchase_requisition_ids': instance.purchaseRequisitionIds,
      'crm_is_order_request': instance.crmIsOrderRequest,
      'prefer_payment_method': instance.preferPaymentMethod,
      'prefer_payment_account': instance.preferPaymentAccount,
      'sales_order_ids': instance.salesOrderIds,
      'purchase_order_ids': instance.purchaseOrderIds,
      'custom_field_1': instance.customField1,
      'custom_field_2': instance.customField2,
      'custom_field_3': instance.customField3,
      'custom_field_4': instance.customField4,
      'mfg_parent_production_purchase_id':
          instance.mfgParentProductionPurchaseId,
      'mfg_wasted_units': instance.mfgWastedUnits,
      'mfg_production_cost': instance.mfgProductionCost,
      'mfg_is_final': instance.mfgIsFinal,
      'repair_completed_on': instance.repairCompletedOn,
      'repair_warranty_id': instance.repairWarrantyId,
      'repair_brand_id': instance.repairBrandId,
      'repair_status_id': instance.repairStatusId,
      'repair_model_id': instance.repairModelId,
      'repair_job_sheet_id': instance.repairJobSheetId,
      'repair_defects': instance.repairDefects,
      'repair_serial_no': instance.repairSerialNo,
      'repair_checklist': instance.repairChecklist,
      'repair_security_pwd': instance.repairSecurityPwd,
      'repair_security_pattern': instance.repairSecurityPattern,
      'repair_due_date': instance.repairDueDate,
      'repair_device_id': instance.repairDeviceId,
      'repair_updates_notif': instance.repairUpdatesNotif,
      'essentials_duration': instance.essentialsDuration,
      'essentials_duration_unit': instance.essentialsDurationUnit,
      'essentials_amount_per_unit_duration':
          instance.essentialsAmountPerUnitDuration,
      'essentials_allowances': instance.essentialsAllowances,
      'essentials_deductions': instance.essentialsDeductions,
      'woocommerce_order_id': instance.woocommerceOrderId,
      'import_batch': instance.importBatch,
      'import_time': instance.importTime,
      'types_of_service_id': instance.typesOfServiceId,
      'packing_charge': instance.packingCharge,
      'packing_charge_type': instance.packingChargeType,
      'service_custom_field_1': instance.serviceCustomField1,
      'service_custom_field_2': instance.serviceCustomField2,
      'service_custom_field_3': instance.serviceCustomField3,
      'service_custom_field_4': instance.serviceCustomField4,
      'service_custom_field_5': instance.serviceCustomField5,
      'service_custom_field_6': instance.serviceCustomField6,
      'is_created_from_api': instance.isCreatedFromApi,
      'rp_earned': instance.rpEarned,
      'order_addresses': instance.orderAddresses,
      'is_recurring': instance.isRecurring,
      'recur_interval': instance.recurInterval,
      'recur_interval_type': instance.recurIntervalType,
      'recur_repetitions': instance.recurRepetitions,
      'recur_stopped_on': instance.recurStoppedOn,
      'recur_parent_id': instance.recurParentId,
      'invoice_token': instance.invoiceToken,
      'pay_term_number': instance.payTermNumber,
      'pay_term_type': instance.payTermType,
      'pjt_project_id': instance.pjtProjectId,
      'pjt_title': instance.pjtTitle,
      'selling_price_group_id': instance.sellingPriceGroupId,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
      'end_date': instance.endDate,
      'shipping_company_id': instance.shippingCompanyId,
      'package_count': instance.packageCount,
      'paymob_order_id': instance.paymobOrderId,
      'prev_balance': instance.prevBalance,
      'sub_counter': instance.subCounter,
      'customer_id': instance.customerId,
      'supplier_id': instance.supplierId,
      'sell_id': instance.sellId,
      'purchase_id': instance.purchaseId,
      'eta_submissionId': instance.etaSubmissionId,
      'eta_uuid': instance.etaUuid,
      'eta_longId': instance.etaLongId,
      'eta_hashKey': instance.etaHashKey,
      'eta_status': instance.etaStatus,
      'eta_notes': instance.etaNotes,
      'invoice_scheme_id': instance.invoiceSchemeId,
      'custom_status': instance.customStatus,
      'is_reservation': instance.isReservation,
      'review_status': instance.reviewStatus,
      'review_details': instance.reviewDetails,
      'settlement_purchase_id': instance.settlementPurchaseId,
      'settlement_sell_id': instance.settlementSellId,
      'invoice_layout_id': instance.invoiceLayoutId,
      'invoice_commision': instance.invoiceCommision,
      'commision_as_leader': instance.commisionAsLeader,
      'leader_id': instance.leaderId,
      'purchase_lines':
          Purchase._purchaseLinesToJsonList(instance.purchaseLines),
      'payment_lines': Purchase._paymentLinesToJsonList(instance.paymentLines),
      'location_info': instance.locationInfo,
    };

PurchaseLine _$PurchaseLineFromJson(Map<String, dynamic> json) => PurchaseLine(
      id: (json['id'] as num?)?.toInt(),
      transactionId: (json['transaction_id'] as num?)?.toInt(),
      productId: (json['product_id'] as num?)?.toInt(),
      variationId: (json['variation_id'] as num?)?.toInt(),
      quantity: (json['quantity'] as num?)?.toDouble(),
      secondaryUnitQuantity: json['secondary_unit_quantity'] as String?,
      ppWithoutDiscount: json['pp_without_discount'] as String?,
      discountPercent: json['discount_percent'] as String?,
      purchasePrice: json['purchase_price'] as String?,
      purchasePriceIncTax: json['purchase_price_inc_tax'] as String?,
      itemTax: json['item_tax'] as String?,
      taxId: json['tax_id'],
      purchaseOrderLineId: json['purchase_order_line_id'],
      quantitySold: json['quantity_sold'] as String?,
      quantityAdjusted: json['quantity_adjusted'] as String?,
      quantityReturned: json['quantity_returned'] as String?,
      poQuantityPurchased: json['po_quantity_purchased'] as String?,
      mfgQuantityUsed: json['mfg_quantity_used'] as String?,
      mfgDate: json['mfg_date'],
      expDate: json['exp_date'],
      lotNumber: json['lot_number'],
      subUnitId: (json['sub_unit_id'] as num?)?.toInt(),
      createdAt: json['created_at'] as String?,
      updatedAt: json['updated_at'] as String?,
      packagesCounter: (json['packages_counter'] as num?)?.toInt(),
      serialNumber: json['serial_number'],
      bonusQuantity: json['bonus_quantity'] as String?,
      isBonus: (json['is_bonus'] as num?)?.toInt(),
      x: json['x'],
      y: json['y'],
      z: json['z'],
      num: json['num'],
    );

Map<String, dynamic> _$PurchaseLineToJson(PurchaseLine instance) =>
    <String, dynamic>{
      'id': instance.id,
      'transaction_id': instance.transactionId,
      'product_id': instance.productId,
      'variation_id': instance.variationId,
      'quantity': instance.quantity,
      'secondary_unit_quantity': instance.secondaryUnitQuantity,
      'pp_without_discount': instance.ppWithoutDiscount,
      'discount_percent': instance.discountPercent,
      'purchase_price': instance.purchasePrice,
      'purchase_price_inc_tax': instance.purchasePriceIncTax,
      'item_tax': instance.itemTax,
      'tax_id': instance.taxId,
      'purchase_order_line_id': instance.purchaseOrderLineId,
      'quantity_sold': instance.quantitySold,
      'quantity_adjusted': instance.quantityAdjusted,
      'quantity_returned': instance.quantityReturned,
      'po_quantity_purchased': instance.poQuantityPurchased,
      'mfg_quantity_used': instance.mfgQuantityUsed,
      'mfg_date': instance.mfgDate,
      'exp_date': instance.expDate,
      'lot_number': instance.lotNumber,
      'sub_unit_id': instance.subUnitId,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
      'packages_counter': instance.packagesCounter,
      'serial_number': instance.serialNumber,
      'bonus_quantity': instance.bonusQuantity,
      'is_bonus': instance.isBonus,
      'x': instance.x,
      'y': instance.y,
      'z': instance.z,
      'num': instance.num,
    };
