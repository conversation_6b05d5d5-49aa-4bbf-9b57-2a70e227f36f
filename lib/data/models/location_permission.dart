import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:go_router/go_router.dart';

import '../repository/api_repo.dart';

class LocationDisclosureDialog extends StatelessWidget {
  const LocationDisclosureDialog({super.key});

  @override
  Widget build(BuildContext context) {
    final strings = AppLocalizations.of(context)!;
    return AlertDialog(
      title: Text(strings.permission_disclosure_title),
      content: Text(strings.permission_disclosure,textAlign: TextAlign.center),
      actions: <Widget>[
        FilledButton(
          child: Text(strings.okay),
          onPressed: () {
            context.pop();
            ApiRepository.get().getCurrentLocation();
          },
        ),
      ],
    );
  }
}
