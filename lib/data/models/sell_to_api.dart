import 'package:hive/hive.dart';
import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:we2up/data/models/location_info.dart';
import 'package:we2up/data/models/payment.dart';

import '../../utils/we2up_constants.dart';

part 'sell_to_api.g.dart';

@JsonSerializable()
@HiveType(typeId: 22)
class SellToAPI extends Equatable {
  @HiveField(0)
  @JsonKey(name: 'location_id')
  final int locationId;

  @HiveField(1)
  @JsonKey(name: 'contact_id')
  final int contactId;

  @HiveField(2)
  @JsonKey(name: 'transaction_date')
  final String transactionDate;

  @HiveField(3)
  @JsonKey(name: 'invoice_no')
  final String? invoiceNo;

  @HiveField(4)
  @JsonKey(name: 'source')
  final String? source;

  @HiveField(5)
  @JsonKey(name: 'status')
  final String status;

  @HiveField(6)
  @JsonKey(name: 'sub_status')
  final String? subStatus;

  @HiveField(7)
  @JsonKey(name: 'is_quotation')
  final int? isQuotation;

  @HiveField(8)
  @JsonKey(name: 'tax_rate_id')
  final int? taxRateId;

  @HiveField(9)
  @JsonKey(name: 'discount_amount')
  final double discountAmount;

  @HiveField(10)
  @JsonKey(name: 'discount_type')
  final String? discountType;

  @HiveField(11)
  @JsonKey(name: 'sale_note')
  final String? saleNote;

  @HiveField(12)
  @JsonKey(name: 'staff_note')
  final String? staffNote;

  @HiveField(13)
  @JsonKey(name: 'commission_agent')
  final int? commissionAgent;

  @HiveField(14)
  @JsonKey(name: 'shipping_details')
  final String? shippingDetails;

  @HiveField(15)
  @JsonKey(name: 'shipping_address')
  final String? shippingAddress;

  @HiveField(16)
  @JsonKey(name: 'shipping_status')
  final String? shippingStatus;

  @HiveField(17)
  @JsonKey(name: 'delivered_to')
  final String? deliveredTo;

  @HiveField(18)
  @JsonKey(name: 'shipping_charges')
  final double? shippingCharges;

  @HiveField(19)
  @JsonKey(name: 'packing_charge')
  final double? packingCharge;

  @HiveField(20)
  @JsonKey(name: 'exchange_rate')
  final int? exchangeRate;

  @HiveField(21)
  @JsonKey(name: 'selling_price_group_id')
  final int? sellingPriceGroupId;

  @HiveField(22)
  @JsonKey(name: 'pay_term_number')
  final int? payTermNumber;

  @HiveField(23)
  @JsonKey(name: 'pay_term_type')
  final String? payTermType;

  @HiveField(24)
  @JsonKey(name: 'is_suspend')
  final bool? isSuspend;

  @HiveField(25)
  @JsonKey(name: 'is_recurring')
  final int? isRecurring;

  @HiveField(26)
  @JsonKey(name: 'recur_interval')
  final int? recurInterval;

  @HiveField(27)
  @JsonKey(name: 'recur_interval_type')
  final String? recurIntervalType;

  @HiveField(28)
  @JsonKey(name: 'subscription_repeat_on')
  final int? subscriptionRepeatOn;

  @HiveField(29)
  @JsonKey(name: 'subscription_no')
  final String? subscriptionNo;

  @HiveField(30)
  @JsonKey(name: 'recur_repetitions')
  final int? recurRepetitions;

  @HiveField(31)
  @JsonKey(name: 'rp_redeemed')
  final int? rpRedeemed;

  @HiveField(32)
  @JsonKey(name: 'rp_redeemed_amount')
  final double? rpRedeemedAmount;

  @HiveField(33)
  @JsonKey(name: 'types_of_service_id')
  final int? typesOfServiceId;

  @HiveField(34)
  @JsonKey(name: 'service_custom_field_1')
  final String? serviceCustomField1;

  @HiveField(35)
  @JsonKey(name: 'service_custom_field_2')
  final String? serviceCustomField2;

  @HiveField(36)
  @JsonKey(name: 'service_custom_field_3')
  final String? serviceCustomField3;

  @HiveField(37)
  @JsonKey(name: 'service_custom_field_4')
  final String? serviceCustomField4;

  @HiveField(38)
  @JsonKey(name: 'service_custom_field_5')
  final String? serviceCustomField5;

  @HiveField(39)
  @JsonKey(name: 'service_custom_field_6')
  final String? serviceCustomField6;

  @HiveField(40)
  @JsonKey(name: 'round_off_amount')
  final double? roundOffAmount;

  @HiveField(41)
  @JsonKey(name: 'table_id')
  final int? tableId;

  @HiveField(42)
  @JsonKey(name: 'service_staff_id')
  final int? serviceStaffId;

  @HiveField(43)
  @JsonKey(name: 'change_return')
  final double? changeReturn;

  @HiveField(44)
  @JsonKey(toJson: _productsToJson, fromJson: _productsFromJson)
  final List<ProductToAPI> products;

  @HiveField(45)
  @JsonKey(toJson: _toJsonList, fromJson: _fromJsonList)
  final List<Payment> payments;

  @HiveField(46)
  @JsonKey(includeFromJson: false, includeToJson: false)
  final String invoiceAmount;

  @HiveField(47)
  @JsonKey(includeFromJson: false, includeToJson: false)
  final String? offlineID;

  @HiveField(48)
  @JsonKey(name: 'location_info', toJson: locationInfoToJson)
  final LocationInfo? locationInfo;

  @HiveField(49)
  @JsonKey(name: 'packing_charge_type')
  final String? packingChargeType;

  @HiveField(50)
  @JsonKey(name: 'shipping_company_id')
  final int? shippingCompanyId;

  const SellToAPI({
    required this.locationId,
    required this.contactId,
    required this.transactionDate,
    this.invoiceNo,
    this.source,
    required this.status,
    this.subStatus,
    this.isQuotation,
    this.taxRateId,
    required this.discountAmount,
    this.discountType,
    this.saleNote,
    this.staffNote,
    this.commissionAgent,
    this.shippingDetails,
    this.shippingAddress,
    this.shippingStatus,
    this.deliveredTo,
    this.shippingCharges,
    this.packingCharge,
    this.exchangeRate,
    this.sellingPriceGroupId,
    this.payTermNumber,
    this.payTermType,
    this.isSuspend,
    this.isRecurring,
    this.recurInterval,
    this.recurIntervalType,
    this.subscriptionRepeatOn,
    this.subscriptionNo,
    this.recurRepetitions,
    this.rpRedeemed,
    this.rpRedeemedAmount,
    this.typesOfServiceId,
    this.serviceCustomField1,
    this.serviceCustomField2,
    this.serviceCustomField3,
    this.serviceCustomField4,
    this.serviceCustomField5,
    this.serviceCustomField6,
    this.roundOffAmount,
    this.tableId,
    this.serviceStaffId,
    this.changeReturn,
    required this.products,
    required this.payments,
    this.invoiceAmount = "",
    this.offlineID,
    this.locationInfo,
    this.packingChargeType,
    this.shippingCompanyId,
  });

  SellToAPI copyWith({
    int? locationId,
    int? contactId,
    String? transactionDate,
    String? invoiceNo,
    String? source,
    String? status,
    String? subStatus,
    int? isQuotation,
    int? taxRateId,
    double? discountAmount,
    String? discountType,
    String? saleNote,
    String? staffNote,
    int? commissionAgent,
    String? shippingDetails,
    String? shippingAddress,
    String? shippingStatus,
    String? deliveredTo,
    double? shippingCharges,
    double? packingCharge,
    int? exchangeRate,
    int? sellingPriceGroupId,
    int? payTermNumber,
    String? payTermType,
    bool? isSuspend,
    int? isRecurring,
    int? recurInterval,
    String? recurIntervalType,
    int? subscriptionRepeatOn,
    String? subscriptionNo,
    int? recurRepetitions,
    int? rpRedeemed,
    double? rpRedeemedAmount,
    int? typesOfServiceId,
    String? serviceCustomField1,
    String? serviceCustomField2,
    String? serviceCustomField3,
    String? serviceCustomField4,
    String? serviceCustomField5,
    String? serviceCustomField6,
    double? roundOffAmount,
    int? tableId,
    int? serviceStaffId,
    double? changeReturn,
    List<ProductToAPI>? products,
    List<Payment>? payments,
    String? invoiceAmount,
    String? offlineID,
    LocationInfo? locationInfo,
    String? packingChargeType,
    int? shippingCompanyId,
  }) {
    return SellToAPI(
      locationId: locationId ?? this.locationId,
      contactId: contactId ?? this.contactId,
      transactionDate: transactionDate ?? this.transactionDate,
      invoiceNo: invoiceNo ?? this.invoiceNo,
      source: source ?? this.source,
      status: status ?? this.status,
      subStatus: subStatus ?? this.subStatus,
      isQuotation: isQuotation ?? this.isQuotation,
      taxRateId: taxRateId ?? this.taxRateId,
      discountAmount: discountAmount ?? this.discountAmount,
      discountType: discountType ?? this.discountType,
      saleNote: saleNote ?? this.saleNote,
      staffNote: staffNote ?? this.staffNote,
      commissionAgent: commissionAgent ?? this.commissionAgent,
      shippingDetails: shippingDetails ?? this.shippingDetails,
      shippingAddress: shippingAddress ?? this.shippingAddress,
      shippingStatus: shippingStatus ?? this.shippingStatus,
      deliveredTo: deliveredTo ?? this.deliveredTo,
      shippingCharges: shippingCharges ?? this.shippingCharges,
      packingCharge: packingCharge ?? this.packingCharge,
      exchangeRate: exchangeRate ?? this.exchangeRate,
      sellingPriceGroupId: sellingPriceGroupId ?? this.sellingPriceGroupId,
      payTermNumber: payTermNumber ?? this.payTermNumber,
      payTermType: payTermType ?? this.payTermType,
      isSuspend: isSuspend ?? this.isSuspend,
      isRecurring: isRecurring ?? this.isRecurring,
      recurInterval: recurInterval ?? this.recurInterval,
      recurIntervalType: recurIntervalType ?? this.recurIntervalType,
      subscriptionRepeatOn: subscriptionRepeatOn ?? this.subscriptionRepeatOn,
      subscriptionNo: subscriptionNo ?? this.subscriptionNo,
      recurRepetitions: recurRepetitions ?? this.recurRepetitions,
      rpRedeemed: rpRedeemed ?? this.rpRedeemed,
      rpRedeemedAmount: rpRedeemedAmount ?? this.rpRedeemedAmount,
      typesOfServiceId: typesOfServiceId ?? this.typesOfServiceId,
      serviceCustomField1: serviceCustomField1 ?? this.serviceCustomField1,
      serviceCustomField2: serviceCustomField2 ?? this.serviceCustomField2,
      serviceCustomField3: serviceCustomField3 ?? this.serviceCustomField3,
      serviceCustomField4: serviceCustomField4 ?? this.serviceCustomField4,
      serviceCustomField5: serviceCustomField5 ?? this.serviceCustomField5,
      serviceCustomField6: serviceCustomField6 ?? this.serviceCustomField6,
      roundOffAmount: roundOffAmount ?? this.roundOffAmount,
      tableId: tableId ?? this.tableId,
      serviceStaffId: serviceStaffId ?? this.serviceStaffId,
      changeReturn: changeReturn ?? this.changeReturn,
      products: products ?? this.products,
      payments: payments ?? this.payments,
      invoiceAmount: invoiceAmount ?? this.invoiceAmount,
      offlineID: offlineID ?? this.offlineID,
      locationInfo: locationInfo ?? this.locationInfo,
      packingChargeType: packingChargeType ?? this.packingChargeType,
      shippingCompanyId: shippingCompanyId ?? this.shippingCompanyId,
    );
  }

  static List<Map<String, dynamic>> _productsToJson(
      List<ProductToAPI> products) {
    return products.map((product) => product.toJson()).toList();
  }

  static List<ProductToAPI> _productsFromJson(List<Map<String, dynamic>> json) {
    return json.map((map) => ProductToAPI.fromJson(map)).toList();
  }

  static List<Map<String, dynamic>> _toJsonList(List<Payment> list) {
    return list.map((item) => item.toJson()).toList();
  }

  static List<Payment> _fromJsonList(List<Map<String, dynamic>> json) {
    return json.map((map) => Payment.fromJson(map)).toList();
  }

  factory SellToAPI.fromJson(Map<String, dynamic> json) =>
      _$SellToAPIFromJson(json);

  Map<String, dynamic> toJson() => _$SellToAPIToJson(this);

  @override
  List<Object?> get props => [
        locationId,
        contactId,
        transactionDate,
        invoiceNo,
        source,
        status,
        subStatus,
        isQuotation,
        taxRateId,
        discountAmount,
        discountType,
        saleNote,
        staffNote,
        commissionAgent,
        shippingDetails,
        shippingAddress,
        shippingStatus,
        deliveredTo,
        shippingCharges,
        packingCharge,
        exchangeRate,
        sellingPriceGroupId,
        payTermNumber,
        payTermType,
        isSuspend,
        isRecurring,
        recurInterval,
        recurIntervalType,
        subscriptionRepeatOn,
        subscriptionNo,
        recurRepetitions,
        rpRedeemed,
        rpRedeemedAmount,
        typesOfServiceId,
        serviceCustomField1,
        serviceCustomField2,
        serviceCustomField3,
        serviceCustomField4,
        serviceCustomField5,
        serviceCustomField6,
        roundOffAmount,
        tableId,
        serviceStaffId,
        changeReturn,
        products,
        payments,
        offlineID,
        locationInfo,
      ];
}

@JsonSerializable()
@HiveType(typeId: 23)
class ProductToAPI extends Equatable {
  @HiveField(0)
  @JsonKey(name: 'product_id', toJson: removeLastDigit)
  final int productId;

  @HiveField(1)
  @JsonKey(name: 'variation_id')
  final int? variationId;

  @HiveField(2)
  @JsonKey(name: 'quantity')
  final double quantity;

  @HiveField(3)
  @JsonKey(name: 'unit_price')
  final double unitPrice;

  @HiveField(4)
  @JsonKey(name: 'tax_rate_id')
  final int? taxRateId;

  @HiveField(5)
  @JsonKey(name: 'discount_amount')
  final double discountAmount;

  @HiveField(6)
  @JsonKey(name: 'discount_type')
  final String? discountType;

  @HiveField(7)
  @JsonKey(name: 'sub_unit_id')
  final int subUnitId;

  @HiveField(8)
  @JsonKey(name: 'note')
  final String note;

  @HiveField(9)
  @JsonKey(name: 'unit_weight')
  final String? unitWeight;

  const ProductToAPI({
    required this.productId,
    this.variationId,
    required this.quantity,
    required this.unitPrice,
    this.taxRateId,
    required this.discountAmount,
    this.discountType,
    required this.subUnitId,
    this.note = "",
    this.unitWeight,
  });

  factory ProductToAPI.fromJson(Map<String, dynamic> json) =>
      _$ProductToAPIFromJson(json);

  Map<String, dynamic> toJson() => _$ProductToAPIToJson(this);

  @override
  List<Object?> get props => [
        productId,
        variationId,
        quantity,
        unitPrice,
        taxRateId,
        discountAmount,
        discountType,
        subUnitId,
        note,
        unitWeight,
      ];
}
