import 'package:hive/hive.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

part 'payment_status.g.dart';

@HiveType(typeId: 51)
enum PaymentStatus {
  @HiveField(0)
  paid,
  @HiveField(1)
  due,
  @HiveField(2)
  partial,
  @HiveField(3)
  offline,
}

String paymentStatusToJson(PaymentStatus status) {
  return status.toString().split('.').last;
}

PaymentStatus paymentStatusFromJson(String? status) {
  switch (status) {
    case 'paid':
      return PaymentStatus.paid;
    case 'due':
      return PaymentStatus.due;
    case 'partial':
      return PaymentStatus.partial;
    case 'final':
      return PaymentStatus.paid;
    case 'offline':
      return PaymentStatus.offline;
    default:
      return PaymentStatus.paid;
  }
}


extension AppLocalizationsExtension on AppLocalizations {
  String translatePaymentStatus(PaymentStatus? status) {
    switch (status) {
      case PaymentStatus.paid:
        return paid;
      case PaymentStatus.due:
        return credit;
      case PaymentStatus.partial:
        return partial;
      case PaymentStatus.offline:
        return offline;
      default:
        return paid;
    }
  }
}