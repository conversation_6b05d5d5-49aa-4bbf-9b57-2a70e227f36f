import 'dart:convert';

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:hive/hive.dart';

import '../../utils/converters/boolean_converter.dart';
import '../../utils/converters/date_time_converter.dart';
import '../../utils/converters/discount_type_converter.dart';
import '../../utils/converters/double_converter.dart';
import '../../utils/we2up_constants.dart';

part 'product.freezed.dart';

part 'product.g.dart';

@HiveType(typeId: 13)
@freezed
class Product with _$Product {
  const factory Product({
    @HiveField(0) required int id,
    @HiveField(1) @Default("N/A") String name,
    @HiveField(2) @JsonKey(name: 'business_id') required int businessId,
    @HiveField(3) String? type,
    @HiveField(4) @JsonKey(name: 'sub_unit_ids') List<String>? subUnitIds,
    @HiveField(5) @JsonKey(name: 'enable_stock') required int enableStock,
    @HiveField(6) @JsonKey(name: 'alert_quantity') String? alertQuantity,
    @HiveField(7) String? sku,
    @HiveField(8) @JsonKey(name: 'barcode_type') String? barcodeType,
    @HiveField(9) @JsonKey(name: 'expiry_period') String? expiryPeriod,
    @HiveField(10)
    @JsonKey(name: 'expiry_period_type')
    String? expiryPeriodType,
    @HiveField(11) String? weight,
    @HiveField(12)
    @JsonKey(name: 'product_description')
    String? productDescription,
    @HiveField(13) @JsonKey(name: 'created_by') int? createdBy,
    @HiveField(14) @JsonKey(name: 'is_inactive') int? isInactive,
    @HiveField(15) @JsonKey(name: 'not_for_selling') int? notForSelling,
    @HiveField(16) @JsonKey(name: 'max_in_invoice') int? maxInInvoice,
    @HiveField(17) @JsonKey(name: 'max_discount') int? maxDiscount,
    @HiveField(18) @JsonKey(name: 'in_offer') int? inOffer,
    @HiveField(19) @JsonKey(name: 'image_url') required String imageUrl,
    @HiveField(20)
    @JsonKey(name: 'product_variations')
    required List<ProductVariation> productVariations,
    @HiveField(21) Brand? brand,
    @HiveField(22) required Unit unit,
    @HiveField(23) ProductCategory? category,
    @HiveField(24) @JsonKey(name: 'sub_category') ProductCategory? subCategory,
    @HiveField(25) @JsonKey(name: 'product_tax') TaxRate? productTax,
    @HiveField(26)
    @JsonKey(name: 'product_locations')
    required List<ProductLocation> productLocations,
  }) = _Product;

  factory Product.fromJson(Map<String, dynamic> json) =>
      _$ProductFromJson(json);
}

@HiveType(typeId: 1)
@freezed
class ProductCategory with _$ProductCategory {
  const factory ProductCategory({
    @HiveField(0) required int id,
    @HiveField(1) @JsonKey(name: 'business_id') required int businessId,
    @HiveField(2) @Default("N/A") String name,
    @HiveField(3) @JsonKey(name: 'short_code') String? shortCode,
    @HiveField(4) @JsonKey(name: 'parent_id') int? parentId,
    @HiveField(5) @JsonKey(name: 'created_by') int? createdBy,
    @HiveField(6) @JsonKey(name: 'category_type') String? categoryType,
    @HiveField(7) String? description,
    @HiveField(8)
    @Default(_placeholderImageUrl)
    @JsonKey(name: 'image_url')
    String imageUrl,
    @HiveField(9)
    @Default([])
    @JsonKey(name: 'sub_categories')
    List<ProductCategory> subCategories,
  }) = _ProductCategory;

  factory ProductCategory.fromJson(Map<String, dynamic> json) =>
      _$ProductCategoryFromJson(json);
}

@HiveType(typeId: 3)
@freezed
class Brand with _$Brand {
  const factory Brand({
    @HiveField(0) @JsonKey(name: 'id') int? id,
    @HiveField(1) @JsonKey(name: 'business_id') int? businessId,
    @HiveField(2) @JsonKey(name: 'name') String? name,
    @HiveField(3) @JsonKey(name: 'description') String? description,
    @HiveField(4) @JsonKey(name: 'created_by') int? createdBy,
    @HiveField(5) @JsonKey(name: 'deleted_at') DateTime? deletedAt,
    @HiveField(6) @JsonKey(name: 'created_at') DateTime? createdAt,
    @HiveField(7) @JsonKey(name: 'updated_at') DateTime? updatedAt,
  }) = _Brand;

  factory Brand.fromJson(Map<String, dynamic> json) => _$BrandFromJson(json);
}

@HiveType(typeId: 12)
@freezed
class ProductLocation with _$ProductLocation {
  const factory ProductLocation({
    @HiveField(0) required int id,
    @HiveField(1) @JsonKey(name: 'business_id') int? businessId,
    @HiveField(2) @JsonKey(name: 'location_id') String? locationId,
    @HiveField(3) @Default("N/A") String name,
    @HiveField(4) @JsonKey(name: 'is_active') int? isActive,
    @HiveField(5)
    @JsonKey(name: 'default_payment_accounts', fromJson: _decodeJsonString)
    Map<String, dynamic>? defaultPaymentAccounts,
    @HiveField(6) @JsonKey(name: 'created_at') DateTime? createdAt,
    @HiveField(7) @JsonKey(name: 'updated_at') DateTime? updatedAt,
    @HiveField(8)
    @JsonKey(name: 'purchase_account_id')
    String? purchaseAccountId,
    @HiveField(9) @JsonKey(name: 'show_qty_info') int? showQtyInfo,
    @HiveField(10) @JsonKey(name: 'activity_code') String? activityCode,
  }) = _ProductLocation;

  factory ProductLocation.fromJson(Map<String, dynamic> json) =>
      _$ProductLocationFromJson(json);
}

@HiveType(typeId: 8)
@freezed
class ProductVariation with _$ProductVariation {
  const factory ProductVariation({
    @HiveField(0) int? id,
    @HiveField(1) String? name,
    @HiveField(2) @JsonKey(name: 'product_id') int? productId,
    @HiveField(3) @JsonKey(name: 'created_at') DateTime? createdAt,
    @HiveField(4) @JsonKey(name: 'updated_at') DateTime? updatedAt,
    @HiveField(5) List<Variation>? variations,
  }) = _ProductVariation;

  factory ProductVariation.fromJson(Map<String, dynamic> json) =>
      _$ProductVariationFromJson(json);
}

@HiveType(typeId: 9)
@freezed
class Variation with _$Variation {
  const factory Variation({
    @HiveField(0) required int id,
    @HiveField(1) @JsonKey(name: 'product_id') required int productId,
    @HiveField(2) required String name,
    @HiveField(3) @JsonKey(name: 'sub_sku') required String subSku,
    @HiveField(4)
    @JsonKey(name: 'product_variation_id')
    required int productVariationId,
    @HiveField(5)
    @JsonKey(name: 'default_purchase_price')
    @DoubleConverter()
    required double defaultPurchasePrice,
    @HiveField(6)
    @JsonKey(name: 'dpp_inc_tax')
    @DoubleConverter()
    required double dppIncTax,
    @HiveField(7)
    @JsonKey(name: 'profit_percent')
    @DoubleConverter()
    required double profitPercent,
    @HiveField(8)
    @JsonKey(name: 'default_sell_price')
    @DoubleConverter()
    required double defaultSellPrice,
    @HiveField(9)
    @JsonKey(name: 'sell_price_inc_tax')
    @DoubleConverter()
    required double sellPriceIncTax,
    @HiveField(10) @JsonKey(name: 'created_at') required DateTime createdAt,
    @HiveField(11) @JsonKey(name: 'updated_at') required DateTime updatedAt,
    @HiveField(12)
    @JsonKey(name: 'variation_location_details')
    required List<VariationLocationDetails> variationLocationDetails,
    @HiveField(13) required List<Media> media,
    @HiveField(14) @Default([]) List<Discount> discounts,
    @HiveField(15)
    @JsonKey(name: 'selling_price_group')
    @Default([])
    List<ProductPriceGroup?> sellingPriceGroups,
    @HiveField(16)
    @JsonKey(name: 'combo_variations')
    @Default([])
    List<ComboVariation?> comboVariations,
  }) = _Variation;

  factory Variation.fromJson(Map<String, dynamic> json) =>
      _$VariationFromJson(json);
}

@HiveType(typeId: 10)
@freezed
class VariationLocationDetails with _$VariationLocationDetails {
  const factory VariationLocationDetails({
    @HiveField(0) required int id,
    @HiveField(1) @JsonKey(name: 'product_id') required int productId,
    @HiveField(2)
    @JsonKey(name: 'product_variation_id')
    required int productVariationId,
    @HiveField(3) @JsonKey(name: 'variation_id') required int variationId,
    @HiveField(4) @JsonKey(name: 'location_id') required int locationId,
    @HiveField(5)
    @JsonKey(name: 'qty_available', fromJson: _parseDouble)
    required double qtyAvailable,
    @HiveField(6)
    @JsonKey(name: 'created_at', fromJson: DateTime.parse)
    required DateTime createdAt,
    @HiveField(7)
    @JsonKey(name: 'updated_at', fromJson: DateTime.parse)
    required DateTime updatedAt,
  }) = _VariationLocationDetails;

  factory VariationLocationDetails.fromJson(Map<String, dynamic> json) =>
      _$VariationLocationDetailsFromJson(json);
}

@freezed
class Discount with _$Discount {
  const factory Discount({
    required int id,
    @Default("N/A") String name,
    @JsonKey(name: 'business_id') required int businessId,
    @JsonKey(name: 'brand_id') int? brandId,
    @JsonKey(name: 'category_id') int? categoryId,
    @JsonKey(name: 'location_id') required int locationId,
    @Default(1) int priority,
    @JsonKey(name: 'discount_type')
    @Default(DiscountType.percentage)
    @DiscountTypeConverter()
    DiscountType discountType,
    @JsonKey(name: 'discount_amount')
    @Default(0.0)
    @DoubleConverter()
    double discountAmount,
    @JsonKey(name: 'starts_at') @DateTimeConverter() required DateTime startsAt,
    @JsonKey(name: 'ends_at') @DateTimeConverter() required DateTime endsAt,
    @JsonKey(name: 'is_active')
    @BooleanConverter()
    @Default(true)
    bool isActive,
    String? spg,
    @JsonKey(name: 'applicable_in_cg')
    @Default(false)
    @BooleanConverter()
    bool applicableInCg,
    @JsonKey(name: 'created_at')
    @DateTimeConverter()
    required DateTime createdAt,
    @JsonKey(name: 'updated_at')
    @DateTimeConverter()
    required DateTime updatedAt,
    @JsonKey(name: 'price_group_id') @Default("0") String priceGroupId,
    @JsonKey(name: 'applicable_in_spg')
    @Default(false)
    @BooleanConverter()
    bool applicableInSpg,
    @JsonKey(name: 'max_qty')
    @Default(double.infinity)
    @DoubleConverter()
    double maxQty,
    String? coupon,
    @JsonKey(name: 'formated_starts_at') @Default("") String formatedStartsAt,
    @JsonKey(name: 'formated_ends_at') @Default("") String formatedEndsAt,
  }) = _Discount;

  factory Discount.fromJson(Map<String, dynamic> json) =>
      _$DiscountFromJson(json);
}

enum DiscountType { percentage, fixed }

@HiveType(typeId: 11)
@freezed
class Media with _$Media {
  const factory Media({
    @HiveField(0) required int id,
    @HiveField(1) @JsonKey(name: 'business_id') required int businessId,
    @HiveField(2) @JsonKey(name: 'media_name') String? mediaName,
    @HiveField(3) @JsonKey(name: 'model_media_type') String? modelMediaType,
    @HiveField(4) @JsonKey(name: 'display_url') String? displayUrl,
  }) = _Media;

  factory Media.fromJson(Map<String, dynamic> json) => _$MediaFromJson(json);
}

@HiveType(typeId: 50)
@freezed
class ComboVariation with _$ComboVariation {
  const factory ComboVariation({
    @HiveField(0)
    @JsonKey(name: "variation_id", fromJson: int.parse)
    required int variationId,
    @HiveField(1) @JsonKey(fromJson: _parseDouble) required double quantity,
    @HiveField(2)
    @JsonKey(name: "unit_id", fromJson: int.parse)
    required int unitId,
  }) = _ComboVariation;

  factory ComboVariation.fromJson(Map<String, dynamic> json) =>
      _$ComboVariationFromJson(json);
}

@HiveType(typeId: 48)
@freezed
class ProductPriceGroup with _$ProductPriceGroup {
  const factory ProductPriceGroup({
    @HiveField(0) @JsonKey(name: 'id') required int id,
    @HiveField(1) @JsonKey(name: 'variation_id') required int variationId,
    @HiveField(2) @JsonKey(name: 'price_group_id') required int priceGroupId,
    @HiveField(3)
    @JsonKey(name: 'price_inc_tax', fromJson: double.parse)
    required double priceIncTax,
    @HiveField(4) @JsonKey(name: 'price_type') required String priceType,
    @HiveField(5)
    @JsonKey(name: 'created_at', fromJson: DateTime.parse)
    required DateTime createdAt,
    @HiveField(6)
    @JsonKey(name: 'updated_at', fromJson: DateTime.parse)
    required DateTime updatedAt,
  }) = _ProductPriceGroup;

  factory ProductPriceGroup.fromJson(Map<String, dynamic> json) =>
      _$ProductPriceGroupFromJson(json);
}

@HiveType(typeId: 0)
@freezed
class TaxRate with _$TaxRate {
  const factory TaxRate({
    @HiveField(0) @JsonKey(name: 'id') int? id,
    @HiveField(1) @JsonKey(name: 'business_id') int? businessId,
    @HiveField(2) @JsonKey(name: 'name') String? name,
    @HiveField(3) @JsonKey(name: 'amount') double? amount,
    @HiveField(4) @JsonKey(name: 'created_by') int? createdBy,
    @HiveField(5) @JsonKey(name: 'deleted_at') DateTime? deletedAt,
    @HiveField(6) @JsonKey(name: 'created_at') DateTime? createdAt,
    @HiveField(7) @JsonKey(name: 'updated_at') DateTime? updatedAt,
  }) = _TaxRate;

  factory TaxRate.fromJson(Map<String, dynamic> json) =>
      _$TaxRateFromJson(json);
}

@HiveType(typeId: 7)
@freezed
class Unit with _$Unit {
  const factory Unit({
    @HiveField(0) @JsonKey(name: 'id') required int id,
    @HiveField(1) @JsonKey(name: 'business_id') required int businessId,
    @HiveField(2) @JsonKey(name: 'actual_name') required String actualName,
    @HiveField(3) @JsonKey(name: 'short_name') required String shortName,
    @HiveField(4) @JsonKey(name: 'allow_decimal') required int allowDecimal,
    @HiveField(5)
    @JsonKey(name: 'base_unit_multiplier', fromJson: _parseUnitMultiplierDouble)
    @Default(1) double baseUnitMultiplier,
    @HiveField(6)
    @JsonKey(name: 'base_unit_id', fromJson: _tryParseInt)
    int? baseUnitId,
    @HiveField(7) @JsonKey(name: 'base_unit') Unit? baseUnit,
  }) = _Unit;

  factory Unit.fromJson(Map<String, dynamic> json) => _$UnitFromJson(json);
}

double _parseUnitMultiplierDouble(dynamic value) {
  if (value == null) return 1.0;
  if (value is num) return value.toDouble();
  if (value is String) return double.tryParse(value) ?? 1.0;
  return 1.0;
}

double _parseDouble(dynamic value) {
  if (value is num) return value.toDouble();
  if (value is String) return double.tryParse(value) ?? 0.0;
  return 0.0;
}

int? _tryParseInt(dynamic value) {
  if (value is num) return value.toInt();
  if (value is String) return int.tryParse(value);
  return null;
}

Map<String, dynamic>? _decodeJsonString(dynamic value) {
  if (value == null) return null;
  if (value is Map<String, dynamic>) return value;
  if (value is String) {
    try {
      return Map<String, dynamic>.from(jsonDecode(value));
    } catch (e, s) {
      logAppError('Error decoding JSON string', e, s);
      return null;
    }
  }
  return null;
}

const String _placeholderImageUrl = "https://developers.google.com/static/"
    "maps/documentation/streetview/images/"
    "error-image-generic.png";
