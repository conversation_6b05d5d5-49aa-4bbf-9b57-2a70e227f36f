// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'base_url.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class BaseUrlAdapter extends TypeAdapter<BaseUrl> {
  @override
  final int typeId = 19;

  @override
  BaseUrl read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return BaseUrl(
      name: fields[0] as String,
      url: fields[1] as String,
      id: fields[2] as String,
      secret: fields[3] as String,
    );
  }

  @override
  void write(BinaryWriter writer, BaseUrl obj) {
    writer
      ..writeByte(4)
      ..writeByte(0)
      ..write(obj.name)
      ..writeByte(1)
      ..write(obj.url)
      ..writeByte(2)
      ..write(obj.id)
      ..writeByte(3)
      ..write(obj.secret);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is BaseUrlAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
