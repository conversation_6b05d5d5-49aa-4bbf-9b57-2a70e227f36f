import 'package:json_annotation/json_annotation.dart';
import 'package:hive/hive.dart';
import 'package:equatable/equatable.dart';

part 'customer_group.g.dart';

@JsonSerializable()
@HiveType(typeId: 57)
class CustomerGroup extends Equatable {
  @Json<PERSON><PERSON>(name: 'name')
  @HiveField(0)
  final String? name;

  @JsonKey(name: 'amount')
  @HiveField(1)
  final double? amount;

  @JsonKey(name: 'selling_price_group')
  @HiveField(2)
  final String? sellingPriceGroup;

  @<PERSON>son<PERSON>ey(name: 'id')
  @HiveField(3)
  final int id;

  @<PERSON>sonKey(name: 'price_calculation_type')
  @HiveField(4)
  final String? priceCalculationType;

  const CustomerGroup({
    this.name,
    this.amount,
    this.sellingPriceGroup,
    required this.id,
    this.priceCalculationType,
  });

  factory CustomerGroup.fromJson(Map<String, dynamic> json) =>
      _$CustomerGroupFromJson(json);

  Map<String, dynamic> toJson() => _$CustomerGroupToJson(this);

  @override
  List<Object?> get props => [
        name,
        amount,
        sellingPriceGroup,
        id,
        priceCalculationType,
      ];
}
