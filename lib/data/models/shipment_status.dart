import 'package:hive/hive.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

part 'shipment_status.g.dart';

@HiveType(typeId: 52)
enum ShipmentStatus {
  @HiveField(0)
  shipped,
  @HiveField(1)
  ordered,
  @HiveField(2)
  packed,
  @HiveField(3)
  delivered,
  @HiveField(4)
  cancelled,
}

String shipmentStatusToJson(ShipmentStatus? status) {
  return status.toString().split('.').last;
}

ShipmentStatus shipmentStatusFromJson(String? status) {
  switch (status) {
    case 'shipped':
      return ShipmentStatus.shipped;
    case 'ordered':
      return ShipmentStatus.ordered;
    case 'packed':
      return ShipmentStatus.packed;
    case 'delivered':
      return ShipmentStatus.delivered;
    case 'cancelled':
      return ShipmentStatus.cancelled;
    default:
      return ShipmentStatus.ordered;
  }
}

extension AppLocalizationsExtension on AppLocalizations {
  String translateShippingStatus(ShipmentStatus? status) {
    switch (status) {
      case ShipmentStatus.shipped:
        return shipped;
      case ShipmentStatus.ordered:
        return ordered;
      case ShipmentStatus.packed:
        return packed;
      case ShipmentStatus.delivered:
        return delivered;
      case ShipmentStatus.cancelled:
        return cancelled;
      default:
        return ordered;
    }
  }
}
