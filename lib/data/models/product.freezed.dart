// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'product.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

Product _$ProductFromJson(Map<String, dynamic> json) {
  return _Product.fromJson(json);
}

/// @nodoc
mixin _$Product {
  @HiveField(0)
  int get id => throw _privateConstructorUsedError;
  @HiveField(1)
  String get name => throw _privateConstructorUsedError;
  @HiveField(2)
  @JsonKey(name: 'business_id')
  int get businessId => throw _privateConstructorUsedError;
  @HiveField(3)
  String? get type => throw _privateConstructorUsedError;
  @HiveField(4)
  @JsonKey(name: 'sub_unit_ids')
  List<String>? get subUnitIds => throw _privateConstructorUsedError;
  @HiveField(5)
  @JsonKey(name: 'enable_stock')
  int get enableStock => throw _privateConstructorUsedError;
  @HiveField(6)
  @JsonKey(name: 'alert_quantity')
  String? get alertQuantity => throw _privateConstructorUsedError;
  @HiveField(7)
  String? get sku => throw _privateConstructorUsedError;
  @HiveField(8)
  @JsonKey(name: 'barcode_type')
  String? get barcodeType => throw _privateConstructorUsedError;
  @HiveField(9)
  @JsonKey(name: 'expiry_period')
  String? get expiryPeriod => throw _privateConstructorUsedError;
  @HiveField(10)
  @JsonKey(name: 'expiry_period_type')
  String? get expiryPeriodType => throw _privateConstructorUsedError;
  @HiveField(11)
  String? get weight => throw _privateConstructorUsedError;
  @HiveField(12)
  @JsonKey(name: 'product_description')
  String? get productDescription => throw _privateConstructorUsedError;
  @HiveField(13)
  @JsonKey(name: 'created_by')
  int? get createdBy => throw _privateConstructorUsedError;
  @HiveField(14)
  @JsonKey(name: 'is_inactive')
  int? get isInactive => throw _privateConstructorUsedError;
  @HiveField(15)
  @JsonKey(name: 'not_for_selling')
  int? get notForSelling => throw _privateConstructorUsedError;
  @HiveField(16)
  @JsonKey(name: 'max_in_invoice')
  int? get maxInInvoice => throw _privateConstructorUsedError;
  @HiveField(17)
  @JsonKey(name: 'max_discount')
  int? get maxDiscount => throw _privateConstructorUsedError;
  @HiveField(18)
  @JsonKey(name: 'in_offer')
  int? get inOffer => throw _privateConstructorUsedError;
  @HiveField(19)
  @JsonKey(name: 'image_url')
  String get imageUrl => throw _privateConstructorUsedError;
  @HiveField(20)
  @JsonKey(name: 'product_variations')
  List<ProductVariation> get productVariations =>
      throw _privateConstructorUsedError;
  @HiveField(21)
  Brand? get brand => throw _privateConstructorUsedError;
  @HiveField(22)
  Unit get unit => throw _privateConstructorUsedError;
  @HiveField(23)
  ProductCategory? get category => throw _privateConstructorUsedError;
  @HiveField(24)
  @JsonKey(name: 'sub_category')
  ProductCategory? get subCategory => throw _privateConstructorUsedError;
  @HiveField(25)
  @JsonKey(name: 'product_tax')
  TaxRate? get productTax => throw _privateConstructorUsedError;
  @HiveField(26)
  @JsonKey(name: 'product_locations')
  List<ProductLocation> get productLocations =>
      throw _privateConstructorUsedError;

  /// Serializes this Product to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Product
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProductCopyWith<Product> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProductCopyWith<$Res> {
  factory $ProductCopyWith(Product value, $Res Function(Product) then) =
      _$ProductCopyWithImpl<$Res, Product>;
  @useResult
  $Res call(
      {@HiveField(0) int id,
      @HiveField(1) String name,
      @HiveField(2) @JsonKey(name: 'business_id') int businessId,
      @HiveField(3) String? type,
      @HiveField(4) @JsonKey(name: 'sub_unit_ids') List<String>? subUnitIds,
      @HiveField(5) @JsonKey(name: 'enable_stock') int enableStock,
      @HiveField(6) @JsonKey(name: 'alert_quantity') String? alertQuantity,
      @HiveField(7) String? sku,
      @HiveField(8) @JsonKey(name: 'barcode_type') String? barcodeType,
      @HiveField(9) @JsonKey(name: 'expiry_period') String? expiryPeriod,
      @HiveField(10)
      @JsonKey(name: 'expiry_period_type')
      String? expiryPeriodType,
      @HiveField(11) String? weight,
      @HiveField(12)
      @JsonKey(name: 'product_description')
      String? productDescription,
      @HiveField(13) @JsonKey(name: 'created_by') int? createdBy,
      @HiveField(14) @JsonKey(name: 'is_inactive') int? isInactive,
      @HiveField(15) @JsonKey(name: 'not_for_selling') int? notForSelling,
      @HiveField(16) @JsonKey(name: 'max_in_invoice') int? maxInInvoice,
      @HiveField(17) @JsonKey(name: 'max_discount') int? maxDiscount,
      @HiveField(18) @JsonKey(name: 'in_offer') int? inOffer,
      @HiveField(19) @JsonKey(name: 'image_url') String imageUrl,
      @HiveField(20)
      @JsonKey(name: 'product_variations')
      List<ProductVariation> productVariations,
      @HiveField(21) Brand? brand,
      @HiveField(22) Unit unit,
      @HiveField(23) ProductCategory? category,
      @HiveField(24)
      @JsonKey(name: 'sub_category')
      ProductCategory? subCategory,
      @HiveField(25) @JsonKey(name: 'product_tax') TaxRate? productTax,
      @HiveField(26)
      @JsonKey(name: 'product_locations')
      List<ProductLocation> productLocations});

  $BrandCopyWith<$Res>? get brand;
  $UnitCopyWith<$Res> get unit;
  $ProductCategoryCopyWith<$Res>? get category;
  $ProductCategoryCopyWith<$Res>? get subCategory;
  $TaxRateCopyWith<$Res>? get productTax;
}

/// @nodoc
class _$ProductCopyWithImpl<$Res, $Val extends Product>
    implements $ProductCopyWith<$Res> {
  _$ProductCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Product
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? businessId = null,
    Object? type = freezed,
    Object? subUnitIds = freezed,
    Object? enableStock = null,
    Object? alertQuantity = freezed,
    Object? sku = freezed,
    Object? barcodeType = freezed,
    Object? expiryPeriod = freezed,
    Object? expiryPeriodType = freezed,
    Object? weight = freezed,
    Object? productDescription = freezed,
    Object? createdBy = freezed,
    Object? isInactive = freezed,
    Object? notForSelling = freezed,
    Object? maxInInvoice = freezed,
    Object? maxDiscount = freezed,
    Object? inOffer = freezed,
    Object? imageUrl = null,
    Object? productVariations = null,
    Object? brand = freezed,
    Object? unit = null,
    Object? category = freezed,
    Object? subCategory = freezed,
    Object? productTax = freezed,
    Object? productLocations = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      businessId: null == businessId
          ? _value.businessId
          : businessId // ignore: cast_nullable_to_non_nullable
              as int,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
      subUnitIds: freezed == subUnitIds
          ? _value.subUnitIds
          : subUnitIds // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      enableStock: null == enableStock
          ? _value.enableStock
          : enableStock // ignore: cast_nullable_to_non_nullable
              as int,
      alertQuantity: freezed == alertQuantity
          ? _value.alertQuantity
          : alertQuantity // ignore: cast_nullable_to_non_nullable
              as String?,
      sku: freezed == sku
          ? _value.sku
          : sku // ignore: cast_nullable_to_non_nullable
              as String?,
      barcodeType: freezed == barcodeType
          ? _value.barcodeType
          : barcodeType // ignore: cast_nullable_to_non_nullable
              as String?,
      expiryPeriod: freezed == expiryPeriod
          ? _value.expiryPeriod
          : expiryPeriod // ignore: cast_nullable_to_non_nullable
              as String?,
      expiryPeriodType: freezed == expiryPeriodType
          ? _value.expiryPeriodType
          : expiryPeriodType // ignore: cast_nullable_to_non_nullable
              as String?,
      weight: freezed == weight
          ? _value.weight
          : weight // ignore: cast_nullable_to_non_nullable
              as String?,
      productDescription: freezed == productDescription
          ? _value.productDescription
          : productDescription // ignore: cast_nullable_to_non_nullable
              as String?,
      createdBy: freezed == createdBy
          ? _value.createdBy
          : createdBy // ignore: cast_nullable_to_non_nullable
              as int?,
      isInactive: freezed == isInactive
          ? _value.isInactive
          : isInactive // ignore: cast_nullable_to_non_nullable
              as int?,
      notForSelling: freezed == notForSelling
          ? _value.notForSelling
          : notForSelling // ignore: cast_nullable_to_non_nullable
              as int?,
      maxInInvoice: freezed == maxInInvoice
          ? _value.maxInInvoice
          : maxInInvoice // ignore: cast_nullable_to_non_nullable
              as int?,
      maxDiscount: freezed == maxDiscount
          ? _value.maxDiscount
          : maxDiscount // ignore: cast_nullable_to_non_nullable
              as int?,
      inOffer: freezed == inOffer
          ? _value.inOffer
          : inOffer // ignore: cast_nullable_to_non_nullable
              as int?,
      imageUrl: null == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String,
      productVariations: null == productVariations
          ? _value.productVariations
          : productVariations // ignore: cast_nullable_to_non_nullable
              as List<ProductVariation>,
      brand: freezed == brand
          ? _value.brand
          : brand // ignore: cast_nullable_to_non_nullable
              as Brand?,
      unit: null == unit
          ? _value.unit
          : unit // ignore: cast_nullable_to_non_nullable
              as Unit,
      category: freezed == category
          ? _value.category
          : category // ignore: cast_nullable_to_non_nullable
              as ProductCategory?,
      subCategory: freezed == subCategory
          ? _value.subCategory
          : subCategory // ignore: cast_nullable_to_non_nullable
              as ProductCategory?,
      productTax: freezed == productTax
          ? _value.productTax
          : productTax // ignore: cast_nullable_to_non_nullable
              as TaxRate?,
      productLocations: null == productLocations
          ? _value.productLocations
          : productLocations // ignore: cast_nullable_to_non_nullable
              as List<ProductLocation>,
    ) as $Val);
  }

  /// Create a copy of Product
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $BrandCopyWith<$Res>? get brand {
    if (_value.brand == null) {
      return null;
    }

    return $BrandCopyWith<$Res>(_value.brand!, (value) {
      return _then(_value.copyWith(brand: value) as $Val);
    });
  }

  /// Create a copy of Product
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UnitCopyWith<$Res> get unit {
    return $UnitCopyWith<$Res>(_value.unit, (value) {
      return _then(_value.copyWith(unit: value) as $Val);
    });
  }

  /// Create a copy of Product
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ProductCategoryCopyWith<$Res>? get category {
    if (_value.category == null) {
      return null;
    }

    return $ProductCategoryCopyWith<$Res>(_value.category!, (value) {
      return _then(_value.copyWith(category: value) as $Val);
    });
  }

  /// Create a copy of Product
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ProductCategoryCopyWith<$Res>? get subCategory {
    if (_value.subCategory == null) {
      return null;
    }

    return $ProductCategoryCopyWith<$Res>(_value.subCategory!, (value) {
      return _then(_value.copyWith(subCategory: value) as $Val);
    });
  }

  /// Create a copy of Product
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $TaxRateCopyWith<$Res>? get productTax {
    if (_value.productTax == null) {
      return null;
    }

    return $TaxRateCopyWith<$Res>(_value.productTax!, (value) {
      return _then(_value.copyWith(productTax: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ProductImplCopyWith<$Res> implements $ProductCopyWith<$Res> {
  factory _$$ProductImplCopyWith(
          _$ProductImpl value, $Res Function(_$ProductImpl) then) =
      __$$ProductImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@HiveField(0) int id,
      @HiveField(1) String name,
      @HiveField(2) @JsonKey(name: 'business_id') int businessId,
      @HiveField(3) String? type,
      @HiveField(4) @JsonKey(name: 'sub_unit_ids') List<String>? subUnitIds,
      @HiveField(5) @JsonKey(name: 'enable_stock') int enableStock,
      @HiveField(6) @JsonKey(name: 'alert_quantity') String? alertQuantity,
      @HiveField(7) String? sku,
      @HiveField(8) @JsonKey(name: 'barcode_type') String? barcodeType,
      @HiveField(9) @JsonKey(name: 'expiry_period') String? expiryPeriod,
      @HiveField(10)
      @JsonKey(name: 'expiry_period_type')
      String? expiryPeriodType,
      @HiveField(11) String? weight,
      @HiveField(12)
      @JsonKey(name: 'product_description')
      String? productDescription,
      @HiveField(13) @JsonKey(name: 'created_by') int? createdBy,
      @HiveField(14) @JsonKey(name: 'is_inactive') int? isInactive,
      @HiveField(15) @JsonKey(name: 'not_for_selling') int? notForSelling,
      @HiveField(16) @JsonKey(name: 'max_in_invoice') int? maxInInvoice,
      @HiveField(17) @JsonKey(name: 'max_discount') int? maxDiscount,
      @HiveField(18) @JsonKey(name: 'in_offer') int? inOffer,
      @HiveField(19) @JsonKey(name: 'image_url') String imageUrl,
      @HiveField(20)
      @JsonKey(name: 'product_variations')
      List<ProductVariation> productVariations,
      @HiveField(21) Brand? brand,
      @HiveField(22) Unit unit,
      @HiveField(23) ProductCategory? category,
      @HiveField(24)
      @JsonKey(name: 'sub_category')
      ProductCategory? subCategory,
      @HiveField(25) @JsonKey(name: 'product_tax') TaxRate? productTax,
      @HiveField(26)
      @JsonKey(name: 'product_locations')
      List<ProductLocation> productLocations});

  @override
  $BrandCopyWith<$Res>? get brand;
  @override
  $UnitCopyWith<$Res> get unit;
  @override
  $ProductCategoryCopyWith<$Res>? get category;
  @override
  $ProductCategoryCopyWith<$Res>? get subCategory;
  @override
  $TaxRateCopyWith<$Res>? get productTax;
}

/// @nodoc
class __$$ProductImplCopyWithImpl<$Res>
    extends _$ProductCopyWithImpl<$Res, _$ProductImpl>
    implements _$$ProductImplCopyWith<$Res> {
  __$$ProductImplCopyWithImpl(
      _$ProductImpl _value, $Res Function(_$ProductImpl) _then)
      : super(_value, _then);

  /// Create a copy of Product
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? businessId = null,
    Object? type = freezed,
    Object? subUnitIds = freezed,
    Object? enableStock = null,
    Object? alertQuantity = freezed,
    Object? sku = freezed,
    Object? barcodeType = freezed,
    Object? expiryPeriod = freezed,
    Object? expiryPeriodType = freezed,
    Object? weight = freezed,
    Object? productDescription = freezed,
    Object? createdBy = freezed,
    Object? isInactive = freezed,
    Object? notForSelling = freezed,
    Object? maxInInvoice = freezed,
    Object? maxDiscount = freezed,
    Object? inOffer = freezed,
    Object? imageUrl = null,
    Object? productVariations = null,
    Object? brand = freezed,
    Object? unit = null,
    Object? category = freezed,
    Object? subCategory = freezed,
    Object? productTax = freezed,
    Object? productLocations = null,
  }) {
    return _then(_$ProductImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      businessId: null == businessId
          ? _value.businessId
          : businessId // ignore: cast_nullable_to_non_nullable
              as int,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
      subUnitIds: freezed == subUnitIds
          ? _value._subUnitIds
          : subUnitIds // ignore: cast_nullable_to_non_nullable
              as List<String>?,
      enableStock: null == enableStock
          ? _value.enableStock
          : enableStock // ignore: cast_nullable_to_non_nullable
              as int,
      alertQuantity: freezed == alertQuantity
          ? _value.alertQuantity
          : alertQuantity // ignore: cast_nullable_to_non_nullable
              as String?,
      sku: freezed == sku
          ? _value.sku
          : sku // ignore: cast_nullable_to_non_nullable
              as String?,
      barcodeType: freezed == barcodeType
          ? _value.barcodeType
          : barcodeType // ignore: cast_nullable_to_non_nullable
              as String?,
      expiryPeriod: freezed == expiryPeriod
          ? _value.expiryPeriod
          : expiryPeriod // ignore: cast_nullable_to_non_nullable
              as String?,
      expiryPeriodType: freezed == expiryPeriodType
          ? _value.expiryPeriodType
          : expiryPeriodType // ignore: cast_nullable_to_non_nullable
              as String?,
      weight: freezed == weight
          ? _value.weight
          : weight // ignore: cast_nullable_to_non_nullable
              as String?,
      productDescription: freezed == productDescription
          ? _value.productDescription
          : productDescription // ignore: cast_nullable_to_non_nullable
              as String?,
      createdBy: freezed == createdBy
          ? _value.createdBy
          : createdBy // ignore: cast_nullable_to_non_nullable
              as int?,
      isInactive: freezed == isInactive
          ? _value.isInactive
          : isInactive // ignore: cast_nullable_to_non_nullable
              as int?,
      notForSelling: freezed == notForSelling
          ? _value.notForSelling
          : notForSelling // ignore: cast_nullable_to_non_nullable
              as int?,
      maxInInvoice: freezed == maxInInvoice
          ? _value.maxInInvoice
          : maxInInvoice // ignore: cast_nullable_to_non_nullable
              as int?,
      maxDiscount: freezed == maxDiscount
          ? _value.maxDiscount
          : maxDiscount // ignore: cast_nullable_to_non_nullable
              as int?,
      inOffer: freezed == inOffer
          ? _value.inOffer
          : inOffer // ignore: cast_nullable_to_non_nullable
              as int?,
      imageUrl: null == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String,
      productVariations: null == productVariations
          ? _value._productVariations
          : productVariations // ignore: cast_nullable_to_non_nullable
              as List<ProductVariation>,
      brand: freezed == brand
          ? _value.brand
          : brand // ignore: cast_nullable_to_non_nullable
              as Brand?,
      unit: null == unit
          ? _value.unit
          : unit // ignore: cast_nullable_to_non_nullable
              as Unit,
      category: freezed == category
          ? _value.category
          : category // ignore: cast_nullable_to_non_nullable
              as ProductCategory?,
      subCategory: freezed == subCategory
          ? _value.subCategory
          : subCategory // ignore: cast_nullable_to_non_nullable
              as ProductCategory?,
      productTax: freezed == productTax
          ? _value.productTax
          : productTax // ignore: cast_nullable_to_non_nullable
              as TaxRate?,
      productLocations: null == productLocations
          ? _value._productLocations
          : productLocations // ignore: cast_nullable_to_non_nullable
              as List<ProductLocation>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ProductImpl implements _Product {
  const _$ProductImpl(
      {@HiveField(0) required this.id,
      @HiveField(1) this.name = "N/A",
      @HiveField(2) @JsonKey(name: 'business_id') required this.businessId,
      @HiveField(3) this.type,
      @HiveField(4)
      @JsonKey(name: 'sub_unit_ids')
      final List<String>? subUnitIds,
      @HiveField(5) @JsonKey(name: 'enable_stock') required this.enableStock,
      @HiveField(6) @JsonKey(name: 'alert_quantity') this.alertQuantity,
      @HiveField(7) this.sku,
      @HiveField(8) @JsonKey(name: 'barcode_type') this.barcodeType,
      @HiveField(9) @JsonKey(name: 'expiry_period') this.expiryPeriod,
      @HiveField(10) @JsonKey(name: 'expiry_period_type') this.expiryPeriodType,
      @HiveField(11) this.weight,
      @HiveField(12)
      @JsonKey(name: 'product_description')
      this.productDescription,
      @HiveField(13) @JsonKey(name: 'created_by') this.createdBy,
      @HiveField(14) @JsonKey(name: 'is_inactive') this.isInactive,
      @HiveField(15) @JsonKey(name: 'not_for_selling') this.notForSelling,
      @HiveField(16) @JsonKey(name: 'max_in_invoice') this.maxInInvoice,
      @HiveField(17) @JsonKey(name: 'max_discount') this.maxDiscount,
      @HiveField(18) @JsonKey(name: 'in_offer') this.inOffer,
      @HiveField(19) @JsonKey(name: 'image_url') required this.imageUrl,
      @HiveField(20)
      @JsonKey(name: 'product_variations')
      required final List<ProductVariation> productVariations,
      @HiveField(21) this.brand,
      @HiveField(22) required this.unit,
      @HiveField(23) this.category,
      @HiveField(24) @JsonKey(name: 'sub_category') this.subCategory,
      @HiveField(25) @JsonKey(name: 'product_tax') this.productTax,
      @HiveField(26)
      @JsonKey(name: 'product_locations')
      required final List<ProductLocation> productLocations})
      : _subUnitIds = subUnitIds,
        _productVariations = productVariations,
        _productLocations = productLocations;

  factory _$ProductImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProductImplFromJson(json);

  @override
  @HiveField(0)
  final int id;
  @override
  @JsonKey()
  @HiveField(1)
  final String name;
  @override
  @HiveField(2)
  @JsonKey(name: 'business_id')
  final int businessId;
  @override
  @HiveField(3)
  final String? type;
  final List<String>? _subUnitIds;
  @override
  @HiveField(4)
  @JsonKey(name: 'sub_unit_ids')
  List<String>? get subUnitIds {
    final value = _subUnitIds;
    if (value == null) return null;
    if (_subUnitIds is EqualUnmodifiableListView) return _subUnitIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  @HiveField(5)
  @JsonKey(name: 'enable_stock')
  final int enableStock;
  @override
  @HiveField(6)
  @JsonKey(name: 'alert_quantity')
  final String? alertQuantity;
  @override
  @HiveField(7)
  final String? sku;
  @override
  @HiveField(8)
  @JsonKey(name: 'barcode_type')
  final String? barcodeType;
  @override
  @HiveField(9)
  @JsonKey(name: 'expiry_period')
  final String? expiryPeriod;
  @override
  @HiveField(10)
  @JsonKey(name: 'expiry_period_type')
  final String? expiryPeriodType;
  @override
  @HiveField(11)
  final String? weight;
  @override
  @HiveField(12)
  @JsonKey(name: 'product_description')
  final String? productDescription;
  @override
  @HiveField(13)
  @JsonKey(name: 'created_by')
  final int? createdBy;
  @override
  @HiveField(14)
  @JsonKey(name: 'is_inactive')
  final int? isInactive;
  @override
  @HiveField(15)
  @JsonKey(name: 'not_for_selling')
  final int? notForSelling;
  @override
  @HiveField(16)
  @JsonKey(name: 'max_in_invoice')
  final int? maxInInvoice;
  @override
  @HiveField(17)
  @JsonKey(name: 'max_discount')
  final int? maxDiscount;
  @override
  @HiveField(18)
  @JsonKey(name: 'in_offer')
  final int? inOffer;
  @override
  @HiveField(19)
  @JsonKey(name: 'image_url')
  final String imageUrl;
  final List<ProductVariation> _productVariations;
  @override
  @HiveField(20)
  @JsonKey(name: 'product_variations')
  List<ProductVariation> get productVariations {
    if (_productVariations is EqualUnmodifiableListView)
      return _productVariations;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_productVariations);
  }

  @override
  @HiveField(21)
  final Brand? brand;
  @override
  @HiveField(22)
  final Unit unit;
  @override
  @HiveField(23)
  final ProductCategory? category;
  @override
  @HiveField(24)
  @JsonKey(name: 'sub_category')
  final ProductCategory? subCategory;
  @override
  @HiveField(25)
  @JsonKey(name: 'product_tax')
  final TaxRate? productTax;
  final List<ProductLocation> _productLocations;
  @override
  @HiveField(26)
  @JsonKey(name: 'product_locations')
  List<ProductLocation> get productLocations {
    if (_productLocations is EqualUnmodifiableListView)
      return _productLocations;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_productLocations);
  }

  @override
  String toString() {
    return 'Product(id: $id, name: $name, businessId: $businessId, type: $type, subUnitIds: $subUnitIds, enableStock: $enableStock, alertQuantity: $alertQuantity, sku: $sku, barcodeType: $barcodeType, expiryPeriod: $expiryPeriod, expiryPeriodType: $expiryPeriodType, weight: $weight, productDescription: $productDescription, createdBy: $createdBy, isInactive: $isInactive, notForSelling: $notForSelling, maxInInvoice: $maxInInvoice, maxDiscount: $maxDiscount, inOffer: $inOffer, imageUrl: $imageUrl, productVariations: $productVariations, brand: $brand, unit: $unit, category: $category, subCategory: $subCategory, productTax: $productTax, productLocations: $productLocations)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProductImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.businessId, businessId) ||
                other.businessId == businessId) &&
            (identical(other.type, type) || other.type == type) &&
            const DeepCollectionEquality()
                .equals(other._subUnitIds, _subUnitIds) &&
            (identical(other.enableStock, enableStock) ||
                other.enableStock == enableStock) &&
            (identical(other.alertQuantity, alertQuantity) ||
                other.alertQuantity == alertQuantity) &&
            (identical(other.sku, sku) || other.sku == sku) &&
            (identical(other.barcodeType, barcodeType) ||
                other.barcodeType == barcodeType) &&
            (identical(other.expiryPeriod, expiryPeriod) ||
                other.expiryPeriod == expiryPeriod) &&
            (identical(other.expiryPeriodType, expiryPeriodType) ||
                other.expiryPeriodType == expiryPeriodType) &&
            (identical(other.weight, weight) || other.weight == weight) &&
            (identical(other.productDescription, productDescription) ||
                other.productDescription == productDescription) &&
            (identical(other.createdBy, createdBy) ||
                other.createdBy == createdBy) &&
            (identical(other.isInactive, isInactive) ||
                other.isInactive == isInactive) &&
            (identical(other.notForSelling, notForSelling) ||
                other.notForSelling == notForSelling) &&
            (identical(other.maxInInvoice, maxInInvoice) ||
                other.maxInInvoice == maxInInvoice) &&
            (identical(other.maxDiscount, maxDiscount) ||
                other.maxDiscount == maxDiscount) &&
            (identical(other.inOffer, inOffer) || other.inOffer == inOffer) &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl) &&
            const DeepCollectionEquality()
                .equals(other._productVariations, _productVariations) &&
            (identical(other.brand, brand) || other.brand == brand) &&
            (identical(other.unit, unit) || other.unit == unit) &&
            (identical(other.category, category) ||
                other.category == category) &&
            (identical(other.subCategory, subCategory) ||
                other.subCategory == subCategory) &&
            (identical(other.productTax, productTax) ||
                other.productTax == productTax) &&
            const DeepCollectionEquality()
                .equals(other._productLocations, _productLocations));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        name,
        businessId,
        type,
        const DeepCollectionEquality().hash(_subUnitIds),
        enableStock,
        alertQuantity,
        sku,
        barcodeType,
        expiryPeriod,
        expiryPeriodType,
        weight,
        productDescription,
        createdBy,
        isInactive,
        notForSelling,
        maxInInvoice,
        maxDiscount,
        inOffer,
        imageUrl,
        const DeepCollectionEquality().hash(_productVariations),
        brand,
        unit,
        category,
        subCategory,
        productTax,
        const DeepCollectionEquality().hash(_productLocations)
      ]);

  /// Create a copy of Product
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProductImplCopyWith<_$ProductImpl> get copyWith =>
      __$$ProductImplCopyWithImpl<_$ProductImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProductImplToJson(
      this,
    );
  }
}

abstract class _Product implements Product {
  const factory _Product(
      {@HiveField(0) required final int id,
      @HiveField(1) final String name,
      @HiveField(2) @JsonKey(name: 'business_id') required final int businessId,
      @HiveField(3) final String? type,
      @HiveField(4)
      @JsonKey(name: 'sub_unit_ids')
      final List<String>? subUnitIds,
      @HiveField(5)
      @JsonKey(name: 'enable_stock')
      required final int enableStock,
      @HiveField(6)
      @JsonKey(name: 'alert_quantity')
      final String? alertQuantity,
      @HiveField(7) final String? sku,
      @HiveField(8) @JsonKey(name: 'barcode_type') final String? barcodeType,
      @HiveField(9) @JsonKey(name: 'expiry_period') final String? expiryPeriod,
      @HiveField(10)
      @JsonKey(name: 'expiry_period_type')
      final String? expiryPeriodType,
      @HiveField(11) final String? weight,
      @HiveField(12)
      @JsonKey(name: 'product_description')
      final String? productDescription,
      @HiveField(13) @JsonKey(name: 'created_by') final int? createdBy,
      @HiveField(14) @JsonKey(name: 'is_inactive') final int? isInactive,
      @HiveField(15) @JsonKey(name: 'not_for_selling') final int? notForSelling,
      @HiveField(16) @JsonKey(name: 'max_in_invoice') final int? maxInInvoice,
      @HiveField(17) @JsonKey(name: 'max_discount') final int? maxDiscount,
      @HiveField(18) @JsonKey(name: 'in_offer') final int? inOffer,
      @HiveField(19) @JsonKey(name: 'image_url') required final String imageUrl,
      @HiveField(20)
      @JsonKey(name: 'product_variations')
      required final List<ProductVariation> productVariations,
      @HiveField(21) final Brand? brand,
      @HiveField(22) required final Unit unit,
      @HiveField(23) final ProductCategory? category,
      @HiveField(24)
      @JsonKey(name: 'sub_category')
      final ProductCategory? subCategory,
      @HiveField(25) @JsonKey(name: 'product_tax') final TaxRate? productTax,
      @HiveField(26)
      @JsonKey(name: 'product_locations')
      required final List<ProductLocation> productLocations}) = _$ProductImpl;

  factory _Product.fromJson(Map<String, dynamic> json) = _$ProductImpl.fromJson;

  @override
  @HiveField(0)
  int get id;
  @override
  @HiveField(1)
  String get name;
  @override
  @HiveField(2)
  @JsonKey(name: 'business_id')
  int get businessId;
  @override
  @HiveField(3)
  String? get type;
  @override
  @HiveField(4)
  @JsonKey(name: 'sub_unit_ids')
  List<String>? get subUnitIds;
  @override
  @HiveField(5)
  @JsonKey(name: 'enable_stock')
  int get enableStock;
  @override
  @HiveField(6)
  @JsonKey(name: 'alert_quantity')
  String? get alertQuantity;
  @override
  @HiveField(7)
  String? get sku;
  @override
  @HiveField(8)
  @JsonKey(name: 'barcode_type')
  String? get barcodeType;
  @override
  @HiveField(9)
  @JsonKey(name: 'expiry_period')
  String? get expiryPeriod;
  @override
  @HiveField(10)
  @JsonKey(name: 'expiry_period_type')
  String? get expiryPeriodType;
  @override
  @HiveField(11)
  String? get weight;
  @override
  @HiveField(12)
  @JsonKey(name: 'product_description')
  String? get productDescription;
  @override
  @HiveField(13)
  @JsonKey(name: 'created_by')
  int? get createdBy;
  @override
  @HiveField(14)
  @JsonKey(name: 'is_inactive')
  int? get isInactive;
  @override
  @HiveField(15)
  @JsonKey(name: 'not_for_selling')
  int? get notForSelling;
  @override
  @HiveField(16)
  @JsonKey(name: 'max_in_invoice')
  int? get maxInInvoice;
  @override
  @HiveField(17)
  @JsonKey(name: 'max_discount')
  int? get maxDiscount;
  @override
  @HiveField(18)
  @JsonKey(name: 'in_offer')
  int? get inOffer;
  @override
  @HiveField(19)
  @JsonKey(name: 'image_url')
  String get imageUrl;
  @override
  @HiveField(20)
  @JsonKey(name: 'product_variations')
  List<ProductVariation> get productVariations;
  @override
  @HiveField(21)
  Brand? get brand;
  @override
  @HiveField(22)
  Unit get unit;
  @override
  @HiveField(23)
  ProductCategory? get category;
  @override
  @HiveField(24)
  @JsonKey(name: 'sub_category')
  ProductCategory? get subCategory;
  @override
  @HiveField(25)
  @JsonKey(name: 'product_tax')
  TaxRate? get productTax;
  @override
  @HiveField(26)
  @JsonKey(name: 'product_locations')
  List<ProductLocation> get productLocations;

  /// Create a copy of Product
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProductImplCopyWith<_$ProductImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ProductCategory _$ProductCategoryFromJson(Map<String, dynamic> json) {
  return _ProductCategory.fromJson(json);
}

/// @nodoc
mixin _$ProductCategory {
  @HiveField(0)
  int get id => throw _privateConstructorUsedError;
  @HiveField(1)
  @JsonKey(name: 'business_id')
  int get businessId => throw _privateConstructorUsedError;
  @HiveField(2)
  String get name => throw _privateConstructorUsedError;
  @HiveField(3)
  @JsonKey(name: 'short_code')
  String? get shortCode => throw _privateConstructorUsedError;
  @HiveField(4)
  @JsonKey(name: 'parent_id')
  int? get parentId => throw _privateConstructorUsedError;
  @HiveField(5)
  @JsonKey(name: 'created_by')
  int? get createdBy => throw _privateConstructorUsedError;
  @HiveField(6)
  @JsonKey(name: 'category_type')
  String? get categoryType => throw _privateConstructorUsedError;
  @HiveField(7)
  String? get description => throw _privateConstructorUsedError;
  @HiveField(8)
  @JsonKey(name: 'image_url')
  String get imageUrl => throw _privateConstructorUsedError;
  @HiveField(9)
  @JsonKey(name: 'sub_categories')
  List<ProductCategory> get subCategories => throw _privateConstructorUsedError;

  /// Serializes this ProductCategory to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProductCategory
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProductCategoryCopyWith<ProductCategory> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProductCategoryCopyWith<$Res> {
  factory $ProductCategoryCopyWith(
          ProductCategory value, $Res Function(ProductCategory) then) =
      _$ProductCategoryCopyWithImpl<$Res, ProductCategory>;
  @useResult
  $Res call(
      {@HiveField(0) int id,
      @HiveField(1) @JsonKey(name: 'business_id') int businessId,
      @HiveField(2) String name,
      @HiveField(3) @JsonKey(name: 'short_code') String? shortCode,
      @HiveField(4) @JsonKey(name: 'parent_id') int? parentId,
      @HiveField(5) @JsonKey(name: 'created_by') int? createdBy,
      @HiveField(6) @JsonKey(name: 'category_type') String? categoryType,
      @HiveField(7) String? description,
      @HiveField(8) @JsonKey(name: 'image_url') String imageUrl,
      @HiveField(9)
      @JsonKey(name: 'sub_categories')
      List<ProductCategory> subCategories});
}

/// @nodoc
class _$ProductCategoryCopyWithImpl<$Res, $Val extends ProductCategory>
    implements $ProductCategoryCopyWith<$Res> {
  _$ProductCategoryCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProductCategory
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? businessId = null,
    Object? name = null,
    Object? shortCode = freezed,
    Object? parentId = freezed,
    Object? createdBy = freezed,
    Object? categoryType = freezed,
    Object? description = freezed,
    Object? imageUrl = null,
    Object? subCategories = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      businessId: null == businessId
          ? _value.businessId
          : businessId // ignore: cast_nullable_to_non_nullable
              as int,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      shortCode: freezed == shortCode
          ? _value.shortCode
          : shortCode // ignore: cast_nullable_to_non_nullable
              as String?,
      parentId: freezed == parentId
          ? _value.parentId
          : parentId // ignore: cast_nullable_to_non_nullable
              as int?,
      createdBy: freezed == createdBy
          ? _value.createdBy
          : createdBy // ignore: cast_nullable_to_non_nullable
              as int?,
      categoryType: freezed == categoryType
          ? _value.categoryType
          : categoryType // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      imageUrl: null == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String,
      subCategories: null == subCategories
          ? _value.subCategories
          : subCategories // ignore: cast_nullable_to_non_nullable
              as List<ProductCategory>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProductCategoryImplCopyWith<$Res>
    implements $ProductCategoryCopyWith<$Res> {
  factory _$$ProductCategoryImplCopyWith(_$ProductCategoryImpl value,
          $Res Function(_$ProductCategoryImpl) then) =
      __$$ProductCategoryImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@HiveField(0) int id,
      @HiveField(1) @JsonKey(name: 'business_id') int businessId,
      @HiveField(2) String name,
      @HiveField(3) @JsonKey(name: 'short_code') String? shortCode,
      @HiveField(4) @JsonKey(name: 'parent_id') int? parentId,
      @HiveField(5) @JsonKey(name: 'created_by') int? createdBy,
      @HiveField(6) @JsonKey(name: 'category_type') String? categoryType,
      @HiveField(7) String? description,
      @HiveField(8) @JsonKey(name: 'image_url') String imageUrl,
      @HiveField(9)
      @JsonKey(name: 'sub_categories')
      List<ProductCategory> subCategories});
}

/// @nodoc
class __$$ProductCategoryImplCopyWithImpl<$Res>
    extends _$ProductCategoryCopyWithImpl<$Res, _$ProductCategoryImpl>
    implements _$$ProductCategoryImplCopyWith<$Res> {
  __$$ProductCategoryImplCopyWithImpl(
      _$ProductCategoryImpl _value, $Res Function(_$ProductCategoryImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProductCategory
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? businessId = null,
    Object? name = null,
    Object? shortCode = freezed,
    Object? parentId = freezed,
    Object? createdBy = freezed,
    Object? categoryType = freezed,
    Object? description = freezed,
    Object? imageUrl = null,
    Object? subCategories = null,
  }) {
    return _then(_$ProductCategoryImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      businessId: null == businessId
          ? _value.businessId
          : businessId // ignore: cast_nullable_to_non_nullable
              as int,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      shortCode: freezed == shortCode
          ? _value.shortCode
          : shortCode // ignore: cast_nullable_to_non_nullable
              as String?,
      parentId: freezed == parentId
          ? _value.parentId
          : parentId // ignore: cast_nullable_to_non_nullable
              as int?,
      createdBy: freezed == createdBy
          ? _value.createdBy
          : createdBy // ignore: cast_nullable_to_non_nullable
              as int?,
      categoryType: freezed == categoryType
          ? _value.categoryType
          : categoryType // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      imageUrl: null == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String,
      subCategories: null == subCategories
          ? _value._subCategories
          : subCategories // ignore: cast_nullable_to_non_nullable
              as List<ProductCategory>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ProductCategoryImpl implements _ProductCategory {
  const _$ProductCategoryImpl(
      {@HiveField(0) required this.id,
      @HiveField(1) @JsonKey(name: 'business_id') required this.businessId,
      @HiveField(2) this.name = "N/A",
      @HiveField(3) @JsonKey(name: 'short_code') this.shortCode,
      @HiveField(4) @JsonKey(name: 'parent_id') this.parentId,
      @HiveField(5) @JsonKey(name: 'created_by') this.createdBy,
      @HiveField(6) @JsonKey(name: 'category_type') this.categoryType,
      @HiveField(7) this.description,
      @HiveField(8)
      @JsonKey(name: 'image_url')
      this.imageUrl = _placeholderImageUrl,
      @HiveField(9)
      @JsonKey(name: 'sub_categories')
      final List<ProductCategory> subCategories = const []})
      : _subCategories = subCategories;

  factory _$ProductCategoryImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProductCategoryImplFromJson(json);

  @override
  @HiveField(0)
  final int id;
  @override
  @HiveField(1)
  @JsonKey(name: 'business_id')
  final int businessId;
  @override
  @JsonKey()
  @HiveField(2)
  final String name;
  @override
  @HiveField(3)
  @JsonKey(name: 'short_code')
  final String? shortCode;
  @override
  @HiveField(4)
  @JsonKey(name: 'parent_id')
  final int? parentId;
  @override
  @HiveField(5)
  @JsonKey(name: 'created_by')
  final int? createdBy;
  @override
  @HiveField(6)
  @JsonKey(name: 'category_type')
  final String? categoryType;
  @override
  @HiveField(7)
  final String? description;
  @override
  @HiveField(8)
  @JsonKey(name: 'image_url')
  final String imageUrl;
  final List<ProductCategory> _subCategories;
  @override
  @HiveField(9)
  @JsonKey(name: 'sub_categories')
  List<ProductCategory> get subCategories {
    if (_subCategories is EqualUnmodifiableListView) return _subCategories;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_subCategories);
  }

  @override
  String toString() {
    return 'ProductCategory(id: $id, businessId: $businessId, name: $name, shortCode: $shortCode, parentId: $parentId, createdBy: $createdBy, categoryType: $categoryType, description: $description, imageUrl: $imageUrl, subCategories: $subCategories)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProductCategoryImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.businessId, businessId) ||
                other.businessId == businessId) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.shortCode, shortCode) ||
                other.shortCode == shortCode) &&
            (identical(other.parentId, parentId) ||
                other.parentId == parentId) &&
            (identical(other.createdBy, createdBy) ||
                other.createdBy == createdBy) &&
            (identical(other.categoryType, categoryType) ||
                other.categoryType == categoryType) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl) &&
            const DeepCollectionEquality()
                .equals(other._subCategories, _subCategories));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      businessId,
      name,
      shortCode,
      parentId,
      createdBy,
      categoryType,
      description,
      imageUrl,
      const DeepCollectionEquality().hash(_subCategories));

  /// Create a copy of ProductCategory
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProductCategoryImplCopyWith<_$ProductCategoryImpl> get copyWith =>
      __$$ProductCategoryImplCopyWithImpl<_$ProductCategoryImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProductCategoryImplToJson(
      this,
    );
  }
}

abstract class _ProductCategory implements ProductCategory {
  const factory _ProductCategory(
      {@HiveField(0) required final int id,
      @HiveField(1) @JsonKey(name: 'business_id') required final int businessId,
      @HiveField(2) final String name,
      @HiveField(3) @JsonKey(name: 'short_code') final String? shortCode,
      @HiveField(4) @JsonKey(name: 'parent_id') final int? parentId,
      @HiveField(5) @JsonKey(name: 'created_by') final int? createdBy,
      @HiveField(6) @JsonKey(name: 'category_type') final String? categoryType,
      @HiveField(7) final String? description,
      @HiveField(8) @JsonKey(name: 'image_url') final String imageUrl,
      @HiveField(9)
      @JsonKey(name: 'sub_categories')
      final List<ProductCategory> subCategories}) = _$ProductCategoryImpl;

  factory _ProductCategory.fromJson(Map<String, dynamic> json) =
      _$ProductCategoryImpl.fromJson;

  @override
  @HiveField(0)
  int get id;
  @override
  @HiveField(1)
  @JsonKey(name: 'business_id')
  int get businessId;
  @override
  @HiveField(2)
  String get name;
  @override
  @HiveField(3)
  @JsonKey(name: 'short_code')
  String? get shortCode;
  @override
  @HiveField(4)
  @JsonKey(name: 'parent_id')
  int? get parentId;
  @override
  @HiveField(5)
  @JsonKey(name: 'created_by')
  int? get createdBy;
  @override
  @HiveField(6)
  @JsonKey(name: 'category_type')
  String? get categoryType;
  @override
  @HiveField(7)
  String? get description;
  @override
  @HiveField(8)
  @JsonKey(name: 'image_url')
  String get imageUrl;
  @override
  @HiveField(9)
  @JsonKey(name: 'sub_categories')
  List<ProductCategory> get subCategories;

  /// Create a copy of ProductCategory
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProductCategoryImplCopyWith<_$ProductCategoryImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Brand _$BrandFromJson(Map<String, dynamic> json) {
  return _Brand.fromJson(json);
}

/// @nodoc
mixin _$Brand {
  @HiveField(0)
  @JsonKey(name: 'id')
  int? get id => throw _privateConstructorUsedError;
  @HiveField(1)
  @JsonKey(name: 'business_id')
  int? get businessId => throw _privateConstructorUsedError;
  @HiveField(2)
  @JsonKey(name: 'name')
  String? get name => throw _privateConstructorUsedError;
  @HiveField(3)
  @JsonKey(name: 'description')
  String? get description => throw _privateConstructorUsedError;
  @HiveField(4)
  @JsonKey(name: 'created_by')
  int? get createdBy => throw _privateConstructorUsedError;
  @HiveField(5)
  @JsonKey(name: 'deleted_at')
  DateTime? get deletedAt => throw _privateConstructorUsedError;
  @HiveField(6)
  @JsonKey(name: 'created_at')
  DateTime? get createdAt => throw _privateConstructorUsedError;
  @HiveField(7)
  @JsonKey(name: 'updated_at')
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this Brand to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Brand
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BrandCopyWith<Brand> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BrandCopyWith<$Res> {
  factory $BrandCopyWith(Brand value, $Res Function(Brand) then) =
      _$BrandCopyWithImpl<$Res, Brand>;
  @useResult
  $Res call(
      {@HiveField(0) @JsonKey(name: 'id') int? id,
      @HiveField(1) @JsonKey(name: 'business_id') int? businessId,
      @HiveField(2) @JsonKey(name: 'name') String? name,
      @HiveField(3) @JsonKey(name: 'description') String? description,
      @HiveField(4) @JsonKey(name: 'created_by') int? createdBy,
      @HiveField(5) @JsonKey(name: 'deleted_at') DateTime? deletedAt,
      @HiveField(6) @JsonKey(name: 'created_at') DateTime? createdAt,
      @HiveField(7) @JsonKey(name: 'updated_at') DateTime? updatedAt});
}

/// @nodoc
class _$BrandCopyWithImpl<$Res, $Val extends Brand>
    implements $BrandCopyWith<$Res> {
  _$BrandCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Brand
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? businessId = freezed,
    Object? name = freezed,
    Object? description = freezed,
    Object? createdBy = freezed,
    Object? deletedAt = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      businessId: freezed == businessId
          ? _value.businessId
          : businessId // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      createdBy: freezed == createdBy
          ? _value.createdBy
          : createdBy // ignore: cast_nullable_to_non_nullable
              as int?,
      deletedAt: freezed == deletedAt
          ? _value.deletedAt
          : deletedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$BrandImplCopyWith<$Res> implements $BrandCopyWith<$Res> {
  factory _$$BrandImplCopyWith(
          _$BrandImpl value, $Res Function(_$BrandImpl) then) =
      __$$BrandImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@HiveField(0) @JsonKey(name: 'id') int? id,
      @HiveField(1) @JsonKey(name: 'business_id') int? businessId,
      @HiveField(2) @JsonKey(name: 'name') String? name,
      @HiveField(3) @JsonKey(name: 'description') String? description,
      @HiveField(4) @JsonKey(name: 'created_by') int? createdBy,
      @HiveField(5) @JsonKey(name: 'deleted_at') DateTime? deletedAt,
      @HiveField(6) @JsonKey(name: 'created_at') DateTime? createdAt,
      @HiveField(7) @JsonKey(name: 'updated_at') DateTime? updatedAt});
}

/// @nodoc
class __$$BrandImplCopyWithImpl<$Res>
    extends _$BrandCopyWithImpl<$Res, _$BrandImpl>
    implements _$$BrandImplCopyWith<$Res> {
  __$$BrandImplCopyWithImpl(
      _$BrandImpl _value, $Res Function(_$BrandImpl) _then)
      : super(_value, _then);

  /// Create a copy of Brand
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? businessId = freezed,
    Object? name = freezed,
    Object? description = freezed,
    Object? createdBy = freezed,
    Object? deletedAt = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_$BrandImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      businessId: freezed == businessId
          ? _value.businessId
          : businessId // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      description: freezed == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String?,
      createdBy: freezed == createdBy
          ? _value.createdBy
          : createdBy // ignore: cast_nullable_to_non_nullable
              as int?,
      deletedAt: freezed == deletedAt
          ? _value.deletedAt
          : deletedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$BrandImpl implements _Brand {
  const _$BrandImpl(
      {@HiveField(0) @JsonKey(name: 'id') this.id,
      @HiveField(1) @JsonKey(name: 'business_id') this.businessId,
      @HiveField(2) @JsonKey(name: 'name') this.name,
      @HiveField(3) @JsonKey(name: 'description') this.description,
      @HiveField(4) @JsonKey(name: 'created_by') this.createdBy,
      @HiveField(5) @JsonKey(name: 'deleted_at') this.deletedAt,
      @HiveField(6) @JsonKey(name: 'created_at') this.createdAt,
      @HiveField(7) @JsonKey(name: 'updated_at') this.updatedAt});

  factory _$BrandImpl.fromJson(Map<String, dynamic> json) =>
      _$$BrandImplFromJson(json);

  @override
  @HiveField(0)
  @JsonKey(name: 'id')
  final int? id;
  @override
  @HiveField(1)
  @JsonKey(name: 'business_id')
  final int? businessId;
  @override
  @HiveField(2)
  @JsonKey(name: 'name')
  final String? name;
  @override
  @HiveField(3)
  @JsonKey(name: 'description')
  final String? description;
  @override
  @HiveField(4)
  @JsonKey(name: 'created_by')
  final int? createdBy;
  @override
  @HiveField(5)
  @JsonKey(name: 'deleted_at')
  final DateTime? deletedAt;
  @override
  @HiveField(6)
  @JsonKey(name: 'created_at')
  final DateTime? createdAt;
  @override
  @HiveField(7)
  @JsonKey(name: 'updated_at')
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'Brand(id: $id, businessId: $businessId, name: $name, description: $description, createdBy: $createdBy, deletedAt: $deletedAt, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BrandImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.businessId, businessId) ||
                other.businessId == businessId) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.createdBy, createdBy) ||
                other.createdBy == createdBy) &&
            (identical(other.deletedAt, deletedAt) ||
                other.deletedAt == deletedAt) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, businessId, name,
      description, createdBy, deletedAt, createdAt, updatedAt);

  /// Create a copy of Brand
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BrandImplCopyWith<_$BrandImpl> get copyWith =>
      __$$BrandImplCopyWithImpl<_$BrandImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$BrandImplToJson(
      this,
    );
  }
}

abstract class _Brand implements Brand {
  const factory _Brand(
      {@HiveField(0) @JsonKey(name: 'id') final int? id,
      @HiveField(1) @JsonKey(name: 'business_id') final int? businessId,
      @HiveField(2) @JsonKey(name: 'name') final String? name,
      @HiveField(3) @JsonKey(name: 'description') final String? description,
      @HiveField(4) @JsonKey(name: 'created_by') final int? createdBy,
      @HiveField(5) @JsonKey(name: 'deleted_at') final DateTime? deletedAt,
      @HiveField(6) @JsonKey(name: 'created_at') final DateTime? createdAt,
      @HiveField(7)
      @JsonKey(name: 'updated_at')
      final DateTime? updatedAt}) = _$BrandImpl;

  factory _Brand.fromJson(Map<String, dynamic> json) = _$BrandImpl.fromJson;

  @override
  @HiveField(0)
  @JsonKey(name: 'id')
  int? get id;
  @override
  @HiveField(1)
  @JsonKey(name: 'business_id')
  int? get businessId;
  @override
  @HiveField(2)
  @JsonKey(name: 'name')
  String? get name;
  @override
  @HiveField(3)
  @JsonKey(name: 'description')
  String? get description;
  @override
  @HiveField(4)
  @JsonKey(name: 'created_by')
  int? get createdBy;
  @override
  @HiveField(5)
  @JsonKey(name: 'deleted_at')
  DateTime? get deletedAt;
  @override
  @HiveField(6)
  @JsonKey(name: 'created_at')
  DateTime? get createdAt;
  @override
  @HiveField(7)
  @JsonKey(name: 'updated_at')
  DateTime? get updatedAt;

  /// Create a copy of Brand
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BrandImplCopyWith<_$BrandImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ProductLocation _$ProductLocationFromJson(Map<String, dynamic> json) {
  return _ProductLocation.fromJson(json);
}

/// @nodoc
mixin _$ProductLocation {
  @HiveField(0)
  int get id => throw _privateConstructorUsedError;
  @HiveField(1)
  @JsonKey(name: 'business_id')
  int? get businessId => throw _privateConstructorUsedError;
  @HiveField(2)
  @JsonKey(name: 'location_id')
  String? get locationId => throw _privateConstructorUsedError;
  @HiveField(3)
  String get name => throw _privateConstructorUsedError;
  @HiveField(4)
  @JsonKey(name: 'is_active')
  int? get isActive => throw _privateConstructorUsedError;
  @HiveField(5)
  @JsonKey(name: 'default_payment_accounts', fromJson: _decodeJsonString)
  Map<String, dynamic>? get defaultPaymentAccounts =>
      throw _privateConstructorUsedError;
  @HiveField(6)
  @JsonKey(name: 'created_at')
  DateTime? get createdAt => throw _privateConstructorUsedError;
  @HiveField(7)
  @JsonKey(name: 'updated_at')
  DateTime? get updatedAt => throw _privateConstructorUsedError;
  @HiveField(8)
  @JsonKey(name: 'purchase_account_id')
  String? get purchaseAccountId => throw _privateConstructorUsedError;
  @HiveField(9)
  @JsonKey(name: 'show_qty_info')
  int? get showQtyInfo => throw _privateConstructorUsedError;
  @HiveField(10)
  @JsonKey(name: 'activity_code')
  String? get activityCode => throw _privateConstructorUsedError;

  /// Serializes this ProductLocation to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProductLocation
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProductLocationCopyWith<ProductLocation> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProductLocationCopyWith<$Res> {
  factory $ProductLocationCopyWith(
          ProductLocation value, $Res Function(ProductLocation) then) =
      _$ProductLocationCopyWithImpl<$Res, ProductLocation>;
  @useResult
  $Res call(
      {@HiveField(0) int id,
      @HiveField(1) @JsonKey(name: 'business_id') int? businessId,
      @HiveField(2) @JsonKey(name: 'location_id') String? locationId,
      @HiveField(3) String name,
      @HiveField(4) @JsonKey(name: 'is_active') int? isActive,
      @HiveField(5)
      @JsonKey(name: 'default_payment_accounts', fromJson: _decodeJsonString)
      Map<String, dynamic>? defaultPaymentAccounts,
      @HiveField(6) @JsonKey(name: 'created_at') DateTime? createdAt,
      @HiveField(7) @JsonKey(name: 'updated_at') DateTime? updatedAt,
      @HiveField(8)
      @JsonKey(name: 'purchase_account_id')
      String? purchaseAccountId,
      @HiveField(9) @JsonKey(name: 'show_qty_info') int? showQtyInfo,
      @HiveField(10) @JsonKey(name: 'activity_code') String? activityCode});
}

/// @nodoc
class _$ProductLocationCopyWithImpl<$Res, $Val extends ProductLocation>
    implements $ProductLocationCopyWith<$Res> {
  _$ProductLocationCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProductLocation
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? businessId = freezed,
    Object? locationId = freezed,
    Object? name = null,
    Object? isActive = freezed,
    Object? defaultPaymentAccounts = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? purchaseAccountId = freezed,
    Object? showQtyInfo = freezed,
    Object? activityCode = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      businessId: freezed == businessId
          ? _value.businessId
          : businessId // ignore: cast_nullable_to_non_nullable
              as int?,
      locationId: freezed == locationId
          ? _value.locationId
          : locationId // ignore: cast_nullable_to_non_nullable
              as String?,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as int?,
      defaultPaymentAccounts: freezed == defaultPaymentAccounts
          ? _value.defaultPaymentAccounts
          : defaultPaymentAccounts // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      purchaseAccountId: freezed == purchaseAccountId
          ? _value.purchaseAccountId
          : purchaseAccountId // ignore: cast_nullable_to_non_nullable
              as String?,
      showQtyInfo: freezed == showQtyInfo
          ? _value.showQtyInfo
          : showQtyInfo // ignore: cast_nullable_to_non_nullable
              as int?,
      activityCode: freezed == activityCode
          ? _value.activityCode
          : activityCode // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProductLocationImplCopyWith<$Res>
    implements $ProductLocationCopyWith<$Res> {
  factory _$$ProductLocationImplCopyWith(_$ProductLocationImpl value,
          $Res Function(_$ProductLocationImpl) then) =
      __$$ProductLocationImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@HiveField(0) int id,
      @HiveField(1) @JsonKey(name: 'business_id') int? businessId,
      @HiveField(2) @JsonKey(name: 'location_id') String? locationId,
      @HiveField(3) String name,
      @HiveField(4) @JsonKey(name: 'is_active') int? isActive,
      @HiveField(5)
      @JsonKey(name: 'default_payment_accounts', fromJson: _decodeJsonString)
      Map<String, dynamic>? defaultPaymentAccounts,
      @HiveField(6) @JsonKey(name: 'created_at') DateTime? createdAt,
      @HiveField(7) @JsonKey(name: 'updated_at') DateTime? updatedAt,
      @HiveField(8)
      @JsonKey(name: 'purchase_account_id')
      String? purchaseAccountId,
      @HiveField(9) @JsonKey(name: 'show_qty_info') int? showQtyInfo,
      @HiveField(10) @JsonKey(name: 'activity_code') String? activityCode});
}

/// @nodoc
class __$$ProductLocationImplCopyWithImpl<$Res>
    extends _$ProductLocationCopyWithImpl<$Res, _$ProductLocationImpl>
    implements _$$ProductLocationImplCopyWith<$Res> {
  __$$ProductLocationImplCopyWithImpl(
      _$ProductLocationImpl _value, $Res Function(_$ProductLocationImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProductLocation
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? businessId = freezed,
    Object? locationId = freezed,
    Object? name = null,
    Object? isActive = freezed,
    Object? defaultPaymentAccounts = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? purchaseAccountId = freezed,
    Object? showQtyInfo = freezed,
    Object? activityCode = freezed,
  }) {
    return _then(_$ProductLocationImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      businessId: freezed == businessId
          ? _value.businessId
          : businessId // ignore: cast_nullable_to_non_nullable
              as int?,
      locationId: freezed == locationId
          ? _value.locationId
          : locationId // ignore: cast_nullable_to_non_nullable
              as String?,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      isActive: freezed == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as int?,
      defaultPaymentAccounts: freezed == defaultPaymentAccounts
          ? _value._defaultPaymentAccounts
          : defaultPaymentAccounts // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      purchaseAccountId: freezed == purchaseAccountId
          ? _value.purchaseAccountId
          : purchaseAccountId // ignore: cast_nullable_to_non_nullable
              as String?,
      showQtyInfo: freezed == showQtyInfo
          ? _value.showQtyInfo
          : showQtyInfo // ignore: cast_nullable_to_non_nullable
              as int?,
      activityCode: freezed == activityCode
          ? _value.activityCode
          : activityCode // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ProductLocationImpl implements _ProductLocation {
  const _$ProductLocationImpl(
      {@HiveField(0) required this.id,
      @HiveField(1) @JsonKey(name: 'business_id') this.businessId,
      @HiveField(2) @JsonKey(name: 'location_id') this.locationId,
      @HiveField(3) this.name = "N/A",
      @HiveField(4) @JsonKey(name: 'is_active') this.isActive,
      @HiveField(5)
      @JsonKey(name: 'default_payment_accounts', fromJson: _decodeJsonString)
      final Map<String, dynamic>? defaultPaymentAccounts,
      @HiveField(6) @JsonKey(name: 'created_at') this.createdAt,
      @HiveField(7) @JsonKey(name: 'updated_at') this.updatedAt,
      @HiveField(8)
      @JsonKey(name: 'purchase_account_id')
      this.purchaseAccountId,
      @HiveField(9) @JsonKey(name: 'show_qty_info') this.showQtyInfo,
      @HiveField(10) @JsonKey(name: 'activity_code') this.activityCode})
      : _defaultPaymentAccounts = defaultPaymentAccounts;

  factory _$ProductLocationImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProductLocationImplFromJson(json);

  @override
  @HiveField(0)
  final int id;
  @override
  @HiveField(1)
  @JsonKey(name: 'business_id')
  final int? businessId;
  @override
  @HiveField(2)
  @JsonKey(name: 'location_id')
  final String? locationId;
  @override
  @JsonKey()
  @HiveField(3)
  final String name;
  @override
  @HiveField(4)
  @JsonKey(name: 'is_active')
  final int? isActive;
  final Map<String, dynamic>? _defaultPaymentAccounts;
  @override
  @HiveField(5)
  @JsonKey(name: 'default_payment_accounts', fromJson: _decodeJsonString)
  Map<String, dynamic>? get defaultPaymentAccounts {
    final value = _defaultPaymentAccounts;
    if (value == null) return null;
    if (_defaultPaymentAccounts is EqualUnmodifiableMapView)
      return _defaultPaymentAccounts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(value);
  }

  @override
  @HiveField(6)
  @JsonKey(name: 'created_at')
  final DateTime? createdAt;
  @override
  @HiveField(7)
  @JsonKey(name: 'updated_at')
  final DateTime? updatedAt;
  @override
  @HiveField(8)
  @JsonKey(name: 'purchase_account_id')
  final String? purchaseAccountId;
  @override
  @HiveField(9)
  @JsonKey(name: 'show_qty_info')
  final int? showQtyInfo;
  @override
  @HiveField(10)
  @JsonKey(name: 'activity_code')
  final String? activityCode;

  @override
  String toString() {
    return 'ProductLocation(id: $id, businessId: $businessId, locationId: $locationId, name: $name, isActive: $isActive, defaultPaymentAccounts: $defaultPaymentAccounts, createdAt: $createdAt, updatedAt: $updatedAt, purchaseAccountId: $purchaseAccountId, showQtyInfo: $showQtyInfo, activityCode: $activityCode)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProductLocationImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.businessId, businessId) ||
                other.businessId == businessId) &&
            (identical(other.locationId, locationId) ||
                other.locationId == locationId) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            const DeepCollectionEquality().equals(
                other._defaultPaymentAccounts, _defaultPaymentAccounts) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.purchaseAccountId, purchaseAccountId) ||
                other.purchaseAccountId == purchaseAccountId) &&
            (identical(other.showQtyInfo, showQtyInfo) ||
                other.showQtyInfo == showQtyInfo) &&
            (identical(other.activityCode, activityCode) ||
                other.activityCode == activityCode));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      businessId,
      locationId,
      name,
      isActive,
      const DeepCollectionEquality().hash(_defaultPaymentAccounts),
      createdAt,
      updatedAt,
      purchaseAccountId,
      showQtyInfo,
      activityCode);

  /// Create a copy of ProductLocation
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProductLocationImplCopyWith<_$ProductLocationImpl> get copyWith =>
      __$$ProductLocationImplCopyWithImpl<_$ProductLocationImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProductLocationImplToJson(
      this,
    );
  }
}

abstract class _ProductLocation implements ProductLocation {
  const factory _ProductLocation(
      {@HiveField(0) required final int id,
      @HiveField(1) @JsonKey(name: 'business_id') final int? businessId,
      @HiveField(2) @JsonKey(name: 'location_id') final String? locationId,
      @HiveField(3) final String name,
      @HiveField(4) @JsonKey(name: 'is_active') final int? isActive,
      @HiveField(5)
      @JsonKey(name: 'default_payment_accounts', fromJson: _decodeJsonString)
      final Map<String, dynamic>? defaultPaymentAccounts,
      @HiveField(6) @JsonKey(name: 'created_at') final DateTime? createdAt,
      @HiveField(7) @JsonKey(name: 'updated_at') final DateTime? updatedAt,
      @HiveField(8)
      @JsonKey(name: 'purchase_account_id')
      final String? purchaseAccountId,
      @HiveField(9) @JsonKey(name: 'show_qty_info') final int? showQtyInfo,
      @HiveField(10)
      @JsonKey(name: 'activity_code')
      final String? activityCode}) = _$ProductLocationImpl;

  factory _ProductLocation.fromJson(Map<String, dynamic> json) =
      _$ProductLocationImpl.fromJson;

  @override
  @HiveField(0)
  int get id;
  @override
  @HiveField(1)
  @JsonKey(name: 'business_id')
  int? get businessId;
  @override
  @HiveField(2)
  @JsonKey(name: 'location_id')
  String? get locationId;
  @override
  @HiveField(3)
  String get name;
  @override
  @HiveField(4)
  @JsonKey(name: 'is_active')
  int? get isActive;
  @override
  @HiveField(5)
  @JsonKey(name: 'default_payment_accounts', fromJson: _decodeJsonString)
  Map<String, dynamic>? get defaultPaymentAccounts;
  @override
  @HiveField(6)
  @JsonKey(name: 'created_at')
  DateTime? get createdAt;
  @override
  @HiveField(7)
  @JsonKey(name: 'updated_at')
  DateTime? get updatedAt;
  @override
  @HiveField(8)
  @JsonKey(name: 'purchase_account_id')
  String? get purchaseAccountId;
  @override
  @HiveField(9)
  @JsonKey(name: 'show_qty_info')
  int? get showQtyInfo;
  @override
  @HiveField(10)
  @JsonKey(name: 'activity_code')
  String? get activityCode;

  /// Create a copy of ProductLocation
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProductLocationImplCopyWith<_$ProductLocationImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ProductVariation _$ProductVariationFromJson(Map<String, dynamic> json) {
  return _ProductVariation.fromJson(json);
}

/// @nodoc
mixin _$ProductVariation {
  @HiveField(0)
  int? get id => throw _privateConstructorUsedError;
  @HiveField(1)
  String? get name => throw _privateConstructorUsedError;
  @HiveField(2)
  @JsonKey(name: 'product_id')
  int? get productId => throw _privateConstructorUsedError;
  @HiveField(3)
  @JsonKey(name: 'created_at')
  DateTime? get createdAt => throw _privateConstructorUsedError;
  @HiveField(4)
  @JsonKey(name: 'updated_at')
  DateTime? get updatedAt => throw _privateConstructorUsedError;
  @HiveField(5)
  List<Variation>? get variations => throw _privateConstructorUsedError;

  /// Serializes this ProductVariation to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProductVariation
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProductVariationCopyWith<ProductVariation> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProductVariationCopyWith<$Res> {
  factory $ProductVariationCopyWith(
          ProductVariation value, $Res Function(ProductVariation) then) =
      _$ProductVariationCopyWithImpl<$Res, ProductVariation>;
  @useResult
  $Res call(
      {@HiveField(0) int? id,
      @HiveField(1) String? name,
      @HiveField(2) @JsonKey(name: 'product_id') int? productId,
      @HiveField(3) @JsonKey(name: 'created_at') DateTime? createdAt,
      @HiveField(4) @JsonKey(name: 'updated_at') DateTime? updatedAt,
      @HiveField(5) List<Variation>? variations});
}

/// @nodoc
class _$ProductVariationCopyWithImpl<$Res, $Val extends ProductVariation>
    implements $ProductVariationCopyWith<$Res> {
  _$ProductVariationCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProductVariation
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
    Object? productId = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? variations = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      productId: freezed == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as int?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      variations: freezed == variations
          ? _value.variations
          : variations // ignore: cast_nullable_to_non_nullable
              as List<Variation>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProductVariationImplCopyWith<$Res>
    implements $ProductVariationCopyWith<$Res> {
  factory _$$ProductVariationImplCopyWith(_$ProductVariationImpl value,
          $Res Function(_$ProductVariationImpl) then) =
      __$$ProductVariationImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@HiveField(0) int? id,
      @HiveField(1) String? name,
      @HiveField(2) @JsonKey(name: 'product_id') int? productId,
      @HiveField(3) @JsonKey(name: 'created_at') DateTime? createdAt,
      @HiveField(4) @JsonKey(name: 'updated_at') DateTime? updatedAt,
      @HiveField(5) List<Variation>? variations});
}

/// @nodoc
class __$$ProductVariationImplCopyWithImpl<$Res>
    extends _$ProductVariationCopyWithImpl<$Res, _$ProductVariationImpl>
    implements _$$ProductVariationImplCopyWith<$Res> {
  __$$ProductVariationImplCopyWithImpl(_$ProductVariationImpl _value,
      $Res Function(_$ProductVariationImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProductVariation
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? name = freezed,
    Object? productId = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
    Object? variations = freezed,
  }) {
    return _then(_$ProductVariationImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      productId: freezed == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as int?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      variations: freezed == variations
          ? _value._variations
          : variations // ignore: cast_nullable_to_non_nullable
              as List<Variation>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ProductVariationImpl implements _ProductVariation {
  const _$ProductVariationImpl(
      {@HiveField(0) this.id,
      @HiveField(1) this.name,
      @HiveField(2) @JsonKey(name: 'product_id') this.productId,
      @HiveField(3) @JsonKey(name: 'created_at') this.createdAt,
      @HiveField(4) @JsonKey(name: 'updated_at') this.updatedAt,
      @HiveField(5) final List<Variation>? variations})
      : _variations = variations;

  factory _$ProductVariationImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProductVariationImplFromJson(json);

  @override
  @HiveField(0)
  final int? id;
  @override
  @HiveField(1)
  final String? name;
  @override
  @HiveField(2)
  @JsonKey(name: 'product_id')
  final int? productId;
  @override
  @HiveField(3)
  @JsonKey(name: 'created_at')
  final DateTime? createdAt;
  @override
  @HiveField(4)
  @JsonKey(name: 'updated_at')
  final DateTime? updatedAt;
  final List<Variation>? _variations;
  @override
  @HiveField(5)
  List<Variation>? get variations {
    final value = _variations;
    if (value == null) return null;
    if (_variations is EqualUnmodifiableListView) return _variations;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'ProductVariation(id: $id, name: $name, productId: $productId, createdAt: $createdAt, updatedAt: $updatedAt, variations: $variations)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProductVariationImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.productId, productId) ||
                other.productId == productId) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            const DeepCollectionEquality()
                .equals(other._variations, _variations));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, name, productId, createdAt,
      updatedAt, const DeepCollectionEquality().hash(_variations));

  /// Create a copy of ProductVariation
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProductVariationImplCopyWith<_$ProductVariationImpl> get copyWith =>
      __$$ProductVariationImplCopyWithImpl<_$ProductVariationImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProductVariationImplToJson(
      this,
    );
  }
}

abstract class _ProductVariation implements ProductVariation {
  const factory _ProductVariation(
          {@HiveField(0) final int? id,
          @HiveField(1) final String? name,
          @HiveField(2) @JsonKey(name: 'product_id') final int? productId,
          @HiveField(3) @JsonKey(name: 'created_at') final DateTime? createdAt,
          @HiveField(4) @JsonKey(name: 'updated_at') final DateTime? updatedAt,
          @HiveField(5) final List<Variation>? variations}) =
      _$ProductVariationImpl;

  factory _ProductVariation.fromJson(Map<String, dynamic> json) =
      _$ProductVariationImpl.fromJson;

  @override
  @HiveField(0)
  int? get id;
  @override
  @HiveField(1)
  String? get name;
  @override
  @HiveField(2)
  @JsonKey(name: 'product_id')
  int? get productId;
  @override
  @HiveField(3)
  @JsonKey(name: 'created_at')
  DateTime? get createdAt;
  @override
  @HiveField(4)
  @JsonKey(name: 'updated_at')
  DateTime? get updatedAt;
  @override
  @HiveField(5)
  List<Variation>? get variations;

  /// Create a copy of ProductVariation
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProductVariationImplCopyWith<_$ProductVariationImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Variation _$VariationFromJson(Map<String, dynamic> json) {
  return _Variation.fromJson(json);
}

/// @nodoc
mixin _$Variation {
  @HiveField(0)
  int get id => throw _privateConstructorUsedError;
  @HiveField(1)
  @JsonKey(name: 'product_id')
  int get productId => throw _privateConstructorUsedError;
  @HiveField(2)
  String get name => throw _privateConstructorUsedError;
  @HiveField(3)
  @JsonKey(name: 'sub_sku')
  String get subSku => throw _privateConstructorUsedError;
  @HiveField(4)
  @JsonKey(name: 'product_variation_id')
  int get productVariationId => throw _privateConstructorUsedError;
  @HiveField(5)
  @JsonKey(name: 'default_purchase_price')
  @DoubleConverter()
  double get defaultPurchasePrice => throw _privateConstructorUsedError;
  @HiveField(6)
  @JsonKey(name: 'dpp_inc_tax')
  @DoubleConverter()
  double get dppIncTax => throw _privateConstructorUsedError;
  @HiveField(7)
  @JsonKey(name: 'profit_percent')
  @DoubleConverter()
  double get profitPercent => throw _privateConstructorUsedError;
  @HiveField(8)
  @JsonKey(name: 'default_sell_price')
  @DoubleConverter()
  double get defaultSellPrice => throw _privateConstructorUsedError;
  @HiveField(9)
  @JsonKey(name: 'sell_price_inc_tax')
  @DoubleConverter()
  double get sellPriceIncTax => throw _privateConstructorUsedError;
  @HiveField(10)
  @JsonKey(name: 'created_at')
  DateTime get createdAt => throw _privateConstructorUsedError;
  @HiveField(11)
  @JsonKey(name: 'updated_at')
  DateTime get updatedAt => throw _privateConstructorUsedError;
  @HiveField(12)
  @JsonKey(name: 'variation_location_details')
  List<VariationLocationDetails> get variationLocationDetails =>
      throw _privateConstructorUsedError;
  @HiveField(13)
  List<Media> get media => throw _privateConstructorUsedError;
  @HiveField(14)
  List<Discount> get discounts => throw _privateConstructorUsedError;
  @HiveField(15)
  @JsonKey(name: 'selling_price_group')
  List<ProductPriceGroup?> get sellingPriceGroups =>
      throw _privateConstructorUsedError;
  @HiveField(16)
  @JsonKey(name: 'combo_variations')
  List<ComboVariation?> get comboVariations =>
      throw _privateConstructorUsedError;

  /// Serializes this Variation to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Variation
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $VariationCopyWith<Variation> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VariationCopyWith<$Res> {
  factory $VariationCopyWith(Variation value, $Res Function(Variation) then) =
      _$VariationCopyWithImpl<$Res, Variation>;
  @useResult
  $Res call(
      {@HiveField(0) int id,
      @HiveField(1) @JsonKey(name: 'product_id') int productId,
      @HiveField(2) String name,
      @HiveField(3) @JsonKey(name: 'sub_sku') String subSku,
      @HiveField(4)
      @JsonKey(name: 'product_variation_id')
      int productVariationId,
      @HiveField(5)
      @JsonKey(name: 'default_purchase_price')
      @DoubleConverter()
      double defaultPurchasePrice,
      @HiveField(6)
      @JsonKey(name: 'dpp_inc_tax')
      @DoubleConverter()
      double dppIncTax,
      @HiveField(7)
      @JsonKey(name: 'profit_percent')
      @DoubleConverter()
      double profitPercent,
      @HiveField(8)
      @JsonKey(name: 'default_sell_price')
      @DoubleConverter()
      double defaultSellPrice,
      @HiveField(9)
      @JsonKey(name: 'sell_price_inc_tax')
      @DoubleConverter()
      double sellPriceIncTax,
      @HiveField(10) @JsonKey(name: 'created_at') DateTime createdAt,
      @HiveField(11) @JsonKey(name: 'updated_at') DateTime updatedAt,
      @HiveField(12)
      @JsonKey(name: 'variation_location_details')
      List<VariationLocationDetails> variationLocationDetails,
      @HiveField(13) List<Media> media,
      @HiveField(14) List<Discount> discounts,
      @HiveField(15)
      @JsonKey(name: 'selling_price_group')
      List<ProductPriceGroup?> sellingPriceGroups,
      @HiveField(16)
      @JsonKey(name: 'combo_variations')
      List<ComboVariation?> comboVariations});
}

/// @nodoc
class _$VariationCopyWithImpl<$Res, $Val extends Variation>
    implements $VariationCopyWith<$Res> {
  _$VariationCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Variation
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? productId = null,
    Object? name = null,
    Object? subSku = null,
    Object? productVariationId = null,
    Object? defaultPurchasePrice = null,
    Object? dppIncTax = null,
    Object? profitPercent = null,
    Object? defaultSellPrice = null,
    Object? sellPriceIncTax = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? variationLocationDetails = null,
    Object? media = null,
    Object? discounts = null,
    Object? sellingPriceGroups = null,
    Object? comboVariations = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      productId: null == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as int,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      subSku: null == subSku
          ? _value.subSku
          : subSku // ignore: cast_nullable_to_non_nullable
              as String,
      productVariationId: null == productVariationId
          ? _value.productVariationId
          : productVariationId // ignore: cast_nullable_to_non_nullable
              as int,
      defaultPurchasePrice: null == defaultPurchasePrice
          ? _value.defaultPurchasePrice
          : defaultPurchasePrice // ignore: cast_nullable_to_non_nullable
              as double,
      dppIncTax: null == dppIncTax
          ? _value.dppIncTax
          : dppIncTax // ignore: cast_nullable_to_non_nullable
              as double,
      profitPercent: null == profitPercent
          ? _value.profitPercent
          : profitPercent // ignore: cast_nullable_to_non_nullable
              as double,
      defaultSellPrice: null == defaultSellPrice
          ? _value.defaultSellPrice
          : defaultSellPrice // ignore: cast_nullable_to_non_nullable
              as double,
      sellPriceIncTax: null == sellPriceIncTax
          ? _value.sellPriceIncTax
          : sellPriceIncTax // ignore: cast_nullable_to_non_nullable
              as double,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      variationLocationDetails: null == variationLocationDetails
          ? _value.variationLocationDetails
          : variationLocationDetails // ignore: cast_nullable_to_non_nullable
              as List<VariationLocationDetails>,
      media: null == media
          ? _value.media
          : media // ignore: cast_nullable_to_non_nullable
              as List<Media>,
      discounts: null == discounts
          ? _value.discounts
          : discounts // ignore: cast_nullable_to_non_nullable
              as List<Discount>,
      sellingPriceGroups: null == sellingPriceGroups
          ? _value.sellingPriceGroups
          : sellingPriceGroups // ignore: cast_nullable_to_non_nullable
              as List<ProductPriceGroup?>,
      comboVariations: null == comboVariations
          ? _value.comboVariations
          : comboVariations // ignore: cast_nullable_to_non_nullable
              as List<ComboVariation?>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$VariationImplCopyWith<$Res>
    implements $VariationCopyWith<$Res> {
  factory _$$VariationImplCopyWith(
          _$VariationImpl value, $Res Function(_$VariationImpl) then) =
      __$$VariationImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@HiveField(0) int id,
      @HiveField(1) @JsonKey(name: 'product_id') int productId,
      @HiveField(2) String name,
      @HiveField(3) @JsonKey(name: 'sub_sku') String subSku,
      @HiveField(4)
      @JsonKey(name: 'product_variation_id')
      int productVariationId,
      @HiveField(5)
      @JsonKey(name: 'default_purchase_price')
      @DoubleConverter()
      double defaultPurchasePrice,
      @HiveField(6)
      @JsonKey(name: 'dpp_inc_tax')
      @DoubleConverter()
      double dppIncTax,
      @HiveField(7)
      @JsonKey(name: 'profit_percent')
      @DoubleConverter()
      double profitPercent,
      @HiveField(8)
      @JsonKey(name: 'default_sell_price')
      @DoubleConverter()
      double defaultSellPrice,
      @HiveField(9)
      @JsonKey(name: 'sell_price_inc_tax')
      @DoubleConverter()
      double sellPriceIncTax,
      @HiveField(10) @JsonKey(name: 'created_at') DateTime createdAt,
      @HiveField(11) @JsonKey(name: 'updated_at') DateTime updatedAt,
      @HiveField(12)
      @JsonKey(name: 'variation_location_details')
      List<VariationLocationDetails> variationLocationDetails,
      @HiveField(13) List<Media> media,
      @HiveField(14) List<Discount> discounts,
      @HiveField(15)
      @JsonKey(name: 'selling_price_group')
      List<ProductPriceGroup?> sellingPriceGroups,
      @HiveField(16)
      @JsonKey(name: 'combo_variations')
      List<ComboVariation?> comboVariations});
}

/// @nodoc
class __$$VariationImplCopyWithImpl<$Res>
    extends _$VariationCopyWithImpl<$Res, _$VariationImpl>
    implements _$$VariationImplCopyWith<$Res> {
  __$$VariationImplCopyWithImpl(
      _$VariationImpl _value, $Res Function(_$VariationImpl) _then)
      : super(_value, _then);

  /// Create a copy of Variation
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? productId = null,
    Object? name = null,
    Object? subSku = null,
    Object? productVariationId = null,
    Object? defaultPurchasePrice = null,
    Object? dppIncTax = null,
    Object? profitPercent = null,
    Object? defaultSellPrice = null,
    Object? sellPriceIncTax = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? variationLocationDetails = null,
    Object? media = null,
    Object? discounts = null,
    Object? sellingPriceGroups = null,
    Object? comboVariations = null,
  }) {
    return _then(_$VariationImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      productId: null == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as int,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      subSku: null == subSku
          ? _value.subSku
          : subSku // ignore: cast_nullable_to_non_nullable
              as String,
      productVariationId: null == productVariationId
          ? _value.productVariationId
          : productVariationId // ignore: cast_nullable_to_non_nullable
              as int,
      defaultPurchasePrice: null == defaultPurchasePrice
          ? _value.defaultPurchasePrice
          : defaultPurchasePrice // ignore: cast_nullable_to_non_nullable
              as double,
      dppIncTax: null == dppIncTax
          ? _value.dppIncTax
          : dppIncTax // ignore: cast_nullable_to_non_nullable
              as double,
      profitPercent: null == profitPercent
          ? _value.profitPercent
          : profitPercent // ignore: cast_nullable_to_non_nullable
              as double,
      defaultSellPrice: null == defaultSellPrice
          ? _value.defaultSellPrice
          : defaultSellPrice // ignore: cast_nullable_to_non_nullable
              as double,
      sellPriceIncTax: null == sellPriceIncTax
          ? _value.sellPriceIncTax
          : sellPriceIncTax // ignore: cast_nullable_to_non_nullable
              as double,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      variationLocationDetails: null == variationLocationDetails
          ? _value._variationLocationDetails
          : variationLocationDetails // ignore: cast_nullable_to_non_nullable
              as List<VariationLocationDetails>,
      media: null == media
          ? _value._media
          : media // ignore: cast_nullable_to_non_nullable
              as List<Media>,
      discounts: null == discounts
          ? _value._discounts
          : discounts // ignore: cast_nullable_to_non_nullable
              as List<Discount>,
      sellingPriceGroups: null == sellingPriceGroups
          ? _value._sellingPriceGroups
          : sellingPriceGroups // ignore: cast_nullable_to_non_nullable
              as List<ProductPriceGroup?>,
      comboVariations: null == comboVariations
          ? _value._comboVariations
          : comboVariations // ignore: cast_nullable_to_non_nullable
              as List<ComboVariation?>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$VariationImpl implements _Variation {
  const _$VariationImpl(
      {@HiveField(0) required this.id,
      @HiveField(1) @JsonKey(name: 'product_id') required this.productId,
      @HiveField(2) required this.name,
      @HiveField(3) @JsonKey(name: 'sub_sku') required this.subSku,
      @HiveField(4)
      @JsonKey(name: 'product_variation_id')
      required this.productVariationId,
      @HiveField(5)
      @JsonKey(name: 'default_purchase_price')
      @DoubleConverter()
      required this.defaultPurchasePrice,
      @HiveField(6)
      @JsonKey(name: 'dpp_inc_tax')
      @DoubleConverter()
      required this.dppIncTax,
      @HiveField(7)
      @JsonKey(name: 'profit_percent')
      @DoubleConverter()
      required this.profitPercent,
      @HiveField(8)
      @JsonKey(name: 'default_sell_price')
      @DoubleConverter()
      required this.defaultSellPrice,
      @HiveField(9)
      @JsonKey(name: 'sell_price_inc_tax')
      @DoubleConverter()
      required this.sellPriceIncTax,
      @HiveField(10) @JsonKey(name: 'created_at') required this.createdAt,
      @HiveField(11) @JsonKey(name: 'updated_at') required this.updatedAt,
      @HiveField(12)
      @JsonKey(name: 'variation_location_details')
      required final List<VariationLocationDetails> variationLocationDetails,
      @HiveField(13) required final List<Media> media,
      @HiveField(14) final List<Discount> discounts = const [],
      @HiveField(15)
      @JsonKey(name: 'selling_price_group')
      final List<ProductPriceGroup?> sellingPriceGroups = const [],
      @HiveField(16)
      @JsonKey(name: 'combo_variations')
      final List<ComboVariation?> comboVariations = const []})
      : _variationLocationDetails = variationLocationDetails,
        _media = media,
        _discounts = discounts,
        _sellingPriceGroups = sellingPriceGroups,
        _comboVariations = comboVariations;

  factory _$VariationImpl.fromJson(Map<String, dynamic> json) =>
      _$$VariationImplFromJson(json);

  @override
  @HiveField(0)
  final int id;
  @override
  @HiveField(1)
  @JsonKey(name: 'product_id')
  final int productId;
  @override
  @HiveField(2)
  final String name;
  @override
  @HiveField(3)
  @JsonKey(name: 'sub_sku')
  final String subSku;
  @override
  @HiveField(4)
  @JsonKey(name: 'product_variation_id')
  final int productVariationId;
  @override
  @HiveField(5)
  @JsonKey(name: 'default_purchase_price')
  @DoubleConverter()
  final double defaultPurchasePrice;
  @override
  @HiveField(6)
  @JsonKey(name: 'dpp_inc_tax')
  @DoubleConverter()
  final double dppIncTax;
  @override
  @HiveField(7)
  @JsonKey(name: 'profit_percent')
  @DoubleConverter()
  final double profitPercent;
  @override
  @HiveField(8)
  @JsonKey(name: 'default_sell_price')
  @DoubleConverter()
  final double defaultSellPrice;
  @override
  @HiveField(9)
  @JsonKey(name: 'sell_price_inc_tax')
  @DoubleConverter()
  final double sellPriceIncTax;
  @override
  @HiveField(10)
  @JsonKey(name: 'created_at')
  final DateTime createdAt;
  @override
  @HiveField(11)
  @JsonKey(name: 'updated_at')
  final DateTime updatedAt;
  final List<VariationLocationDetails> _variationLocationDetails;
  @override
  @HiveField(12)
  @JsonKey(name: 'variation_location_details')
  List<VariationLocationDetails> get variationLocationDetails {
    if (_variationLocationDetails is EqualUnmodifiableListView)
      return _variationLocationDetails;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_variationLocationDetails);
  }

  final List<Media> _media;
  @override
  @HiveField(13)
  List<Media> get media {
    if (_media is EqualUnmodifiableListView) return _media;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_media);
  }

  final List<Discount> _discounts;
  @override
  @JsonKey()
  @HiveField(14)
  List<Discount> get discounts {
    if (_discounts is EqualUnmodifiableListView) return _discounts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_discounts);
  }

  final List<ProductPriceGroup?> _sellingPriceGroups;
  @override
  @HiveField(15)
  @JsonKey(name: 'selling_price_group')
  List<ProductPriceGroup?> get sellingPriceGroups {
    if (_sellingPriceGroups is EqualUnmodifiableListView)
      return _sellingPriceGroups;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_sellingPriceGroups);
  }

  final List<ComboVariation?> _comboVariations;
  @override
  @HiveField(16)
  @JsonKey(name: 'combo_variations')
  List<ComboVariation?> get comboVariations {
    if (_comboVariations is EqualUnmodifiableListView) return _comboVariations;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_comboVariations);
  }

  @override
  String toString() {
    return 'Variation(id: $id, productId: $productId, name: $name, subSku: $subSku, productVariationId: $productVariationId, defaultPurchasePrice: $defaultPurchasePrice, dppIncTax: $dppIncTax, profitPercent: $profitPercent, defaultSellPrice: $defaultSellPrice, sellPriceIncTax: $sellPriceIncTax, createdAt: $createdAt, updatedAt: $updatedAt, variationLocationDetails: $variationLocationDetails, media: $media, discounts: $discounts, sellingPriceGroups: $sellingPriceGroups, comboVariations: $comboVariations)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VariationImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.productId, productId) ||
                other.productId == productId) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.subSku, subSku) || other.subSku == subSku) &&
            (identical(other.productVariationId, productVariationId) ||
                other.productVariationId == productVariationId) &&
            (identical(other.defaultPurchasePrice, defaultPurchasePrice) ||
                other.defaultPurchasePrice == defaultPurchasePrice) &&
            (identical(other.dppIncTax, dppIncTax) ||
                other.dppIncTax == dppIncTax) &&
            (identical(other.profitPercent, profitPercent) ||
                other.profitPercent == profitPercent) &&
            (identical(other.defaultSellPrice, defaultSellPrice) ||
                other.defaultSellPrice == defaultSellPrice) &&
            (identical(other.sellPriceIncTax, sellPriceIncTax) ||
                other.sellPriceIncTax == sellPriceIncTax) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            const DeepCollectionEquality().equals(
                other._variationLocationDetails, _variationLocationDetails) &&
            const DeepCollectionEquality().equals(other._media, _media) &&
            const DeepCollectionEquality()
                .equals(other._discounts, _discounts) &&
            const DeepCollectionEquality()
                .equals(other._sellingPriceGroups, _sellingPriceGroups) &&
            const DeepCollectionEquality()
                .equals(other._comboVariations, _comboVariations));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      productId,
      name,
      subSku,
      productVariationId,
      defaultPurchasePrice,
      dppIncTax,
      profitPercent,
      defaultSellPrice,
      sellPriceIncTax,
      createdAt,
      updatedAt,
      const DeepCollectionEquality().hash(_variationLocationDetails),
      const DeepCollectionEquality().hash(_media),
      const DeepCollectionEquality().hash(_discounts),
      const DeepCollectionEquality().hash(_sellingPriceGroups),
      const DeepCollectionEquality().hash(_comboVariations));

  /// Create a copy of Variation
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$VariationImplCopyWith<_$VariationImpl> get copyWith =>
      __$$VariationImplCopyWithImpl<_$VariationImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$VariationImplToJson(
      this,
    );
  }
}

abstract class _Variation implements Variation {
  const factory _Variation(
      {@HiveField(0) required final int id,
      @HiveField(1) @JsonKey(name: 'product_id') required final int productId,
      @HiveField(2) required final String name,
      @HiveField(3) @JsonKey(name: 'sub_sku') required final String subSku,
      @HiveField(4)
      @JsonKey(name: 'product_variation_id')
      required final int productVariationId,
      @HiveField(5)
      @JsonKey(name: 'default_purchase_price')
      @DoubleConverter()
      required final double defaultPurchasePrice,
      @HiveField(6)
      @JsonKey(name: 'dpp_inc_tax')
      @DoubleConverter()
      required final double dppIncTax,
      @HiveField(7)
      @JsonKey(name: 'profit_percent')
      @DoubleConverter()
      required final double profitPercent,
      @HiveField(8)
      @JsonKey(name: 'default_sell_price')
      @DoubleConverter()
      required final double defaultSellPrice,
      @HiveField(9)
      @JsonKey(name: 'sell_price_inc_tax')
      @DoubleConverter()
      required final double sellPriceIncTax,
      @HiveField(10)
      @JsonKey(name: 'created_at')
      required final DateTime createdAt,
      @HiveField(11)
      @JsonKey(name: 'updated_at')
      required final DateTime updatedAt,
      @HiveField(12)
      @JsonKey(name: 'variation_location_details')
      required final List<VariationLocationDetails> variationLocationDetails,
      @HiveField(13) required final List<Media> media,
      @HiveField(14) final List<Discount> discounts,
      @HiveField(15)
      @JsonKey(name: 'selling_price_group')
      final List<ProductPriceGroup?> sellingPriceGroups,
      @HiveField(16)
      @JsonKey(name: 'combo_variations')
      final List<ComboVariation?> comboVariations}) = _$VariationImpl;

  factory _Variation.fromJson(Map<String, dynamic> json) =
      _$VariationImpl.fromJson;

  @override
  @HiveField(0)
  int get id;
  @override
  @HiveField(1)
  @JsonKey(name: 'product_id')
  int get productId;
  @override
  @HiveField(2)
  String get name;
  @override
  @HiveField(3)
  @JsonKey(name: 'sub_sku')
  String get subSku;
  @override
  @HiveField(4)
  @JsonKey(name: 'product_variation_id')
  int get productVariationId;
  @override
  @HiveField(5)
  @JsonKey(name: 'default_purchase_price')
  @DoubleConverter()
  double get defaultPurchasePrice;
  @override
  @HiveField(6)
  @JsonKey(name: 'dpp_inc_tax')
  @DoubleConverter()
  double get dppIncTax;
  @override
  @HiveField(7)
  @JsonKey(name: 'profit_percent')
  @DoubleConverter()
  double get profitPercent;
  @override
  @HiveField(8)
  @JsonKey(name: 'default_sell_price')
  @DoubleConverter()
  double get defaultSellPrice;
  @override
  @HiveField(9)
  @JsonKey(name: 'sell_price_inc_tax')
  @DoubleConverter()
  double get sellPriceIncTax;
  @override
  @HiveField(10)
  @JsonKey(name: 'created_at')
  DateTime get createdAt;
  @override
  @HiveField(11)
  @JsonKey(name: 'updated_at')
  DateTime get updatedAt;
  @override
  @HiveField(12)
  @JsonKey(name: 'variation_location_details')
  List<VariationLocationDetails> get variationLocationDetails;
  @override
  @HiveField(13)
  List<Media> get media;
  @override
  @HiveField(14)
  List<Discount> get discounts;
  @override
  @HiveField(15)
  @JsonKey(name: 'selling_price_group')
  List<ProductPriceGroup?> get sellingPriceGroups;
  @override
  @HiveField(16)
  @JsonKey(name: 'combo_variations')
  List<ComboVariation?> get comboVariations;

  /// Create a copy of Variation
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$VariationImplCopyWith<_$VariationImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

VariationLocationDetails _$VariationLocationDetailsFromJson(
    Map<String, dynamic> json) {
  return _VariationLocationDetails.fromJson(json);
}

/// @nodoc
mixin _$VariationLocationDetails {
  @HiveField(0)
  int get id => throw _privateConstructorUsedError;
  @HiveField(1)
  @JsonKey(name: 'product_id')
  int get productId => throw _privateConstructorUsedError;
  @HiveField(2)
  @JsonKey(name: 'product_variation_id')
  int get productVariationId => throw _privateConstructorUsedError;
  @HiveField(3)
  @JsonKey(name: 'variation_id')
  int get variationId => throw _privateConstructorUsedError;
  @HiveField(4)
  @JsonKey(name: 'location_id')
  int get locationId => throw _privateConstructorUsedError;
  @HiveField(5)
  @JsonKey(name: 'qty_available', fromJson: _parseDouble)
  double get qtyAvailable => throw _privateConstructorUsedError;
  @HiveField(6)
  @JsonKey(name: 'created_at', fromJson: DateTime.parse)
  DateTime get createdAt => throw _privateConstructorUsedError;
  @HiveField(7)
  @JsonKey(name: 'updated_at', fromJson: DateTime.parse)
  DateTime get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this VariationLocationDetails to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of VariationLocationDetails
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $VariationLocationDetailsCopyWith<VariationLocationDetails> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VariationLocationDetailsCopyWith<$Res> {
  factory $VariationLocationDetailsCopyWith(VariationLocationDetails value,
          $Res Function(VariationLocationDetails) then) =
      _$VariationLocationDetailsCopyWithImpl<$Res, VariationLocationDetails>;
  @useResult
  $Res call(
      {@HiveField(0) int id,
      @HiveField(1) @JsonKey(name: 'product_id') int productId,
      @HiveField(2)
      @JsonKey(name: 'product_variation_id')
      int productVariationId,
      @HiveField(3) @JsonKey(name: 'variation_id') int variationId,
      @HiveField(4) @JsonKey(name: 'location_id') int locationId,
      @HiveField(5)
      @JsonKey(name: 'qty_available', fromJson: _parseDouble)
      double qtyAvailable,
      @HiveField(6)
      @JsonKey(name: 'created_at', fromJson: DateTime.parse)
      DateTime createdAt,
      @HiveField(7)
      @JsonKey(name: 'updated_at', fromJson: DateTime.parse)
      DateTime updatedAt});
}

/// @nodoc
class _$VariationLocationDetailsCopyWithImpl<$Res,
        $Val extends VariationLocationDetails>
    implements $VariationLocationDetailsCopyWith<$Res> {
  _$VariationLocationDetailsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of VariationLocationDetails
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? productId = null,
    Object? productVariationId = null,
    Object? variationId = null,
    Object? locationId = null,
    Object? qtyAvailable = null,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      productId: null == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as int,
      productVariationId: null == productVariationId
          ? _value.productVariationId
          : productVariationId // ignore: cast_nullable_to_non_nullable
              as int,
      variationId: null == variationId
          ? _value.variationId
          : variationId // ignore: cast_nullable_to_non_nullable
              as int,
      locationId: null == locationId
          ? _value.locationId
          : locationId // ignore: cast_nullable_to_non_nullable
              as int,
      qtyAvailable: null == qtyAvailable
          ? _value.qtyAvailable
          : qtyAvailable // ignore: cast_nullable_to_non_nullable
              as double,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$VariationLocationDetailsImplCopyWith<$Res>
    implements $VariationLocationDetailsCopyWith<$Res> {
  factory _$$VariationLocationDetailsImplCopyWith(
          _$VariationLocationDetailsImpl value,
          $Res Function(_$VariationLocationDetailsImpl) then) =
      __$$VariationLocationDetailsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@HiveField(0) int id,
      @HiveField(1) @JsonKey(name: 'product_id') int productId,
      @HiveField(2)
      @JsonKey(name: 'product_variation_id')
      int productVariationId,
      @HiveField(3) @JsonKey(name: 'variation_id') int variationId,
      @HiveField(4) @JsonKey(name: 'location_id') int locationId,
      @HiveField(5)
      @JsonKey(name: 'qty_available', fromJson: _parseDouble)
      double qtyAvailable,
      @HiveField(6)
      @JsonKey(name: 'created_at', fromJson: DateTime.parse)
      DateTime createdAt,
      @HiveField(7)
      @JsonKey(name: 'updated_at', fromJson: DateTime.parse)
      DateTime updatedAt});
}

/// @nodoc
class __$$VariationLocationDetailsImplCopyWithImpl<$Res>
    extends _$VariationLocationDetailsCopyWithImpl<$Res,
        _$VariationLocationDetailsImpl>
    implements _$$VariationLocationDetailsImplCopyWith<$Res> {
  __$$VariationLocationDetailsImplCopyWithImpl(
      _$VariationLocationDetailsImpl _value,
      $Res Function(_$VariationLocationDetailsImpl) _then)
      : super(_value, _then);

  /// Create a copy of VariationLocationDetails
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? productId = null,
    Object? productVariationId = null,
    Object? variationId = null,
    Object? locationId = null,
    Object? qtyAvailable = null,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(_$VariationLocationDetailsImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      productId: null == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as int,
      productVariationId: null == productVariationId
          ? _value.productVariationId
          : productVariationId // ignore: cast_nullable_to_non_nullable
              as int,
      variationId: null == variationId
          ? _value.variationId
          : variationId // ignore: cast_nullable_to_non_nullable
              as int,
      locationId: null == locationId
          ? _value.locationId
          : locationId // ignore: cast_nullable_to_non_nullable
              as int,
      qtyAvailable: null == qtyAvailable
          ? _value.qtyAvailable
          : qtyAvailable // ignore: cast_nullable_to_non_nullable
              as double,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$VariationLocationDetailsImpl implements _VariationLocationDetails {
  const _$VariationLocationDetailsImpl(
      {@HiveField(0) required this.id,
      @HiveField(1) @JsonKey(name: 'product_id') required this.productId,
      @HiveField(2)
      @JsonKey(name: 'product_variation_id')
      required this.productVariationId,
      @HiveField(3) @JsonKey(name: 'variation_id') required this.variationId,
      @HiveField(4) @JsonKey(name: 'location_id') required this.locationId,
      @HiveField(5)
      @JsonKey(name: 'qty_available', fromJson: _parseDouble)
      required this.qtyAvailable,
      @HiveField(6)
      @JsonKey(name: 'created_at', fromJson: DateTime.parse)
      required this.createdAt,
      @HiveField(7)
      @JsonKey(name: 'updated_at', fromJson: DateTime.parse)
      required this.updatedAt});

  factory _$VariationLocationDetailsImpl.fromJson(Map<String, dynamic> json) =>
      _$$VariationLocationDetailsImplFromJson(json);

  @override
  @HiveField(0)
  final int id;
  @override
  @HiveField(1)
  @JsonKey(name: 'product_id')
  final int productId;
  @override
  @HiveField(2)
  @JsonKey(name: 'product_variation_id')
  final int productVariationId;
  @override
  @HiveField(3)
  @JsonKey(name: 'variation_id')
  final int variationId;
  @override
  @HiveField(4)
  @JsonKey(name: 'location_id')
  final int locationId;
  @override
  @HiveField(5)
  @JsonKey(name: 'qty_available', fromJson: _parseDouble)
  final double qtyAvailable;
  @override
  @HiveField(6)
  @JsonKey(name: 'created_at', fromJson: DateTime.parse)
  final DateTime createdAt;
  @override
  @HiveField(7)
  @JsonKey(name: 'updated_at', fromJson: DateTime.parse)
  final DateTime updatedAt;

  @override
  String toString() {
    return 'VariationLocationDetails(id: $id, productId: $productId, productVariationId: $productVariationId, variationId: $variationId, locationId: $locationId, qtyAvailable: $qtyAvailable, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VariationLocationDetailsImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.productId, productId) ||
                other.productId == productId) &&
            (identical(other.productVariationId, productVariationId) ||
                other.productVariationId == productVariationId) &&
            (identical(other.variationId, variationId) ||
                other.variationId == variationId) &&
            (identical(other.locationId, locationId) ||
                other.locationId == locationId) &&
            (identical(other.qtyAvailable, qtyAvailable) ||
                other.qtyAvailable == qtyAvailable) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      productId,
      productVariationId,
      variationId,
      locationId,
      qtyAvailable,
      createdAt,
      updatedAt);

  /// Create a copy of VariationLocationDetails
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$VariationLocationDetailsImplCopyWith<_$VariationLocationDetailsImpl>
      get copyWith => __$$VariationLocationDetailsImplCopyWithImpl<
          _$VariationLocationDetailsImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$VariationLocationDetailsImplToJson(
      this,
    );
  }
}

abstract class _VariationLocationDetails implements VariationLocationDetails {
  const factory _VariationLocationDetails(
      {@HiveField(0) required final int id,
      @HiveField(1) @JsonKey(name: 'product_id') required final int productId,
      @HiveField(2)
      @JsonKey(name: 'product_variation_id')
      required final int productVariationId,
      @HiveField(3)
      @JsonKey(name: 'variation_id')
      required final int variationId,
      @HiveField(4) @JsonKey(name: 'location_id') required final int locationId,
      @HiveField(5)
      @JsonKey(name: 'qty_available', fromJson: _parseDouble)
      required final double qtyAvailable,
      @HiveField(6)
      @JsonKey(name: 'created_at', fromJson: DateTime.parse)
      required final DateTime createdAt,
      @HiveField(7)
      @JsonKey(name: 'updated_at', fromJson: DateTime.parse)
      required final DateTime updatedAt}) = _$VariationLocationDetailsImpl;

  factory _VariationLocationDetails.fromJson(Map<String, dynamic> json) =
      _$VariationLocationDetailsImpl.fromJson;

  @override
  @HiveField(0)
  int get id;
  @override
  @HiveField(1)
  @JsonKey(name: 'product_id')
  int get productId;
  @override
  @HiveField(2)
  @JsonKey(name: 'product_variation_id')
  int get productVariationId;
  @override
  @HiveField(3)
  @JsonKey(name: 'variation_id')
  int get variationId;
  @override
  @HiveField(4)
  @JsonKey(name: 'location_id')
  int get locationId;
  @override
  @HiveField(5)
  @JsonKey(name: 'qty_available', fromJson: _parseDouble)
  double get qtyAvailable;
  @override
  @HiveField(6)
  @JsonKey(name: 'created_at', fromJson: DateTime.parse)
  DateTime get createdAt;
  @override
  @HiveField(7)
  @JsonKey(name: 'updated_at', fromJson: DateTime.parse)
  DateTime get updatedAt;

  /// Create a copy of VariationLocationDetails
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$VariationLocationDetailsImplCopyWith<_$VariationLocationDetailsImpl>
      get copyWith => throw _privateConstructorUsedError;
}

Discount _$DiscountFromJson(Map<String, dynamic> json) {
  return _Discount.fromJson(json);
}

/// @nodoc
mixin _$Discount {
  int get id => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  @JsonKey(name: 'business_id')
  int get businessId => throw _privateConstructorUsedError;
  @JsonKey(name: 'brand_id')
  int? get brandId => throw _privateConstructorUsedError;
  @JsonKey(name: 'category_id')
  int? get categoryId => throw _privateConstructorUsedError;
  @JsonKey(name: 'location_id')
  int get locationId => throw _privateConstructorUsedError;
  int get priority => throw _privateConstructorUsedError;
  @JsonKey(name: 'discount_type')
  @DiscountTypeConverter()
  DiscountType get discountType => throw _privateConstructorUsedError;
  @JsonKey(name: 'discount_amount')
  @DoubleConverter()
  double get discountAmount => throw _privateConstructorUsedError;
  @JsonKey(name: 'starts_at')
  @DateTimeConverter()
  DateTime get startsAt => throw _privateConstructorUsedError;
  @JsonKey(name: 'ends_at')
  @DateTimeConverter()
  DateTime get endsAt => throw _privateConstructorUsedError;
  @JsonKey(name: 'is_active')
  @BooleanConverter()
  bool get isActive => throw _privateConstructorUsedError;
  String? get spg => throw _privateConstructorUsedError;
  @JsonKey(name: 'applicable_in_cg')
  @BooleanConverter()
  bool get applicableInCg => throw _privateConstructorUsedError;
  @JsonKey(name: 'created_at')
  @DateTimeConverter()
  DateTime get createdAt => throw _privateConstructorUsedError;
  @JsonKey(name: 'updated_at')
  @DateTimeConverter()
  DateTime get updatedAt => throw _privateConstructorUsedError;
  @JsonKey(name: 'price_group_id')
  String get priceGroupId => throw _privateConstructorUsedError;
  @JsonKey(name: 'applicable_in_spg')
  @BooleanConverter()
  bool get applicableInSpg => throw _privateConstructorUsedError;
  @JsonKey(name: 'max_qty')
  @DoubleConverter()
  double get maxQty => throw _privateConstructorUsedError;
  String? get coupon => throw _privateConstructorUsedError;
  @JsonKey(name: 'formated_starts_at')
  String get formatedStartsAt => throw _privateConstructorUsedError;
  @JsonKey(name: 'formated_ends_at')
  String get formatedEndsAt => throw _privateConstructorUsedError;

  /// Serializes this Discount to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Discount
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DiscountCopyWith<Discount> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DiscountCopyWith<$Res> {
  factory $DiscountCopyWith(Discount value, $Res Function(Discount) then) =
      _$DiscountCopyWithImpl<$Res, Discount>;
  @useResult
  $Res call(
      {int id,
      String name,
      @JsonKey(name: 'business_id') int businessId,
      @JsonKey(name: 'brand_id') int? brandId,
      @JsonKey(name: 'category_id') int? categoryId,
      @JsonKey(name: 'location_id') int locationId,
      int priority,
      @JsonKey(name: 'discount_type')
      @DiscountTypeConverter()
      DiscountType discountType,
      @JsonKey(name: 'discount_amount')
      @DoubleConverter()
      double discountAmount,
      @JsonKey(name: 'starts_at') @DateTimeConverter() DateTime startsAt,
      @JsonKey(name: 'ends_at') @DateTimeConverter() DateTime endsAt,
      @JsonKey(name: 'is_active') @BooleanConverter() bool isActive,
      String? spg,
      @JsonKey(name: 'applicable_in_cg')
      @BooleanConverter()
      bool applicableInCg,
      @JsonKey(name: 'created_at') @DateTimeConverter() DateTime createdAt,
      @JsonKey(name: 'updated_at') @DateTimeConverter() DateTime updatedAt,
      @JsonKey(name: 'price_group_id') String priceGroupId,
      @JsonKey(name: 'applicable_in_spg')
      @BooleanConverter()
      bool applicableInSpg,
      @JsonKey(name: 'max_qty') @DoubleConverter() double maxQty,
      String? coupon,
      @JsonKey(name: 'formated_starts_at') String formatedStartsAt,
      @JsonKey(name: 'formated_ends_at') String formatedEndsAt});
}

/// @nodoc
class _$DiscountCopyWithImpl<$Res, $Val extends Discount>
    implements $DiscountCopyWith<$Res> {
  _$DiscountCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Discount
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? businessId = null,
    Object? brandId = freezed,
    Object? categoryId = freezed,
    Object? locationId = null,
    Object? priority = null,
    Object? discountType = null,
    Object? discountAmount = null,
    Object? startsAt = null,
    Object? endsAt = null,
    Object? isActive = null,
    Object? spg = freezed,
    Object? applicableInCg = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? priceGroupId = null,
    Object? applicableInSpg = null,
    Object? maxQty = null,
    Object? coupon = freezed,
    Object? formatedStartsAt = null,
    Object? formatedEndsAt = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      businessId: null == businessId
          ? _value.businessId
          : businessId // ignore: cast_nullable_to_non_nullable
              as int,
      brandId: freezed == brandId
          ? _value.brandId
          : brandId // ignore: cast_nullable_to_non_nullable
              as int?,
      categoryId: freezed == categoryId
          ? _value.categoryId
          : categoryId // ignore: cast_nullable_to_non_nullable
              as int?,
      locationId: null == locationId
          ? _value.locationId
          : locationId // ignore: cast_nullable_to_non_nullable
              as int,
      priority: null == priority
          ? _value.priority
          : priority // ignore: cast_nullable_to_non_nullable
              as int,
      discountType: null == discountType
          ? _value.discountType
          : discountType // ignore: cast_nullable_to_non_nullable
              as DiscountType,
      discountAmount: null == discountAmount
          ? _value.discountAmount
          : discountAmount // ignore: cast_nullable_to_non_nullable
              as double,
      startsAt: null == startsAt
          ? _value.startsAt
          : startsAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      endsAt: null == endsAt
          ? _value.endsAt
          : endsAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      isActive: null == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
      spg: freezed == spg
          ? _value.spg
          : spg // ignore: cast_nullable_to_non_nullable
              as String?,
      applicableInCg: null == applicableInCg
          ? _value.applicableInCg
          : applicableInCg // ignore: cast_nullable_to_non_nullable
              as bool,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      priceGroupId: null == priceGroupId
          ? _value.priceGroupId
          : priceGroupId // ignore: cast_nullable_to_non_nullable
              as String,
      applicableInSpg: null == applicableInSpg
          ? _value.applicableInSpg
          : applicableInSpg // ignore: cast_nullable_to_non_nullable
              as bool,
      maxQty: null == maxQty
          ? _value.maxQty
          : maxQty // ignore: cast_nullable_to_non_nullable
              as double,
      coupon: freezed == coupon
          ? _value.coupon
          : coupon // ignore: cast_nullable_to_non_nullable
              as String?,
      formatedStartsAt: null == formatedStartsAt
          ? _value.formatedStartsAt
          : formatedStartsAt // ignore: cast_nullable_to_non_nullable
              as String,
      formatedEndsAt: null == formatedEndsAt
          ? _value.formatedEndsAt
          : formatedEndsAt // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DiscountImplCopyWith<$Res>
    implements $DiscountCopyWith<$Res> {
  factory _$$DiscountImplCopyWith(
          _$DiscountImpl value, $Res Function(_$DiscountImpl) then) =
      __$$DiscountImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int id,
      String name,
      @JsonKey(name: 'business_id') int businessId,
      @JsonKey(name: 'brand_id') int? brandId,
      @JsonKey(name: 'category_id') int? categoryId,
      @JsonKey(name: 'location_id') int locationId,
      int priority,
      @JsonKey(name: 'discount_type')
      @DiscountTypeConverter()
      DiscountType discountType,
      @JsonKey(name: 'discount_amount')
      @DoubleConverter()
      double discountAmount,
      @JsonKey(name: 'starts_at') @DateTimeConverter() DateTime startsAt,
      @JsonKey(name: 'ends_at') @DateTimeConverter() DateTime endsAt,
      @JsonKey(name: 'is_active') @BooleanConverter() bool isActive,
      String? spg,
      @JsonKey(name: 'applicable_in_cg')
      @BooleanConverter()
      bool applicableInCg,
      @JsonKey(name: 'created_at') @DateTimeConverter() DateTime createdAt,
      @JsonKey(name: 'updated_at') @DateTimeConverter() DateTime updatedAt,
      @JsonKey(name: 'price_group_id') String priceGroupId,
      @JsonKey(name: 'applicable_in_spg')
      @BooleanConverter()
      bool applicableInSpg,
      @JsonKey(name: 'max_qty') @DoubleConverter() double maxQty,
      String? coupon,
      @JsonKey(name: 'formated_starts_at') String formatedStartsAt,
      @JsonKey(name: 'formated_ends_at') String formatedEndsAt});
}

/// @nodoc
class __$$DiscountImplCopyWithImpl<$Res>
    extends _$DiscountCopyWithImpl<$Res, _$DiscountImpl>
    implements _$$DiscountImplCopyWith<$Res> {
  __$$DiscountImplCopyWithImpl(
      _$DiscountImpl _value, $Res Function(_$DiscountImpl) _then)
      : super(_value, _then);

  /// Create a copy of Discount
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? name = null,
    Object? businessId = null,
    Object? brandId = freezed,
    Object? categoryId = freezed,
    Object? locationId = null,
    Object? priority = null,
    Object? discountType = null,
    Object? discountAmount = null,
    Object? startsAt = null,
    Object? endsAt = null,
    Object? isActive = null,
    Object? spg = freezed,
    Object? applicableInCg = null,
    Object? createdAt = null,
    Object? updatedAt = null,
    Object? priceGroupId = null,
    Object? applicableInSpg = null,
    Object? maxQty = null,
    Object? coupon = freezed,
    Object? formatedStartsAt = null,
    Object? formatedEndsAt = null,
  }) {
    return _then(_$DiscountImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      businessId: null == businessId
          ? _value.businessId
          : businessId // ignore: cast_nullable_to_non_nullable
              as int,
      brandId: freezed == brandId
          ? _value.brandId
          : brandId // ignore: cast_nullable_to_non_nullable
              as int?,
      categoryId: freezed == categoryId
          ? _value.categoryId
          : categoryId // ignore: cast_nullable_to_non_nullable
              as int?,
      locationId: null == locationId
          ? _value.locationId
          : locationId // ignore: cast_nullable_to_non_nullable
              as int,
      priority: null == priority
          ? _value.priority
          : priority // ignore: cast_nullable_to_non_nullable
              as int,
      discountType: null == discountType
          ? _value.discountType
          : discountType // ignore: cast_nullable_to_non_nullable
              as DiscountType,
      discountAmount: null == discountAmount
          ? _value.discountAmount
          : discountAmount // ignore: cast_nullable_to_non_nullable
              as double,
      startsAt: null == startsAt
          ? _value.startsAt
          : startsAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      endsAt: null == endsAt
          ? _value.endsAt
          : endsAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      isActive: null == isActive
          ? _value.isActive
          : isActive // ignore: cast_nullable_to_non_nullable
              as bool,
      spg: freezed == spg
          ? _value.spg
          : spg // ignore: cast_nullable_to_non_nullable
              as String?,
      applicableInCg: null == applicableInCg
          ? _value.applicableInCg
          : applicableInCg // ignore: cast_nullable_to_non_nullable
              as bool,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      priceGroupId: null == priceGroupId
          ? _value.priceGroupId
          : priceGroupId // ignore: cast_nullable_to_non_nullable
              as String,
      applicableInSpg: null == applicableInSpg
          ? _value.applicableInSpg
          : applicableInSpg // ignore: cast_nullable_to_non_nullable
              as bool,
      maxQty: null == maxQty
          ? _value.maxQty
          : maxQty // ignore: cast_nullable_to_non_nullable
              as double,
      coupon: freezed == coupon
          ? _value.coupon
          : coupon // ignore: cast_nullable_to_non_nullable
              as String?,
      formatedStartsAt: null == formatedStartsAt
          ? _value.formatedStartsAt
          : formatedStartsAt // ignore: cast_nullable_to_non_nullable
              as String,
      formatedEndsAt: null == formatedEndsAt
          ? _value.formatedEndsAt
          : formatedEndsAt // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$DiscountImpl implements _Discount {
  const _$DiscountImpl(
      {required this.id,
      this.name = "N/A",
      @JsonKey(name: 'business_id') required this.businessId,
      @JsonKey(name: 'brand_id') this.brandId,
      @JsonKey(name: 'category_id') this.categoryId,
      @JsonKey(name: 'location_id') required this.locationId,
      this.priority = 1,
      @JsonKey(name: 'discount_type')
      @DiscountTypeConverter()
      this.discountType = DiscountType.percentage,
      @JsonKey(name: 'discount_amount')
      @DoubleConverter()
      this.discountAmount = 0.0,
      @JsonKey(name: 'starts_at') @DateTimeConverter() required this.startsAt,
      @JsonKey(name: 'ends_at') @DateTimeConverter() required this.endsAt,
      @JsonKey(name: 'is_active') @BooleanConverter() this.isActive = true,
      this.spg,
      @JsonKey(name: 'applicable_in_cg')
      @BooleanConverter()
      this.applicableInCg = false,
      @JsonKey(name: 'created_at') @DateTimeConverter() required this.createdAt,
      @JsonKey(name: 'updated_at') @DateTimeConverter() required this.updatedAt,
      @JsonKey(name: 'price_group_id') this.priceGroupId = "0",
      @JsonKey(name: 'applicable_in_spg')
      @BooleanConverter()
      this.applicableInSpg = false,
      @JsonKey(name: 'max_qty')
      @DoubleConverter()
      this.maxQty = double.infinity,
      this.coupon,
      @JsonKey(name: 'formated_starts_at') this.formatedStartsAt = "",
      @JsonKey(name: 'formated_ends_at') this.formatedEndsAt = ""});

  factory _$DiscountImpl.fromJson(Map<String, dynamic> json) =>
      _$$DiscountImplFromJson(json);

  @override
  final int id;
  @override
  @JsonKey()
  final String name;
  @override
  @JsonKey(name: 'business_id')
  final int businessId;
  @override
  @JsonKey(name: 'brand_id')
  final int? brandId;
  @override
  @JsonKey(name: 'category_id')
  final int? categoryId;
  @override
  @JsonKey(name: 'location_id')
  final int locationId;
  @override
  @JsonKey()
  final int priority;
  @override
  @JsonKey(name: 'discount_type')
  @DiscountTypeConverter()
  final DiscountType discountType;
  @override
  @JsonKey(name: 'discount_amount')
  @DoubleConverter()
  final double discountAmount;
  @override
  @JsonKey(name: 'starts_at')
  @DateTimeConverter()
  final DateTime startsAt;
  @override
  @JsonKey(name: 'ends_at')
  @DateTimeConverter()
  final DateTime endsAt;
  @override
  @JsonKey(name: 'is_active')
  @BooleanConverter()
  final bool isActive;
  @override
  final String? spg;
  @override
  @JsonKey(name: 'applicable_in_cg')
  @BooleanConverter()
  final bool applicableInCg;
  @override
  @JsonKey(name: 'created_at')
  @DateTimeConverter()
  final DateTime createdAt;
  @override
  @JsonKey(name: 'updated_at')
  @DateTimeConverter()
  final DateTime updatedAt;
  @override
  @JsonKey(name: 'price_group_id')
  final String priceGroupId;
  @override
  @JsonKey(name: 'applicable_in_spg')
  @BooleanConverter()
  final bool applicableInSpg;
  @override
  @JsonKey(name: 'max_qty')
  @DoubleConverter()
  final double maxQty;
  @override
  final String? coupon;
  @override
  @JsonKey(name: 'formated_starts_at')
  final String formatedStartsAt;
  @override
  @JsonKey(name: 'formated_ends_at')
  final String formatedEndsAt;

  @override
  String toString() {
    return 'Discount(id: $id, name: $name, businessId: $businessId, brandId: $brandId, categoryId: $categoryId, locationId: $locationId, priority: $priority, discountType: $discountType, discountAmount: $discountAmount, startsAt: $startsAt, endsAt: $endsAt, isActive: $isActive, spg: $spg, applicableInCg: $applicableInCg, createdAt: $createdAt, updatedAt: $updatedAt, priceGroupId: $priceGroupId, applicableInSpg: $applicableInSpg, maxQty: $maxQty, coupon: $coupon, formatedStartsAt: $formatedStartsAt, formatedEndsAt: $formatedEndsAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DiscountImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.businessId, businessId) ||
                other.businessId == businessId) &&
            (identical(other.brandId, brandId) || other.brandId == brandId) &&
            (identical(other.categoryId, categoryId) ||
                other.categoryId == categoryId) &&
            (identical(other.locationId, locationId) ||
                other.locationId == locationId) &&
            (identical(other.priority, priority) ||
                other.priority == priority) &&
            (identical(other.discountType, discountType) ||
                other.discountType == discountType) &&
            (identical(other.discountAmount, discountAmount) ||
                other.discountAmount == discountAmount) &&
            (identical(other.startsAt, startsAt) ||
                other.startsAt == startsAt) &&
            (identical(other.endsAt, endsAt) || other.endsAt == endsAt) &&
            (identical(other.isActive, isActive) ||
                other.isActive == isActive) &&
            (identical(other.spg, spg) || other.spg == spg) &&
            (identical(other.applicableInCg, applicableInCg) ||
                other.applicableInCg == applicableInCg) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt) &&
            (identical(other.priceGroupId, priceGroupId) ||
                other.priceGroupId == priceGroupId) &&
            (identical(other.applicableInSpg, applicableInSpg) ||
                other.applicableInSpg == applicableInSpg) &&
            (identical(other.maxQty, maxQty) || other.maxQty == maxQty) &&
            (identical(other.coupon, coupon) || other.coupon == coupon) &&
            (identical(other.formatedStartsAt, formatedStartsAt) ||
                other.formatedStartsAt == formatedStartsAt) &&
            (identical(other.formatedEndsAt, formatedEndsAt) ||
                other.formatedEndsAt == formatedEndsAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        id,
        name,
        businessId,
        brandId,
        categoryId,
        locationId,
        priority,
        discountType,
        discountAmount,
        startsAt,
        endsAt,
        isActive,
        spg,
        applicableInCg,
        createdAt,
        updatedAt,
        priceGroupId,
        applicableInSpg,
        maxQty,
        coupon,
        formatedStartsAt,
        formatedEndsAt
      ]);

  /// Create a copy of Discount
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DiscountImplCopyWith<_$DiscountImpl> get copyWith =>
      __$$DiscountImplCopyWithImpl<_$DiscountImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DiscountImplToJson(
      this,
    );
  }
}

abstract class _Discount implements Discount {
  const factory _Discount(
          {required final int id,
          final String name,
          @JsonKey(name: 'business_id') required final int businessId,
          @JsonKey(name: 'brand_id') final int? brandId,
          @JsonKey(name: 'category_id') final int? categoryId,
          @JsonKey(name: 'location_id') required final int locationId,
          final int priority,
          @JsonKey(name: 'discount_type')
          @DiscountTypeConverter()
          final DiscountType discountType,
          @JsonKey(name: 'discount_amount')
          @DoubleConverter()
          final double discountAmount,
          @JsonKey(name: 'starts_at')
          @DateTimeConverter()
          required final DateTime startsAt,
          @JsonKey(name: 'ends_at')
          @DateTimeConverter()
          required final DateTime endsAt,
          @JsonKey(name: 'is_active') @BooleanConverter() final bool isActive,
          final String? spg,
          @JsonKey(name: 'applicable_in_cg')
          @BooleanConverter()
          final bool applicableInCg,
          @JsonKey(name: 'created_at')
          @DateTimeConverter()
          required final DateTime createdAt,
          @JsonKey(name: 'updated_at')
          @DateTimeConverter()
          required final DateTime updatedAt,
          @JsonKey(name: 'price_group_id') final String priceGroupId,
          @JsonKey(name: 'applicable_in_spg')
          @BooleanConverter()
          final bool applicableInSpg,
          @JsonKey(name: 'max_qty') @DoubleConverter() final double maxQty,
          final String? coupon,
          @JsonKey(name: 'formated_starts_at') final String formatedStartsAt,
          @JsonKey(name: 'formated_ends_at') final String formatedEndsAt}) =
      _$DiscountImpl;

  factory _Discount.fromJson(Map<String, dynamic> json) =
      _$DiscountImpl.fromJson;

  @override
  int get id;
  @override
  String get name;
  @override
  @JsonKey(name: 'business_id')
  int get businessId;
  @override
  @JsonKey(name: 'brand_id')
  int? get brandId;
  @override
  @JsonKey(name: 'category_id')
  int? get categoryId;
  @override
  @JsonKey(name: 'location_id')
  int get locationId;
  @override
  int get priority;
  @override
  @JsonKey(name: 'discount_type')
  @DiscountTypeConverter()
  DiscountType get discountType;
  @override
  @JsonKey(name: 'discount_amount')
  @DoubleConverter()
  double get discountAmount;
  @override
  @JsonKey(name: 'starts_at')
  @DateTimeConverter()
  DateTime get startsAt;
  @override
  @JsonKey(name: 'ends_at')
  @DateTimeConverter()
  DateTime get endsAt;
  @override
  @JsonKey(name: 'is_active')
  @BooleanConverter()
  bool get isActive;
  @override
  String? get spg;
  @override
  @JsonKey(name: 'applicable_in_cg')
  @BooleanConverter()
  bool get applicableInCg;
  @override
  @JsonKey(name: 'created_at')
  @DateTimeConverter()
  DateTime get createdAt;
  @override
  @JsonKey(name: 'updated_at')
  @DateTimeConverter()
  DateTime get updatedAt;
  @override
  @JsonKey(name: 'price_group_id')
  String get priceGroupId;
  @override
  @JsonKey(name: 'applicable_in_spg')
  @BooleanConverter()
  bool get applicableInSpg;
  @override
  @JsonKey(name: 'max_qty')
  @DoubleConverter()
  double get maxQty;
  @override
  String? get coupon;
  @override
  @JsonKey(name: 'formated_starts_at')
  String get formatedStartsAt;
  @override
  @JsonKey(name: 'formated_ends_at')
  String get formatedEndsAt;

  /// Create a copy of Discount
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DiscountImplCopyWith<_$DiscountImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Media _$MediaFromJson(Map<String, dynamic> json) {
  return _Media.fromJson(json);
}

/// @nodoc
mixin _$Media {
  @HiveField(0)
  int get id => throw _privateConstructorUsedError;
  @HiveField(1)
  @JsonKey(name: 'business_id')
  int get businessId => throw _privateConstructorUsedError;
  @HiveField(2)
  @JsonKey(name: 'media_name')
  String? get mediaName => throw _privateConstructorUsedError;
  @HiveField(3)
  @JsonKey(name: 'model_media_type')
  String? get modelMediaType => throw _privateConstructorUsedError;
  @HiveField(4)
  @JsonKey(name: 'display_url')
  String? get displayUrl => throw _privateConstructorUsedError;

  /// Serializes this Media to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Media
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $MediaCopyWith<Media> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MediaCopyWith<$Res> {
  factory $MediaCopyWith(Media value, $Res Function(Media) then) =
      _$MediaCopyWithImpl<$Res, Media>;
  @useResult
  $Res call(
      {@HiveField(0) int id,
      @HiveField(1) @JsonKey(name: 'business_id') int businessId,
      @HiveField(2) @JsonKey(name: 'media_name') String? mediaName,
      @HiveField(3) @JsonKey(name: 'model_media_type') String? modelMediaType,
      @HiveField(4) @JsonKey(name: 'display_url') String? displayUrl});
}

/// @nodoc
class _$MediaCopyWithImpl<$Res, $Val extends Media>
    implements $MediaCopyWith<$Res> {
  _$MediaCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Media
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? businessId = null,
    Object? mediaName = freezed,
    Object? modelMediaType = freezed,
    Object? displayUrl = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      businessId: null == businessId
          ? _value.businessId
          : businessId // ignore: cast_nullable_to_non_nullable
              as int,
      mediaName: freezed == mediaName
          ? _value.mediaName
          : mediaName // ignore: cast_nullable_to_non_nullable
              as String?,
      modelMediaType: freezed == modelMediaType
          ? _value.modelMediaType
          : modelMediaType // ignore: cast_nullable_to_non_nullable
              as String?,
      displayUrl: freezed == displayUrl
          ? _value.displayUrl
          : displayUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MediaImplCopyWith<$Res> implements $MediaCopyWith<$Res> {
  factory _$$MediaImplCopyWith(
          _$MediaImpl value, $Res Function(_$MediaImpl) then) =
      __$$MediaImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@HiveField(0) int id,
      @HiveField(1) @JsonKey(name: 'business_id') int businessId,
      @HiveField(2) @JsonKey(name: 'media_name') String? mediaName,
      @HiveField(3) @JsonKey(name: 'model_media_type') String? modelMediaType,
      @HiveField(4) @JsonKey(name: 'display_url') String? displayUrl});
}

/// @nodoc
class __$$MediaImplCopyWithImpl<$Res>
    extends _$MediaCopyWithImpl<$Res, _$MediaImpl>
    implements _$$MediaImplCopyWith<$Res> {
  __$$MediaImplCopyWithImpl(
      _$MediaImpl _value, $Res Function(_$MediaImpl) _then)
      : super(_value, _then);

  /// Create a copy of Media
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? businessId = null,
    Object? mediaName = freezed,
    Object? modelMediaType = freezed,
    Object? displayUrl = freezed,
  }) {
    return _then(_$MediaImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      businessId: null == businessId
          ? _value.businessId
          : businessId // ignore: cast_nullable_to_non_nullable
              as int,
      mediaName: freezed == mediaName
          ? _value.mediaName
          : mediaName // ignore: cast_nullable_to_non_nullable
              as String?,
      modelMediaType: freezed == modelMediaType
          ? _value.modelMediaType
          : modelMediaType // ignore: cast_nullable_to_non_nullable
              as String?,
      displayUrl: freezed == displayUrl
          ? _value.displayUrl
          : displayUrl // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MediaImpl implements _Media {
  const _$MediaImpl(
      {@HiveField(0) required this.id,
      @HiveField(1) @JsonKey(name: 'business_id') required this.businessId,
      @HiveField(2) @JsonKey(name: 'media_name') this.mediaName,
      @HiveField(3) @JsonKey(name: 'model_media_type') this.modelMediaType,
      @HiveField(4) @JsonKey(name: 'display_url') this.displayUrl});

  factory _$MediaImpl.fromJson(Map<String, dynamic> json) =>
      _$$MediaImplFromJson(json);

  @override
  @HiveField(0)
  final int id;
  @override
  @HiveField(1)
  @JsonKey(name: 'business_id')
  final int businessId;
  @override
  @HiveField(2)
  @JsonKey(name: 'media_name')
  final String? mediaName;
  @override
  @HiveField(3)
  @JsonKey(name: 'model_media_type')
  final String? modelMediaType;
  @override
  @HiveField(4)
  @JsonKey(name: 'display_url')
  final String? displayUrl;

  @override
  String toString() {
    return 'Media(id: $id, businessId: $businessId, mediaName: $mediaName, modelMediaType: $modelMediaType, displayUrl: $displayUrl)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MediaImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.businessId, businessId) ||
                other.businessId == businessId) &&
            (identical(other.mediaName, mediaName) ||
                other.mediaName == mediaName) &&
            (identical(other.modelMediaType, modelMediaType) ||
                other.modelMediaType == modelMediaType) &&
            (identical(other.displayUrl, displayUrl) ||
                other.displayUrl == displayUrl));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType, id, businessId, mediaName, modelMediaType, displayUrl);

  /// Create a copy of Media
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MediaImplCopyWith<_$MediaImpl> get copyWith =>
      __$$MediaImplCopyWithImpl<_$MediaImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MediaImplToJson(
      this,
    );
  }
}

abstract class _Media implements Media {
  const factory _Media(
      {@HiveField(0) required final int id,
      @HiveField(1) @JsonKey(name: 'business_id') required final int businessId,
      @HiveField(2) @JsonKey(name: 'media_name') final String? mediaName,
      @HiveField(3)
      @JsonKey(name: 'model_media_type')
      final String? modelMediaType,
      @HiveField(4)
      @JsonKey(name: 'display_url')
      final String? displayUrl}) = _$MediaImpl;

  factory _Media.fromJson(Map<String, dynamic> json) = _$MediaImpl.fromJson;

  @override
  @HiveField(0)
  int get id;
  @override
  @HiveField(1)
  @JsonKey(name: 'business_id')
  int get businessId;
  @override
  @HiveField(2)
  @JsonKey(name: 'media_name')
  String? get mediaName;
  @override
  @HiveField(3)
  @JsonKey(name: 'model_media_type')
  String? get modelMediaType;
  @override
  @HiveField(4)
  @JsonKey(name: 'display_url')
  String? get displayUrl;

  /// Create a copy of Media
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MediaImplCopyWith<_$MediaImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ComboVariation _$ComboVariationFromJson(Map<String, dynamic> json) {
  return _ComboVariation.fromJson(json);
}

/// @nodoc
mixin _$ComboVariation {
  @HiveField(0)
  @JsonKey(name: "variation_id", fromJson: int.parse)
  int get variationId => throw _privateConstructorUsedError;
  @HiveField(1)
  @JsonKey(fromJson: _parseDouble)
  double get quantity => throw _privateConstructorUsedError;
  @HiveField(2)
  @JsonKey(name: "unit_id", fromJson: int.parse)
  int get unitId => throw _privateConstructorUsedError;

  /// Serializes this ComboVariation to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ComboVariation
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ComboVariationCopyWith<ComboVariation> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ComboVariationCopyWith<$Res> {
  factory $ComboVariationCopyWith(
          ComboVariation value, $Res Function(ComboVariation) then) =
      _$ComboVariationCopyWithImpl<$Res, ComboVariation>;
  @useResult
  $Res call(
      {@HiveField(0)
      @JsonKey(name: "variation_id", fromJson: int.parse)
      int variationId,
      @HiveField(1) @JsonKey(fromJson: _parseDouble) double quantity,
      @HiveField(2) @JsonKey(name: "unit_id", fromJson: int.parse) int unitId});
}

/// @nodoc
class _$ComboVariationCopyWithImpl<$Res, $Val extends ComboVariation>
    implements $ComboVariationCopyWith<$Res> {
  _$ComboVariationCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ComboVariation
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? variationId = null,
    Object? quantity = null,
    Object? unitId = null,
  }) {
    return _then(_value.copyWith(
      variationId: null == variationId
          ? _value.variationId
          : variationId // ignore: cast_nullable_to_non_nullable
              as int,
      quantity: null == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as double,
      unitId: null == unitId
          ? _value.unitId
          : unitId // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ComboVariationImplCopyWith<$Res>
    implements $ComboVariationCopyWith<$Res> {
  factory _$$ComboVariationImplCopyWith(_$ComboVariationImpl value,
          $Res Function(_$ComboVariationImpl) then) =
      __$$ComboVariationImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@HiveField(0)
      @JsonKey(name: "variation_id", fromJson: int.parse)
      int variationId,
      @HiveField(1) @JsonKey(fromJson: _parseDouble) double quantity,
      @HiveField(2) @JsonKey(name: "unit_id", fromJson: int.parse) int unitId});
}

/// @nodoc
class __$$ComboVariationImplCopyWithImpl<$Res>
    extends _$ComboVariationCopyWithImpl<$Res, _$ComboVariationImpl>
    implements _$$ComboVariationImplCopyWith<$Res> {
  __$$ComboVariationImplCopyWithImpl(
      _$ComboVariationImpl _value, $Res Function(_$ComboVariationImpl) _then)
      : super(_value, _then);

  /// Create a copy of ComboVariation
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? variationId = null,
    Object? quantity = null,
    Object? unitId = null,
  }) {
    return _then(_$ComboVariationImpl(
      variationId: null == variationId
          ? _value.variationId
          : variationId // ignore: cast_nullable_to_non_nullable
              as int,
      quantity: null == quantity
          ? _value.quantity
          : quantity // ignore: cast_nullable_to_non_nullable
              as double,
      unitId: null == unitId
          ? _value.unitId
          : unitId // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ComboVariationImpl implements _ComboVariation {
  const _$ComboVariationImpl(
      {@HiveField(0)
      @JsonKey(name: "variation_id", fromJson: int.parse)
      required this.variationId,
      @HiveField(1) @JsonKey(fromJson: _parseDouble) required this.quantity,
      @HiveField(2)
      @JsonKey(name: "unit_id", fromJson: int.parse)
      required this.unitId});

  factory _$ComboVariationImpl.fromJson(Map<String, dynamic> json) =>
      _$$ComboVariationImplFromJson(json);

  @override
  @HiveField(0)
  @JsonKey(name: "variation_id", fromJson: int.parse)
  final int variationId;
  @override
  @HiveField(1)
  @JsonKey(fromJson: _parseDouble)
  final double quantity;
  @override
  @HiveField(2)
  @JsonKey(name: "unit_id", fromJson: int.parse)
  final int unitId;

  @override
  String toString() {
    return 'ComboVariation(variationId: $variationId, quantity: $quantity, unitId: $unitId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ComboVariationImpl &&
            (identical(other.variationId, variationId) ||
                other.variationId == variationId) &&
            (identical(other.quantity, quantity) ||
                other.quantity == quantity) &&
            (identical(other.unitId, unitId) || other.unitId == unitId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, variationId, quantity, unitId);

  /// Create a copy of ComboVariation
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ComboVariationImplCopyWith<_$ComboVariationImpl> get copyWith =>
      __$$ComboVariationImplCopyWithImpl<_$ComboVariationImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ComboVariationImplToJson(
      this,
    );
  }
}

abstract class _ComboVariation implements ComboVariation {
  const factory _ComboVariation(
      {@HiveField(0)
      @JsonKey(name: "variation_id", fromJson: int.parse)
      required final int variationId,
      @HiveField(1)
      @JsonKey(fromJson: _parseDouble)
      required final double quantity,
      @HiveField(2)
      @JsonKey(name: "unit_id", fromJson: int.parse)
      required final int unitId}) = _$ComboVariationImpl;

  factory _ComboVariation.fromJson(Map<String, dynamic> json) =
      _$ComboVariationImpl.fromJson;

  @override
  @HiveField(0)
  @JsonKey(name: "variation_id", fromJson: int.parse)
  int get variationId;
  @override
  @HiveField(1)
  @JsonKey(fromJson: _parseDouble)
  double get quantity;
  @override
  @HiveField(2)
  @JsonKey(name: "unit_id", fromJson: int.parse)
  int get unitId;

  /// Create a copy of ComboVariation
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ComboVariationImplCopyWith<_$ComboVariationImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ProductPriceGroup _$ProductPriceGroupFromJson(Map<String, dynamic> json) {
  return _ProductPriceGroup.fromJson(json);
}

/// @nodoc
mixin _$ProductPriceGroup {
  @HiveField(0)
  @JsonKey(name: 'id')
  int get id => throw _privateConstructorUsedError;
  @HiveField(1)
  @JsonKey(name: 'variation_id')
  int get variationId => throw _privateConstructorUsedError;
  @HiveField(2)
  @JsonKey(name: 'price_group_id')
  int get priceGroupId => throw _privateConstructorUsedError;
  @HiveField(3)
  @JsonKey(name: 'price_inc_tax', fromJson: double.parse)
  double get priceIncTax => throw _privateConstructorUsedError;
  @HiveField(4)
  @JsonKey(name: 'price_type')
  String get priceType => throw _privateConstructorUsedError;
  @HiveField(5)
  @JsonKey(name: 'created_at', fromJson: DateTime.parse)
  DateTime get createdAt => throw _privateConstructorUsedError;
  @HiveField(6)
  @JsonKey(name: 'updated_at', fromJson: DateTime.parse)
  DateTime get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this ProductPriceGroup to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProductPriceGroup
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProductPriceGroupCopyWith<ProductPriceGroup> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProductPriceGroupCopyWith<$Res> {
  factory $ProductPriceGroupCopyWith(
          ProductPriceGroup value, $Res Function(ProductPriceGroup) then) =
      _$ProductPriceGroupCopyWithImpl<$Res, ProductPriceGroup>;
  @useResult
  $Res call(
      {@HiveField(0) @JsonKey(name: 'id') int id,
      @HiveField(1) @JsonKey(name: 'variation_id') int variationId,
      @HiveField(2) @JsonKey(name: 'price_group_id') int priceGroupId,
      @HiveField(3)
      @JsonKey(name: 'price_inc_tax', fromJson: double.parse)
      double priceIncTax,
      @HiveField(4) @JsonKey(name: 'price_type') String priceType,
      @HiveField(5)
      @JsonKey(name: 'created_at', fromJson: DateTime.parse)
      DateTime createdAt,
      @HiveField(6)
      @JsonKey(name: 'updated_at', fromJson: DateTime.parse)
      DateTime updatedAt});
}

/// @nodoc
class _$ProductPriceGroupCopyWithImpl<$Res, $Val extends ProductPriceGroup>
    implements $ProductPriceGroupCopyWith<$Res> {
  _$ProductPriceGroupCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProductPriceGroup
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? variationId = null,
    Object? priceGroupId = null,
    Object? priceIncTax = null,
    Object? priceType = null,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      variationId: null == variationId
          ? _value.variationId
          : variationId // ignore: cast_nullable_to_non_nullable
              as int,
      priceGroupId: null == priceGroupId
          ? _value.priceGroupId
          : priceGroupId // ignore: cast_nullable_to_non_nullable
              as int,
      priceIncTax: null == priceIncTax
          ? _value.priceIncTax
          : priceIncTax // ignore: cast_nullable_to_non_nullable
              as double,
      priceType: null == priceType
          ? _value.priceType
          : priceType // ignore: cast_nullable_to_non_nullable
              as String,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProductPriceGroupImplCopyWith<$Res>
    implements $ProductPriceGroupCopyWith<$Res> {
  factory _$$ProductPriceGroupImplCopyWith(_$ProductPriceGroupImpl value,
          $Res Function(_$ProductPriceGroupImpl) then) =
      __$$ProductPriceGroupImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@HiveField(0) @JsonKey(name: 'id') int id,
      @HiveField(1) @JsonKey(name: 'variation_id') int variationId,
      @HiveField(2) @JsonKey(name: 'price_group_id') int priceGroupId,
      @HiveField(3)
      @JsonKey(name: 'price_inc_tax', fromJson: double.parse)
      double priceIncTax,
      @HiveField(4) @JsonKey(name: 'price_type') String priceType,
      @HiveField(5)
      @JsonKey(name: 'created_at', fromJson: DateTime.parse)
      DateTime createdAt,
      @HiveField(6)
      @JsonKey(name: 'updated_at', fromJson: DateTime.parse)
      DateTime updatedAt});
}

/// @nodoc
class __$$ProductPriceGroupImplCopyWithImpl<$Res>
    extends _$ProductPriceGroupCopyWithImpl<$Res, _$ProductPriceGroupImpl>
    implements _$$ProductPriceGroupImplCopyWith<$Res> {
  __$$ProductPriceGroupImplCopyWithImpl(_$ProductPriceGroupImpl _value,
      $Res Function(_$ProductPriceGroupImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProductPriceGroup
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? variationId = null,
    Object? priceGroupId = null,
    Object? priceIncTax = null,
    Object? priceType = null,
    Object? createdAt = null,
    Object? updatedAt = null,
  }) {
    return _then(_$ProductPriceGroupImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      variationId: null == variationId
          ? _value.variationId
          : variationId // ignore: cast_nullable_to_non_nullable
              as int,
      priceGroupId: null == priceGroupId
          ? _value.priceGroupId
          : priceGroupId // ignore: cast_nullable_to_non_nullable
              as int,
      priceIncTax: null == priceIncTax
          ? _value.priceIncTax
          : priceIncTax // ignore: cast_nullable_to_non_nullable
              as double,
      priceType: null == priceType
          ? _value.priceType
          : priceType // ignore: cast_nullable_to_non_nullable
              as String,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      updatedAt: null == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ProductPriceGroupImpl implements _ProductPriceGroup {
  const _$ProductPriceGroupImpl(
      {@HiveField(0) @JsonKey(name: 'id') required this.id,
      @HiveField(1) @JsonKey(name: 'variation_id') required this.variationId,
      @HiveField(2) @JsonKey(name: 'price_group_id') required this.priceGroupId,
      @HiveField(3)
      @JsonKey(name: 'price_inc_tax', fromJson: double.parse)
      required this.priceIncTax,
      @HiveField(4) @JsonKey(name: 'price_type') required this.priceType,
      @HiveField(5)
      @JsonKey(name: 'created_at', fromJson: DateTime.parse)
      required this.createdAt,
      @HiveField(6)
      @JsonKey(name: 'updated_at', fromJson: DateTime.parse)
      required this.updatedAt});

  factory _$ProductPriceGroupImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProductPriceGroupImplFromJson(json);

  @override
  @HiveField(0)
  @JsonKey(name: 'id')
  final int id;
  @override
  @HiveField(1)
  @JsonKey(name: 'variation_id')
  final int variationId;
  @override
  @HiveField(2)
  @JsonKey(name: 'price_group_id')
  final int priceGroupId;
  @override
  @HiveField(3)
  @JsonKey(name: 'price_inc_tax', fromJson: double.parse)
  final double priceIncTax;
  @override
  @HiveField(4)
  @JsonKey(name: 'price_type')
  final String priceType;
  @override
  @HiveField(5)
  @JsonKey(name: 'created_at', fromJson: DateTime.parse)
  final DateTime createdAt;
  @override
  @HiveField(6)
  @JsonKey(name: 'updated_at', fromJson: DateTime.parse)
  final DateTime updatedAt;

  @override
  String toString() {
    return 'ProductPriceGroup(id: $id, variationId: $variationId, priceGroupId: $priceGroupId, priceIncTax: $priceIncTax, priceType: $priceType, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProductPriceGroupImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.variationId, variationId) ||
                other.variationId == variationId) &&
            (identical(other.priceGroupId, priceGroupId) ||
                other.priceGroupId == priceGroupId) &&
            (identical(other.priceIncTax, priceIncTax) ||
                other.priceIncTax == priceIncTax) &&
            (identical(other.priceType, priceType) ||
                other.priceType == priceType) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, variationId, priceGroupId,
      priceIncTax, priceType, createdAt, updatedAt);

  /// Create a copy of ProductPriceGroup
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProductPriceGroupImplCopyWith<_$ProductPriceGroupImpl> get copyWith =>
      __$$ProductPriceGroupImplCopyWithImpl<_$ProductPriceGroupImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProductPriceGroupImplToJson(
      this,
    );
  }
}

abstract class _ProductPriceGroup implements ProductPriceGroup {
  const factory _ProductPriceGroup(
      {@HiveField(0) @JsonKey(name: 'id') required final int id,
      @HiveField(1)
      @JsonKey(name: 'variation_id')
      required final int variationId,
      @HiveField(2)
      @JsonKey(name: 'price_group_id')
      required final int priceGroupId,
      @HiveField(3)
      @JsonKey(name: 'price_inc_tax', fromJson: double.parse)
      required final double priceIncTax,
      @HiveField(4)
      @JsonKey(name: 'price_type')
      required final String priceType,
      @HiveField(5)
      @JsonKey(name: 'created_at', fromJson: DateTime.parse)
      required final DateTime createdAt,
      @HiveField(6)
      @JsonKey(name: 'updated_at', fromJson: DateTime.parse)
      required final DateTime updatedAt}) = _$ProductPriceGroupImpl;

  factory _ProductPriceGroup.fromJson(Map<String, dynamic> json) =
      _$ProductPriceGroupImpl.fromJson;

  @override
  @HiveField(0)
  @JsonKey(name: 'id')
  int get id;
  @override
  @HiveField(1)
  @JsonKey(name: 'variation_id')
  int get variationId;
  @override
  @HiveField(2)
  @JsonKey(name: 'price_group_id')
  int get priceGroupId;
  @override
  @HiveField(3)
  @JsonKey(name: 'price_inc_tax', fromJson: double.parse)
  double get priceIncTax;
  @override
  @HiveField(4)
  @JsonKey(name: 'price_type')
  String get priceType;
  @override
  @HiveField(5)
  @JsonKey(name: 'created_at', fromJson: DateTime.parse)
  DateTime get createdAt;
  @override
  @HiveField(6)
  @JsonKey(name: 'updated_at', fromJson: DateTime.parse)
  DateTime get updatedAt;

  /// Create a copy of ProductPriceGroup
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProductPriceGroupImplCopyWith<_$ProductPriceGroupImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

TaxRate _$TaxRateFromJson(Map<String, dynamic> json) {
  return _TaxRate.fromJson(json);
}

/// @nodoc
mixin _$TaxRate {
  @HiveField(0)
  @JsonKey(name: 'id')
  int? get id => throw _privateConstructorUsedError;
  @HiveField(1)
  @JsonKey(name: 'business_id')
  int? get businessId => throw _privateConstructorUsedError;
  @HiveField(2)
  @JsonKey(name: 'name')
  String? get name => throw _privateConstructorUsedError;
  @HiveField(3)
  @JsonKey(name: 'amount')
  double? get amount => throw _privateConstructorUsedError;
  @HiveField(4)
  @JsonKey(name: 'created_by')
  int? get createdBy => throw _privateConstructorUsedError;
  @HiveField(5)
  @JsonKey(name: 'deleted_at')
  DateTime? get deletedAt => throw _privateConstructorUsedError;
  @HiveField(6)
  @JsonKey(name: 'created_at')
  DateTime? get createdAt => throw _privateConstructorUsedError;
  @HiveField(7)
  @JsonKey(name: 'updated_at')
  DateTime? get updatedAt => throw _privateConstructorUsedError;

  /// Serializes this TaxRate to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of TaxRate
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TaxRateCopyWith<TaxRate> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TaxRateCopyWith<$Res> {
  factory $TaxRateCopyWith(TaxRate value, $Res Function(TaxRate) then) =
      _$TaxRateCopyWithImpl<$Res, TaxRate>;
  @useResult
  $Res call(
      {@HiveField(0) @JsonKey(name: 'id') int? id,
      @HiveField(1) @JsonKey(name: 'business_id') int? businessId,
      @HiveField(2) @JsonKey(name: 'name') String? name,
      @HiveField(3) @JsonKey(name: 'amount') double? amount,
      @HiveField(4) @JsonKey(name: 'created_by') int? createdBy,
      @HiveField(5) @JsonKey(name: 'deleted_at') DateTime? deletedAt,
      @HiveField(6) @JsonKey(name: 'created_at') DateTime? createdAt,
      @HiveField(7) @JsonKey(name: 'updated_at') DateTime? updatedAt});
}

/// @nodoc
class _$TaxRateCopyWithImpl<$Res, $Val extends TaxRate>
    implements $TaxRateCopyWith<$Res> {
  _$TaxRateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TaxRate
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? businessId = freezed,
    Object? name = freezed,
    Object? amount = freezed,
    Object? createdBy = freezed,
    Object? deletedAt = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      businessId: freezed == businessId
          ? _value.businessId
          : businessId // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      amount: freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as double?,
      createdBy: freezed == createdBy
          ? _value.createdBy
          : createdBy // ignore: cast_nullable_to_non_nullable
              as int?,
      deletedAt: freezed == deletedAt
          ? _value.deletedAt
          : deletedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TaxRateImplCopyWith<$Res> implements $TaxRateCopyWith<$Res> {
  factory _$$TaxRateImplCopyWith(
          _$TaxRateImpl value, $Res Function(_$TaxRateImpl) then) =
      __$$TaxRateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@HiveField(0) @JsonKey(name: 'id') int? id,
      @HiveField(1) @JsonKey(name: 'business_id') int? businessId,
      @HiveField(2) @JsonKey(name: 'name') String? name,
      @HiveField(3) @JsonKey(name: 'amount') double? amount,
      @HiveField(4) @JsonKey(name: 'created_by') int? createdBy,
      @HiveField(5) @JsonKey(name: 'deleted_at') DateTime? deletedAt,
      @HiveField(6) @JsonKey(name: 'created_at') DateTime? createdAt,
      @HiveField(7) @JsonKey(name: 'updated_at') DateTime? updatedAt});
}

/// @nodoc
class __$$TaxRateImplCopyWithImpl<$Res>
    extends _$TaxRateCopyWithImpl<$Res, _$TaxRateImpl>
    implements _$$TaxRateImplCopyWith<$Res> {
  __$$TaxRateImplCopyWithImpl(
      _$TaxRateImpl _value, $Res Function(_$TaxRateImpl) _then)
      : super(_value, _then);

  /// Create a copy of TaxRate
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? businessId = freezed,
    Object? name = freezed,
    Object? amount = freezed,
    Object? createdBy = freezed,
    Object? deletedAt = freezed,
    Object? createdAt = freezed,
    Object? updatedAt = freezed,
  }) {
    return _then(_$TaxRateImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      businessId: freezed == businessId
          ? _value.businessId
          : businessId // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      amount: freezed == amount
          ? _value.amount
          : amount // ignore: cast_nullable_to_non_nullable
              as double?,
      createdBy: freezed == createdBy
          ? _value.createdBy
          : createdBy // ignore: cast_nullable_to_non_nullable
              as int?,
      deletedAt: freezed == deletedAt
          ? _value.deletedAt
          : deletedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      createdAt: freezed == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      updatedAt: freezed == updatedAt
          ? _value.updatedAt
          : updatedAt // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$TaxRateImpl implements _TaxRate {
  const _$TaxRateImpl(
      {@HiveField(0) @JsonKey(name: 'id') this.id,
      @HiveField(1) @JsonKey(name: 'business_id') this.businessId,
      @HiveField(2) @JsonKey(name: 'name') this.name,
      @HiveField(3) @JsonKey(name: 'amount') this.amount,
      @HiveField(4) @JsonKey(name: 'created_by') this.createdBy,
      @HiveField(5) @JsonKey(name: 'deleted_at') this.deletedAt,
      @HiveField(6) @JsonKey(name: 'created_at') this.createdAt,
      @HiveField(7) @JsonKey(name: 'updated_at') this.updatedAt});

  factory _$TaxRateImpl.fromJson(Map<String, dynamic> json) =>
      _$$TaxRateImplFromJson(json);

  @override
  @HiveField(0)
  @JsonKey(name: 'id')
  final int? id;
  @override
  @HiveField(1)
  @JsonKey(name: 'business_id')
  final int? businessId;
  @override
  @HiveField(2)
  @JsonKey(name: 'name')
  final String? name;
  @override
  @HiveField(3)
  @JsonKey(name: 'amount')
  final double? amount;
  @override
  @HiveField(4)
  @JsonKey(name: 'created_by')
  final int? createdBy;
  @override
  @HiveField(5)
  @JsonKey(name: 'deleted_at')
  final DateTime? deletedAt;
  @override
  @HiveField(6)
  @JsonKey(name: 'created_at')
  final DateTime? createdAt;
  @override
  @HiveField(7)
  @JsonKey(name: 'updated_at')
  final DateTime? updatedAt;

  @override
  String toString() {
    return 'TaxRate(id: $id, businessId: $businessId, name: $name, amount: $amount, createdBy: $createdBy, deletedAt: $deletedAt, createdAt: $createdAt, updatedAt: $updatedAt)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TaxRateImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.businessId, businessId) ||
                other.businessId == businessId) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.amount, amount) || other.amount == amount) &&
            (identical(other.createdBy, createdBy) ||
                other.createdBy == createdBy) &&
            (identical(other.deletedAt, deletedAt) ||
                other.deletedAt == deletedAt) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.updatedAt, updatedAt) ||
                other.updatedAt == updatedAt));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, businessId, name, amount,
      createdBy, deletedAt, createdAt, updatedAt);

  /// Create a copy of TaxRate
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TaxRateImplCopyWith<_$TaxRateImpl> get copyWith =>
      __$$TaxRateImplCopyWithImpl<_$TaxRateImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TaxRateImplToJson(
      this,
    );
  }
}

abstract class _TaxRate implements TaxRate {
  const factory _TaxRate(
      {@HiveField(0) @JsonKey(name: 'id') final int? id,
      @HiveField(1) @JsonKey(name: 'business_id') final int? businessId,
      @HiveField(2) @JsonKey(name: 'name') final String? name,
      @HiveField(3) @JsonKey(name: 'amount') final double? amount,
      @HiveField(4) @JsonKey(name: 'created_by') final int? createdBy,
      @HiveField(5) @JsonKey(name: 'deleted_at') final DateTime? deletedAt,
      @HiveField(6) @JsonKey(name: 'created_at') final DateTime? createdAt,
      @HiveField(7)
      @JsonKey(name: 'updated_at')
      final DateTime? updatedAt}) = _$TaxRateImpl;

  factory _TaxRate.fromJson(Map<String, dynamic> json) = _$TaxRateImpl.fromJson;

  @override
  @HiveField(0)
  @JsonKey(name: 'id')
  int? get id;
  @override
  @HiveField(1)
  @JsonKey(name: 'business_id')
  int? get businessId;
  @override
  @HiveField(2)
  @JsonKey(name: 'name')
  String? get name;
  @override
  @HiveField(3)
  @JsonKey(name: 'amount')
  double? get amount;
  @override
  @HiveField(4)
  @JsonKey(name: 'created_by')
  int? get createdBy;
  @override
  @HiveField(5)
  @JsonKey(name: 'deleted_at')
  DateTime? get deletedAt;
  @override
  @HiveField(6)
  @JsonKey(name: 'created_at')
  DateTime? get createdAt;
  @override
  @HiveField(7)
  @JsonKey(name: 'updated_at')
  DateTime? get updatedAt;

  /// Create a copy of TaxRate
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TaxRateImplCopyWith<_$TaxRateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

Unit _$UnitFromJson(Map<String, dynamic> json) {
  return _Unit.fromJson(json);
}

/// @nodoc
mixin _$Unit {
  @HiveField(0)
  @JsonKey(name: 'id')
  int get id => throw _privateConstructorUsedError;
  @HiveField(1)
  @JsonKey(name: 'business_id')
  int get businessId => throw _privateConstructorUsedError;
  @HiveField(2)
  @JsonKey(name: 'actual_name')
  String get actualName => throw _privateConstructorUsedError;
  @HiveField(3)
  @JsonKey(name: 'short_name')
  String get shortName => throw _privateConstructorUsedError;
  @HiveField(4)
  @JsonKey(name: 'allow_decimal')
  int get allowDecimal => throw _privateConstructorUsedError;
  @HiveField(5)
  @JsonKey(name: 'base_unit_multiplier', fromJson: _parseUnitMultiplierDouble)
  double get baseUnitMultiplier => throw _privateConstructorUsedError;
  @HiveField(6)
  @JsonKey(name: 'base_unit_id', fromJson: _tryParseInt)
  int? get baseUnitId => throw _privateConstructorUsedError;
  @HiveField(7)
  @JsonKey(name: 'base_unit')
  Unit? get baseUnit => throw _privateConstructorUsedError;

  /// Serializes this Unit to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Unit
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $UnitCopyWith<Unit> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UnitCopyWith<$Res> {
  factory $UnitCopyWith(Unit value, $Res Function(Unit) then) =
      _$UnitCopyWithImpl<$Res, Unit>;
  @useResult
  $Res call(
      {@HiveField(0) @JsonKey(name: 'id') int id,
      @HiveField(1) @JsonKey(name: 'business_id') int businessId,
      @HiveField(2) @JsonKey(name: 'actual_name') String actualName,
      @HiveField(3) @JsonKey(name: 'short_name') String shortName,
      @HiveField(4) @JsonKey(name: 'allow_decimal') int allowDecimal,
      @HiveField(5)
      @JsonKey(
          name: 'base_unit_multiplier', fromJson: _parseUnitMultiplierDouble)
      double baseUnitMultiplier,
      @HiveField(6)
      @JsonKey(name: 'base_unit_id', fromJson: _tryParseInt)
      int? baseUnitId,
      @HiveField(7) @JsonKey(name: 'base_unit') Unit? baseUnit});

  $UnitCopyWith<$Res>? get baseUnit;
}

/// @nodoc
class _$UnitCopyWithImpl<$Res, $Val extends Unit>
    implements $UnitCopyWith<$Res> {
  _$UnitCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Unit
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? businessId = null,
    Object? actualName = null,
    Object? shortName = null,
    Object? allowDecimal = null,
    Object? baseUnitMultiplier = null,
    Object? baseUnitId = freezed,
    Object? baseUnit = freezed,
  }) {
    return _then(_value.copyWith(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      businessId: null == businessId
          ? _value.businessId
          : businessId // ignore: cast_nullable_to_non_nullable
              as int,
      actualName: null == actualName
          ? _value.actualName
          : actualName // ignore: cast_nullable_to_non_nullable
              as String,
      shortName: null == shortName
          ? _value.shortName
          : shortName // ignore: cast_nullable_to_non_nullable
              as String,
      allowDecimal: null == allowDecimal
          ? _value.allowDecimal
          : allowDecimal // ignore: cast_nullable_to_non_nullable
              as int,
      baseUnitMultiplier: null == baseUnitMultiplier
          ? _value.baseUnitMultiplier
          : baseUnitMultiplier // ignore: cast_nullable_to_non_nullable
              as double,
      baseUnitId: freezed == baseUnitId
          ? _value.baseUnitId
          : baseUnitId // ignore: cast_nullable_to_non_nullable
              as int?,
      baseUnit: freezed == baseUnit
          ? _value.baseUnit
          : baseUnit // ignore: cast_nullable_to_non_nullable
              as Unit?,
    ) as $Val);
  }

  /// Create a copy of Unit
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $UnitCopyWith<$Res>? get baseUnit {
    if (_value.baseUnit == null) {
      return null;
    }

    return $UnitCopyWith<$Res>(_value.baseUnit!, (value) {
      return _then(_value.copyWith(baseUnit: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$UnitImplCopyWith<$Res> implements $UnitCopyWith<$Res> {
  factory _$$UnitImplCopyWith(
          _$UnitImpl value, $Res Function(_$UnitImpl) then) =
      __$$UnitImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@HiveField(0) @JsonKey(name: 'id') int id,
      @HiveField(1) @JsonKey(name: 'business_id') int businessId,
      @HiveField(2) @JsonKey(name: 'actual_name') String actualName,
      @HiveField(3) @JsonKey(name: 'short_name') String shortName,
      @HiveField(4) @JsonKey(name: 'allow_decimal') int allowDecimal,
      @HiveField(5)
      @JsonKey(
          name: 'base_unit_multiplier', fromJson: _parseUnitMultiplierDouble)
      double baseUnitMultiplier,
      @HiveField(6)
      @JsonKey(name: 'base_unit_id', fromJson: _tryParseInt)
      int? baseUnitId,
      @HiveField(7) @JsonKey(name: 'base_unit') Unit? baseUnit});

  @override
  $UnitCopyWith<$Res>? get baseUnit;
}

/// @nodoc
class __$$UnitImplCopyWithImpl<$Res>
    extends _$UnitCopyWithImpl<$Res, _$UnitImpl>
    implements _$$UnitImplCopyWith<$Res> {
  __$$UnitImplCopyWithImpl(_$UnitImpl _value, $Res Function(_$UnitImpl) _then)
      : super(_value, _then);

  /// Create a copy of Unit
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = null,
    Object? businessId = null,
    Object? actualName = null,
    Object? shortName = null,
    Object? allowDecimal = null,
    Object? baseUnitMultiplier = null,
    Object? baseUnitId = freezed,
    Object? baseUnit = freezed,
  }) {
    return _then(_$UnitImpl(
      id: null == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int,
      businessId: null == businessId
          ? _value.businessId
          : businessId // ignore: cast_nullable_to_non_nullable
              as int,
      actualName: null == actualName
          ? _value.actualName
          : actualName // ignore: cast_nullable_to_non_nullable
              as String,
      shortName: null == shortName
          ? _value.shortName
          : shortName // ignore: cast_nullable_to_non_nullable
              as String,
      allowDecimal: null == allowDecimal
          ? _value.allowDecimal
          : allowDecimal // ignore: cast_nullable_to_non_nullable
              as int,
      baseUnitMultiplier: null == baseUnitMultiplier
          ? _value.baseUnitMultiplier
          : baseUnitMultiplier // ignore: cast_nullable_to_non_nullable
              as double,
      baseUnitId: freezed == baseUnitId
          ? _value.baseUnitId
          : baseUnitId // ignore: cast_nullable_to_non_nullable
              as int?,
      baseUnit: freezed == baseUnit
          ? _value.baseUnit
          : baseUnit // ignore: cast_nullable_to_non_nullable
              as Unit?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UnitImpl implements _Unit {
  const _$UnitImpl(
      {@HiveField(0) @JsonKey(name: 'id') required this.id,
      @HiveField(1) @JsonKey(name: 'business_id') required this.businessId,
      @HiveField(2) @JsonKey(name: 'actual_name') required this.actualName,
      @HiveField(3) @JsonKey(name: 'short_name') required this.shortName,
      @HiveField(4) @JsonKey(name: 'allow_decimal') required this.allowDecimal,
      @HiveField(5)
      @JsonKey(
          name: 'base_unit_multiplier', fromJson: _parseUnitMultiplierDouble)
      this.baseUnitMultiplier = 1,
      @HiveField(6)
      @JsonKey(name: 'base_unit_id', fromJson: _tryParseInt)
      this.baseUnitId,
      @HiveField(7) @JsonKey(name: 'base_unit') this.baseUnit});

  factory _$UnitImpl.fromJson(Map<String, dynamic> json) =>
      _$$UnitImplFromJson(json);

  @override
  @HiveField(0)
  @JsonKey(name: 'id')
  final int id;
  @override
  @HiveField(1)
  @JsonKey(name: 'business_id')
  final int businessId;
  @override
  @HiveField(2)
  @JsonKey(name: 'actual_name')
  final String actualName;
  @override
  @HiveField(3)
  @JsonKey(name: 'short_name')
  final String shortName;
  @override
  @HiveField(4)
  @JsonKey(name: 'allow_decimal')
  final int allowDecimal;
  @override
  @HiveField(5)
  @JsonKey(name: 'base_unit_multiplier', fromJson: _parseUnitMultiplierDouble)
  final double baseUnitMultiplier;
  @override
  @HiveField(6)
  @JsonKey(name: 'base_unit_id', fromJson: _tryParseInt)
  final int? baseUnitId;
  @override
  @HiveField(7)
  @JsonKey(name: 'base_unit')
  final Unit? baseUnit;

  @override
  String toString() {
    return 'Unit(id: $id, businessId: $businessId, actualName: $actualName, shortName: $shortName, allowDecimal: $allowDecimal, baseUnitMultiplier: $baseUnitMultiplier, baseUnitId: $baseUnitId, baseUnit: $baseUnit)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UnitImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.businessId, businessId) ||
                other.businessId == businessId) &&
            (identical(other.actualName, actualName) ||
                other.actualName == actualName) &&
            (identical(other.shortName, shortName) ||
                other.shortName == shortName) &&
            (identical(other.allowDecimal, allowDecimal) ||
                other.allowDecimal == allowDecimal) &&
            (identical(other.baseUnitMultiplier, baseUnitMultiplier) ||
                other.baseUnitMultiplier == baseUnitMultiplier) &&
            (identical(other.baseUnitId, baseUnitId) ||
                other.baseUnitId == baseUnitId) &&
            (identical(other.baseUnit, baseUnit) ||
                other.baseUnit == baseUnit));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, id, businessId, actualName,
      shortName, allowDecimal, baseUnitMultiplier, baseUnitId, baseUnit);

  /// Create a copy of Unit
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$UnitImplCopyWith<_$UnitImpl> get copyWith =>
      __$$UnitImplCopyWithImpl<_$UnitImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UnitImplToJson(
      this,
    );
  }
}

abstract class _Unit implements Unit {
  const factory _Unit(
      {@HiveField(0) @JsonKey(name: 'id') required final int id,
      @HiveField(1) @JsonKey(name: 'business_id') required final int businessId,
      @HiveField(2)
      @JsonKey(name: 'actual_name')
      required final String actualName,
      @HiveField(3)
      @JsonKey(name: 'short_name')
      required final String shortName,
      @HiveField(4)
      @JsonKey(name: 'allow_decimal')
      required final int allowDecimal,
      @HiveField(5)
      @JsonKey(
          name: 'base_unit_multiplier', fromJson: _parseUnitMultiplierDouble)
      final double baseUnitMultiplier,
      @HiveField(6)
      @JsonKey(name: 'base_unit_id', fromJson: _tryParseInt)
      final int? baseUnitId,
      @HiveField(7)
      @JsonKey(name: 'base_unit')
      final Unit? baseUnit}) = _$UnitImpl;

  factory _Unit.fromJson(Map<String, dynamic> json) = _$UnitImpl.fromJson;

  @override
  @HiveField(0)
  @JsonKey(name: 'id')
  int get id;
  @override
  @HiveField(1)
  @JsonKey(name: 'business_id')
  int get businessId;
  @override
  @HiveField(2)
  @JsonKey(name: 'actual_name')
  String get actualName;
  @override
  @HiveField(3)
  @JsonKey(name: 'short_name')
  String get shortName;
  @override
  @HiveField(4)
  @JsonKey(name: 'allow_decimal')
  int get allowDecimal;
  @override
  @HiveField(5)
  @JsonKey(name: 'base_unit_multiplier', fromJson: _parseUnitMultiplierDouble)
  double get baseUnitMultiplier;
  @override
  @HiveField(6)
  @JsonKey(name: 'base_unit_id', fromJson: _tryParseInt)
  int? get baseUnitId;
  @override
  @HiveField(7)
  @JsonKey(name: 'base_unit')
  Unit? get baseUnit;

  /// Create a copy of Unit
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$UnitImplCopyWith<_$UnitImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
