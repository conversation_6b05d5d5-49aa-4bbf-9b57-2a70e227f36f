import 'package:equatable/equatable.dart';
import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';

import 'location_info.dart';

part 'business_location.g.dart';

@HiveType(typeId: 4)
@JsonSerializable()
class BusinessLocation extends Equatable {
  @HiveField(0)
  @Json<PERSON>ey(name: 'id')
  final int id;

  @HiveField(1)
  @JsonKey(name: 'business_id')
  final int businessId;

  @HiveField(2)
  @JsonKey(name: 'location_id')
  final String? locationId;

  @HiveField(3)
  @JsonKey(name: 'name')
  final String name;

  @HiveField(4)
  @JsonKey(name: 'landmark')
  final String? landmark;

  @HiveField(5)
  @JsonKey(name: 'country')
  final String country;

  @HiveField(6)
  @JsonKey(name: 'state')
  final String state;

  @HiveField(7)
  @JsonKey(name: 'city')
  final String city;

  @HiveField(8)
  @<PERSON>sonKey(name: 'zip_code')
  final String zipCode;

  @HiveField(9)
  @JsonKey(name: 'invoice_scheme_id')
  final int invoiceSchemeId;

  @HiveField(10)
  @JsonKey(name: 'invoice_layout_id')
  final int invoiceLayoutId;

  @HiveField(11)
  @JsonKey(name: 'sale_invoice_layout_id')
  final int saleInvoiceLayoutId;

  @HiveField(12)
  @JsonKey(name: 'selling_price_group_id')
  final int? sellingPriceGroupId;

  @HiveField(13)
  @JsonKey(name: 'print_receipt_on_invoice')
  final int printReceiptOnInvoice;

  @HiveField(14)
  @JsonKey(name: 'receipt_printer_type')
  final String receiptPrinterType;

  @HiveField(15)
  @JsonKey(name: 'printer_id')
  final int? printerId;

  @HiveField(16)
  @JsonKey(name: 'mobile')
  final String? mobile;

  @HiveField(17)
  @JsonKey(name: 'alternate_number')
  final String? alternateNumber;

  @HiveField(18)
  @JsonKey(name: 'email')
  final String? email;

  @HiveField(19)
  @JsonKey(name: 'website')
  final String? website;

  @HiveField(20)
  @JsonKey(name: 'featured_products')
  final List<dynamic>? featuredProducts;

  @HiveField(21)
  @JsonKey(name: 'is_active')
  final int isActive;

  @HiveField(22)
  @JsonKey(name: 'custom_field1')
  final String? customField1;

  @HiveField(23)
  @JsonKey(name: 'custom_field2')
  final String? customField2;

  @HiveField(24)
  @JsonKey(name: 'custom_field3')
  final String? customField3;

  @HiveField(25)
  @JsonKey(name: 'custom_field4')
  final String? customField4;

  @HiveField(26)
  @JsonKey(name: 'deleted_at')
  final DateTime? deletedAt;

  @HiveField(27)
  @JsonKey(name: 'created_at')
  final DateTime createdAt;

  @HiveField(28)
  @JsonKey(name: 'updated_at')
  final DateTime updatedAt;

  @HiveField(29)
  @JsonKey(name: 'purchase_account_id')
  final String? purchaseAccountId;

  @HiveField(30)
  @JsonKey(name: 'show_qty_info')
  final int? showQtyInfo;

  @HiveField(31)
  @JsonKey(name: 'activity_code')
  final String? activityCode;

  @HiveField(32)
  @JsonKey(name: 'payment_methods')
  final List<PaymentMethod> paymentMethods;

  @HiveField(33)
  @JsonKey(name: 'location_info', fromJson: locationInfoFromListJson)
  final LocationInfo? locationInfo;

  const BusinessLocation({
    required this.id,
    required this.businessId,
    this.locationId,
    required this.name,
    this.landmark,
    required this.country,
    required this.state,
    required this.city,
    required this.zipCode,
    required this.invoiceSchemeId,
    required this.invoiceLayoutId,
    required this.saleInvoiceLayoutId,
    required this.sellingPriceGroupId,
    required this.printReceiptOnInvoice,
    required this.receiptPrinterType,
    this.printerId,
    this.mobile,
    this.alternateNumber,
    this.email,
    this.website,
    this.featuredProducts,
    required this.isActive,
    this.customField1,
    this.customField2,
    this.customField3,
    this.customField4,
    this.deletedAt,
    required this.createdAt,
    required this.updatedAt,
    this.purchaseAccountId,
    this.showQtyInfo,
    this.activityCode,
    required this.paymentMethods,
    this.locationInfo,
  });

  factory BusinessLocation.fromJson(Map<String, dynamic> json) =>
      _$BusinessLocationFromJson(json);

  Map<String, dynamic> toJson() => _$BusinessLocationToJson(this);

  @override
  List<Object?> get props => [
    id,
    businessId,
    locationId,
    name,
    landmark,
    country,
    state,
    city,
    zipCode,
    invoiceSchemeId,
    invoiceLayoutId,
    saleInvoiceLayoutId,
    sellingPriceGroupId,
    printReceiptOnInvoice,
    receiptPrinterType,
    printerId,
    mobile,
    alternateNumber,
    email,
    website,
    featuredProducts,
    isActive,
    customField1,
    customField2,
    customField3,
    customField4,
    deletedAt,
    createdAt,
    updatedAt,
    purchaseAccountId,
    showQtyInfo,
    activityCode,
    paymentMethods,
    locationInfo,
  ];
}

@JsonSerializable()
@HiveType(typeId: 5)
class PaymentMethod extends Equatable {
  @HiveField(0)
  @JsonKey(name: 'name')
  final String? name;

  @HiveField(1)
  @JsonKey(name: 'label')
  final String? label;

  @HiveField(2)
  @JsonKey(name: 'account_id')
  final String? accountId;

  const PaymentMethod({
    this.name,
    this.label,
    this.accountId,
  });

  factory PaymentMethod.fromJson(Map<String, dynamic> json) =>
      _$PaymentMethodFromJson(json);

  Map<String, dynamic> toJson() => _$PaymentMethodToJson(this);

  @override
  List<Object?> get props => [name, label, accountId];
}
