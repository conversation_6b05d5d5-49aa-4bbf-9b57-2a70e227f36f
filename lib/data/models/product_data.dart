import 'package:flutter/material.dart';
import 'package:we2up/data/models/product.dart';

class ProductData {
  final Product product;
  final TextEditingController quantityController;
  final TextEditingController discountController;
  final TextEditingController customPiecePriceController;
  final TextEditingController noteController;
  Unit unit;
  Variation? variation;
  TaxRate? taxRate;
  bool editable = false;
  bool isActive = false;
  bool isPercentage = false;

  ProductData({
    required this.product,
    required this.quantityController,
    required this.discountController,
    required this.customPiecePriceController,
    required this.noteController,
    required this.unit,
    this.variation,
    this.taxRate,
    required this.editable,
    required this.isActive,
    required this.isPercentage,
  });
}
