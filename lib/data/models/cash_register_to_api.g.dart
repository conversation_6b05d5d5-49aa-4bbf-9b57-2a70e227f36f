// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'cash_register_to_api.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class CashRegisterToAPIAdapter extends TypeAdapter<CashRegisterToAPI> {
  @override
  final int typeId = 44;

  @override
  CashRegisterToAPI read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return CashRegisterToAPI(
      locationInfo: fields[0] as LocationInfo?,
      locationId: fields[1] as int?,
      createdAt: fields[3] as DateTime,
      closedAt: fields[4] as DateTime?,
      status: fields[5] as CashRegisterStatus,
      closingAmount: fields[6] as double,
      initialAmount: fields[2] as double?,
      totalCardSlips: fields[7] as int,
      totalCheques: fields[8] as int,
      closingNote: fields[9] as String?,
      transactionIds: (fields[10] as List?)?.cast<int>(),
      id: fields[11] as int?,
    );
  }

  @override
  void write(BinaryWriter writer, CashRegisterToAPI obj) {
    writer
      ..writeByte(12)
      ..writeByte(0)
      ..write(obj.locationInfo)
      ..writeByte(1)
      ..write(obj.locationId)
      ..writeByte(2)
      ..write(obj.initialAmount)
      ..writeByte(3)
      ..write(obj.createdAt)
      ..writeByte(4)
      ..write(obj.closedAt)
      ..writeByte(5)
      ..write(obj.status)
      ..writeByte(6)
      ..write(obj.closingAmount)
      ..writeByte(7)
      ..write(obj.totalCardSlips)
      ..writeByte(8)
      ..write(obj.totalCheques)
      ..writeByte(9)
      ..write(obj.closingNote)
      ..writeByte(10)
      ..write(obj.transactionIds)
      ..writeByte(11)
      ..write(obj.id);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CashRegisterToAPIAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CashRegisterToAPI _$CashRegisterToAPIFromJson(Map<String, dynamic> json) =>
    CashRegisterToAPI(
      locationInfo: json['location_info'] == null
          ? null
          : LocationInfo.fromJson(
              json['location_info'] as Map<String, dynamic>),
      locationId: (json['location_id'] as num?)?.toInt(),
      createdAt:
          CashRegisterToAPI._dateTimeFromString(json['created_at'] as String),
      closedAt:
          CashRegisterToAPI._dateTimeFromString(json['closed_at'] as String),
      status: CashRegisterToAPI._statusFromJson(json['status'] as String),
      closingAmount: (json['closing_amount'] as num).toDouble(),
      initialAmount: (json['initial_amount'] as num?)?.toDouble(),
      totalCardSlips: (json['total_card_slips'] as num?)?.toInt() ?? 0,
      totalCheques: (json['total_cheques'] as num?)?.toInt() ?? 0,
      closingNote: json['closing_note'] as String?,
      transactionIds: CashRegisterToAPI._transactionIdsFromJson(
          json['transaction_ids'] as String?),
      id: (json['id'] as num?)?.toInt(),
    );

Map<String, dynamic> _$CashRegisterToAPIToJson(CashRegisterToAPI instance) =>
    <String, dynamic>{
      'location_info': locationInfoToJson(instance.locationInfo),
      'location_id': instance.locationId,
      'initial_amount': instance.initialAmount,
      'created_at': CashRegisterToAPI._dateTimeToString(instance.createdAt),
      'closed_at': CashRegisterToAPI._dateTimeToString(instance.closedAt),
      'status': CashRegisterToAPI._statusToJson(instance.status),
      'closing_amount': instance.closingAmount,
      'total_card_slips': instance.totalCardSlips,
      'total_cheques': instance.totalCheques,
      'closing_note': instance.closingNote,
      'transaction_ids':
          CashRegisterToAPI._transactionIdsToJson(instance.transactionIds),
      'id': instance.id,
    };
