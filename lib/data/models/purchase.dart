import 'package:equatable/equatable.dart';
import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:we2up/data/models/purchase_to_api.dart';
import 'package:we2up/data/models/sell.dart';

import '../../utils/we2up_constants.dart';
import 'location_info.dart';

part 'purchase.g.dart';

@JsonSerializable()
@HiveType(typeId: 34)
class Purchase extends Equatable {
  @JsonKey(name: 'id')
  @HiveField(0)
  final int? id;

  @JsonKey(name: 'business_id')
  @HiveField(1)
  final int? businessId;

  @JsonKey(name: 'location_id')
  @HiveField(2)
  final int? locationId;

  @JsonKey(name: 'res_table_id')
  @HiveField(3)
  final dynamic resTableId;

  @JsonKey(name: 'res_waiter_id')
  @HiveField(4)
  final dynamic resWaiterId;

  @<PERSON>son<PERSON>ey(name: 'res_order_status')
  @HiveField(5)
  final dynamic resOrderStatus;

  @Json<PERSON>ey(name: 'type')
  @HiveField(6)
  final String? type;

  @JsonKey(name: 'sub_type')
  @HiveField(7)
  final dynamic subType;

  @JsonKey(name: 'status')
  @HiveField(8)
  final String? status;

  @JsonKey(name: 'sub_status')
  @HiveField(9)
  final dynamic subStatus;

  @JsonKey(name: 'is_quotation')
  @HiveField(10)
  final int? isQuotation;

  @JsonKey(name: 'payment_status')
  @HiveField(11)
  final String? paymentStatus;

  @JsonKey(name: 'adjustment_type')
  @HiveField(12)
  final dynamic adjustmentType;

  @JsonKey(name: 'contact_id')
  @HiveField(13)
  final int? contactId;

  @JsonKey(name: 'customer_group_id')
  @HiveField(14)
  final dynamic customerGroupId;

  @JsonKey(name: 'invoice_no')
  @HiveField(15)
  final dynamic invoiceNo;

  @JsonKey(name: 'ref_no')
  @HiveField(16)
  final dynamic refNo;

  @JsonKey(name: 'source')
  @HiveField(17)
  final dynamic source;

  @JsonKey(name: 'subscription_no')
  @HiveField(18)
  final dynamic subscriptionNo;

  @JsonKey(name: 'subscription_repeat_on')
  @HiveField(19)
  final dynamic subscriptionRepeatOn;

  @JsonKey(
    name: 'transaction_date',
    fromJson: DateTime.parse,
    toJson: dateToString,
  )
  @HiveField(20)
  final DateTime transactionDate;

  @JsonKey(name: 'total_before_tax')
  @HiveField(21)
  final String? totalBeforeTax;

  @JsonKey(name: 'tax_id')
  @HiveField(22)
  final dynamic taxId;

  @JsonKey(name: 'tax_amount')
  @HiveField(23)
  final String? taxAmount;

  @JsonKey(name: 'discount_type')
  @HiveField(24)
  final String? discountType;

  @JsonKey(name: 'discount_amount')
  @HiveField(25)
  final String? discountAmount;

  @JsonKey(name: 'rp_redeemed')
  @HiveField(26)
  final int? rpRedeemed;

  @JsonKey(name: 'rp_redeemed_amount')
  @HiveField(27)
  final String? rpRedeemedAmount;

  @JsonKey(name: 'shipping_details')
  @HiveField(28)
  final dynamic shippingDetails;

  @JsonKey(name: 'shipping_address')
  @HiveField(29)
  final dynamic shippingAddress;

  @JsonKey(name: 'delivery_date')
  @HiveField(30)
  final dynamic deliveryDate;

  @JsonKey(name: 'shipping_status')
  @HiveField(31)
  final dynamic shippingStatus;

  @JsonKey(name: 'delivered_to')
  @HiveField(32)
  final dynamic deliveredTo;

  @JsonKey(name: 'shipping_charges')
  @HiveField(33)
  final String? shippingCharges;

  @JsonKey(name: 'shipping_custom_field_1')
  @HiveField(34)
  final dynamic shippingCustomField1;

  @JsonKey(name: 'shipping_custom_field_2')
  @HiveField(35)
  final dynamic shippingCustomField2;

  @JsonKey(name: 'shipping_custom_field_3')
  @HiveField(36)
  final dynamic shippingCustomField3;

  @JsonKey(name: 'shipping_custom_field_4')
  @HiveField(37)
  final dynamic shippingCustomField4;

  @JsonKey(name: 'shipping_custom_field_5')
  @HiveField(38)
  final dynamic shippingCustomField5;

  @JsonKey(name: 'additional_notes')
  @HiveField(39)
  final dynamic additionalNotes;

  @JsonKey(name: 'staff_note')
  @HiveField(40)
  final dynamic staffNote;

  @JsonKey(name: 'is_export')
  @HiveField(41)
  final int? isExport;

  @JsonKey(name: 'export_custom_fields_info')
  @HiveField(42)
  final dynamic exportCustomFieldsInfo;

  @JsonKey(name: 'round_off_amount')
  @HiveField(43)
  final String? roundOffAmount;

  @JsonKey(name: 'additional_expense_key_1')
  @HiveField(44)
  final String? additionalExpenseKey1;

  @JsonKey(name: 'additional_expense_value_1')
  @HiveField(45)
  final String? additionalExpenseValue1;

  @JsonKey(name: 'additional_expense_key_2')
  @HiveField(46)
  final String? additionalExpenseKey2;

  @JsonKey(name: 'additional_expense_value_2')
  @HiveField(47)
  final String? additionalExpenseValue2;

  @JsonKey(name: 'additional_expense_key_3')
  @HiveField(48)
  final String? additionalExpenseKey3;

  @JsonKey(name: 'additional_expense_value_3')
  @HiveField(49)
  final String? additionalExpenseValue3;

  @JsonKey(name: 'additional_expense_key_4')
  @HiveField(50)
  final String? additionalExpenseKey4;

  @JsonKey(name: 'additional_expense_value_4')
  @HiveField(51)
  final String? additionalExpenseValue4;

  @JsonKey(name: 'final_total')
  @HiveField(52)
  final String? finalTotal;

  @JsonKey(name: 'expense_category_id')
  @HiveField(53)
  final dynamic expenseCategoryId;

  @JsonKey(name: 'expense_for')
  @HiveField(54)
  final dynamic expenseFor;

  @JsonKey(name: 'commission_agent')
  @HiveField(55)
  final dynamic commissionAgent;

  @JsonKey(name: 'document')
  @HiveField(56)
  final dynamic document;

  @JsonKey(name: 'is_direct_sale')
  @HiveField(57)
  final int? isDirectSale;

  @JsonKey(name: 'is_suspend')
  @HiveField(58)
  final int? isSuspend;

  @JsonKey(name: 'exchange_rate')
  @HiveField(59)
  final String? exchangeRate;

  @JsonKey(name: 'total_amount_recovered')
  @HiveField(60)
  final dynamic totalAmountRecovered;

  @JsonKey(name: 'transfer_parent_id')
  @HiveField(61)
  final dynamic transferParentId;

  @JsonKey(name: 'return_parent_id')
  @HiveField(62)
  final dynamic returnParentId;

  @JsonKey(name: 'opening_stock_product_id')
  @HiveField(63)
  final dynamic openingStockProductId;

  @JsonKey(name: 'created_by')
  @HiveField(64)
  final int? createdBy;

  @JsonKey(name: 'purchase_requisition_ids')
  @HiveField(65)
  final dynamic purchaseRequisitionIds;

  @JsonKey(name: 'crm_is_order_request')
  @HiveField(66)
  final int? crmIsOrderRequest;

  @JsonKey(name: 'prefer_payment_method')
  @HiveField(67)
  final dynamic preferPaymentMethod;

  @JsonKey(name: 'prefer_payment_account')
  @HiveField(68)
  final dynamic preferPaymentAccount;

  @JsonKey(name: 'sales_order_ids')
  @HiveField(69)
  final dynamic salesOrderIds;

  @JsonKey(name: 'purchase_order_ids')
  @HiveField(70)
  final dynamic purchaseOrderIds;

  @JsonKey(name: 'custom_field_1')
  @HiveField(71)
  final dynamic customField1;

  @JsonKey(name: 'custom_field_2')
  @HiveField(72)
  final dynamic customField2;

  @JsonKey(name: 'custom_field_3')
  @HiveField(73)
  final dynamic customField3;

  @JsonKey(name: 'custom_field_4')
  @HiveField(74)
  final dynamic customField4;

  @JsonKey(name: 'mfg_parent_production_purchase_id')
  @HiveField(75)
  final dynamic mfgParentProductionPurchaseId;

  @JsonKey(name: 'mfg_wasted_units')
  @HiveField(76)
  final dynamic mfgWastedUnits;

  @JsonKey(name: 'mfg_production_cost')
  @HiveField(77)
  final String? mfgProductionCost;

  @JsonKey(name: 'mfg_is_final')
  @HiveField(78)
  final int? mfgIsFinal;

  @JsonKey(name: 'repair_completed_on')
  @HiveField(79)
  final dynamic repairCompletedOn;

  @JsonKey(name: 'repair_warranty_id')
  @HiveField(80)
  final dynamic repairWarrantyId;

  @JsonKey(name: 'repair_brand_id')
  @HiveField(81)
  final dynamic repairBrandId;

  @JsonKey(name: 'repair_status_id')
  @HiveField(82)
  final dynamic repairStatusId;

  @JsonKey(name: 'repair_model_id')
  @HiveField(83)
  final dynamic repairModelId;

  @JsonKey(name: 'repair_job_sheet_id')
  @HiveField(84)
  final dynamic repairJobSheetId;

  @JsonKey(name: 'repair_defects')
  @HiveField(85)
  final dynamic repairDefects;

  @JsonKey(name: 'repair_serial_no')
  @HiveField(86)
  final dynamic repairSerialNo;

  @JsonKey(name: 'repair_checklist')
  @HiveField(87)
  final dynamic repairChecklist;

  @JsonKey(name: 'repair_security_pwd')
  @HiveField(88)
  final dynamic repairSecurityPwd;

  @JsonKey(name: 'repair_security_pattern')
  @HiveField(89)
  final dynamic repairSecurityPattern;

  @JsonKey(name: 'repair_due_date')
  @HiveField(90)
  final dynamic repairDueDate;

  @JsonKey(name: 'repair_device_id')
  @HiveField(91)
  final dynamic repairDeviceId;

  @JsonKey(name: 'repair_updates_notif')
  @HiveField(92)
  final int? repairUpdatesNotif;

  @JsonKey(name: 'essentials_duration')
  @HiveField(93)
  final String? essentialsDuration;

  @JsonKey(name: 'essentials_duration_unit')
  @HiveField(94)
  final dynamic essentialsDurationUnit;

  @JsonKey(name: 'essentials_amount_per_unit_duration')
  @HiveField(95)
  final String? essentialsAmountPerUnitDuration;

  @JsonKey(name: 'essentials_allowances')
  @HiveField(96)
  final dynamic essentialsAllowances;

  @JsonKey(name: 'essentials_deductions')
  @HiveField(97)
  final dynamic essentialsDeductions;

  @JsonKey(name: 'woocommerce_order_id')
  @HiveField(98)
  final dynamic woocommerceOrderId;

  @JsonKey(name: 'import_batch')
  @HiveField(99)
  final dynamic importBatch;

  @JsonKey(name: 'import_time')
  @HiveField(100)
  final dynamic importTime;

  @JsonKey(name: 'types_of_service_id')
  @HiveField(101)
  final dynamic typesOfServiceId;

  @JsonKey(name: 'packing_charge')
  @HiveField(102)
  final dynamic packingCharge;

  @JsonKey(name: 'packing_charge_type')
  @HiveField(103)
  final dynamic packingChargeType;

  @JsonKey(name: 'service_custom_field_1')
  @HiveField(104)
  final dynamic serviceCustomField1;

  @JsonKey(name: 'service_custom_field_2')
  @HiveField(105)
  final dynamic serviceCustomField2;

  @JsonKey(name: 'service_custom_field_3')
  @HiveField(106)
  final dynamic serviceCustomField3;

  @JsonKey(name: 'service_custom_field_4')
  @HiveField(107)
  final dynamic serviceCustomField4;

  @JsonKey(name: 'service_custom_field_5')
  @HiveField(108)
  final dynamic serviceCustomField5;

  @JsonKey(name: 'service_custom_field_6')
  @HiveField(109)
  final dynamic serviceCustomField6;

  @JsonKey(name: 'is_created_from_api')
  @HiveField(110)
  final int? isCreatedFromApi;

  @JsonKey(name: 'rp_earned')
  @HiveField(111)
  final int? rpEarned;

  @JsonKey(name: 'order_addresses')
  @HiveField(112)
  final dynamic orderAddresses;

  @JsonKey(name: 'is_recurring')
  @HiveField(113)
  final int? isRecurring;

  @JsonKey(name: 'recur_interval')
  @HiveField(114)
  final dynamic recurInterval;

  @JsonKey(name: 'recur_interval_type')
  @HiveField(115)
  final dynamic recurIntervalType;

  @JsonKey(name: 'recur_repetitions')
  @HiveField(116)
  final dynamic recurRepetitions;

  @JsonKey(name: 'recur_stopped_on')
  @HiveField(117)
  final dynamic recurStoppedOn;

  @JsonKey(name: 'recur_parent_id')
  @HiveField(118)
  final dynamic recurParentId;

  @JsonKey(name: 'invoice_token')
  @HiveField(119)
  final String? invoiceToken;

  @JsonKey(name: 'pay_term_number')
  @HiveField(120)
  final dynamic payTermNumber;

  @JsonKey(name: 'pay_term_type')
  @HiveField(121)
  final dynamic payTermType;

  @JsonKey(name: 'pjt_project_id')
  @HiveField(122)
  final dynamic pjtProjectId;

  @JsonKey(name: 'pjt_title')
  @HiveField(123)
  final dynamic pjtTitle;

  @JsonKey(name: 'selling_price_group_id')
  @HiveField(124)
  final dynamic sellingPriceGroupId;

  @JsonKey(name: 'created_at')
  @HiveField(125)
  final String? createdAt;

  @JsonKey(name: 'updated_at')
  @HiveField(126)
  final String? updatedAt;

  @JsonKey(name: 'end_date')
  @HiveField(127)
  final dynamic endDate;

  @JsonKey(name: 'shipping_company_id')
  @HiveField(128)
  final dynamic shippingCompanyId;

  @JsonKey(name: 'package_count')
  @HiveField(129)
  final dynamic packageCount;

  @JsonKey(name: 'paymob_order_id')
  @HiveField(130)
  final dynamic paymobOrderId;

  @JsonKey(name: 'prev_balance')
  @HiveField(131)
  final dynamic prevBalance;

  @JsonKey(name: 'sub_counter')
  @HiveField(132)
  final dynamic subCounter;

  @JsonKey(name: 'customer_id')
  @HiveField(133)
  final dynamic customerId;

  @JsonKey(name: 'supplier_id')
  @HiveField(134)
  final dynamic supplierId;

  @JsonKey(name: 'sell_id')
  @HiveField(135)
  final dynamic sellId;

  @JsonKey(name: 'purchase_id')
  @HiveField(136)
  final dynamic purchaseId;

  @JsonKey(name: 'eta_submissionId')
  @HiveField(137)
  final dynamic etaSubmissionId;

  @JsonKey(name: 'eta_uuid')
  @HiveField(138)
  final dynamic etaUuid;

  @JsonKey(name: 'eta_longId')
  @HiveField(139)
  final dynamic etaLongId;

  @JsonKey(name: 'eta_hashKey')
  @HiveField(140)
  final dynamic etaHashKey;

  @JsonKey(name: 'eta_status')
  @HiveField(141)
  final dynamic etaStatus;

  @JsonKey(name: 'eta_notes')
  @HiveField(142)
  final dynamic etaNotes;

  @JsonKey(name: 'invoice_scheme_id')
  @HiveField(143)
  final dynamic invoiceSchemeId;

  @JsonKey(name: 'custom_status')
  @HiveField(144)
  final dynamic customStatus;

  @JsonKey(name: 'is_reservation')
  @HiveField(145)
  final dynamic isReservation;

  @JsonKey(name: 'review_status')
  @HiveField(146)
  final dynamic reviewStatus;

  @JsonKey(name: 'review_details')
  @HiveField(147)
  final dynamic reviewDetails;

  @JsonKey(name: 'settlement_purchase_id')
  @HiveField(148)
  final dynamic settlementPurchaseId;

  @JsonKey(name: 'settlement_sell_id')
  @HiveField(149)
  final dynamic settlementSellId;

  @JsonKey(name: 'invoice_layout_id')
  @HiveField(150)
  final dynamic invoiceLayoutId;

  @JsonKey(name: 'invoice_commision')
  @HiveField(151)
  final dynamic invoiceCommision;

  @JsonKey(name: 'commision_as_leader')
  @HiveField(152)
  final String? commisionAsLeader;

  @JsonKey(name: 'leader_id')
  @HiveField(153)
  final dynamic leaderId;

  @JsonKey(
    name: 'purchase_lines',
    toJson: _purchaseLinesToJsonList,
    fromJson: _purchaseLinesFromJsonList,
  )
  @HiveField(154)
  final List<PurchaseLine>? purchaseLines;

  @JsonKey(
    name: 'payment_lines',
    toJson: _paymentLinesToJsonList,
    fromJson: _paymentLinesFromJsonList,
  )
  @HiveField(155)
  final List<PaymentLine>? paymentLines;

  @HiveField(156)
  @JsonKey(includeFromJson: false, includeToJson: false)
  final bool offline;

  @HiveField(157)
  @JsonKey(includeFromJson: false, includeToJson: false)
  final PurchaseToAPI? purchaseToAPI;

  @HiveField(158)
  @JsonKey(name: 'location_info', fromJson: locationInfoFromListJson)
  final LocationInfo? locationInfo;

  const Purchase({
    this.id,
    this.businessId,
    this.locationId,
    this.resTableId,
    this.resWaiterId,
    this.resOrderStatus,
    this.type,
    this.subType,
    this.status,
    this.subStatus,
    this.isQuotation,
    this.paymentStatus,
    this.adjustmentType,
    this.contactId,
    this.customerGroupId,
    this.invoiceNo,
    this.refNo,
    this.source,
    this.subscriptionNo,
    this.subscriptionRepeatOn,
    required this.transactionDate,
    this.totalBeforeTax,
    this.taxId,
    this.taxAmount,
    this.discountType,
    this.discountAmount,
    this.rpRedeemed,
    this.rpRedeemedAmount,
    this.shippingDetails,
    this.shippingAddress,
    this.deliveryDate,
    this.shippingStatus,
    this.deliveredTo,
    this.shippingCharges,
    this.shippingCustomField1,
    this.shippingCustomField2,
    this.shippingCustomField3,
    this.shippingCustomField4,
    this.shippingCustomField5,
    this.additionalNotes,
    this.staffNote,
    this.isExport,
    this.exportCustomFieldsInfo,
    this.roundOffAmount,
    this.additionalExpenseKey1,
    this.additionalExpenseValue1,
    this.additionalExpenseKey2,
    this.additionalExpenseValue2,
    this.additionalExpenseKey3,
    this.additionalExpenseValue3,
    this.additionalExpenseKey4,
    this.additionalExpenseValue4,
    this.finalTotal,
    this.expenseCategoryId,
    this.expenseFor,
    this.commissionAgent,
    this.document,
    this.isDirectSale,
    this.isSuspend,
    this.exchangeRate,
    this.totalAmountRecovered,
    this.transferParentId,
    this.returnParentId,
    this.openingStockProductId,
    this.createdBy,
    this.purchaseRequisitionIds,
    this.crmIsOrderRequest,
    this.preferPaymentMethod,
    this.preferPaymentAccount,
    this.salesOrderIds,
    this.purchaseOrderIds,
    this.customField1,
    this.customField2,
    this.customField3,
    this.customField4,
    this.mfgParentProductionPurchaseId,
    this.mfgWastedUnits,
    this.mfgProductionCost,
    this.mfgIsFinal,
    this.repairCompletedOn,
    this.repairWarrantyId,
    this.repairBrandId,
    this.repairStatusId,
    this.repairModelId,
    this.repairJobSheetId,
    this.repairDefects,
    this.repairSerialNo,
    this.repairChecklist,
    this.repairSecurityPwd,
    this.repairSecurityPattern,
    this.repairDueDate,
    this.repairDeviceId,
    this.repairUpdatesNotif,
    this.essentialsDuration,
    this.essentialsDurationUnit,
    this.essentialsAmountPerUnitDuration,
    this.essentialsAllowances,
    this.essentialsDeductions,
    this.woocommerceOrderId,
    this.importBatch,
    this.importTime,
    this.typesOfServiceId,
    this.packingCharge,
    this.packingChargeType,
    this.serviceCustomField1,
    this.serviceCustomField2,
    this.serviceCustomField3,
    this.serviceCustomField4,
    this.serviceCustomField5,
    this.serviceCustomField6,
    this.isCreatedFromApi,
    this.rpEarned,
    this.orderAddresses,
    this.isRecurring,
    this.recurInterval,
    this.recurIntervalType,
    this.recurRepetitions,
    this.recurStoppedOn,
    this.recurParentId,
    this.invoiceToken,
    this.payTermNumber,
    this.payTermType,
    this.pjtProjectId,
    this.pjtTitle,
    this.sellingPriceGroupId,
    this.createdAt,
    this.updatedAt,
    this.endDate,
    this.shippingCompanyId,
    this.packageCount,
    this.paymobOrderId,
    this.prevBalance,
    this.subCounter,
    this.customerId,
    this.supplierId,
    this.sellId,
    this.purchaseId,
    this.etaSubmissionId,
    this.etaUuid,
    this.etaLongId,
    this.etaHashKey,
    this.etaStatus,
    this.etaNotes,
    this.invoiceSchemeId,
    this.customStatus,
    this.isReservation,
    this.reviewStatus,
    this.reviewDetails,
    this.settlementPurchaseId,
    this.settlementSellId,
    this.invoiceLayoutId,
    this.invoiceCommision,
    this.commisionAsLeader,
    this.leaderId,
    this.purchaseLines,
    this.paymentLines,
    this.offline = false,
    this.purchaseToAPI,
    required this.locationInfo,
  });

  static List<PurchaseLine>? _purchaseLinesFromJsonList(List<dynamic>? list) {
    return list?.map((item) => PurchaseLine.fromJson(item)).toList();
  }

  static List<dynamic>? _purchaseLinesToJsonList(List<PurchaseLine>? list) {
    return list?.map((item) => item.toJson()).toList();
  }

  static List<PaymentLine>? _paymentLinesFromJsonList(List<dynamic>? list) {
    return list?.map((item) => PaymentLine.fromJson(item)).toList();
  }

  static List<dynamic>? _paymentLinesToJsonList(List<PaymentLine>? list) {
    return list?.map((item) => item.toJson()).toList();
  }

  factory Purchase.fromJson(Map<String, dynamic> json) =>
      _$PurchaseFromJson(json);

  Map<String, dynamic> toJson() => _$PurchaseToJson(this);

  @override
  List<Object?> get props => [
        id,
        businessId,
        locationId,
        resTableId,
        resWaiterId,
        resOrderStatus,
        type,
        subType,
        status,
        subStatus,
        isQuotation,
        paymentStatus,
        adjustmentType,
        contactId,
        customerGroupId,
        invoiceNo,
        refNo,
        source,
        subscriptionNo,
        subscriptionRepeatOn,
        transactionDate,
        totalBeforeTax,
        taxId,
        taxAmount,
        discountType,
        discountAmount,
        rpRedeemed,
        rpRedeemedAmount,
        shippingDetails,
        shippingAddress,
        deliveryDate,
        shippingStatus,
        deliveredTo,
        shippingCharges,
        shippingCustomField1,
        shippingCustomField2,
        shippingCustomField3,
        shippingCustomField4,
        shippingCustomField5,
        additionalNotes,
        staffNote,
        isExport,
        exportCustomFieldsInfo,
        roundOffAmount,
        additionalExpenseKey1,
        additionalExpenseValue1,
        additionalExpenseKey2,
        additionalExpenseValue2,
        additionalExpenseKey3,
        additionalExpenseValue3,
        additionalExpenseKey4,
        additionalExpenseValue4,
        finalTotal,
        expenseCategoryId,
        expenseFor,
        commissionAgent,
        document,
        isDirectSale,
        isSuspend,
        exchangeRate,
        totalAmountRecovered,
        transferParentId,
        returnParentId,
        openingStockProductId,
        createdBy,
        purchaseRequisitionIds,
        crmIsOrderRequest,
        preferPaymentMethod,
        preferPaymentAccount,
        salesOrderIds,
        purchaseOrderIds,
        customField1,
        customField2,
        customField3,
        customField4,
        mfgParentProductionPurchaseId,
        mfgWastedUnits,
        mfgProductionCost,
        mfgIsFinal,
        repairCompletedOn,
        repairWarrantyId,
        repairBrandId,
        repairStatusId,
        repairModelId,
        repairJobSheetId,
        repairDefects,
        repairSerialNo,
        repairChecklist,
        repairSecurityPwd,
        repairSecurityPattern,
        repairDueDate,
        repairDeviceId,
        repairUpdatesNotif,
        essentialsDuration,
        essentialsDurationUnit,
        essentialsAmountPerUnitDuration,
        essentialsAllowances,
        essentialsDeductions,
        woocommerceOrderId,
        importBatch,
        importTime,
        typesOfServiceId,
        packingCharge,
        packingChargeType,
        serviceCustomField1,
        serviceCustomField2,
        serviceCustomField3,
        serviceCustomField4,
        serviceCustomField5,
        serviceCustomField6,
        isCreatedFromApi,
        rpEarned,
        orderAddresses,
        isRecurring,
        recurInterval,
        recurIntervalType,
        recurRepetitions,
        recurStoppedOn,
        recurParentId,
        invoiceToken,
        payTermNumber,
        payTermType,
        pjtProjectId,
        pjtTitle,
        sellingPriceGroupId,
        createdAt,
        updatedAt,
        endDate,
        shippingCompanyId,
        packageCount,
        paymobOrderId,
        prevBalance,
        subCounter,
        customerId,
        supplierId,
        sellId,
        purchaseId,
        etaSubmissionId,
        etaUuid,
        etaLongId,
        etaHashKey,
        etaStatus,
        etaNotes,
        invoiceSchemeId,
        customStatus,
        isReservation,
        reviewStatus,
        reviewDetails,
        settlementPurchaseId,
        settlementSellId,
        invoiceLayoutId,
        invoiceCommision,
        commisionAsLeader,
        leaderId,
        purchaseLines,
        paymentLines,
        offline,
        purchaseToAPI,
        locationInfo,
      ];
}

@JsonSerializable()
@HiveType(typeId: 35)
class PurchaseLine extends Equatable {
  @JsonKey(name: 'id')
  @HiveField(0)
  final int? id;

  @JsonKey(name: 'transaction_id')
  @HiveField(1)
  final int? transactionId;

  @JsonKey(name: 'product_id')
  @HiveField(2)
  final int? productId;

  @JsonKey(name: 'variation_id')
  @HiveField(3)
  final int? variationId;

  @JsonKey(name: 'quantity')
  @HiveField(4)
  final double? quantity;

  @JsonKey(name: 'secondary_unit_quantity')
  @HiveField(5)
  final String? secondaryUnitQuantity;

  @JsonKey(name: 'pp_without_discount')
  @HiveField(6)
  final String? ppWithoutDiscount;

  @JsonKey(name: 'discount_percent')
  @HiveField(7)
  final String? discountPercent;

  @JsonKey(name: 'purchase_price')
  @HiveField(8)
  final String? purchasePrice;

  @JsonKey(name: 'purchase_price_inc_tax')
  @HiveField(9)
  final String? purchasePriceIncTax;

  @JsonKey(name: 'item_tax')
  @HiveField(10)
  final String? itemTax;

  @JsonKey(name: 'tax_id')
  @HiveField(11)
  final dynamic taxId;

  @JsonKey(name: 'purchase_order_line_id')
  @HiveField(12)
  final dynamic purchaseOrderLineId;

  @JsonKey(name: 'quantity_sold')
  @HiveField(13)
  final String? quantitySold;

  @JsonKey(name: 'quantity_adjusted')
  @HiveField(14)
  final String? quantityAdjusted;

  @JsonKey(name: 'quantity_returned')
  @HiveField(15)
  final String? quantityReturned;

  @JsonKey(name: 'po_quantity_purchased')
  @HiveField(16)
  final String? poQuantityPurchased;

  @JsonKey(name: 'mfg_quantity_used')
  @HiveField(17)
  final String? mfgQuantityUsed;

  @JsonKey(name: 'mfg_date')
  @HiveField(18)
  final dynamic mfgDate;

  @JsonKey(name: 'exp_date')
  @HiveField(19)
  final dynamic expDate;

  @JsonKey(name: 'lot_number')
  @HiveField(20)
  final dynamic lotNumber;

  @JsonKey(name: 'sub_unit_id')
  @HiveField(21)
  final int? subUnitId;

  @JsonKey(name: 'created_at')
  @HiveField(22)
  final String? createdAt;

  @JsonKey(name: 'updated_at')
  @HiveField(23)
  final String? updatedAt;

  @JsonKey(name: 'packages_counter')
  @HiveField(24)
  final int? packagesCounter;

  @JsonKey(name: 'serial_number')
  @HiveField(25)
  final dynamic serialNumber;

  @JsonKey(name: 'bonus_quantity')
  @HiveField(26)
  final String? bonusQuantity;

  @JsonKey(name: 'is_bonus')
  @HiveField(27)
  final int? isBonus;

  @JsonKey(name: 'x')
  @HiveField(28)
  final dynamic x;

  @JsonKey(name: 'y')
  @HiveField(29)
  final dynamic y;

  @JsonKey(name: 'z')
  @HiveField(30)
  final dynamic z;

  @JsonKey(name: 'num')
  @HiveField(31)
  final dynamic num;

  const PurchaseLine({
    this.id,
    this.transactionId,
    this.productId,
    this.variationId,
    this.quantity,
    this.secondaryUnitQuantity,
    this.ppWithoutDiscount,
    this.discountPercent,
    this.purchasePrice,
    this.purchasePriceIncTax,
    this.itemTax,
    this.taxId,
    this.purchaseOrderLineId,
    this.quantitySold,
    this.quantityAdjusted,
    this.quantityReturned,
    this.poQuantityPurchased,
    this.mfgQuantityUsed,
    this.mfgDate,
    this.expDate,
    this.lotNumber,
    this.subUnitId,
    this.createdAt,
    this.updatedAt,
    this.packagesCounter,
    this.serialNumber,
    this.bonusQuantity,
    this.isBonus,
    this.x,
    this.y,
    this.z,
    this.num,
  });

  factory PurchaseLine.fromJson(Map<String, dynamic> json) =>
      _$PurchaseLineFromJson(json);

  Map<String, dynamic> toJson() => _$PurchaseLineToJson(this);

  @override
  List<Object?> get props => [
        id,
        transactionId,
        productId,
        variationId,
        quantity,
        secondaryUnitQuantity,
        ppWithoutDiscount,
        discountPercent,
        purchasePrice,
        purchasePriceIncTax,
        itemTax,
        taxId,
        purchaseOrderLineId,
        quantitySold,
        quantityAdjusted,
        quantityReturned,
        poQuantityPurchased,
        mfgQuantityUsed,
        mfgDate,
        expDate,
        lotNumber,
        subUnitId,
        createdAt,
        updatedAt,
        packagesCounter,
        serialNumber,
        bonusQuantity,
        isBonus,
        x,
        y,
        z,
        num,
      ];
}
