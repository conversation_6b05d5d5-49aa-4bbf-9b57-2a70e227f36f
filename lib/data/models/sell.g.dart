// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'sell.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class SellAdapter extends TypeAdapter<Sell> {
  @override
  final int typeId = 20;

  @override
  Sell read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Sell(
      id: fields[0] as int?,
      businessId: fields[1] as int?,
      locationId: fields[2] as int?,
      resTableId: fields[3] as int?,
      resWaiterId: fields[4] as int?,
      resOrderStatus: fields[5] as String?,
      type: fields[6] as String?,
      subType: fields[7] as String?,
      status: fields[8] as String?,
      subStatus: fields[9] as String?,
      isQuotation: fields[10] as int?,
      paymentStatus: fields[11] as PaymentStatus,
      adjustmentType: fields[12] as String?,
      contactId: fields[13] as int?,
      customerGroupId: fields[14] as int?,
      invoiceNo: fields[15] as String?,
      refNo: fields[16] as String?,
      source: fields[17] as String?,
      subscriptionNo: fields[18] as String?,
      subscriptionRepeatOn: fields[19] as String?,
      transactionDate: fields[20] as DateTime,
      totalBeforeTax: fields[21] as String?,
      taxId: fields[22] as int?,
      taxAmount: fields[23] as String?,
      discountType: fields[24] as String?,
      discountAmount: fields[25] as String?,
      rpRedeemed: fields[26] as int?,
      rpRedeemedAmount: fields[27] as String?,
      shippingDetails: fields[28] as String?,
      shippingAddress: fields[29] as String?,
      shippingStatus: fields[30] as ShipmentStatus?,
      deliveredTo: fields[31] as String?,
      shippingCharges: fields[32] as String?,
      shippingCustomField1: fields[33] as String?,
      shippingCustomField2: fields[34] as String?,
      shippingCustomField3: fields[35] as String?,
      shippingCustomField4: fields[36] as String?,
      shippingCustomField5: fields[37] as String?,
      additionalNotes: fields[38] as String?,
      staffNote: fields[39] as String?,
      isExport: fields[40] as int?,
      exportCustomFieldsInfo: fields[41] as String?,
      roundOffAmount: fields[42] as String?,
      additionalExpenseKey1: fields[43] as String?,
      additionalExpenseValue1: fields[44] as String?,
      additionalExpenseKey2: fields[45] as String?,
      additionalExpenseValue2: fields[46] as String?,
      additionalExpenseKey3: fields[47] as String?,
      additionalExpenseValue3: fields[48] as String?,
      additionalExpenseKey4: fields[49] as String?,
      additionalExpenseValue4: fields[50] as String?,
      finalTotal: fields[51] as String?,
      expenseCategoryId: fields[52] as int?,
      expenseFor: fields[53] as String?,
      commissionAgent: fields[54] as int?,
      document: fields[55] as String?,
      isDirectSale: fields[56] as int?,
      isSuspend: fields[57] as int?,
      exchangeRate: fields[58] as String?,
      totalAmountRecovered: fields[59] as String?,
      transferParentId: fields[60] as int?,
      returnParentId: fields[61] as int?,
      openingStockProductId: fields[62] as int?,
      createdBy: fields[63] as int?,
      crmIsOrderRequest: fields[64] as int?,
      preferPaymentMethod: fields[65] as String?,
      preferPaymentAccount: fields[66] as String?,
      salesOrderIds: fields[67] as String?,
      purchaseOrderIds: fields[68] as String?,
      customField1: fields[69] as String?,
      customField2: fields[70] as String?,
      customField3: fields[71] as String?,
      customField4: fields[72] as String?,
      mfgParentProductionPurchaseId: fields[73] as int?,
      mfgWastedUnits: fields[74] as String?,
      mfgProductionCost: fields[75] as String?,
      mfgIsFinal: fields[76] as int?,
      repairCompletedOn: fields[77] as String?,
      repairWarrantyId: fields[78] as int?,
      repairBrandId: fields[79] as int?,
      repairStatusId: fields[80] as int?,
      repairModelId: fields[81] as int?,
      repairJobSheetId: fields[82] as int?,
      repairDefects: fields[83] as String?,
      repairSerialNo: fields[84] as String?,
      repairChecklist: fields[85] as String?,
      repairSecurityPwd: fields[86] as String?,
      repairSecurityPattern: fields[87] as String?,
      repairDueDate: fields[88] as String?,
      repairDeviceId: fields[89] as int?,
      repairUpdatesNotif: fields[90] as int?,
      essentialsDuration: fields[91] as String?,
      essentialsDurationUnit: fields[92] as String?,
      essentialsAmountPerUnitDuration: fields[93] as String?,
      essentialsAllowances: fields[94] as String?,
      essentialsDeductions: fields[95] as String?,
      woocommerceOrderId: fields[96] as int?,
      importBatch: fields[97] as String?,
      importTime: fields[98] as String?,
      typesOfServiceId: fields[99] as int?,
      packingId: fields[100] as int?,
      terminalId: fields[101] as int?,
      isComplementary: fields[102] as int?,
      complementaryDetails: fields[103] as String?,
      voidAmount: fields[104] as String?,
      changeReturned: fields[105] as String?,
      voucherId: fields[106] as int?,
      payingDue: fields[107] as String?,
      customersVoucherNo: fields[108] as String?,
      customersVoucherId: fields[109] as int?,
      isIntegrated: fields[110] as int?,
      isEuvatInvoice: fields[111] as int?,
      isAbandoned: fields[112] as int?,
      sellersInvoiceId: fields[113] as int?,
      isIgs: fields[114] as int?,
      isPos: fields[115] as int?,
      isTds: fields[116] as int?,
      isDineIn: fields[117] as int?,
      createdOn: fields[118] as String?,
      syncedOn: fields[119] as String?,
      paymentType: fields[120] as String?,
      paymentLines: (fields[121] as List?)?.cast<PaymentLine>(),
      sellLines: (fields[122] as List?)?.cast<SellLine>(),
      offline: fields[123] as bool,
      sellToAPI: fields[124] as SellToAPI?,
      qrCode: fields[126] as String?,
      qrCodeImage: fields[127] as String?,
      invoiceUrl: fields[128] as String?,
      locationInfo: fields[125] as LocationInfo?,
      shippingCompanyId: fields[129] as int?,
    );
  }

  @override
  void write(BinaryWriter writer, Sell obj) {
    writer
      ..writeByte(130)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.businessId)
      ..writeByte(2)
      ..write(obj.locationId)
      ..writeByte(3)
      ..write(obj.resTableId)
      ..writeByte(4)
      ..write(obj.resWaiterId)
      ..writeByte(5)
      ..write(obj.resOrderStatus)
      ..writeByte(6)
      ..write(obj.type)
      ..writeByte(7)
      ..write(obj.subType)
      ..writeByte(8)
      ..write(obj.status)
      ..writeByte(9)
      ..write(obj.subStatus)
      ..writeByte(10)
      ..write(obj.isQuotation)
      ..writeByte(11)
      ..write(obj.paymentStatus)
      ..writeByte(12)
      ..write(obj.adjustmentType)
      ..writeByte(13)
      ..write(obj.contactId)
      ..writeByte(14)
      ..write(obj.customerGroupId)
      ..writeByte(15)
      ..write(obj.invoiceNo)
      ..writeByte(16)
      ..write(obj.refNo)
      ..writeByte(17)
      ..write(obj.source)
      ..writeByte(18)
      ..write(obj.subscriptionNo)
      ..writeByte(19)
      ..write(obj.subscriptionRepeatOn)
      ..writeByte(20)
      ..write(obj.transactionDate)
      ..writeByte(21)
      ..write(obj.totalBeforeTax)
      ..writeByte(22)
      ..write(obj.taxId)
      ..writeByte(23)
      ..write(obj.taxAmount)
      ..writeByte(24)
      ..write(obj.discountType)
      ..writeByte(25)
      ..write(obj.discountAmount)
      ..writeByte(26)
      ..write(obj.rpRedeemed)
      ..writeByte(27)
      ..write(obj.rpRedeemedAmount)
      ..writeByte(28)
      ..write(obj.shippingDetails)
      ..writeByte(29)
      ..write(obj.shippingAddress)
      ..writeByte(30)
      ..write(obj.shippingStatus)
      ..writeByte(31)
      ..write(obj.deliveredTo)
      ..writeByte(32)
      ..write(obj.shippingCharges)
      ..writeByte(33)
      ..write(obj.shippingCustomField1)
      ..writeByte(34)
      ..write(obj.shippingCustomField2)
      ..writeByte(35)
      ..write(obj.shippingCustomField3)
      ..writeByte(36)
      ..write(obj.shippingCustomField4)
      ..writeByte(37)
      ..write(obj.shippingCustomField5)
      ..writeByte(38)
      ..write(obj.additionalNotes)
      ..writeByte(39)
      ..write(obj.staffNote)
      ..writeByte(40)
      ..write(obj.isExport)
      ..writeByte(41)
      ..write(obj.exportCustomFieldsInfo)
      ..writeByte(42)
      ..write(obj.roundOffAmount)
      ..writeByte(43)
      ..write(obj.additionalExpenseKey1)
      ..writeByte(44)
      ..write(obj.additionalExpenseValue1)
      ..writeByte(45)
      ..write(obj.additionalExpenseKey2)
      ..writeByte(46)
      ..write(obj.additionalExpenseValue2)
      ..writeByte(47)
      ..write(obj.additionalExpenseKey3)
      ..writeByte(48)
      ..write(obj.additionalExpenseValue3)
      ..writeByte(49)
      ..write(obj.additionalExpenseKey4)
      ..writeByte(50)
      ..write(obj.additionalExpenseValue4)
      ..writeByte(51)
      ..write(obj.finalTotal)
      ..writeByte(52)
      ..write(obj.expenseCategoryId)
      ..writeByte(53)
      ..write(obj.expenseFor)
      ..writeByte(54)
      ..write(obj.commissionAgent)
      ..writeByte(55)
      ..write(obj.document)
      ..writeByte(56)
      ..write(obj.isDirectSale)
      ..writeByte(57)
      ..write(obj.isSuspend)
      ..writeByte(58)
      ..write(obj.exchangeRate)
      ..writeByte(59)
      ..write(obj.totalAmountRecovered)
      ..writeByte(60)
      ..write(obj.transferParentId)
      ..writeByte(61)
      ..write(obj.returnParentId)
      ..writeByte(62)
      ..write(obj.openingStockProductId)
      ..writeByte(63)
      ..write(obj.createdBy)
      ..writeByte(64)
      ..write(obj.crmIsOrderRequest)
      ..writeByte(65)
      ..write(obj.preferPaymentMethod)
      ..writeByte(66)
      ..write(obj.preferPaymentAccount)
      ..writeByte(67)
      ..write(obj.salesOrderIds)
      ..writeByte(68)
      ..write(obj.purchaseOrderIds)
      ..writeByte(69)
      ..write(obj.customField1)
      ..writeByte(70)
      ..write(obj.customField2)
      ..writeByte(71)
      ..write(obj.customField3)
      ..writeByte(72)
      ..write(obj.customField4)
      ..writeByte(73)
      ..write(obj.mfgParentProductionPurchaseId)
      ..writeByte(74)
      ..write(obj.mfgWastedUnits)
      ..writeByte(75)
      ..write(obj.mfgProductionCost)
      ..writeByte(76)
      ..write(obj.mfgIsFinal)
      ..writeByte(77)
      ..write(obj.repairCompletedOn)
      ..writeByte(78)
      ..write(obj.repairWarrantyId)
      ..writeByte(79)
      ..write(obj.repairBrandId)
      ..writeByte(80)
      ..write(obj.repairStatusId)
      ..writeByte(81)
      ..write(obj.repairModelId)
      ..writeByte(82)
      ..write(obj.repairJobSheetId)
      ..writeByte(83)
      ..write(obj.repairDefects)
      ..writeByte(84)
      ..write(obj.repairSerialNo)
      ..writeByte(85)
      ..write(obj.repairChecklist)
      ..writeByte(86)
      ..write(obj.repairSecurityPwd)
      ..writeByte(87)
      ..write(obj.repairSecurityPattern)
      ..writeByte(88)
      ..write(obj.repairDueDate)
      ..writeByte(89)
      ..write(obj.repairDeviceId)
      ..writeByte(90)
      ..write(obj.repairUpdatesNotif)
      ..writeByte(91)
      ..write(obj.essentialsDuration)
      ..writeByte(92)
      ..write(obj.essentialsDurationUnit)
      ..writeByte(93)
      ..write(obj.essentialsAmountPerUnitDuration)
      ..writeByte(94)
      ..write(obj.essentialsAllowances)
      ..writeByte(95)
      ..write(obj.essentialsDeductions)
      ..writeByte(96)
      ..write(obj.woocommerceOrderId)
      ..writeByte(97)
      ..write(obj.importBatch)
      ..writeByte(98)
      ..write(obj.importTime)
      ..writeByte(99)
      ..write(obj.typesOfServiceId)
      ..writeByte(100)
      ..write(obj.packingId)
      ..writeByte(101)
      ..write(obj.terminalId)
      ..writeByte(102)
      ..write(obj.isComplementary)
      ..writeByte(103)
      ..write(obj.complementaryDetails)
      ..writeByte(104)
      ..write(obj.voidAmount)
      ..writeByte(105)
      ..write(obj.changeReturned)
      ..writeByte(106)
      ..write(obj.voucherId)
      ..writeByte(107)
      ..write(obj.payingDue)
      ..writeByte(108)
      ..write(obj.customersVoucherNo)
      ..writeByte(109)
      ..write(obj.customersVoucherId)
      ..writeByte(110)
      ..write(obj.isIntegrated)
      ..writeByte(111)
      ..write(obj.isEuvatInvoice)
      ..writeByte(112)
      ..write(obj.isAbandoned)
      ..writeByte(113)
      ..write(obj.sellersInvoiceId)
      ..writeByte(114)
      ..write(obj.isIgs)
      ..writeByte(115)
      ..write(obj.isPos)
      ..writeByte(116)
      ..write(obj.isTds)
      ..writeByte(117)
      ..write(obj.isDineIn)
      ..writeByte(118)
      ..write(obj.createdOn)
      ..writeByte(119)
      ..write(obj.syncedOn)
      ..writeByte(120)
      ..write(obj.paymentType)
      ..writeByte(121)
      ..write(obj.paymentLines)
      ..writeByte(122)
      ..write(obj.sellLines)
      ..writeByte(123)
      ..write(obj.offline)
      ..writeByte(124)
      ..write(obj.sellToAPI)
      ..writeByte(125)
      ..write(obj.locationInfo)
      ..writeByte(126)
      ..write(obj.qrCode)
      ..writeByte(127)
      ..write(obj.qrCodeImage)
      ..writeByte(128)
      ..write(obj.invoiceUrl)
      ..writeByte(129)
      ..write(obj.shippingCompanyId);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SellAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class PaymentLineAdapter extends TypeAdapter<PaymentLine> {
  @override
  final int typeId = 28;

  @override
  PaymentLine read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return PaymentLine(
      id: fields[0] as int?,
      transactionId: fields[1] as int?,
      businessId: fields[2] as int?,
      isReturn: fields[3] as int?,
      amount: fields[4] as String?,
      method: fields[5] as String?,
      cardTransactionNumber: fields[6] as String?,
      cardNumber: fields[7] as String?,
      cardType: fields[8] as String?,
      cardHolderName: fields[9] as String?,
      cardMonth: fields[10] as String?,
      cardYear: fields[11] as String?,
      cardSecurity: fields[12] as String?,
      chequeNumber: fields[13] as String?,
      bankAccountNumber: fields[14] as String?,
      paidOn: fields[15] as String?,
      createdBy: fields[16] as int?,
      paidThroughLink: fields[17] as int?,
      gateway: fields[18] as String?,
      isAdvance: fields[19] as int?,
      paymentFor: fields[20] as int?,
      parentId: fields[21] as int?,
      note: fields[22] as String?,
      document: fields[23] as String?,
      paymentRefNo: fields[24] as String?,
      accountId: fields[25] as int?,
      createdAt: fields[26] as String?,
      updatedAt: fields[27] as String?,
      contactType: fields[28] as String?,
      prepaid: fields[29] as String?,
      amountSecondCurr: fields[30] as String?,
      payoutStatus: fields[31] as String?,
      installmentId: fields[32] as int?,
      paymentSubType: fields[33] as String?,
      contactName: fields[34] as String?,
      userId: fields[37] as int?,
      firstName: fields[35] as String?,
      customerGroup: fields[38] as String?,
      lastName: fields[36] as String?,
      offline: fields[39] as bool,
      refNo: fields[40] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, PaymentLine obj) {
    writer
      ..writeByte(41)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.transactionId)
      ..writeByte(2)
      ..write(obj.businessId)
      ..writeByte(3)
      ..write(obj.isReturn)
      ..writeByte(4)
      ..write(obj.amount)
      ..writeByte(5)
      ..write(obj.method)
      ..writeByte(6)
      ..write(obj.cardTransactionNumber)
      ..writeByte(7)
      ..write(obj.cardNumber)
      ..writeByte(8)
      ..write(obj.cardType)
      ..writeByte(9)
      ..write(obj.cardHolderName)
      ..writeByte(10)
      ..write(obj.cardMonth)
      ..writeByte(11)
      ..write(obj.cardYear)
      ..writeByte(12)
      ..write(obj.cardSecurity)
      ..writeByte(13)
      ..write(obj.chequeNumber)
      ..writeByte(14)
      ..write(obj.bankAccountNumber)
      ..writeByte(15)
      ..write(obj.paidOn)
      ..writeByte(16)
      ..write(obj.createdBy)
      ..writeByte(17)
      ..write(obj.paidThroughLink)
      ..writeByte(18)
      ..write(obj.gateway)
      ..writeByte(19)
      ..write(obj.isAdvance)
      ..writeByte(20)
      ..write(obj.paymentFor)
      ..writeByte(21)
      ..write(obj.parentId)
      ..writeByte(22)
      ..write(obj.note)
      ..writeByte(23)
      ..write(obj.document)
      ..writeByte(24)
      ..write(obj.paymentRefNo)
      ..writeByte(25)
      ..write(obj.accountId)
      ..writeByte(26)
      ..write(obj.createdAt)
      ..writeByte(27)
      ..write(obj.updatedAt)
      ..writeByte(28)
      ..write(obj.contactType)
      ..writeByte(29)
      ..write(obj.prepaid)
      ..writeByte(30)
      ..write(obj.amountSecondCurr)
      ..writeByte(31)
      ..write(obj.payoutStatus)
      ..writeByte(32)
      ..write(obj.installmentId)
      ..writeByte(33)
      ..write(obj.paymentSubType)
      ..writeByte(34)
      ..write(obj.contactName)
      ..writeByte(35)
      ..write(obj.firstName)
      ..writeByte(36)
      ..write(obj.lastName)
      ..writeByte(37)
      ..write(obj.userId)
      ..writeByte(38)
      ..write(obj.customerGroup)
      ..writeByte(39)
      ..write(obj.offline)
      ..writeByte(40)
      ..write(obj.refNo);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PaymentLineAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class SellLineAdapter extends TypeAdapter<SellLine> {
  @override
  final int typeId = 29;

  @override
  SellLine read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return SellLine(
      id: fields[0] as int?,
      transactionId: fields[1] as int?,
      productId: fields[2] as int?,
      variationId: fields[3] as int?,
      quantity: fields[4] as double?,
      secondaryUnitQuantity: fields[5] as String?,
      mfgWastePercent: fields[6] as String?,
      quantityReturned: fields[7] as String?,
      unitPriceBeforeDiscount: fields[8] as String?,
      unitPrice: fields[9] as String?,
      lineDiscountType: fields[10] as String?,
      lineDiscountAmount: fields[11] as String?,
      unitPriceIncTax: fields[12] as String?,
      itemTax: fields[13] as String?,
      taxId: fields[14] as int?,
      discountId: fields[15] as int?,
      lotNoLineId: fields[16] as int?,
      sellLineNote: fields[17] as String?,
      soLineId: fields[18] as int?,
      soQuantityInvoiced: fields[19] as String?,
      woocommerceLineItemsId: fields[20] as int?,
      resServiceStaffId: fields[21] as int?,
      resLineOrderStatus: fields[22] as String?,
      parentSellLineId: fields[23] as int?,
      childrenType: fields[24] as String?,
      subUnitId: fields[25] as int?,
      createdAt: fields[26] as String?,
      updatedAt: fields[27] as String?,
      packagesCounter: fields[28] as String?,
      unitWeight: fields[29] as String?,
      isBonus: fields[30] as int?,
      bonusQty: fields[31] as int?,
      isReservation: fields[32] as String?,
      reservationStatus: fields[33] as String?,
      x: fields[34] as String?,
      y: fields[35] as String?,
      z: fields[36] as String?,
      num: fields[37] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, SellLine obj) {
    writer
      ..writeByte(38)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.transactionId)
      ..writeByte(2)
      ..write(obj.productId)
      ..writeByte(3)
      ..write(obj.variationId)
      ..writeByte(4)
      ..write(obj.quantity)
      ..writeByte(5)
      ..write(obj.secondaryUnitQuantity)
      ..writeByte(6)
      ..write(obj.mfgWastePercent)
      ..writeByte(7)
      ..write(obj.quantityReturned)
      ..writeByte(8)
      ..write(obj.unitPriceBeforeDiscount)
      ..writeByte(9)
      ..write(obj.unitPrice)
      ..writeByte(10)
      ..write(obj.lineDiscountType)
      ..writeByte(11)
      ..write(obj.lineDiscountAmount)
      ..writeByte(12)
      ..write(obj.unitPriceIncTax)
      ..writeByte(13)
      ..write(obj.itemTax)
      ..writeByte(14)
      ..write(obj.taxId)
      ..writeByte(15)
      ..write(obj.discountId)
      ..writeByte(16)
      ..write(obj.lotNoLineId)
      ..writeByte(17)
      ..write(obj.sellLineNote)
      ..writeByte(18)
      ..write(obj.soLineId)
      ..writeByte(19)
      ..write(obj.soQuantityInvoiced)
      ..writeByte(20)
      ..write(obj.woocommerceLineItemsId)
      ..writeByte(21)
      ..write(obj.resServiceStaffId)
      ..writeByte(22)
      ..write(obj.resLineOrderStatus)
      ..writeByte(23)
      ..write(obj.parentSellLineId)
      ..writeByte(24)
      ..write(obj.childrenType)
      ..writeByte(25)
      ..write(obj.subUnitId)
      ..writeByte(26)
      ..write(obj.createdAt)
      ..writeByte(27)
      ..write(obj.updatedAt)
      ..writeByte(28)
      ..write(obj.packagesCounter)
      ..writeByte(29)
      ..write(obj.unitWeight)
      ..writeByte(30)
      ..write(obj.isBonus)
      ..writeByte(31)
      ..write(obj.bonusQty)
      ..writeByte(32)
      ..write(obj.isReservation)
      ..writeByte(33)
      ..write(obj.reservationStatus)
      ..writeByte(34)
      ..write(obj.x)
      ..writeByte(35)
      ..write(obj.y)
      ..writeByte(36)
      ..write(obj.z)
      ..writeByte(37)
      ..write(obj.num);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SellLineAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Sell _$SellFromJson(Map<String, dynamic> json) => Sell(
      id: (json['id'] as num?)?.toInt(),
      businessId: (json['business_id'] as num?)?.toInt(),
      locationId: (json['location_id'] as num?)?.toInt(),
      resTableId: (json['res_table_id'] as num?)?.toInt(),
      resWaiterId: (json['res_waiter_id'] as num?)?.toInt(),
      resOrderStatus: json['res_order_status'] as String?,
      type: json['type'] as String?,
      subType: json['sub_type'] as String?,
      status: json['status'] as String?,
      subStatus: json['sub_status'] as String?,
      isQuotation: (json['is_quotation'] as num?)?.toInt(),
      paymentStatus: paymentStatusFromJson(json['payment_status'] as String?),
      adjustmentType: json['adjustment_type'] as String?,
      contactId: (json['contact_id'] as num?)?.toInt(),
      customerGroupId: (json['customer_group_id'] as num?)?.toInt(),
      invoiceNo: json['invoice_no'] as String?,
      refNo: json['ref_no'] as String?,
      source: json['source'] as String?,
      subscriptionNo: json['subscription_no'] as String?,
      subscriptionRepeatOn: json['subscription_repeat_on'] as String?,
      transactionDate: DateTime.parse(json['transaction_date'] as String),
      totalBeforeTax: Sell._toString(json['total_before_tax']),
      taxId: (json['tax_id'] as num?)?.toInt(),
      taxAmount: Sell._toString(json['tax_amount']),
      discountType: json['discount_type'] as String? ?? "percentage",
      discountAmount: Sell._toString(json['discount_amount']),
      rpRedeemed: (json['rp_redeemed'] as num?)?.toInt(),
      rpRedeemedAmount: Sell._toString(json['rp_redeemed_amount']),
      shippingDetails: json['shipping_details'] as String?,
      shippingAddress: json['shipping_address'] as String?,
      shippingStatus:
          shipmentStatusFromJson(json['shipping_status'] as String?),
      deliveredTo: json['delivered_to'] as String?,
      shippingCharges: Sell._toString(json['shipping_charges']),
      shippingCustomField1: json['shipping_custom_field_1'] as String?,
      shippingCustomField2: json['shipping_custom_field_2'] as String?,
      shippingCustomField3: json['shipping_custom_field_3'] as String?,
      shippingCustomField4: json['shipping_custom_field_4'] as String?,
      shippingCustomField5: json['shipping_custom_field_5'] as String?,
      additionalNotes: json['additional_notes'] as String?,
      staffNote: json['staff_note'] as String?,
      isExport: (json['is_export'] as num?)?.toInt(),
      exportCustomFieldsInfo: json['export_custom_fields_info'] as String?,
      roundOffAmount: Sell._toString(json['round_off_amount']),
      additionalExpenseKey1: json['additional_expense_key_1'] as String?,
      additionalExpenseValue1: json['additional_expense_value_1'] as String?,
      additionalExpenseKey2: json['additional_expense_key_2'] as String?,
      additionalExpenseValue2: json['additional_expense_value_2'] as String?,
      additionalExpenseKey3: json['additional_expense_key_3'] as String?,
      additionalExpenseValue3: json['additional_expense_value_3'] as String?,
      additionalExpenseKey4: json['additional_expense_key_4'] as String?,
      additionalExpenseValue4: json['additional_expense_value_4'] as String?,
      finalTotal: Sell._toString(json['final_total']),
      expenseCategoryId: (json['expense_category_id'] as num?)?.toInt(),
      expenseFor: json['expense_for'] as String?,
      commissionAgent: (json['commission_agent'] as num?)?.toInt(),
      document: json['document'] as String?,
      isDirectSale: (json['is_direct_sale'] as num?)?.toInt(),
      isSuspend: (json['is_suspend'] as num?)?.toInt(),
      exchangeRate: Sell._toString(json['exchange_rate']),
      totalAmountRecovered: Sell._toString(json['total_amount_recovered']),
      transferParentId: (json['transfer_parent_id'] as num?)?.toInt(),
      returnParentId: (json['return_parent_id'] as num?)?.toInt(),
      openingStockProductId:
          (json['opening_stock_product_id'] as num?)?.toInt(),
      createdBy: (json['created_by'] as num?)?.toInt(),
      crmIsOrderRequest: (json['crm_is_order_request'] as num?)?.toInt(),
      preferPaymentMethod: json['prefer_payment_method'] as String?,
      preferPaymentAccount: json['prefer_payment_account'] as String?,
      salesOrderIds: json['sales_order_ids'] as String?,
      purchaseOrderIds: json['purchase_order_ids'] as String?,
      customField1: json['custom_field_1'] as String?,
      customField2: json['custom_field_2'] as String?,
      customField3: json['custom_field_3'] as String?,
      customField4: json['custom_field_4'] as String?,
      mfgParentProductionPurchaseId:
          (json['mfg_parent_production_purchase_id'] as num?)?.toInt(),
      mfgWastedUnits: json['mfg_wasted_units'] as String?,
      mfgProductionCost: json['mfg_production_cost'] as String?,
      mfgIsFinal: (json['mfg_is_final'] as num?)?.toInt(),
      repairCompletedOn: json['repair_completed_on'] as String?,
      repairWarrantyId: (json['repair_warranty_id'] as num?)?.toInt(),
      repairBrandId: (json['repair_brand_id'] as num?)?.toInt(),
      repairStatusId: (json['repair_status_id'] as num?)?.toInt(),
      repairModelId: (json['repair_model_id'] as num?)?.toInt(),
      repairJobSheetId: (json['repair_job_sheet_id'] as num?)?.toInt(),
      repairDefects: json['repair_defects'] as String?,
      repairSerialNo: json['repair_serial_no'] as String?,
      repairChecklist: json['repair_checklist'] as String?,
      repairSecurityPwd: json['repair_security_pwd'] as String?,
      repairSecurityPattern: json['repair_security_pattern'] as String?,
      repairDueDate: json['repair_due_date'] as String?,
      repairDeviceId: (json['repair_device_id'] as num?)?.toInt(),
      repairUpdatesNotif: (json['repair_updates_notif'] as num?)?.toInt(),
      essentialsDuration: json['essentials_duration'] as String?,
      essentialsDurationUnit: json['essentials_duration_unit'] as String?,
      essentialsAmountPerUnitDuration:
          Sell._toString(json['essentials_amount_per_unit_duration']),
      essentialsAllowances: json['essentials_allowances'] as String?,
      essentialsDeductions: json['essentials_deductions'] as String?,
      woocommerceOrderId: (json['woocommerce_order_id'] as num?)?.toInt(),
      importBatch: json['import_batch'] as String?,
      importTime: json['import_time'] as String?,
      typesOfServiceId: (json['types_of_service_id'] as num?)?.toInt(),
      packingId: (json['packing_id'] as num?)?.toInt(),
      terminalId: (json['terminal_id'] as num?)?.toInt(),
      isComplementary: (json['is_complementary'] as num?)?.toInt(),
      complementaryDetails: json['complementary_details'] as String?,
      voidAmount: Sell._toString(json['void_amount']),
      changeReturned: json['change_returned'] as String?,
      voucherId: (json['voucher_id'] as num?)?.toInt(),
      payingDue: json['paying_due'] as String?,
      customersVoucherNo: json['customers_voucher_no'] as String?,
      customersVoucherId: (json['customers_voucher_id'] as num?)?.toInt(),
      isIntegrated: (json['is_integrated'] as num?)?.toInt(),
      isEuvatInvoice: (json['is_euvat_invoice'] as num?)?.toInt(),
      isAbandoned: (json['is_abandoned'] as num?)?.toInt(),
      sellersInvoiceId: (json['sellers_invoice_id'] as num?)?.toInt(),
      isIgs: (json['is_igs'] as num?)?.toInt(),
      isPos: (json['is_pos'] as num?)?.toInt(),
      isTds: (json['is_tds'] as num?)?.toInt(),
      isDineIn: (json['is_dine_in'] as num?)?.toInt(),
      createdOn: json['created_on'] as String?,
      syncedOn: json['synced_on'] as String?,
      paymentType: json['payment_type'] as String?,
      paymentLines: (json['payment_lines'] as List<dynamic>?)
          ?.map((e) => PaymentLine.fromJson(e as Map<String, dynamic>))
          .toList(),
      sellLines: (json['sell_lines'] as List<dynamic>?)
          ?.map((e) => SellLine.fromJson(e as Map<String, dynamic>))
          .toList(),
      qrCode: json['qr_code'] as String?,
      qrCodeImage: json['qr_code_img'] as String?,
      invoiceUrl: json['invoice_url'] as String?,
      locationInfo: locationInfoFromListJson(json['location_info'] as List?),
      shippingCompanyId: (json['shipping_company_id'] as num?)?.toInt(),
    );

Map<String, dynamic> _$SellToJson(Sell instance) => <String, dynamic>{
      'id': instance.id,
      'business_id': instance.businessId,
      'location_id': instance.locationId,
      'res_table_id': instance.resTableId,
      'res_waiter_id': instance.resWaiterId,
      'res_order_status': instance.resOrderStatus,
      'type': instance.type,
      'sub_type': instance.subType,
      'status': instance.status,
      'sub_status': instance.subStatus,
      'is_quotation': instance.isQuotation,
      'payment_status': _$PaymentStatusEnumMap[instance.paymentStatus]!,
      'adjustment_type': instance.adjustmentType,
      'contact_id': instance.contactId,
      'customer_group_id': instance.customerGroupId,
      'invoice_no': instance.invoiceNo,
      'ref_no': instance.refNo,
      'source': instance.source,
      'subscription_no': instance.subscriptionNo,
      'subscription_repeat_on': instance.subscriptionRepeatOn,
      'transaction_date': dateToString(instance.transactionDate),
      'total_before_tax': instance.totalBeforeTax,
      'tax_id': instance.taxId,
      'tax_amount': instance.taxAmount,
      'discount_type': instance.discountType,
      'discount_amount': instance.discountAmount,
      'rp_redeemed': instance.rpRedeemed,
      'rp_redeemed_amount': instance.rpRedeemedAmount,
      'shipping_details': instance.shippingDetails,
      'shipping_address': instance.shippingAddress,
      'shipping_status': shipmentStatusToJson(instance.shippingStatus),
      'delivered_to': instance.deliveredTo,
      'shipping_charges': instance.shippingCharges,
      'shipping_custom_field_1': instance.shippingCustomField1,
      'shipping_custom_field_2': instance.shippingCustomField2,
      'shipping_custom_field_3': instance.shippingCustomField3,
      'shipping_custom_field_4': instance.shippingCustomField4,
      'shipping_custom_field_5': instance.shippingCustomField5,
      'additional_notes': instance.additionalNotes,
      'staff_note': instance.staffNote,
      'is_export': instance.isExport,
      'export_custom_fields_info': instance.exportCustomFieldsInfo,
      'round_off_amount': instance.roundOffAmount,
      'additional_expense_key_1': instance.additionalExpenseKey1,
      'additional_expense_value_1': instance.additionalExpenseValue1,
      'additional_expense_key_2': instance.additionalExpenseKey2,
      'additional_expense_value_2': instance.additionalExpenseValue2,
      'additional_expense_key_3': instance.additionalExpenseKey3,
      'additional_expense_value_3': instance.additionalExpenseValue3,
      'additional_expense_key_4': instance.additionalExpenseKey4,
      'additional_expense_value_4': instance.additionalExpenseValue4,
      'final_total': instance.finalTotal,
      'expense_category_id': instance.expenseCategoryId,
      'expense_for': instance.expenseFor,
      'commission_agent': instance.commissionAgent,
      'document': instance.document,
      'is_direct_sale': instance.isDirectSale,
      'is_suspend': instance.isSuspend,
      'exchange_rate': instance.exchangeRate,
      'total_amount_recovered': instance.totalAmountRecovered,
      'transfer_parent_id': instance.transferParentId,
      'return_parent_id': instance.returnParentId,
      'opening_stock_product_id': instance.openingStockProductId,
      'created_by': instance.createdBy,
      'crm_is_order_request': instance.crmIsOrderRequest,
      'prefer_payment_method': instance.preferPaymentMethod,
      'prefer_payment_account': instance.preferPaymentAccount,
      'sales_order_ids': instance.salesOrderIds,
      'purchase_order_ids': instance.purchaseOrderIds,
      'custom_field_1': instance.customField1,
      'custom_field_2': instance.customField2,
      'custom_field_3': instance.customField3,
      'custom_field_4': instance.customField4,
      'mfg_parent_production_purchase_id':
          instance.mfgParentProductionPurchaseId,
      'mfg_wasted_units': instance.mfgWastedUnits,
      'mfg_production_cost': instance.mfgProductionCost,
      'mfg_is_final': instance.mfgIsFinal,
      'repair_completed_on': instance.repairCompletedOn,
      'repair_warranty_id': instance.repairWarrantyId,
      'repair_brand_id': instance.repairBrandId,
      'repair_status_id': instance.repairStatusId,
      'repair_model_id': instance.repairModelId,
      'repair_job_sheet_id': instance.repairJobSheetId,
      'repair_defects': instance.repairDefects,
      'repair_serial_no': instance.repairSerialNo,
      'repair_checklist': instance.repairChecklist,
      'repair_security_pwd': instance.repairSecurityPwd,
      'repair_security_pattern': instance.repairSecurityPattern,
      'repair_due_date': instance.repairDueDate,
      'repair_device_id': instance.repairDeviceId,
      'repair_updates_notif': instance.repairUpdatesNotif,
      'essentials_duration': instance.essentialsDuration,
      'essentials_duration_unit': instance.essentialsDurationUnit,
      'essentials_amount_per_unit_duration':
          instance.essentialsAmountPerUnitDuration,
      'essentials_allowances': instance.essentialsAllowances,
      'essentials_deductions': instance.essentialsDeductions,
      'woocommerce_order_id': instance.woocommerceOrderId,
      'import_batch': instance.importBatch,
      'import_time': instance.importTime,
      'types_of_service_id': instance.typesOfServiceId,
      'packing_id': instance.packingId,
      'terminal_id': instance.terminalId,
      'is_complementary': instance.isComplementary,
      'complementary_details': instance.complementaryDetails,
      'void_amount': instance.voidAmount,
      'change_returned': instance.changeReturned,
      'voucher_id': instance.voucherId,
      'paying_due': instance.payingDue,
      'customers_voucher_no': instance.customersVoucherNo,
      'customers_voucher_id': instance.customersVoucherId,
      'is_integrated': instance.isIntegrated,
      'is_euvat_invoice': instance.isEuvatInvoice,
      'is_abandoned': instance.isAbandoned,
      'sellers_invoice_id': instance.sellersInvoiceId,
      'is_igs': instance.isIgs,
      'is_pos': instance.isPos,
      'is_tds': instance.isTds,
      'is_dine_in': instance.isDineIn,
      'created_on': instance.createdOn,
      'synced_on': instance.syncedOn,
      'payment_type': instance.paymentType,
      'payment_lines': instance.paymentLines,
      'sell_lines': instance.sellLines,
      'location_info': instance.locationInfo,
      'qr_code': instance.qrCode,
      'qr_code_img': instance.qrCodeImage,
      'invoice_url': instance.invoiceUrl,
      'shipping_company_id': instance.shippingCompanyId,
    };

const _$PaymentStatusEnumMap = {
  PaymentStatus.paid: 'paid',
  PaymentStatus.due: 'due',
  PaymentStatus.partial: 'partial',
  PaymentStatus.offline: 'offline',
};

PaymentLine _$PaymentLineFromJson(Map<String, dynamic> json) => PaymentLine(
      id: (json['id'] as num?)?.toInt(),
      transactionId: (json['transaction_id'] as num?)?.toInt(),
      businessId: (json['business_id'] as num?)?.toInt(),
      isReturn: (json['is_return'] as num?)?.toInt(),
      amount: json['amount'] as String?,
      method: json['method'] as String?,
      cardTransactionNumber: json['card_transaction_number'] as String?,
      cardNumber: json['card_number'] as String?,
      cardType: json['card_type'] as String?,
      cardHolderName: json['card_holder_name'] as String?,
      cardMonth: json['card_month'] as String?,
      cardYear: json['card_year'] as String?,
      cardSecurity: json['card_security'] as String?,
      chequeNumber: json['cheque_number'] as String?,
      bankAccountNumber: json['bank_account_number'] as String?,
      paidOn: json['paid_on'] as String?,
      createdBy: (json['created_by'] as num?)?.toInt(),
      paidThroughLink: (json['paid_through_link'] as num?)?.toInt(),
      gateway: json['gateway'] as String?,
      isAdvance: (json['is_advance'] as num?)?.toInt(),
      paymentFor: (json['payment_for'] as num?)?.toInt(),
      parentId: (json['parent_id'] as num?)?.toInt(),
      note: json['note'] as String?,
      document: json['document'] as String?,
      paymentRefNo: json['payment_ref_no'] as String?,
      accountId: (json['account_id'] as num?)?.toInt(),
      createdAt: json['created_at'] as String?,
      updatedAt: json['updated_at'] as String?,
      contactType: json['contact_type'] as String?,
      prepaid: json['prepaid'] as String?,
      amountSecondCurr: json['amount_second_curr'] as String?,
      payoutStatus: json['payout_status'] as String?,
      installmentId: (json['installment_id'] as num?)?.toInt(),
      paymentSubType: json['payment_sub_type'] as String?,
      contactName: json['contact_name'] as String?,
      userId: (json['user_id'] as num?)?.toInt(),
      firstName: json['first_name'] as String?,
      customerGroup: json['customer_group'] as String?,
      lastName: json['last_name'] as String?,
    );

Map<String, dynamic> _$PaymentLineToJson(PaymentLine instance) =>
    <String, dynamic>{
      'id': instance.id,
      'transaction_id': instance.transactionId,
      'business_id': instance.businessId,
      'is_return': instance.isReturn,
      'amount': instance.amount,
      'method': instance.method,
      'card_transaction_number': instance.cardTransactionNumber,
      'card_number': instance.cardNumber,
      'card_type': instance.cardType,
      'card_holder_name': instance.cardHolderName,
      'card_month': instance.cardMonth,
      'card_year': instance.cardYear,
      'card_security': instance.cardSecurity,
      'cheque_number': instance.chequeNumber,
      'bank_account_number': instance.bankAccountNumber,
      'paid_on': instance.paidOn,
      'created_by': instance.createdBy,
      'paid_through_link': instance.paidThroughLink,
      'gateway': instance.gateway,
      'is_advance': instance.isAdvance,
      'payment_for': instance.paymentFor,
      'parent_id': instance.parentId,
      'note': instance.note,
      'document': instance.document,
      'payment_ref_no': instance.paymentRefNo,
      'account_id': instance.accountId,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
      'contact_type': instance.contactType,
      'prepaid': instance.prepaid,
      'amount_second_curr': instance.amountSecondCurr,
      'payout_status': instance.payoutStatus,
      'installment_id': instance.installmentId,
      'payment_sub_type': instance.paymentSubType,
      'contact_name': instance.contactName,
      'first_name': instance.firstName,
      'last_name': instance.lastName,
      'user_id': instance.userId,
      'customer_group': instance.customerGroup,
    };

SellLine _$SellLineFromJson(Map<String, dynamic> json) => SellLine(
      id: (json['id'] as num?)?.toInt(),
      transactionId: (json['transaction_id'] as num?)?.toInt(),
      productId: (json['product_id'] as num?)?.toInt(),
      variationId: (json['variation_id'] as num?)?.toInt(),
      quantity: (json['quantity'] as num?)?.toDouble(),
      secondaryUnitQuantity: json['secondary_unit_quantity'] as String?,
      mfgWastePercent: json['mfg_waste_percent'] as String?,
      quantityReturned: json['quantity_returned'] as String?,
      unitPriceBeforeDiscount: json['unit_price_before_discount'] as String?,
      unitPrice: json['unit_price'] as String?,
      lineDiscountType: json['line_discount_type'] as String? ?? "percentage",
      lineDiscountAmount: json['line_discount_amount'] as String?,
      unitPriceIncTax: json['unit_price_inc_tax'] as String?,
      itemTax: json['item_tax'] as String?,
      taxId: (json['tax_id'] as num?)?.toInt(),
      discountId: (json['discount_id'] as num?)?.toInt(),
      lotNoLineId: (json['lot_no_line_id'] as num?)?.toInt(),
      sellLineNote: json['sell_line_note'] as String?,
      soLineId: (json['so_line_id'] as num?)?.toInt(),
      soQuantityInvoiced: json['so_quantity_invoiced'] as String?,
      woocommerceLineItemsId:
          (json['woocommerce_line_items_id'] as num?)?.toInt(),
      resServiceStaffId: (json['res_service_staff_id'] as num?)?.toInt(),
      resLineOrderStatus: json['res_line_order_status'] as String?,
      parentSellLineId: (json['parent_sell_line_id'] as num?)?.toInt(),
      childrenType: json['children_type'] as String?,
      subUnitId: (json['sub_unit_id'] as num?)?.toInt(),
      createdAt: json['created_at'] as String?,
      updatedAt: json['updated_at'] as String?,
      packagesCounter: json['packages_counter'] as String?,
      unitWeight: json['unit_weight'] as String?,
      isBonus: (json['is_bonus'] as num?)?.toInt(),
      bonusQty: (json['bonus_qty'] as num?)?.toInt(),
      isReservation: json['is_reservation'] as String?,
      reservationStatus: json['reservation_status'] as String?,
      x: json['x'] as String?,
      y: json['y'] as String?,
      z: json['z'] as String?,
      num: json['num'] as String?,
    );

Map<String, dynamic> _$SellLineToJson(SellLine instance) => <String, dynamic>{
      'id': instance.id,
      'transaction_id': instance.transactionId,
      'product_id': instance.productId,
      'variation_id': instance.variationId,
      'quantity': instance.quantity,
      'secondary_unit_quantity': instance.secondaryUnitQuantity,
      'mfg_waste_percent': instance.mfgWastePercent,
      'quantity_returned': instance.quantityReturned,
      'unit_price_before_discount': instance.unitPriceBeforeDiscount,
      'unit_price': instance.unitPrice,
      'line_discount_type': instance.lineDiscountType,
      'line_discount_amount': instance.lineDiscountAmount,
      'unit_price_inc_tax': instance.unitPriceIncTax,
      'item_tax': instance.itemTax,
      'tax_id': instance.taxId,
      'discount_id': instance.discountId,
      'lot_no_line_id': instance.lotNoLineId,
      'sell_line_note': instance.sellLineNote,
      'so_line_id': instance.soLineId,
      'so_quantity_invoiced': instance.soQuantityInvoiced,
      'woocommerce_line_items_id': instance.woocommerceLineItemsId,
      'res_service_staff_id': instance.resServiceStaffId,
      'res_line_order_status': instance.resLineOrderStatus,
      'parent_sell_line_id': instance.parentSellLineId,
      'children_type': instance.childrenType,
      'sub_unit_id': instance.subUnitId,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
      'packages_counter': instance.packagesCounter,
      'unit_weight': instance.unitWeight,
      'is_bonus': instance.isBonus,
      'bonus_qty': instance.bonusQty,
      'is_reservation': instance.isReservation,
      'reservation_status': instance.reservationStatus,
      'x': instance.x,
      'y': instance.y,
      'z': instance.z,
      'num': instance.num,
    };
