// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'location_info.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class LocationInfoAdapter extends TypeAdapter<LocationInfo> {
  @override
  final int typeId = 46;

  @override
  LocationInfo read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return LocationInfo(
      id: fields[0] as int?,
      logName: fields[1] as String?,
      description: fields[2] as String?,
      subjectId: fields[3] as int?,
      subjectType: fields[4] as String?,
      event: fields[5] as dynamic,
      businessId: fields[6] as int?,
      causerId: fields[7] as int?,
      causerType: fields[8] as String?,
      properties: fields[9] as dynamic,
      longitude: fields[10] as double,
      latitude: fields[11] as double,
      batchUuid: fields[12] as dynamic,
      createdAt: fields[13] as String?,
      updatedAt: fields[14] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, LocationInfo obj) {
    writer
      ..writeByte(15)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.logName)
      ..writeByte(2)
      ..write(obj.description)
      ..writeByte(3)
      ..write(obj.subjectId)
      ..writeByte(4)
      ..write(obj.subjectType)
      ..writeByte(5)
      ..write(obj.event)
      ..writeByte(6)
      ..write(obj.businessId)
      ..writeByte(7)
      ..write(obj.causerId)
      ..writeByte(8)
      ..write(obj.causerType)
      ..writeByte(9)
      ..write(obj.properties)
      ..writeByte(10)
      ..write(obj.longitude)
      ..writeByte(11)
      ..write(obj.latitude)
      ..writeByte(12)
      ..write(obj.batchUuid)
      ..writeByte(13)
      ..write(obj.createdAt)
      ..writeByte(14)
      ..write(obj.updatedAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LocationInfoAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LocationInfo _$LocationInfoFromJson(Map<String, dynamic> json) => LocationInfo(
      id: (json['id'] as num?)?.toInt(),
      logName: json['log_name'] as String?,
      description: json['description'] as String?,
      subjectId: (json['subject_id'] as num?)?.toInt(),
      subjectType: json['subject_type'] as String?,
      event: json['event'],
      businessId: (json['business_id'] as num?)?.toInt(),
      causerId: (json['causer_id'] as num?)?.toInt(),
      causerType: json['causer_type'] as String?,
      properties: json['properties'],
      longitude: double.parse(json['longitude'] as String),
      latitude: double.parse(json['latitude'] as String),
      batchUuid: json['batch_uuid'],
      createdAt: json['created_at'] as String?,
      updatedAt: json['updated_at'] as String?,
    );

Map<String, dynamic> _$LocationInfoToJson(LocationInfo instance) =>
    <String, dynamic>{
      'id': instance.id,
      'log_name': instance.logName,
      'description': instance.description,
      'subject_id': instance.subjectId,
      'subject_type': instance.subjectType,
      'event': instance.event,
      'business_id': instance.businessId,
      'causer_id': instance.causerId,
      'causer_type': instance.causerType,
      'properties': instance.properties,
      'longitude': instance.longitude,
      'latitude': instance.latitude,
      'batch_uuid': instance.batchUuid,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
    };
