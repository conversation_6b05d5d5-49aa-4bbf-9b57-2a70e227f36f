import 'package:equatable/equatable.dart';
import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:we2up/data/models/follow_up_to_api.dart';
import 'package:we2up/data/models/user_model.dart';

part 'follow_up.g.dart';

@HiveType(typeId: 37)
@JsonSerializable(explicitToJson: true)
class FollowUp extends Equatable {
  @HiveField(0)
  @<PERSON>son<PERSON><PERSON>(name: 'id')
  final int? id;

  @HiveField(1)
  @<PERSON>son<PERSON>ey(name: 'business_id')
  final int? businessId;

  @HiveField(2)
  @<PERSON><PERSON><PERSON><PERSON>(name: 'contact_id')
  final int? contactId;

  @HiveField(3)
  @<PERSON>son<PERSON><PERSON>(name: 'title')
  final String? title;

  @HiveField(4)
  @Json<PERSON><PERSON>(name: 'status')
  final String? status;

  @HiveField(5)
  @JsonKey(name: 'start_datetime')
  final String? startDatetime;

  @HiveField(6)
  @<PERSON>son<PERSON>ey(name: 'end_datetime')
  final String? endDatetime;

  @HiveField(7)
  @Json<PERSON><PERSON>(name: 'description')
  final String? description;

  @HiveField(8)
  @Json<PERSON><PERSON>(name: 'schedule_type')
  final String? scheduleType;

  @HiveField(9)
  @JsonKey(name: 'allow_notification')
  final int? allowNotification;

  @HiveField(10)
  @JsonKey(name: 'notify_via')
  final Map<String, int>? notifyVia;

  @HiveField(11)
  @JsonKey(name: 'notify_before')
  final int? notifyBefore;

  @HiveField(12)
  @JsonKey(name: 'notify_type')
  final String? notifyType;

  @HiveField(13)
  @JsonKey(name: 'created_by')
  final int? createdBy;

  @HiveField(14)
  @JsonKey(name: 'followup_additional_info')
  final String? followupAdditionalInfo;

  @HiveField(15)
  @JsonKey(name: 'created_at')
  final String? createdAt;

  @HiveField(16)
  @JsonKey(name: 'updated_at')
  final String? updatedAt;

  @HiveField(17)
  @JsonKey(fromJson: _customerFromJson, toJson: _customerToJson)
  final Customer? customer;

  @HiveField(18)
  @JsonKey(includeFromJson: false, includeToJson: false)
  final bool offline;

  @HiveField(19)
  @JsonKey(includeFromJson: false, includeToJson: false)
  final FollowUpToAPI? followUpToAPI;

  @HiveField(20)
  @JsonKey(includeFromJson: false, includeToJson: false)
  final String? refNo;

  @HiveField(21)
  @JsonKey(name: 'users', fromJson: _usersFromJson)
  final List<UserModel>? users;

  const FollowUp({
    this.id,
    this.businessId,
    required this.contactId,
    required this.title,
    required this.status,
    required this.startDatetime,
    required this.endDatetime,
    this.description,
    required this.scheduleType,
    this.allowNotification,
    required this.notifyVia,
    required this.notifyBefore,
    required this.notifyType,
    this.createdBy,
    this.followupAdditionalInfo,
    this.createdAt,
    this.updatedAt,
    this.customer,
    this.offline = false,
    this.followUpToAPI,
    this.refNo,
    required this.users,
  });

  static Map<String, dynamic>? _customerToJson(Customer? customer) {
    return customer?.toJson();
  }

  static Customer? _customerFromJson(Map<String, dynamic>? customer) {
    return customer != null ? Customer.fromJson(customer) : null;
  }

  static List<UserModel>? _usersFromJson(List<dynamic>? usersMap) {
    if (usersMap != null) {
      return usersMap
          .map((e) => UserModel.fromJson(e))
          .toList();
    }
    return null;
  }

  factory FollowUp.fromJson(Map<String, dynamic> json) =>
      _$FollowUpFromJson(json);

  Map<String, dynamic> toJson() => _$FollowUpToJson(this);

  @override
  List<Object?> get props => [
        id,
        businessId,
        contactId,
        title,
        status,
        startDatetime,
        endDatetime,
        description,
        scheduleType,
        allowNotification,
        notifyVia,
        notifyBefore,
        notifyType,
        createdBy,
        followupAdditionalInfo,
        createdAt,
        updatedAt,
        customer,
      ];
}

@HiveType(typeId: 38)
@JsonSerializable(explicitToJson: true)
class Customer extends Equatable {
  @HiveField(0)
  @JsonKey(name: 'id')
  final int id;

  @HiveField(1)
  @JsonKey(name: 'business_id')
  final int businessId;

  @HiveField(2)
  @JsonKey(name: 'type')
  final String type;

  @HiveField(3)
  @JsonKey(name: 'supplier_business_name')
  final String? supplierBusinessName;

  @HiveField(4)
  @JsonKey(name: 'name')
  final String? name;

  @HiveField(5)
  @JsonKey(name: 'prefix')
  final String? prefix;

  @HiveField(6)
  @JsonKey(name: 'first_name')
  final String? firstName;

  @HiveField(7)
  @JsonKey(name: 'middle_name')
  final String? middleName;

  @HiveField(8)
  @JsonKey(name: 'last_name')
  final String? lastName;

  @HiveField(9)
  @JsonKey(name: 'email')
  final String? email;

  @HiveField(10)
  @JsonKey(name: 'contact_id')
  final String? contactId;

  @HiveField(11)
  @JsonKey(name: 'contact_status')
  final String? contactStatus;

  @HiveField(12)
  @JsonKey(name: 'tax_number')
  final String? taxNumber;

  @HiveField(13)
  @JsonKey(name: 'city')
  final String? city;

  @HiveField(14)
  @JsonKey(name: 'state')
  final String? state;

  @HiveField(15)
  @JsonKey(name: 'country')
  final String? country;

  @HiveField(16)
  @JsonKey(name: 'address_line_1')
  final String? addressLine1;

  @HiveField(17)
  @JsonKey(name: 'address_line_2')
  final String? addressLine2;

  @HiveField(18)
  @JsonKey(name: 'zip_code')
  final String? zipCode;

  @HiveField(19)
  @JsonKey(name: 'dob')
  final String? dob;

  @HiveField(20)
  @JsonKey(name: 'mobile')
  final String? mobile;

  @HiveField(21)
  @JsonKey(name: 'landline')
  final String? landline;

  @HiveField(22)
  @JsonKey(name: 'alternate_number')
  final String? alternateNumber;

  @HiveField(23)
  @JsonKey(name: 'pay_term_number')
  final String? payTermNumber;

  @HiveField(24)
  @JsonKey(name: 'pay_term_type')
  final String? payTermType;

  @HiveField(25)
  @JsonKey(name: 'credit_limit')
  final String? creditLimit;

  @HiveField(26)
  @JsonKey(name: 'created_by')
  final int createdBy;

  @HiveField(27)
  @JsonKey(name: 'balance')
  final String? balance;

  @HiveField(28)
  @JsonKey(name: 'total_rp')
  final int? totalRp;

  @HiveField(29)
  @JsonKey(name: 'total_rp_used')
  final int? totalRpUsed;

  @HiveField(30)
  @JsonKey(name: 'total_rp_expired')
  final int? totalRpExpired;

  @HiveField(31)
  @JsonKey(name: 'is_default')
  final int? isDefault;

  @HiveField(32)
  @JsonKey(name: 'shipping_address')
  final String? shippingAddress;

  @HiveField(33)
  @JsonKey(name: 'position')
  final String? position;

  @HiveField(34)
  @JsonKey(name: 'customer_group_id')
  final int? customerGroupId;

  @HiveField(35)
  @JsonKey(name: 'crm_source')
  final String? crmSource;

  @HiveField(36)
  @JsonKey(name: 'crm_life_stage')
  final String? crmLifeStage;

  @HiveField(37)
  @JsonKey(name: 'custom_field1')
  final String? customField1;

  @HiveField(38)
  @JsonKey(name: 'custom_field2')
  final String? customField2;

  @HiveField(39)
  @JsonKey(name: 'custom_field3')
  final String? customField3;

  @HiveField(40)
  @JsonKey(name: 'custom_field4')
  final String? customField4;

  @HiveField(41)
  @JsonKey(name: 'custom_field5')
  final String? customField5;

  @HiveField(42)
  @JsonKey(name: 'custom_field6')
  final String? customField6;

  @HiveField(43)
  @JsonKey(name: 'custom_field7')
  final String? customField7;

  @HiveField(44)
  @JsonKey(name: 'custom_field8')
  final String? customField8;

  @HiveField(45)
  @JsonKey(name: 'custom_field9')
  final String? customField9;

  @HiveField(46)
  @JsonKey(name: 'custom_field10')
  final String? customField10;

  const Customer({
    required this.id,
    required this.businessId,
    required this.type,
    this.supplierBusinessName,
    required this.name,
    this.prefix,
    this.firstName,
    this.middleName,
    this.lastName,
    this.email,
    this.contactId,
    this.contactStatus,
    this.taxNumber,
    this.city,
    this.state,
    this.country,
    this.addressLine1,
    this.addressLine2,
    this.zipCode,
    this.dob,
    this.mobile,
    this.landline,
    this.alternateNumber,
    this.payTermNumber,
    this.payTermType,
    this.creditLimit,
    required this.createdBy,
    this.balance,
    this.totalRp,
    this.totalRpUsed,
    this.totalRpExpired,
    this.isDefault,
    this.shippingAddress,
    this.position,
    this.customerGroupId,
    this.crmSource,
    this.crmLifeStage,
    this.customField1,
    this.customField2,
    this.customField3,
    this.customField4,
    this.customField5,
    this.customField6,
    this.customField7,
    this.customField8,
    this.customField9,
    this.customField10,
  });

  factory Customer.fromJson(Map<String, dynamic> json) =>
      _$CustomerFromJson(json);

  Map<String, dynamic> toJson() => _$CustomerToJson(this);

  @override
  List<Object?> get props => [
        id,
        businessId,
        type,
        supplierBusinessName,
        name,
        prefix,
        firstName,
        middleName,
        lastName,
        email,
        contactId,
        contactStatus,
        taxNumber,
        city,
        state,
        country,
        addressLine1,
        addressLine2,
        zipCode,
        dob,
        mobile,
        landline,
        alternateNumber,
        payTermNumber,
        payTermType,
        creditLimit,
        createdBy,
        balance,
        totalRp,
        totalRpUsed,
        totalRpExpired,
        isDefault,
        shippingAddress,
        position,
        customerGroupId,
        crmSource,
        crmLifeStage,
        customField1,
        customField2,
        customField3,
        customField4,
        customField5,
        customField6,
        customField7,
        customField8,
        customField9,
        customField10,
      ];
}
