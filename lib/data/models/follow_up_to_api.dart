import 'package:equatable/equatable.dart';
import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:we2up/data/models/location_info.dart';

import '../../utils/we2up_constants.dart';

part 'follow_up_to_api.g.dart';

@HiveType(typeId: 36)
@JsonSerializable(explicitToJson: true)
class FollowUpToAPI extends Equatable {
  @HiveField(0)
  @<PERSON>son<PERSON>ey(name: 'title')
  final String title;

  @HiveField(1)
  @<PERSON>son<PERSON>ey(name: 'contact_id')
  final int contactId;

  @HiveField(2)
  @<PERSON>son<PERSON>ey(name: 'description')
  final String description;

  @HiveField(3)
  @<PERSON>son<PERSON><PERSON>(name: 'schedule_type')
  final String scheduleType;

  @HiveField(4)
  @JsonKey(name: 'user_id')
  final List<int> userId;

  @HiveField(5)
  @<PERSON>sonKey(name: 'notify_before')
  final int notifyBefore;

  @HiveField(6)
  @<PERSON>son<PERSON>ey(name: 'notify_type')
  final String notifyType;

  @HiveField(7)
  @Json<PERSON>ey(name: 'status')
  final String status;

  @HiveField(8)
  @Json<PERSON>ey(name: 'notify_via')
  final Map<String, int> notifyVia;

  @HiveField(9)
  @JsonKey(name: 'start_datetime')
  final String? startDatetime;

  @HiveField(10)
  @JsonKey(name: 'end_datetime')
  final String? endDatetime;

  @HiveField(11)
  @JsonKey(name: 'followup_additional_info', toJson: _customToJson)
  final Map<String, int>? followupAdditionalInfo;

  @HiveField(12)
  @JsonKey(name: 'allow_notification')
  final bool allowNotification;

  @HiveField(13)
  @JsonKey(includeFromJson: false, includeToJson: false)
  final String? offlineID;

  @HiveField(14)
  @JsonKey(name: 'location_info', toJson: locationInfoToJson)
  final LocationInfo? locationInfo;

  const FollowUpToAPI({
    required this.title,
    required this.contactId,
    required this.description,
    required this.scheduleType,
    required this.userId,
    required this.notifyBefore,
    required this.notifyType,
    required this.status,
    required this.notifyVia,
    this.startDatetime,
    this.endDatetime,
    this.followupAdditionalInfo,
    required this.allowNotification,
    this.offlineID,
    this.locationInfo,
  });

  static String? _customToJson(Map<String, int>? map) {
    if (map == null) {
      return null;
    }

    final parts = map.entries.map((entry) {
      final key = entry.key;
      final value = entry.value;
      return "'$key' => $value";
    });

    return "[${parts.join(', ')}]";
  }

  factory FollowUpToAPI.fromJson(Map<String, dynamic> json) =>
      _$FollowUpToAPIFromJson(json);

  Map<String, dynamic> toJson() => _$FollowUpToAPIToJson(this);

  @override
  List<Object?> get props => [
        title,
        contactId,
        description,
        scheduleType,
        userId,
        notifyBefore,
        notifyType,
        status,
        notifyVia,
        startDatetime,
        endDatetime,
        followupAdditionalInfo,
        allowNotification,
      ];
}

enum Status { scheduled, open, cancelled, completed }

enum FollowUpType { call, sms, meeting, email }

enum NotifyType { minute, hour, day }

enum NotifyVia { sms, mail }
