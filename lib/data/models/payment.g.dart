// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'payment.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class PaymentAdapter extends TypeAdapter<Payment> {
  @override
  final int typeId = 21;

  @override
  Payment read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Payment(
      amount: fields[0] as double?,
      method: fields[1] as String?,
      accountId: fields[2] as int?,
      cardNumber: fields[3] as String?,
      cardHolderName: fields[4] as String?,
      cardTransactionNumber: fields[5] as String?,
      cardType: fields[6] as String?,
      cardMonth: fields[7] as String?,
      cardYear: fields[8] as String?,
      cardSecurity: fields[9] as String?,
      transactionNo1: fields[10] as String?,
      transactionNo2: fields[11] as String?,
      transactionNo3: fields[12] as String?,
      note: fields[13] as String?,
      chequeNumber: fields[14] as String?,
      paymentFor: fields[15] as int?,
      offline: fields[16] as bool,
      transactionDate: fields[17] as DateTime?,
      refNo: fields[18] as String?,
      paymentId: fields[19] as int?,
    );
  }

  @override
  void write(BinaryWriter writer, Payment obj) {
    writer
      ..writeByte(20)
      ..writeByte(0)
      ..write(obj.amount)
      ..writeByte(1)
      ..write(obj.method)
      ..writeByte(2)
      ..write(obj.accountId)
      ..writeByte(3)
      ..write(obj.cardNumber)
      ..writeByte(4)
      ..write(obj.cardHolderName)
      ..writeByte(5)
      ..write(obj.cardTransactionNumber)
      ..writeByte(6)
      ..write(obj.cardType)
      ..writeByte(7)
      ..write(obj.cardMonth)
      ..writeByte(8)
      ..write(obj.cardYear)
      ..writeByte(9)
      ..write(obj.cardSecurity)
      ..writeByte(10)
      ..write(obj.transactionNo1)
      ..writeByte(11)
      ..write(obj.transactionNo2)
      ..writeByte(12)
      ..write(obj.transactionNo3)
      ..writeByte(13)
      ..write(obj.note)
      ..writeByte(14)
      ..write(obj.chequeNumber)
      ..writeByte(15)
      ..write(obj.paymentFor)
      ..writeByte(16)
      ..write(obj.offline)
      ..writeByte(17)
      ..write(obj.transactionDate)
      ..writeByte(18)
      ..write(obj.refNo)
      ..writeByte(19)
      ..write(obj.paymentId);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PaymentAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Payment _$PaymentFromJson(Map<String, dynamic> json) => Payment(
      amount: Payment._amountFromJson(json['amount']),
      method: json['method'] as String?,
      accountId: (json['account_id'] as num?)?.toInt(),
      cardNumber: json['card_number'] as String?,
      cardHolderName: json['card_holder_name'] as String?,
      cardTransactionNumber: json['card_transaction_number'] as String?,
      cardType: json['card_type'] as String?,
      cardMonth: json['card_month'] as String?,
      cardYear: json['card_year'] as String?,
      cardSecurity: json['card_security'] as String?,
      transactionNo1: json['transaction_no_1'] as String?,
      transactionNo2: json['transaction_no_2'] as String?,
      transactionNo3: json['transaction_no_3'] as String?,
      note: json['note'] as String?,
      chequeNumber: json['cheque_number'] as String?,
      paymentId: (json['payment_id'] as num?)?.toInt(),
    );

Map<String, dynamic> _$PaymentToJson(Payment instance) => <String, dynamic>{
      'amount': instance.amount,
      'method': instance.method,
      'account_id': instance.accountId,
      'card_number': instance.cardNumber,
      'card_holder_name': instance.cardHolderName,
      'card_transaction_number': instance.cardTransactionNumber,
      'card_type': instance.cardType,
      'card_month': instance.cardMonth,
      'card_year': instance.cardYear,
      'card_security': instance.cardSecurity,
      'transaction_no_1': instance.transactionNo1,
      'transaction_no_2': instance.transactionNo2,
      'transaction_no_3': instance.transactionNo3,
      'note': instance.note,
      'cheque_number': instance.chequeNumber,
      'payment_id': instance.paymentId,
    };
