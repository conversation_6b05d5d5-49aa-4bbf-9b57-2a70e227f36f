// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'business_location.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class BusinessLocationAdapter extends TypeAdapter<BusinessLocation> {
  @override
  final int typeId = 4;

  @override
  BusinessLocation read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return BusinessLocation(
      id: fields[0] as int,
      businessId: fields[1] as int,
      locationId: fields[2] as String?,
      name: fields[3] as String,
      landmark: fields[4] as String?,
      country: fields[5] as String,
      state: fields[6] as String,
      city: fields[7] as String,
      zipCode: fields[8] as String,
      invoiceSchemeId: fields[9] as int,
      invoiceLayoutId: fields[10] as int,
      saleInvoiceLayoutId: fields[11] as int,
      sellingPriceGroupId: fields[12] as int?,
      printReceiptOnInvoice: fields[13] as int,
      receiptPrinterType: fields[14] as String,
      printerId: fields[15] as int?,
      mobile: fields[16] as String?,
      alternateNumber: fields[17] as String?,
      email: fields[18] as String?,
      website: fields[19] as String?,
      featuredProducts: (fields[20] as List?)?.cast<dynamic>(),
      isActive: fields[21] as int,
      customField1: fields[22] as String?,
      customField2: fields[23] as String?,
      customField3: fields[24] as String?,
      customField4: fields[25] as String?,
      deletedAt: fields[26] as DateTime?,
      createdAt: fields[27] as DateTime,
      updatedAt: fields[28] as DateTime,
      purchaseAccountId: fields[29] as String?,
      showQtyInfo: fields[30] as int?,
      activityCode: fields[31] as String?,
      paymentMethods: (fields[32] as List).cast<PaymentMethod>(),
      locationInfo: fields[33] as LocationInfo?,
    );
  }

  @override
  void write(BinaryWriter writer, BusinessLocation obj) {
    writer
      ..writeByte(34)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.businessId)
      ..writeByte(2)
      ..write(obj.locationId)
      ..writeByte(3)
      ..write(obj.name)
      ..writeByte(4)
      ..write(obj.landmark)
      ..writeByte(5)
      ..write(obj.country)
      ..writeByte(6)
      ..write(obj.state)
      ..writeByte(7)
      ..write(obj.city)
      ..writeByte(8)
      ..write(obj.zipCode)
      ..writeByte(9)
      ..write(obj.invoiceSchemeId)
      ..writeByte(10)
      ..write(obj.invoiceLayoutId)
      ..writeByte(11)
      ..write(obj.saleInvoiceLayoutId)
      ..writeByte(12)
      ..write(obj.sellingPriceGroupId)
      ..writeByte(13)
      ..write(obj.printReceiptOnInvoice)
      ..writeByte(14)
      ..write(obj.receiptPrinterType)
      ..writeByte(15)
      ..write(obj.printerId)
      ..writeByte(16)
      ..write(obj.mobile)
      ..writeByte(17)
      ..write(obj.alternateNumber)
      ..writeByte(18)
      ..write(obj.email)
      ..writeByte(19)
      ..write(obj.website)
      ..writeByte(20)
      ..write(obj.featuredProducts)
      ..writeByte(21)
      ..write(obj.isActive)
      ..writeByte(22)
      ..write(obj.customField1)
      ..writeByte(23)
      ..write(obj.customField2)
      ..writeByte(24)
      ..write(obj.customField3)
      ..writeByte(25)
      ..write(obj.customField4)
      ..writeByte(26)
      ..write(obj.deletedAt)
      ..writeByte(27)
      ..write(obj.createdAt)
      ..writeByte(28)
      ..write(obj.updatedAt)
      ..writeByte(29)
      ..write(obj.purchaseAccountId)
      ..writeByte(30)
      ..write(obj.showQtyInfo)
      ..writeByte(31)
      ..write(obj.activityCode)
      ..writeByte(32)
      ..write(obj.paymentMethods)
      ..writeByte(33)
      ..write(obj.locationInfo);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is BusinessLocationAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class PaymentMethodAdapter extends TypeAdapter<PaymentMethod> {
  @override
  final int typeId = 5;

  @override
  PaymentMethod read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return PaymentMethod(
      name: fields[0] as String?,
      label: fields[1] as String?,
      accountId: fields[2] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, PaymentMethod obj) {
    writer
      ..writeByte(3)
      ..writeByte(0)
      ..write(obj.name)
      ..writeByte(1)
      ..write(obj.label)
      ..writeByte(2)
      ..write(obj.accountId);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PaymentMethodAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BusinessLocation _$BusinessLocationFromJson(Map<String, dynamic> json) =>
    BusinessLocation(
      id: (json['id'] as num).toInt(),
      businessId: (json['business_id'] as num).toInt(),
      locationId: json['location_id'] as String?,
      name: json['name'] as String,
      landmark: json['landmark'] as String?,
      country: json['country'] as String,
      state: json['state'] as String,
      city: json['city'] as String,
      zipCode: json['zip_code'] as String,
      invoiceSchemeId: (json['invoice_scheme_id'] as num).toInt(),
      invoiceLayoutId: (json['invoice_layout_id'] as num).toInt(),
      saleInvoiceLayoutId: (json['sale_invoice_layout_id'] as num).toInt(),
      sellingPriceGroupId: (json['selling_price_group_id'] as num?)?.toInt(),
      printReceiptOnInvoice: (json['print_receipt_on_invoice'] as num).toInt(),
      receiptPrinterType: json['receipt_printer_type'] as String,
      printerId: (json['printer_id'] as num?)?.toInt(),
      mobile: json['mobile'] as String?,
      alternateNumber: json['alternate_number'] as String?,
      email: json['email'] as String?,
      website: json['website'] as String?,
      featuredProducts: json['featured_products'] as List<dynamic>?,
      isActive: (json['is_active'] as num).toInt(),
      customField1: json['custom_field1'] as String?,
      customField2: json['custom_field2'] as String?,
      customField3: json['custom_field3'] as String?,
      customField4: json['custom_field4'] as String?,
      deletedAt: json['deleted_at'] == null
          ? null
          : DateTime.parse(json['deleted_at'] as String),
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      purchaseAccountId: json['purchase_account_id'] as String?,
      showQtyInfo: (json['show_qty_info'] as num?)?.toInt(),
      activityCode: json['activity_code'] as String?,
      paymentMethods: (json['payment_methods'] as List<dynamic>)
          .map((e) => PaymentMethod.fromJson(e as Map<String, dynamic>))
          .toList(),
      locationInfo: locationInfoFromListJson(json['location_info'] as List?),
    );

Map<String, dynamic> _$BusinessLocationToJson(BusinessLocation instance) =>
    <String, dynamic>{
      'id': instance.id,
      'business_id': instance.businessId,
      'location_id': instance.locationId,
      'name': instance.name,
      'landmark': instance.landmark,
      'country': instance.country,
      'state': instance.state,
      'city': instance.city,
      'zip_code': instance.zipCode,
      'invoice_scheme_id': instance.invoiceSchemeId,
      'invoice_layout_id': instance.invoiceLayoutId,
      'sale_invoice_layout_id': instance.saleInvoiceLayoutId,
      'selling_price_group_id': instance.sellingPriceGroupId,
      'print_receipt_on_invoice': instance.printReceiptOnInvoice,
      'receipt_printer_type': instance.receiptPrinterType,
      'printer_id': instance.printerId,
      'mobile': instance.mobile,
      'alternate_number': instance.alternateNumber,
      'email': instance.email,
      'website': instance.website,
      'featured_products': instance.featuredProducts,
      'is_active': instance.isActive,
      'custom_field1': instance.customField1,
      'custom_field2': instance.customField2,
      'custom_field3': instance.customField3,
      'custom_field4': instance.customField4,
      'deleted_at': instance.deletedAt?.toIso8601String(),
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
      'purchase_account_id': instance.purchaseAccountId,
      'show_qty_info': instance.showQtyInfo,
      'activity_code': instance.activityCode,
      'payment_methods': instance.paymentMethods,
      'location_info': instance.locationInfo,
    };

PaymentMethod _$PaymentMethodFromJson(Map<String, dynamic> json) =>
    PaymentMethod(
      name: json['name'] as String?,
      label: json['label'] as String?,
      accountId: json['account_id'] as String?,
    );

Map<String, dynamic> _$PaymentMethodToJson(PaymentMethod instance) =>
    <String, dynamic>{
      'name': instance.name,
      'label': instance.label,
      'account_id': instance.accountId,
    };
