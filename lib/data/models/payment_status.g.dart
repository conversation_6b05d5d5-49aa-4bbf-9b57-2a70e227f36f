// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'payment_status.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class PaymentStatusAdapter extends TypeAdapter<PaymentStatus> {
  @override
  final int typeId = 51;

  @override
  PaymentStatus read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return PaymentStatus.paid;
      case 1:
        return PaymentStatus.due;
      case 2:
        return PaymentStatus.partial;
      case 3:
        return PaymentStatus.offline;
      default:
        return PaymentStatus.paid;
    }
  }

  @override
  void write(BinaryWriter writer, PaymentStatus obj) {
    switch (obj) {
      case PaymentStatus.paid:
        writer.writeByte(0);
        break;
      case PaymentStatus.due:
        writer.writeByte(1);
        break;
      case PaymentStatus.partial:
        writer.writeByte(2);
        break;
      case PaymentStatus.offline:
        writer.writeByte(3);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PaymentStatusAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
