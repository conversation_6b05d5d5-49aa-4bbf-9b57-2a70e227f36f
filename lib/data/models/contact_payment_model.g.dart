// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'contact_payment_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class ContactPaymentModelAdapter extends TypeAdapter<ContactPaymentModel> {
  @override
  final int typeId = 56;

  @override
  ContactPaymentModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ContactPaymentModel(
      id: fields[0] as int?,
      refNo: fields[1] as String?,
      createdAt: fields[2] as DateTime,
      amount: fields[3] as double?,
      method: fields[4] as String?,
      contactName: fields[5] as String?,
      contactId: fields[6] as int,
      customerGroup: fields[7] as dynamic,
      firstName: fields[8] as String?,
      lastName: fields[9] as String?,
      payment: fields[12] as Payment?,
      userId: fields[10] as int,
      offline: fields[11] as bool,
      transactionType: fields[13] as String?,
      locationInfo: fields[14] as LocationInfo?,
    );
  }

  @override
  void write(BinaryWriter writer, ContactPaymentModel obj) {
    writer
      ..writeByte(15)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.refNo)
      ..writeByte(2)
      ..write(obj.createdAt)
      ..writeByte(3)
      ..write(obj.amount)
      ..writeByte(4)
      ..write(obj.method)
      ..writeByte(5)
      ..write(obj.contactName)
      ..writeByte(6)
      ..write(obj.contactId)
      ..writeByte(7)
      ..write(obj.customerGroup)
      ..writeByte(8)
      ..write(obj.firstName)
      ..writeByte(9)
      ..write(obj.lastName)
      ..writeByte(10)
      ..write(obj.userId)
      ..writeByte(11)
      ..write(obj.offline)
      ..writeByte(12)
      ..write(obj.payment)
      ..writeByte(13)
      ..write(obj.transactionType)
      ..writeByte(14)
      ..write(obj.locationInfo);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ContactPaymentModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ContactPaymentModel _$ContactPaymentModelFromJson(Map<String, dynamic> json) =>
    ContactPaymentModel(
      id: (json['id'] as num?)?.toInt(),
      refNo: json['payment_ref_no'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      amount: double.parse(json['amount'] as String),
      method: json['method'] as String?,
      contactName: json['contact_name'] as String?,
      contactId: (json['contact_id'] as num).toInt(),
      customerGroup: json['customer_group'],
      firstName: json['first_name'] as String?,
      lastName: json['last_name'] as String?,
      userId: (json['user_id'] as num).toInt(),
      transactionType: json['transaction_type'] as String?,
      locationInfo: locationInfoFromListJson(json['location_info'] as List?),
    );
