import 'package:equatable/equatable.dart';
import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:we2up/data/models/expense_category.dart';
import 'package:we2up/data/models/payment.dart';

import '../../utils/we2up_constants.dart';
import 'expense_to_api.dart';
import 'location_info.dart';

part 'expense.g.dart';

@JsonSerializable()
@HiveType(typeId: 18)
class Expense extends Equatable {
  @HiveField(0)
  @JsonKey(name: 'id')
  final int? id;

  @HiveField(1)
  @JsonKey(name: 'business_id')
  final int? businessId;

  @HiveField(2)
  @Json<PERSON>ey(name: 'location_id')
  final int? locationId;

  @HiveField(3)
  @JsonKey(name: 'payment_status')
  final String? paymentStatus;

  @HiveField(4)
  @JsonKey(name: 'ref_no')
  final String? refNo;

  @HiveField(5)
  @<PERSON>son<PERSON><PERSON>(
    name: 'transaction_date',
    fromJson: DateTime.parse,
    toJson: dateToString,
  )
  final DateTime transactionDate;

  @HiveField(6)
  @JsonKey(name: 'total_before_tax')
  final String? totalBeforeTax;

  @HiveField(7)
  @JsonKey(name: 'tax_id')
  final int? taxId;

  @HiveField(8)
  @JsonKey(name: 'tax_amount')
  final String? taxAmount;

  @HiveField(9)
  @JsonKey(name: 'final_total')
  final String? finalTotal;

  @HiveField(10)
  @JsonKey(name: 'expense_category_id')
  final int? expenseCategoryId;

  @HiveField(11)
  @JsonKey(name: 'document')
  final String? document;

  @HiveField(12)
  @JsonKey(name: 'created_by')
  final int? createdBy;

  @HiveField(13)
  @JsonKey(name: 'created_at')
  final DateTime? createdAt;

  @HiveField(14)
  @JsonKey(name: 'updated_at')
  final DateTime? updatedAt;

  @HiveField(15)
  @JsonKey(name: 'expense_for', fromJson: _expenseForFromJson)
  final Map<String, dynamic>? expenseFor;

  @HiveField(16)
  @JsonKey(name: 'is_recurring')
  final int? isRecurring;

  @HiveField(17)
  @JsonKey(name: 'recur_interval')
  final int? recurInterval;

  @HiveField(18)
  @JsonKey(name: 'recur_interval_type')
  final String? recurIntervalType;

  @HiveField(19)
  @JsonKey(name: 'recur_repetitions')
  final int? recurRepetitions;

  @HiveField(20)
  @JsonKey(name: 'recur_stopped_on')
  final DateTime? recurStoppedOn;

  @HiveField(21)
  @JsonKey(name: 'recur_parent_id')
  final int? recurParentId;

  @HiveField(22)
  final int isRefund;

  @HiveField(23)
  @JsonKey(name: 'additional_notes')
  final String? additionalNotes;

  @HiveField(24)
  @JsonKey(name: 'payments', toJson: paymentListToJson)
  final List<Payment>? payments;

  @HiveField(25)
  @JsonKey(includeFromJson: false, includeToJson: false)
  final bool offline;

  @HiveField(26)
  @JsonKey(includeFromJson: false, includeToJson: false)
  final ExpenseToAPI? expenseToAPI;

  @HiveField(27)
  @JsonKey(name: 'contact_id')
  final int? contactId;

  @HiveField(28)
  @JsonKey(name: 'location_info', fromJson: locationInfoFromListJson)
  final LocationInfo? locationInfo;

  @HiveField(29)
  @JsonKey(name: 'category', fromJson: _categoryForFromJson)
  final ExpenseCategory? category;

  const Expense({
    this.id,
    this.businessId,
    this.locationId,
    this.paymentStatus,
    this.refNo,
    required this.transactionDate,
    this.totalBeforeTax,
    this.taxId,
    this.taxAmount,
    this.finalTotal,
    this.expenseCategoryId,
    this.document,
    this.createdBy,
    this.createdAt,
    this.updatedAt,
    this.expenseFor,
    this.isRecurring,
    this.recurInterval,
    this.recurIntervalType,
    this.recurRepetitions,
    this.recurStoppedOn,
    this.recurParentId,
    required this.isRefund,
    this.additionalNotes,
    required this.payments,
    this.offline = false,
    this.expenseToAPI,
    this.contactId,
    this.locationInfo,
    this.category,
  });

  static Map<String, dynamic> _expenseForFromJson(dynamic json) {
    if (json is List) return {};
    return json as Map<String, dynamic>;
  }

  static ExpenseCategory? _categoryForFromJson(dynamic category) {
    if(category is List){
      return null;
    }
    return ExpenseCategory.fromJson(category);
  }



  static List<Map<String, dynamic>>? paymentListToJson(
      List<Payment>? payments) {
    return payments?.map((payment) => payment.toJson()).toList();
  }

  factory Expense.fromJson(Map<String, dynamic> json) =>
      _$ExpenseFromJson(json);

  Map<String, dynamic> toJson() => _$ExpenseToJson(this);

  @override
  List<Object?> get props => [
        id,
        businessId,
        locationId,
        paymentStatus,
        refNo,
        transactionDate,
        totalBeforeTax,
        taxId,
        taxAmount,
        finalTotal,
        expenseCategoryId,
        document,
        createdBy,
        createdAt,
        updatedAt,
        expenseFor,
        isRecurring,
        recurInterval,
        recurIntervalType,
        recurRepetitions,
        recurStoppedOn,
        recurParentId,
        isRefund,
        additionalNotes,
        payments,
        offline,
        expenseToAPI,
      ];
}
