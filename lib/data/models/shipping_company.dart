import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:hive/hive.dart';

part 'shipping_company.g.dart';

@HiveType(typeId: 62)
@JsonSerializable()
class ShippingCompany extends Equatable {
  @HiveField(0)
  @Json<PERSON>ey(name: 'id')
  final int id;

  @HiveField(1)
  @<PERSON>son<PERSON>ey(name: 'name')
  final String? name;

  @HiveField(2)
  @<PERSON>son<PERSON><PERSON>(name: 'mobile')
  final String? mobile;

  @HiveField(3)
  @<PERSON>son<PERSON><PERSON>(name: 'created_at')
  final String? createdAt;

  @HiveField(4)
  @<PERSON><PERSON><PERSON><PERSON>(name: 'updated_at')
  final String? updatedAt;

  @HiveField(5)
  @<PERSON>son<PERSON><PERSON>(name: 'business_id')
  final String? businessId;

  const ShippingCompany({
    required this.id,
    this.name,
    this.mobile,
    this.createdAt,
    this.updatedAt,
    this.businessId,
  });

  factory ShippingCompany.fromJson(Map<String, dynamic> json) =>
      _$ShippingCompanyFromJson(json);

  Map<String, dynamic> toJson() => _$ShippingCompanyToJson(this);

  @override
  List<Object?> get props => [
        id,
        name,
        mobile,
        createdAt,
        updatedAt,
        businessId,
      ];
}
