// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'sell_return_to_api.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class SellReturnToAPIAdapter extends TypeAdapter<SellReturnToAPI> {
  @override
  final int typeId = 30;

  @override
  SellReturnToAPI read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return SellReturnToAPI(
      transactionId: fields[0] as int,
      transactionDate: fields[1] as String,
      invoiceNo: fields[2] as String?,
      discountAmount: fields[3] as double?,
      discountType: fields[4] as String?,
      products: (fields[5] as List).cast<ReturnProductToAPI>(),
      locationInfo: fields[6] as LocationInfo?,
    );
  }

  @override
  void write(BinaryWriter writer, SellReturnToAPI obj) {
    writer
      ..writeByte(7)
      ..writeByte(0)
      ..write(obj.transactionId)
      ..writeByte(1)
      ..write(obj.transactionDate)
      ..writeByte(2)
      ..write(obj.invoiceNo)
      ..writeByte(3)
      ..write(obj.discountAmount)
      ..writeByte(4)
      ..write(obj.discountType)
      ..writeByte(5)
      ..write(obj.products)
      ..writeByte(6)
      ..write(obj.locationInfo);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SellReturnToAPIAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ReturnProductToAPIAdapter extends TypeAdapter<ReturnProductToAPI> {
  @override
  final int typeId = 31;

  @override
  ReturnProductToAPI read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ReturnProductToAPI(
      sellLineId: fields[0] as int,
      quantity: fields[1] as double?,
      unitPriceIncTax: fields[2] as double,
    );
  }

  @override
  void write(BinaryWriter writer, ReturnProductToAPI obj) {
    writer
      ..writeByte(3)
      ..writeByte(0)
      ..write(obj.sellLineId)
      ..writeByte(1)
      ..write(obj.quantity)
      ..writeByte(2)
      ..write(obj.unitPriceIncTax);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ReturnProductToAPIAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SellReturnToAPI _$SellReturnToAPIFromJson(Map<String, dynamic> json) =>
    SellReturnToAPI(
      transactionId: (json['transaction_id'] as num).toInt(),
      transactionDate: json['transaction_date'] as String,
      invoiceNo: json['invoice_no'] as String?,
      discountAmount: (json['discount_amount'] as num?)?.toDouble(),
      discountType: json['discount_type'] as String?,
      products: (json['products'] as List<dynamic>)
          .map((e) => ReturnProductToAPI.fromJson(e as Map<String, dynamic>))
          .toList(),
      locationInfo: json['location_info'] == null
          ? null
          : LocationInfo.fromJson(
              json['location_info'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$SellReturnToAPIToJson(SellReturnToAPI instance) =>
    <String, dynamic>{
      'transaction_id': instance.transactionId,
      'transaction_date': instance.transactionDate,
      'invoice_no': instance.invoiceNo,
      'discount_amount': instance.discountAmount,
      'discount_type': instance.discountType,
      'products': instance.products,
      'location_info': locationInfoToJson(instance.locationInfo),
    };

ReturnProductToAPI _$ReturnProductToAPIFromJson(Map<String, dynamic> json) =>
    ReturnProductToAPI(
      sellLineId: (json['sell_line_id'] as num).toInt(),
      quantity: (json['quantity'] as num?)?.toDouble(),
      unitPriceIncTax: (json['unit_price_inc_tax'] as num).toDouble(),
    );

Map<String, dynamic> _$ReturnProductToAPIToJson(ReturnProductToAPI instance) =>
    <String, dynamic>{
      'sell_line_id': instance.sellLineId,
      'quantity': instance.quantity,
      'unit_price_inc_tax': instance.unitPriceIncTax,
    };
