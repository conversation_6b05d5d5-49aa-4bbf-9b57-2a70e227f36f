import 'package:hive/hive.dart';
import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:we2up/data/models/purchase.dart';
import 'package:we2up/data/models/purchase_return_to_api.dart';
import 'package:we2up/data/models/sell.dart';

import '../../utils/we2up_constants.dart';
import 'location_info.dart';

part 'purchase_return.g.dart';

@HiveType(typeId: 41)
@JsonSerializable()
class PurchaseReturn extends Equatable {
  @HiveField(0)
  @JsonKey(name: 'id')
  final int? id;

  @HiveField(1)
  @JsonKey(name: 'business_id')
  final int? businessId;

  @HiveField(2)
  @JsonKey(name: 'location_id')
  final int? locationId;

  @HiveField(3)
  @Json<PERSON>ey(name: 'res_table_id')
  final int? resTableId;

  @HiveField(4)
  @Json<PERSON>ey(name: 'res_waiter_id')
  final int? resWaiterId;

  @HiveField(5)
  @JsonKey(name: 'res_order_status')
  final int? resOrderStatus;

  @HiveField(6)
  @JsonKey(name: 'type')
  final String? type;

  @HiveField(7)
  @JsonKey(name: 'sub_type')
  final String? subType;

  @HiveField(8)
  @JsonKey(name: 'status')
  final String? status;

  @HiveField(9)
  @JsonKey(name: 'sub_status')
  final String? subStatus;

  @HiveField(10)
  @JsonKey(name: 'is_quotation')
  final int? isQuotation;

  @HiveField(11)
  @JsonKey(name: 'payment_status')
  final String? paymentStatus;

  @HiveField(12)
  @JsonKey(name: 'adjustment_type')
  final String? adjustmentType;

  @HiveField(13)
  @JsonKey(name: 'contact_id')
  final int? contactId;

  @HiveField(14)
  @JsonKey(name: 'customer_group_id')
  final int? customerGroupId;

  @HiveField(15)
  @JsonKey(name: 'invoice_no')
  final String? invoiceNo;

  @HiveField(16)
  @JsonKey(name: 'ref_no')
  final String? refNo;

  @HiveField(17)
  @JsonKey(name: 'source')
  final String? source;

  @HiveField(18)
  @JsonKey(name: 'subscription_no')
  final String? subscriptionNo;

  @HiveField(19)
  @JsonKey(name: 'subscription_repeat_on')
  final String? subscriptionRepeatOn;

  @HiveField(20)
  @JsonKey(
    name: 'transaction_date',
    fromJson: DateTime.parse,
    toJson: dateToString,
  )
  final DateTime transactionDate;

  @HiveField(21)
  @JsonKey(name: 'total_before_tax')
  final String? totalBeforeTax;

  @HiveField(22)
  @JsonKey(name: 'tax_id')
  final int? taxId;

  @HiveField(23)
  @JsonKey(name: 'tax_amount')
  final String? taxAmount;

  @HiveField(24)
  @JsonKey(name: 'discount_type')
  final String? discountType;

  @HiveField(25)
  @JsonKey(name: 'discount_amount')
  final String? discountAmount;

  @HiveField(26)
  @JsonKey(name: 'rp_redeemed')
  final int? rpRedeemed;

  @HiveField(27)
  @JsonKey(name: 'rp_redeemed_amount')
  final String? rpRedeemedAmount;

  @HiveField(28)
  @JsonKey(name: 'shipping_details')
  final String? shippingDetails;

  @HiveField(29)
  @JsonKey(name: 'shipping_address')
  final String? shippingAddress;

  @HiveField(30)
  @JsonKey(name: 'delivery_date')
  final String? deliveryDate;

  @HiveField(31)
  @JsonKey(name: 'shipping_status')
  final String? shippingStatus;

  @HiveField(32)
  @JsonKey(name: 'delivered_to')
  final String? deliveredTo;

  @HiveField(33)
  @JsonKey(name: 'shipping_charges')
  final String? shippingCharges;

  @HiveField(34)
  @JsonKey(name: 'shipping_custom_field_1')
  final String? shippingCustomField1;

  @HiveField(35)
  @JsonKey(name: 'shipping_custom_field_2')
  final String? shippingCustomField2;

  @HiveField(36)
  @JsonKey(name: 'shipping_custom_field_3')
  final String? shippingCustomField3;

  @HiveField(37)
  @JsonKey(name: 'shipping_custom_field_4')
  final String? shippingCustomField4;

  @HiveField(38)
  @JsonKey(name: 'shipping_custom_field_5')
  final String? shippingCustomField5;

  @HiveField(39)
  @JsonKey(name: 'additional_notes')
  final String? additionalNotes;

  @HiveField(40)
  @JsonKey(name: 'staff_note')
  final String? staffNote;

  @HiveField(41)
  @JsonKey(name: 'is_export')
  final int? isExport;

  @HiveField(42)
  @JsonKey(name: 'export_custom_fields_info')
  final String? exportCustomFieldsInfo;

  @HiveField(43)
  @JsonKey(name: 'round_off_amount')
  final String? roundOffAmount;

  @HiveField(44)
  @JsonKey(name: 'additional_expense_key_1')
  final String? additionalExpenseKey1;

  @HiveField(45)
  @JsonKey(name: 'additional_expense_value_1')
  final String? additionalExpenseValue1;

  @HiveField(46)
  @JsonKey(name: 'additional_expense_key_2')
  final String? additionalExpenseKey2;

  @HiveField(47)
  @JsonKey(name: 'additional_expense_value_2')
  final String? additionalExpenseValue2;

  @HiveField(48)
  @JsonKey(name: 'additional_expense_key_3')
  final String? additionalExpenseKey3;

  @HiveField(49)
  @JsonKey(name: 'additional_expense_value_3')
  final String? additionalExpenseValue3;

  @HiveField(50)
  @JsonKey(name: 'additional_expense_key_4')
  final String? additionalExpenseKey4;

  @HiveField(51)
  @JsonKey(name: 'additional_expense_value_4')
  final String? additionalExpenseValue4;

  @HiveField(52)
  @JsonKey(name: 'final_total')
  final String? finalTotal;

  @HiveField(53)
  @JsonKey(name: 'expense_category_id')
  final int? expenseCategoryId;

  @HiveField(54)
  @JsonKey(name: 'expense_for')
  final String? expenseFor;

  @HiveField(55)
  @JsonKey(name: 'commission_agent')
  final String? commissionAgent;

  @HiveField(56)
  @JsonKey(name: 'document')
  final String? document;

  @HiveField(57)
  @JsonKey(name: 'is_direct_sale')
  final int? isDirectSale;

  @HiveField(58)
  @JsonKey(name: 'is_suspend')
  final int? isSuspend;

  @HiveField(59)
  @JsonKey(name: 'exchange_rate')
  final String? exchangeRate;

  @HiveField(60)
  @JsonKey(name: 'total_amount_recovered')
  final String? totalAmountRecovered;

  @HiveField(61)
  @JsonKey(name: 'transfer_parent_id')
  final int? transferParentId;

  @HiveField(62)
  @JsonKey(name: 'return_parent_id')
  final int? returnParentId;

  @HiveField(63)
  @JsonKey(name: 'opening_stock_product_id')
  final int? openingStockProductId;

  @HiveField(64)
  @JsonKey(name: 'created_by')
  final int? createdBy;

  @HiveField(65)
  @JsonKey(name: 'purchase_requisition_ids')
  final List<int>? purchaseRequisitionIds;

  @HiveField(66)
  @JsonKey(name: 'crm_is_order_request')
  final int? crmIsOrderRequest;

  @HiveField(67)
  @JsonKey(name: 'prefer_payment_method')
  final String? preferPaymentMethod;

  @HiveField(68)
  @JsonKey(name: 'prefer_payment_account')
  final String? preferPaymentAccount;

  @HiveField(69)
  @JsonKey(name: 'sales_order_ids')
  final List<int>? salesOrderIds;

  @HiveField(70)
  @JsonKey(name: 'purchase_order_ids')
  final List<int>? purchaseOrderIds;

  @HiveField(71)
  @JsonKey(name: 'custom_field_1')
  final String? customField1;

  @HiveField(72)
  @JsonKey(name: 'custom_field_2')
  final String? customField2;

  @HiveField(73)
  @JsonKey(name: 'custom_field_3')
  final String? customField3;

  @HiveField(74)
  @JsonKey(name: 'custom_field_4')
  final String? customField4;

  @HiveField(75)
  @JsonKey(name: 'mfg_parent_production_purchase_id')
  final int? mfgParentProductionPurchaseId;

  @HiveField(76)
  @JsonKey(name: 'mfg_wasted_units')
  final int? mfgWastedUnits;

  @HiveField(77)
  @JsonKey(name: 'mfg_production_cost')
  final String? mfgProductionCost;

  @HiveField(78)
  @JsonKey(name: 'mfg_is_final')
  final int? mfgIsFinal;

  @HiveField(79)
  @JsonKey(name: 'repair_completed_on')
  final String? repairCompletedOn;

  @HiveField(80)
  @JsonKey(name: 'repair_warranty_id')
  final int? repairWarrantyId;

  @HiveField(81)
  @JsonKey(name: 'repair_brand_id')
  final int? repairBrandId;

  @HiveField(82)
  @JsonKey(name: 'repair_status_id')
  final int? repairStatusId;

  @HiveField(83)
  @JsonKey(name: 'repair_model_id')
  final int? repairModelId;

  @HiveField(84)
  @JsonKey(name: 'repair_job_sheet_id')
  final int? repairJobSheetId;

  @HiveField(85)
  @JsonKey(name: 'repair_defects')
  final String? repairDefects;

  @HiveField(86)
  @JsonKey(name: 'repair_serial_no')
  final String? repairSerialNo;

  @HiveField(87)
  @JsonKey(name: 'repair_checklist')
  final String? repairChecklist;

  @HiveField(88)
  @JsonKey(name: 'repair_security_pwd')
  final String? repairSecurityPwd;

  @HiveField(89)
  @JsonKey(name: 'repair_security_pattern')
  final String? repairSecurityPattern;

  @HiveField(90)
  @JsonKey(name: 'repair_due_date')
  final String? repairDueDate;

  @HiveField(91)
  @JsonKey(name: 'repair_device_id')
  final int? repairDeviceId;

  @HiveField(92)
  @JsonKey(name: 'repair_updates_notif')
  final int? repairUpdatesNotif;

  @HiveField(93)
  @JsonKey(name: 'essentials_duration')
  final String? essentialsDuration;

  @HiveField(94)
  @JsonKey(name: 'essentials_duration_unit')
  final String? essentialsDurationUnit;

  @HiveField(95)
  @JsonKey(name: 'essentials_amount_per_unit_duration')
  final String? essentialsAmountPerUnitDuration;

  @HiveField(96)
  @JsonKey(name: 'essentials_allowances')
  final String? essentialsAllowances;

  @HiveField(97)
  @JsonKey(name: 'essentials_deductions')
  final String? essentialsDeductions;

  @HiveField(98)
  @JsonKey(name: 'woocommerce_order_id')
  final int? woocommerceOrderId;

  @HiveField(99)
  @JsonKey(name: 'import_batch')
  final String? importBatch;

  @HiveField(100)
  @JsonKey(name: 'import_time')
  final String? importTime;

  @HiveField(101)
  @JsonKey(name: 'types_of_service_id')
  final int? typesOfServiceId;

  @HiveField(102)
  @JsonKey(name: 'packing_charge')
  final String? packingCharge;

  @HiveField(103)
  @JsonKey(name: 'packing_charge_type')
  final String? packingChargeType;

  @HiveField(104)
  @JsonKey(name: 'service_custom_field_1')
  final String? serviceCustomField1;

  @HiveField(105)
  @JsonKey(name: 'service_custom_field_2')
  final String? serviceCustomField2;

  @HiveField(106)
  @JsonKey(name: 'service_custom_field_3')
  final String? serviceCustomField3;

  @HiveField(107)
  @JsonKey(name: 'service_custom_field_4')
  final String? serviceCustomField4;

  @HiveField(108)
  @JsonKey(name: 'service_custom_field_5')
  final String? serviceCustomField5;

  @HiveField(109)
  @JsonKey(name: 'service_custom_field_6')
  final String? serviceCustomField6;

  @HiveField(110)
  @JsonKey(name: 'is_created_from_api')
  final int? isCreatedFromApi;

  @HiveField(111)
  @JsonKey(name: 'rp_earned')
  final int? rpEarned;

  @HiveField(112)
  @JsonKey(name: 'order_addresses')
  final String? orderAddresses;

  @HiveField(113)
  @JsonKey(name: 'is_recurring')
  final int? isRecurring;

  @HiveField(114)
  @JsonKey(name: 'recur_interval')
  final int? recurInterval;

  @HiveField(115)
  @JsonKey(name: 'recur_interval_type')
  final String? recurIntervalType;

  @HiveField(116)
  @JsonKey(name: 'recur_repetitions')
  final int? recurRepetitions;

  @HiveField(117)
  @JsonKey(name: 'recur_stopped_on')
  final String? recurStoppedOn;

  @HiveField(118)
  @JsonKey(name: 'recur_parent_id')
  final int? recurParentId;

  @HiveField(119)
  @JsonKey(name: 'invoice_token')
  final String? invoiceToken;

  @HiveField(120)
  @JsonKey(name: 'pay_term_number')
  final int? payTermNumber;

  @HiveField(121)
  @JsonKey(name: 'pay_term_type')
  final String? payTermType;

  @HiveField(122)
  @JsonKey(name: 'pjt_project_id')
  final int? pjtProjectId;

  @HiveField(123)
  @JsonKey(name: 'pjt_title')
  final String? pjtTitle;

  @HiveField(124)
  @JsonKey(name: 'selling_price_group_id')
  final int? sellingPriceGroupId;

  @HiveField(125)
  @JsonKey(name: 'created_at')
  final String? createdAt;

  @HiveField(126)
  @JsonKey(name: 'updated_at')
  final String? updatedAt;

  @HiveField(127)
  @JsonKey(name: 'end_date')
  final String? endDate;

  @HiveField(128)
  @JsonKey(name: 'shipping_company_id')
  final int? shippingCompanyId;

  @HiveField(129)
  @JsonKey(name: 'package_count')
  final int? packageCount;

  @HiveField(130)
  @JsonKey(name: 'paymob_order_id')
  final int? paymobOrderId;

  @HiveField(131)
  @JsonKey(name: 'prev_balance')
  final String? prevBalance;

  @HiveField(132)
  @JsonKey(name: 'sub_counter')
  final int? subCounter;

  @HiveField(133)
  @JsonKey(name: 'customer_id')
  final int? customerId;

  @HiveField(134)
  @JsonKey(name: 'supplier_id')
  final int? supplierId;

  @HiveField(135)
  @JsonKey(name: 'sell_id')
  final int? sellId;

  @HiveField(136)
  @JsonKey(name: 'purchase_id')
  final int? purchaseId;

  @HiveField(137)
  @JsonKey(name: 'eta_submissionId')
  final int? etaSubmissionId;

  @HiveField(138)
  @JsonKey(name: 'eta_uuid')
  final String? etaUuid;

  @HiveField(139)
  @JsonKey(name: 'eta_longId')
  final int? etaLongId;

  @HiveField(140)
  @JsonKey(name: 'eta_hashKey')
  final String? etaHashKey;

  @HiveField(141)
  @JsonKey(name: 'eta_status')
  final String? etaStatus;

  @HiveField(142)
  @JsonKey(name: 'eta_notes')
  final String? etaNotes;

  @HiveField(143)
  @JsonKey(name: 'invoice_scheme_id')
  final int? invoiceSchemeId;

  @HiveField(144)
  @JsonKey(name: 'custom_status')
  final String? customStatus;

  @HiveField(145)
  @JsonKey(name: 'is_reservation')
  final int? isReservation;

  @HiveField(146)
  @JsonKey(name: 'review_status')
  final String? reviewStatus;

  @HiveField(147)
  @JsonKey(name: 'review_details')
  final String? reviewDetails;

  @HiveField(148)
  @JsonKey(name: 'settlement_purchase_id')
  final int? settlementPurchaseId;

  @HiveField(149)
  @JsonKey(name: 'settlement_sell_id')
  final int? settlementSellId;

  @HiveField(150)
  @JsonKey(name: 'invoice_layout_id')
  final int? invoiceLayoutId;

  @HiveField(151)
  @JsonKey(name: 'invoice_commision')
  final String? invoiceCommision;

  @HiveField(152)
  @JsonKey(name: 'commision_as_leader')
  final String? commisionAsLeader;

  @HiveField(153)
  @JsonKey(name: 'leader_id')
  final int? leaderId;

  @HiveField(154)
  @JsonKey(
    name: 'payment_lines',
    toJson: _paymentLinesToJsonList,
    fromJson: _paymentLinesFromJsonList,
  )
  final List<PaymentLine>? paymentLines;

  @HiveField(155)
  @JsonKey(
    name: 'purchase_lines',
    toJson: _purchaseLinesToJsonList,
    fromJson: _purchaseLinesFromJsonList,
  )
  final List<PurchaseLine>? purchaseLines;

  @HiveField(156)
  @JsonKey(name: 'return_parent_purchase', fromJson: _returnPPFromJsonList)
  final Purchase? returnParentPurchase;

  @HiveField(157)
  @JsonKey(includeToJson: false, includeFromJson: false)
  final bool offline;

  @HiveField(158)
  @JsonKey(includeToJson: false, includeFromJson: false)
  final PurchaseReturnToAPI? purchaseReturnToAPI;

  @HiveField(159)
  @JsonKey(name: 'location_info', fromJson: locationInfoFromListJson)
  final LocationInfo? locationInfo;

  const PurchaseReturn({
    this.id,
    this.offline = false,
    this.purchaseReturnToAPI,
    this.businessId,
    this.locationId,
    this.resTableId,
    this.resWaiterId,
    this.resOrderStatus,
    this.type,
    this.subType,
    this.status,
    this.subStatus,
    this.isQuotation,
    this.paymentStatus,
    this.adjustmentType,
    this.contactId,
    this.customerGroupId,
    this.invoiceNo,
    this.refNo,
    this.source,
    this.subscriptionNo,
    this.subscriptionRepeatOn,
    required this.transactionDate,
    this.totalBeforeTax,
    this.taxId,
    this.taxAmount,
    this.discountType,
    this.discountAmount,
    this.rpRedeemed,
    this.rpRedeemedAmount,
    this.shippingDetails,
    this.shippingAddress,
    this.deliveryDate,
    this.shippingStatus,
    this.deliveredTo,
    this.shippingCharges,
    this.shippingCustomField1,
    this.shippingCustomField2,
    this.shippingCustomField3,
    this.shippingCustomField4,
    this.shippingCustomField5,
    this.additionalNotes,
    this.staffNote,
    this.isExport,
    this.exportCustomFieldsInfo,
    this.roundOffAmount,
    this.additionalExpenseKey1,
    this.additionalExpenseValue1,
    this.additionalExpenseKey2,
    this.additionalExpenseValue2,
    this.additionalExpenseKey3,
    this.additionalExpenseValue3,
    this.additionalExpenseKey4,
    this.additionalExpenseValue4,
    this.finalTotal,
    this.expenseCategoryId,
    this.expenseFor,
    this.commissionAgent,
    this.document,
    this.isDirectSale,
    this.isSuspend,
    this.exchangeRate,
    this.totalAmountRecovered,
    this.transferParentId,
    this.returnParentId,
    this.openingStockProductId,
    this.createdBy,
    this.purchaseRequisitionIds,
    this.crmIsOrderRequest,
    this.preferPaymentMethod,
    this.preferPaymentAccount,
    this.salesOrderIds,
    this.purchaseOrderIds,
    this.customField1,
    this.customField2,
    this.customField3,
    this.customField4,
    this.mfgParentProductionPurchaseId,
    this.mfgWastedUnits,
    this.mfgProductionCost,
    this.mfgIsFinal,
    this.repairCompletedOn,
    this.repairWarrantyId,
    this.repairBrandId,
    this.repairStatusId,
    this.repairModelId,
    this.repairJobSheetId,
    this.repairDefects,
    this.repairSerialNo,
    this.repairChecklist,
    this.repairSecurityPwd,
    this.repairSecurityPattern,
    this.repairDueDate,
    this.repairDeviceId,
    this.repairUpdatesNotif,
    this.essentialsDuration,
    this.essentialsDurationUnit,
    this.essentialsAmountPerUnitDuration,
    this.essentialsAllowances,
    this.essentialsDeductions,
    this.woocommerceOrderId,
    this.importBatch,
    this.importTime,
    this.typesOfServiceId,
    this.packingCharge,
    this.packingChargeType,
    this.serviceCustomField1,
    this.serviceCustomField2,
    this.serviceCustomField3,
    this.serviceCustomField4,
    this.serviceCustomField5,
    this.serviceCustomField6,
    this.isCreatedFromApi,
    this.rpEarned,
    this.orderAddresses,
    this.isRecurring,
    this.recurInterval,
    this.recurIntervalType,
    this.recurRepetitions,
    this.recurStoppedOn,
    this.recurParentId,
    this.invoiceToken,
    this.payTermNumber,
    this.payTermType,
    this.pjtProjectId,
    this.pjtTitle,
    this.sellingPriceGroupId,
    this.createdAt,
    this.updatedAt,
    this.endDate,
    this.shippingCompanyId,
    this.packageCount,
    this.paymobOrderId,
    this.prevBalance,
    this.subCounter,
    this.customerId,
    this.supplierId,
    this.sellId,
    this.purchaseId,
    this.etaSubmissionId,
    this.etaUuid,
    this.etaLongId,
    this.etaHashKey,
    this.etaStatus,
    this.etaNotes,
    this.invoiceSchemeId,
    this.customStatus,
    this.isReservation,
    this.reviewStatus,
    this.reviewDetails,
    this.settlementPurchaseId,
    this.settlementSellId,
    this.invoiceLayoutId,
    this.invoiceCommision,
    this.commisionAsLeader,
    this.leaderId,
    this.paymentLines,
    this.purchaseLines,
    required this.returnParentPurchase,
    this.locationInfo,
  });

  static List<PurchaseLine>? _purchaseLinesFromJsonList(List<dynamic>? list) {
    return list?.map((item) => PurchaseLine.fromJson(item)).toList();
  }

  static List<dynamic>? _purchaseLinesToJsonList(List<PurchaseLine>? list) {
    return list?.map((item) => item.toJson()).toList();
  }

  static Purchase? _returnPPFromJsonList(dynamic item) {
    if (item != null) {
      return Purchase.fromJson(updateProductIdsInPurchaseLines(item));
    }
    return null;
  }

  static List<PaymentLine>? _paymentLinesFromJsonList(List<dynamic>? list) {
    return list?.map((item) => PaymentLine.fromJson(item)).toList();
  }

  static List<dynamic>? _paymentLinesToJsonList(List<PaymentLine>? list) {
    return list?.map((item) => item.toJson()).toList();
  }

  factory PurchaseReturn.fromJson(Map<String, dynamic> json) =>
      _$PurchaseReturnFromJson(json);

  Map<String, dynamic> toJson() => _$PurchaseReturnToJson(this);

  @override
  List<Object?> get props => [
        id,
        businessId,
        locationId,
        resTableId,
        resWaiterId,
        resOrderStatus,
        type,
        subType,
        status,
        subStatus,
        isQuotation,
        paymentStatus,
        adjustmentType,
        contactId,
        customerGroupId,
        invoiceNo,
        refNo,
        source,
        subscriptionNo,
        subscriptionRepeatOn,
        transactionDate,
        totalBeforeTax,
        taxId,
        taxAmount,
        discountType,
        discountAmount,
        rpRedeemed,
        rpRedeemedAmount,
        shippingDetails,
        shippingAddress,
        deliveryDate,
        shippingStatus,
        deliveredTo,
        shippingCharges,
        shippingCustomField1,
        shippingCustomField2,
        shippingCustomField3,
        shippingCustomField4,
        shippingCustomField5,
        additionalNotes,
        staffNote,
        isExport,
        exportCustomFieldsInfo,
        roundOffAmount,
        additionalExpenseKey1,
        additionalExpenseValue1,
        additionalExpenseKey2,
        additionalExpenseValue2,
        additionalExpenseKey3,
        additionalExpenseValue3,
        additionalExpenseKey4,
        additionalExpenseValue4,
        finalTotal,
        expenseCategoryId,
        expenseFor,
        commissionAgent,
        document,
        isDirectSale,
        isSuspend,
        exchangeRate,
        totalAmountRecovered,
        transferParentId,
        returnParentId,
        openingStockProductId,
        createdBy,
        purchaseRequisitionIds,
        crmIsOrderRequest,
        preferPaymentMethod,
        preferPaymentAccount,
        salesOrderIds,
        purchaseOrderIds,
        customField1,
        customField2,
        customField3,
        customField4,
        mfgParentProductionPurchaseId,
        mfgWastedUnits,
        mfgProductionCost,
        mfgIsFinal,
        repairCompletedOn,
        repairWarrantyId,
        repairBrandId,
        repairStatusId,
        repairModelId,
        repairJobSheetId,
        repairDefects,
        repairSerialNo,
        repairChecklist,
        repairSecurityPwd,
        repairSecurityPattern,
        repairDueDate,
        repairDeviceId,
        repairUpdatesNotif,
        essentialsDuration,
        essentialsDurationUnit,
        essentialsAmountPerUnitDuration,
        essentialsAllowances,
        essentialsDeductions,
        woocommerceOrderId,
        importBatch,
        importTime,
        typesOfServiceId,
        packingCharge,
        packingChargeType,
        serviceCustomField1,
        serviceCustomField2,
        serviceCustomField3,
        serviceCustomField4,
        serviceCustomField5,
        serviceCustomField6,
        isCreatedFromApi,
        rpEarned,
        orderAddresses,
        isRecurring,
        recurInterval,
        recurIntervalType,
        recurRepetitions,
        recurStoppedOn,
        recurParentId,
        invoiceToken,
        payTermNumber,
        payTermType,
        pjtProjectId,
        pjtTitle,
        sellingPriceGroupId,
        createdAt,
        updatedAt,
        endDate,
        shippingCompanyId,
        packageCount,
        paymobOrderId,
        prevBalance,
        subCounter,
        customerId,
        supplierId,
        sellId,
        purchaseId,
        etaSubmissionId,
        etaUuid,
        etaLongId,
        etaHashKey,
        etaStatus,
        etaNotes,
        invoiceSchemeId,
        customStatus,
        isReservation,
        reviewStatus,
        reviewDetails,
        settlementPurchaseId,
        settlementSellId,
        invoiceLayoutId,
        invoiceCommision,
        commisionAsLeader,
        leaderId,
        paymentLines,
        purchaseLines,
        returnParentPurchase,
        offline,
        purchaseReturnToAPI,
        locationInfo,
      ];
}
