import 'package:equatable/equatable.dart';
import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:we2up/data/models/location_info.dart';
import 'package:we2up/data/models/payment.dart';

import '../../utils/we2up_constants.dart';

part 'expense_to_api.g.dart';

@JsonSerializable()
@HiveType(typeId: 24)
class ExpenseToAPI extends Equatable {
  @HiveField(0)
  @JsonKey(name: 'location_id')
  final int? locationId;

  @HiveField(1)
  @JsonKey(name: 'final_total')
  final double? finalTotal;

  @HiveField(2)
  @JsonKey(name: 'transaction_date')
  final String? transactionDate;

  @HiveField(3)
  @JsonKey(name: 'tax_id')
  final int? taxRateId;

  @HiveField(4)
  @JsonKey(name: 'expense_for')
  final int? expenseFor;

  @HiveField(5)
  @<PERSON>son<PERSON>ey(name: 'contact_id')
  final int? contactId;

  @HiveField(6)
  @JsonKey(name: 'additional_notes')
  final String? additionalNotes;

  @HiveField(7)
  @Json<PERSON>ey(name: 'is_refund')
  final int isRefund;

  @HiveField(8)
  @JsonKey(name: 'is_recurring')
  final int? isRecurring;

  @HiveField(9)
  @JsonKey(name: 'recur_interval')
  final int? recurInterval;

  @HiveField(10)
  @JsonKey(name: 'recur_interval_type')
  final String? recurIntervalType;

  @HiveField(11)
  @JsonKey(name: 'subscription_repeat_on')
  final int? subscriptionRepeatOn;

  @HiveField(12)
  @JsonKey(name: 'subscription_no')
  final String? subscriptionNo;

  @HiveField(13)
  @JsonKey(name: 'recur_repetitions')
  final int? recurRepetitions;

  @HiveField(14)
  @JsonKey(name: 'payment', toJson: _toJsonList, fromJson: _fromJsonList)
  final List<Payment>? payment;

  @HiveField(15)
  @JsonKey(includeFromJson: false, includeToJson: false)
  final String? offlineID;

  @HiveField(16)
  @JsonKey(name: 'location_info', toJson: locationInfoToJson)
  final LocationInfo? locationInfo;

  @HiveField(17)
  @JsonKey(name: 'expense_category_id')
  final int? expenseCategoryId;

  @HiveField(18)
  @JsonKey(name: 'transaction_no_4')
  final String? transactionNo4;

  const ExpenseToAPI({
    this.locationId,
    this.finalTotal,
    this.transactionDate,
    this.taxRateId,
    this.expenseFor,
    this.contactId,
    this.additionalNotes,
    this.isRefund = 0,
    this.isRecurring,
    this.recurInterval,
    this.recurIntervalType,
    this.subscriptionRepeatOn,
    this.subscriptionNo,
    this.recurRepetitions,
    required this.payment,
    this.offlineID,
    this.locationInfo,
    this.expenseCategoryId,
    this.transactionNo4,
  });

  static List<Map<String, dynamic>> _toJsonList(List<Payment>? list) {
    return list?.map((payment) => payment.toJson()).toList() ?? [];
  }

  static List<Payment>? _fromJsonList(List<dynamic>? json) {
    return json?.map((map) => Payment.fromJson(map)).toList();
  }

  factory ExpenseToAPI.fromJson(Map<String, dynamic> json) =>
      _$ExpenseToAPIFromJson(json);

  Map<String, dynamic> toJson() => _$ExpenseToAPIToJson(this);

  @override
  List<Object?> get props => [
        locationId,
        finalTotal,
        transactionDate,
        taxRateId,
        expenseFor,
        contactId,
        additionalNotes,
        isRefund,
        isRecurring,
        recurInterval,
        recurIntervalType,
        subscriptionRepeatOn,
        subscriptionNo,
        recurRepetitions,
        payment,
        offlineID,
      ];
}
