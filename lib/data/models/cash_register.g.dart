// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'cash_register.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class CashRegisterAdapter extends TypeAdapter<CashRegister> {
  @override
  final int typeId = 47;

  @override
  CashRegister read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return CashRegister(
      id: fields[0] as int,
      businessId: fields[1] as int?,
      locationId: fields[2] as int?,
      userId: fields[3] as int?,
      status: fields[4] as String?,
      closedAt: fields[5] as String?,
      closingAmount: fields[6] as String?,
      totalCardSlips: fields[7] as int?,
      totalCheques: fields[8] as int?,
      denominations: fields[9] as dynamic,
      closingNote: fields[10] as String?,
      createdAt: fields[11] as DateTime,
      updatedAt: fields[12] as DateTime,
      locationInfo: fields[13] as LocationInfo?,
    );
  }

  @override
  void write(BinaryWriter writer, CashRegister obj) {
    writer
      ..writeByte(14)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.businessId)
      ..writeByte(2)
      ..write(obj.locationId)
      ..writeByte(3)
      ..write(obj.userId)
      ..writeByte(4)
      ..write(obj.status)
      ..writeByte(5)
      ..write(obj.closedAt)
      ..writeByte(6)
      ..write(obj.closingAmount)
      ..writeByte(7)
      ..write(obj.totalCardSlips)
      ..writeByte(8)
      ..write(obj.totalCheques)
      ..writeByte(9)
      ..write(obj.denominations)
      ..writeByte(10)
      ..write(obj.closingNote)
      ..writeByte(11)
      ..write(obj.createdAt)
      ..writeByte(12)
      ..write(obj.updatedAt)
      ..writeByte(13)
      ..write(obj.locationInfo);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CashRegisterAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CashRegister _$CashRegisterFromJson(Map<String, dynamic> json) => CashRegister(
      id: (json['id'] as num).toInt(),
      businessId: (json['business_id'] as num?)?.toInt(),
      locationId: (json['location_id'] as num?)?.toInt(),
      userId: (json['user_id'] as num?)?.toInt(),
      status: json['status'] as String?,
      closedAt: json['closed_at'] as String?,
      closingAmount: json['closing_amount'] as String?,
      totalCardSlips: (json['total_card_slips'] as num?)?.toInt(),
      totalCheques: (json['total_cheques'] as num?)?.toInt(),
      denominations: json['denominations'],
      closingNote: json['closing_note'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      locationInfo: locationInfoFromListJson(json['location_info'] as List?),
    );
