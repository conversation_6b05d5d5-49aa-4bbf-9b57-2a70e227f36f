import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:hive/hive.dart';

import '../../utils/we2up_constants.dart';

part 'table.g.dart';

@HiveType(typeId: 61)
@JsonSerializable()
class BusinessTable extends Equatable {
  @HiveField(0)
  @Json<PERSON>ey(name: 'id')
  final int id;

  @HiveField(1)
  @JsonKey(name: 'business_id')
  final int? businessId;

  @HiveField(2)
  @<PERSON>son<PERSON>ey(name: 'location_id')
  final int? locationId;

  @HiveField(3)
  @<PERSON><PERSON><PERSON><PERSON>(name: 'name')
  final String? name;

  @HiveField(4)
  @<PERSON><PERSON><PERSON><PERSON>(name: 'description')
  final String? description;

  @HiveField(5)
  @JsonKey(name: 'created_by')
  final int? createdBy;

  @HiveField(6)
  @<PERSON>sonKey(name: 'deleted_at')
  final String? deletedAt;

  @HiveField(7)
  @<PERSON><PERSON><PERSON><PERSON>(
    name: 'created_at',
    fromJson: DateTime.parse,
    toJson: dateToString,
  )
  final DateTime createdAt;

  @HiveField(8)
  @<PERSON>son<PERSON>ey(
    name: 'updated_at',
    fromJson: DateTime.parse,
    toJson: dateToString,
  )
  final DateTime updatedAt;

  const BusinessTable({
    required this.id,
    this.businessId,
    this.locationId,
    this.name,
    this.description,
    this.createdBy,
    this.deletedAt,
    required this.createdAt,
    required this.updatedAt,
  });

  factory BusinessTable.fromJson(Map<String, dynamic> json) => _$BusinessTableFromJson(json);

  Map<String, dynamic> toJson() => _$BusinessTableToJson(this);

  @override
  List<Object?> get props => [
        id,
        businessId,
        locationId,
        name,
        description,
        createdBy,
        deletedAt,
        createdAt,
        updatedAt,
      ];
}
