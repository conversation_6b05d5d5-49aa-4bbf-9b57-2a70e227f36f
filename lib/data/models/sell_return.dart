import 'package:equatable/equatable.dart';
import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:we2up/data/models/sell.dart';
import 'package:we2up/data/models/sell_return_to_api.dart';

import '../../utils/we2up_constants.dart';
import 'location_info.dart';

part 'sell_return.g.dart';

@HiveType(typeId: 32)
@JsonSerializable(explicitToJson: true)
class SellReturn extends HiveObject with EquatableMixin {
  @HiveField(0)
  @JsonKey(name: 'id')
  final int id;

  @HiveField(1)
  @JsonKey(name: 'business_id')
  final int? businessId;

  @HiveField(2)
  @JsonKey(name: 'location_id')
  final int? locationId;

  @HiveField(3)
  @JsonKey(name: 'res_table_id')
  final dynamic resTableId;

  @HiveField(4)
  @JsonKey(name: 'res_waiter_id')
  final dynamic resWaiterId;

  @HiveField(5)
  @Json<PERSON>ey(name: 'res_order_status')
  final dynamic resOrderStatus;

  @HiveField(6)
  @Json<PERSON>ey(name: 'type')
  final String? type;

  @HiveField(7)
  @JsonKey(name: 'sub_type')
  final dynamic subType;

  @HiveField(8)
  @JsonKey(name: 'status')
  final String? status;

  @HiveField(9)
  @JsonKey(name: 'sub_status')
  final dynamic subStatus;

  @HiveField(10)
  @JsonKey(name: 'is_quotation')
  final int? isQuotation;

  @HiveField(11)
  @JsonKey(name: 'payment_status')
  final String? paymentStatus;

  @HiveField(12)
  @JsonKey(name: 'adjustment_type')
  final dynamic adjustmentType;

  @HiveField(13)
  @JsonKey(name: 'contact_id')
  final int? contactId;

  @HiveField(14)
  @JsonKey(name: 'customer_group_id')
  final dynamic customerGroupId;

  @HiveField(15)
  @JsonKey(name: 'invoice_no')
  final String? invoiceNo;

  @HiveField(16)
  @JsonKey(name: 'ref_no')
  final dynamic refNo;

  @HiveField(17)
  @JsonKey(name: 'source')
  final dynamic source;

  @HiveField(18)
  @JsonKey(name: 'subscription_no')
  final dynamic subscriptionNo;

  @HiveField(19)
  @JsonKey(name: 'subscription_repeat_on')
  final dynamic subscriptionRepeatOn;

  @HiveField(20)
  @JsonKey(
    name: 'transaction_date',
    fromJson: DateTime.parse,
    toJson: dateToString,
  )
  final DateTime transactionDate;

  @HiveField(21)
  @JsonKey(name: 'total_before_tax')
  final String? totalBeforeTax;

  @HiveField(22)
  @JsonKey(name: 'tax_id')
  final dynamic taxId;

  @HiveField(23)
  @JsonKey(name: 'tax_amount')
  final String? taxAmount;

  @HiveField(24)
  @JsonKey(name: 'discount_type')
  final String? discountType;

  @HiveField(25)
  @JsonKey(name: 'discount_amount')
  final String? discountAmount;

  @HiveField(26)
  @JsonKey(name: 'rp_redeemed')
  final int? rpRedeemed;

  @HiveField(27)
  @JsonKey(name: 'rp_redeemed_amount')
  final String? rpRedeemedAmount;

  @HiveField(28)
  @JsonKey(name: 'shipping_details')
  final dynamic shippingDetails;

  @HiveField(29)
  @JsonKey(name: 'shipping_address')
  final dynamic shippingAddress;

  @HiveField(30)
  @JsonKey(name: 'shipping_status')
  final dynamic shippingStatus;

  @HiveField(31)
  @JsonKey(name: 'delivered_to')
  final dynamic deliveredTo;

  @HiveField(32)
  @JsonKey(name: 'shipping_charges')
  final String? shippingCharges;

  @HiveField(33)
  @JsonKey(name: 'shipping_custom_field_1')
  final dynamic shippingCustomField1;

  @HiveField(34)
  @JsonKey(name: 'shipping_custom_field_2')
  final dynamic shippingCustomField2;

  @HiveField(35)
  @JsonKey(name: 'shipping_custom_field_3')
  final dynamic shippingCustomField3;

  @HiveField(36)
  @JsonKey(name: 'shipping_custom_field_4')
  final dynamic shippingCustomField4;

  @HiveField(37)
  @JsonKey(name: 'shipping_custom_field_5')
  final dynamic shippingCustomField5;

  @HiveField(38)
  @JsonKey(name: 'additional_notes')
  final dynamic additionalNotes;

  @HiveField(39)
  @JsonKey(name: 'staff_note')
  final dynamic staffNote;

  @HiveField(40)
  @JsonKey(name: 'is_export')
  final int? isExport;

  @HiveField(41)
  @JsonKey(name: 'export_custom_fields_info')
  final dynamic exportCustomFieldsInfo;

  @HiveField(42)
  @JsonKey(name: 'round_off_amount')
  final String? roundOffAmount;

  @HiveField(43)
  @JsonKey(name: 'additional_expense_key_1')
  final dynamic additionalExpenseKey1;

  @HiveField(44)
  @JsonKey(name: 'additional_expense_value_1')
  final String? additionalExpenseValue1;

  @HiveField(45)
  @JsonKey(name: 'additional_expense_key_2')
  final dynamic additionalExpenseKey2;

  @HiveField(46)
  @JsonKey(name: 'additional_expense_value_2')
  final String? additionalExpenseValue2;

  @HiveField(47)
  @JsonKey(name: 'additional_expense_key_3')
  final dynamic additionalExpenseKey3;

  @HiveField(48)
  @JsonKey(name: 'additional_expense_value_3')
  final String? additionalExpenseValue3;

  @HiveField(49)
  @JsonKey(name: 'additional_expense_key_4')
  final dynamic additionalExpenseKey4;

  @HiveField(50)
  @JsonKey(name: 'additional_expense_value_4')
  final String? additionalExpenseValue4;

  @HiveField(51)
  @JsonKey(name: 'final_total')
  final String? finalTotal;

  @HiveField(52)
  @JsonKey(name: 'expense_category_id')
  final dynamic expenseCategoryId;

  @HiveField(53)
  @JsonKey(name: 'expense_for')
  final dynamic expenseFor;

  @HiveField(54)
  @JsonKey(name: 'commission_agent')
  final dynamic commissionAgent;

  @HiveField(55)
  @JsonKey(name: 'document')
  final dynamic document;

  @HiveField(56)
  @JsonKey(name: 'is_direct_sale')
  final int? isDirectSale;

  @HiveField(57)
  @JsonKey(name: 'is_suspend')
  final int? isSuspend;

  @HiveField(58)
  @JsonKey(name: 'exchange_rate')
  final String? exchangeRate;

  @HiveField(59)
  @JsonKey(name: 'total_amount_recovered')
  final dynamic totalAmountRecovered;

  @HiveField(60)
  @JsonKey(name: 'transfer_parent_id')
  final dynamic transferParentId;

  @HiveField(61)
  @JsonKey(name: 'return_parent_id')
  final int? returnParentId;

  @HiveField(62)
  @JsonKey(name: 'opening_stock_product_id')
  final dynamic openingStockProductId;

  @HiveField(63)
  @JsonKey(name: 'created_by')
  final int? createdBy;

  @HiveField(64)
  @JsonKey(name: 'crm_is_order_request')
  final int? crmIsOrderRequest;

  @HiveField(65)
  @JsonKey(name: 'prefer_payment_method')
  final dynamic preferPaymentMethod;

  @HiveField(66)
  @JsonKey(name: 'prefer_payment_account')
  final dynamic preferPaymentAccount;

  @HiveField(67)
  @JsonKey(name: 'sales_order_ids')
  final dynamic salesOrderIds;

  @HiveField(68)
  @JsonKey(name: 'purchase_order_ids')
  final dynamic purchaseOrderIds;

  @HiveField(69)
  @JsonKey(name: 'custom_field_1')
  final dynamic customField1;

  @HiveField(70)
  @JsonKey(name: 'custom_field_2')
  final dynamic customField2;

  @HiveField(71)
  @JsonKey(name: 'custom_field_3')
  final dynamic customField3;

  @HiveField(72)
  @JsonKey(name: 'custom_field_4')
  final dynamic customField4;

  @HiveField(73)
  @JsonKey(name: 'mfg_parent_production_purchase_id')
  final dynamic mfgParentProductionPurchaseId;

  @HiveField(74)
  @JsonKey(name: 'mfg_wasted_units')
  final dynamic mfgWastedUnits;

  @HiveField(75)
  @JsonKey(name: 'mfg_production_cost')
  final String? mfgProductionCost;

  @HiveField(76)
  @JsonKey(name: 'mfg_is_final')
  final int? mfgIsFinal;

  @HiveField(77)
  @JsonKey(name: 'repair_completed_on')
  final dynamic repairCompletedOn;

  @HiveField(78)
  @JsonKey(name: 'repair_warranty_id')
  final dynamic repairWarrantyId;

  @HiveField(79)
  @JsonKey(name: 'repair_brand_id')
  final dynamic repairBrandId;

  @HiveField(80)
  @JsonKey(name: 'repair_status_id')
  final dynamic repairStatusId;

  @HiveField(81)
  @JsonKey(name: 'repair_model_id')
  final dynamic repairModelId;

  @HiveField(82)
  @JsonKey(name: 'repair_job_sheet_id')
  final dynamic repairJobSheetId;

  @HiveField(83)
  @JsonKey(name: 'repair_defects')
  final dynamic repairDefects;

  @HiveField(84)
  @JsonKey(name: 'repair_serial_no')
  final dynamic repairSerialNo;

  @HiveField(85)
  @JsonKey(name: 'repair_checklist')
  final dynamic repairChecklist;

  @HiveField(86)
  @JsonKey(name: 'repair_security_pwd')
  final dynamic repairSecurityPwd;

  @HiveField(87)
  @JsonKey(name: 'repair_security_pattern')
  final dynamic repairSecurityPattern;

  @HiveField(88)
  @JsonKey(name: 'repair_due_date')
  final dynamic repairDueDate;

  @HiveField(89)
  @JsonKey(name: 'repair_device_id')
  final dynamic repairDeviceId;

  @HiveField(90)
  @JsonKey(name: 'repair_updates_notif')
  final int? repairUpdatesNotif;

  @HiveField(91)
  @JsonKey(name: 'essentials_duration')
  final String? essentialsDuration;

  @HiveField(92)
  @JsonKey(name: 'essentials_duration_unit')
  final dynamic essentialsDurationUnit;

  @HiveField(93)
  @JsonKey(name: 'essentials_amount_per_unit_duration')
  final String? essentialsAmountPerUnitDuration;

  @HiveField(94)
  @JsonKey(name: 'essentials_allowances')
  final dynamic essentialsAllowances;

  @HiveField(95)
  @JsonKey(name: 'essentials_deductions')
  final dynamic essentialsDeductions;

  @HiveField(96)
  @JsonKey(name: 'woocommerce_order_id')
  final dynamic woocommerceOrderId;

  @HiveField(97)
  @JsonKey(name: 'import_batch')
  final dynamic importBatch;

  @HiveField(98)
  @JsonKey(name: 'import_time')
  final dynamic importTime;

  @HiveField(99)
  @JsonKey(name: 'types_of_service_id')
  final dynamic typesOfServiceId;

  @HiveField(100)
  @JsonKey(name: 'packing_charge')
  final dynamic packingCharge;

  @HiveField(101)
  @JsonKey(name: 'packing_charge_type')
  final dynamic packingChargeType;

  @HiveField(102)
  @JsonKey(name: 'service_custom_field_1')
  final dynamic serviceCustomField1;

  @HiveField(103)
  @JsonKey(name: 'service_custom_field_2')
  final dynamic serviceCustomField2;

  @HiveField(104)
  @JsonKey(name: 'service_custom_field_3')
  final dynamic serviceCustomField3;

  @HiveField(105)
  @JsonKey(name: 'service_custom_field_4')
  final dynamic serviceCustomField4;

  @HiveField(106)
  @JsonKey(name: 'service_custom_field_5')
  final dynamic serviceCustomField5;

  @HiveField(107)
  @JsonKey(name: 'service_custom_field_6')
  final dynamic serviceCustomField6;

  @HiveField(108)
  @JsonKey(name: 'is_created_from_api')
  final int? isCreatedFromApi;

  @HiveField(109)
  @JsonKey(name: 'rp_earned')
  final int? rpEarned;

  @HiveField(110)
  @JsonKey(name: 'order_addresses')
  final dynamic orderAddresses;

  @HiveField(111)
  @JsonKey(name: 'is_recurring')
  final int? isRecurring;

  @HiveField(112)
  @JsonKey(name: 'recur_interval')
  final dynamic recurInterval;

  @HiveField(113)
  @JsonKey(name: 'recur_interval_type')
  final dynamic recurIntervalType;

  @HiveField(114)
  @JsonKey(name: 'recur_repetitions')
  final dynamic recurRepetitions;

  @HiveField(115)
  @JsonKey(name: 'recur_stopped_on')
  final dynamic recurStoppedOn;

  @HiveField(116)
  @JsonKey(name: 'recur_parent_id')
  final dynamic recurParentId;

  @HiveField(117)
  @JsonKey(name: 'invoice_token')
  final dynamic invoiceToken;

  @HiveField(118)
  @JsonKey(name: 'pay_term_number')
  final dynamic payTermNumber;

  @HiveField(119)
  @JsonKey(name: 'pay_term_type')
  final dynamic payTermType;

  @HiveField(120)
  @JsonKey(name: 'pjt_project_id')
  final dynamic pjtProjectId;

  @HiveField(121)
  @JsonKey(name: 'pjt_title')
  final dynamic pjtTitle;

  @HiveField(122)
  @JsonKey(name: 'selling_price_group_id')
  final dynamic sellingPriceGroupId;

  @HiveField(123)
  @JsonKey(name: 'created_at')
  final String? createdAt;

  @HiveField(124)
  @JsonKey(name: 'updated_at')
  final String? updatedAt;

  @HiveField(125)
  @JsonKey(name: 'end_date')
  final dynamic endDate;

  @HiveField(126)
  @JsonKey(name: 'shipping_company_id')
  final dynamic shippingCompanyId;

  @HiveField(127)
  @JsonKey(name: 'package_count')
  final dynamic packageCount;

  @HiveField(128)
  @JsonKey(name: 'paymob_order_id')
  final dynamic paymobOrderId;

  @HiveField(129)
  @JsonKey(name: 'prev_balance')
  final dynamic prevBalance;

  @HiveField(130)
  @JsonKey(name: 'sub_counter')
  final dynamic subCounter;

  @HiveField(131)
  @JsonKey(name: 'customer_id')
  final dynamic customerId;

  @HiveField(132)
  @JsonKey(name: 'supplier_id')
  final dynamic supplierId;

  @HiveField(133)
  @JsonKey(name: 'sell_id')
  final dynamic sellId;

  @HiveField(134)
  @JsonKey(name: 'purchase_id')
  final dynamic purchaseId;

  @HiveField(135)
  @JsonKey(name: 'eta_submissionId')
  final dynamic etaSubmissionId;

  @HiveField(136)
  @JsonKey(name: 'eta_uuid')
  final dynamic etaUuid;

  @HiveField(137)
  @JsonKey(name: 'eta_longId')
  final dynamic etaLongId;

  @HiveField(138)
  @JsonKey(name: 'eta_hashKey')
  final dynamic etaHashKey;

  @HiveField(139)
  @JsonKey(name: 'eta_status')
  final dynamic etaStatus;

  @HiveField(140)
  @JsonKey(name: 'eta_notes')
  final dynamic etaNotes;

  @HiveField(141)
  @JsonKey(name: 'invoice_scheme_id')
  final dynamic invoiceSchemeId;

  @HiveField(142)
  @JsonKey(name: 'custom_status')
  final dynamic customStatus;

  @HiveField(143)
  @JsonKey(name: 'is_reservation')
  final dynamic isReservation;

  @HiveField(144)
  @JsonKey(name: 'review_status')
  final dynamic reviewStatus;

  @HiveField(145)
  @JsonKey(name: 'review_details')
  final dynamic reviewDetails;

  @HiveField(146)
  @JsonKey(name: 'settlement_purchase_id')
  final dynamic settlementPurchaseId;

  @HiveField(147)
  @JsonKey(name: 'settlement_sell_id')
  final dynamic settlementSellId;

  @HiveField(148)
  @JsonKey(name: 'invoice_layout_id')
  final dynamic invoiceLayoutId;

  @HiveField(149)
  @JsonKey(name: 'invoice_commision')
  final dynamic invoiceCommision;

  @HiveField(150)
  @JsonKey(name: 'commision_as_leader')
  final String? commisionAsLeader;

  @HiveField(151)
  @JsonKey(name: 'leader_id')
  final dynamic leaderId;

  @HiveField(152)
  @JsonKey(name: 'payment_lines')
  final List<PaymentLine>? paymentLines;

  @HiveField(153)
  @JsonKey(name: 'return_parent_sell', fromJson: _returnPSFromJsonList)
  final Sell? returnParentSell;

  @HiveField(154)
  @JsonKey(includeFromJson: false, includeToJson: false)
  bool offline;

  @HiveField(155)
  @JsonKey(includeFromJson: false, includeToJson: false)
  SellReturnToAPI? sellReturnToAPI;

  @HiveField(156)
  @JsonKey(name: 'location_info', fromJson: locationInfoFromListJson)
  final LocationInfo? locationInfo;

  SellReturn({
    required this.id,
    this.businessId,
    this.locationId,
    this.resTableId,
    this.resWaiterId,
    this.resOrderStatus,
    this.type,
    this.subType,
    this.status,
    this.subStatus,
    this.isQuotation,
    this.paymentStatus,
    this.adjustmentType,
    this.contactId,
    this.customerGroupId,
    this.invoiceNo,
    this.refNo,
    this.source,
    this.subscriptionNo,
    this.subscriptionRepeatOn,
    required this.transactionDate,
    this.totalBeforeTax,
    this.taxId,
    this.taxAmount,
    this.discountType,
    this.discountAmount,
    this.rpRedeemed,
    this.rpRedeemedAmount,
    this.shippingDetails,
    this.shippingAddress,
    this.shippingStatus,
    this.deliveredTo,
    this.shippingCharges,
    this.shippingCustomField1,
    this.shippingCustomField2,
    this.shippingCustomField3,
    this.shippingCustomField4,
    this.shippingCustomField5,
    this.additionalNotes,
    this.staffNote,
    this.isExport,
    this.exportCustomFieldsInfo,
    this.roundOffAmount,
    this.additionalExpenseKey1,
    this.additionalExpenseValue1,
    this.additionalExpenseKey2,
    this.additionalExpenseValue2,
    this.additionalExpenseKey3,
    this.additionalExpenseValue3,
    this.additionalExpenseKey4,
    this.additionalExpenseValue4,
    this.finalTotal,
    this.expenseCategoryId,
    this.expenseFor,
    this.commissionAgent,
    this.document,
    this.isDirectSale,
    this.isSuspend,
    this.exchangeRate,
    this.totalAmountRecovered,
    this.transferParentId,
    this.returnParentId,
    this.openingStockProductId,
    this.createdBy,
    this.crmIsOrderRequest,
    this.preferPaymentMethod,
    this.preferPaymentAccount,
    this.salesOrderIds,
    this.purchaseOrderIds,
    this.customField1,
    this.customField2,
    this.customField3,
    this.customField4,
    this.mfgParentProductionPurchaseId,
    this.mfgWastedUnits,
    this.mfgProductionCost,
    this.mfgIsFinal,
    this.repairCompletedOn,
    this.repairWarrantyId,
    this.repairBrandId,
    this.repairStatusId,
    this.repairModelId,
    this.repairJobSheetId,
    this.repairDefects,
    this.repairSerialNo,
    this.repairChecklist,
    this.repairSecurityPwd,
    this.repairSecurityPattern,
    this.repairDueDate,
    this.repairDeviceId,
    this.repairUpdatesNotif,
    this.essentialsDuration,
    this.essentialsDurationUnit,
    this.essentialsAmountPerUnitDuration,
    this.essentialsAllowances,
    this.essentialsDeductions,
    this.woocommerceOrderId,
    this.importBatch,
    this.importTime,
    this.typesOfServiceId,
    this.packingCharge,
    this.packingChargeType,
    this.serviceCustomField1,
    this.serviceCustomField2,
    this.serviceCustomField3,
    this.serviceCustomField4,
    this.serviceCustomField5,
    this.serviceCustomField6,
    this.isCreatedFromApi,
    this.rpEarned,
    this.orderAddresses,
    this.isRecurring,
    this.recurInterval,
    this.recurIntervalType,
    this.recurRepetitions,
    this.recurStoppedOn,
    this.recurParentId,
    this.invoiceToken,
    this.payTermNumber,
    this.payTermType,
    this.pjtProjectId,
    this.pjtTitle,
    this.sellingPriceGroupId,
    this.createdAt,
    this.updatedAt,
    this.endDate,
    this.shippingCompanyId,
    this.packageCount,
    this.paymobOrderId,
    this.prevBalance,
    this.subCounter,
    this.customerId,
    this.supplierId,
    this.sellId,
    this.purchaseId,
    this.etaSubmissionId,
    this.etaUuid,
    this.etaLongId,
    this.etaHashKey,
    this.etaStatus,
    this.etaNotes,
    this.invoiceSchemeId,
    this.customStatus,
    this.isReservation,
    this.reviewStatus,
    this.reviewDetails,
    this.settlementPurchaseId,
    this.settlementSellId,
    this.invoiceLayoutId,
    this.invoiceCommision,
    this.commisionAsLeader,
    this.leaderId,
    this.paymentLines,
    required this.returnParentSell,
    this.offline = false,
    this.sellReturnToAPI,
    this.locationInfo,
  });

  static Sell? _returnPSFromJsonList(dynamic item) {
    if (item == null) {
      return null;
    }
    return Sell.fromJson(updateProductIdsInSellLines(item));
  }

  factory SellReturn.fromJson(Map<String, dynamic> json) =>
      _$SellReturnFromJson(json);

  Map<String, dynamic> toJson() => _$SellReturnToJson(this);

  @override
  List<Object?> get props => [
        id,
        locationInfo,
        businessId,
        locationId,
        resTableId,
        resWaiterId,
        resOrderStatus,
        type,
        subType,
        status,
        subStatus,
        isQuotation,
        paymentStatus,
        adjustmentType,
        contactId,
        customerGroupId,
        invoiceNo,
        refNo,
        source,
        subscriptionNo,
        subscriptionRepeatOn,
        transactionDate,
        totalBeforeTax,
        taxId,
        taxAmount,
        discountType,
        discountAmount,
        rpRedeemed,
        rpRedeemedAmount,
        shippingDetails,
        shippingAddress,
        shippingStatus,
        deliveredTo,
        shippingCharges,
        shippingCustomField1,
        shippingCustomField2,
        shippingCustomField3,
        shippingCustomField4,
        shippingCustomField5,
        additionalNotes,
        staffNote,
        isExport,
        exportCustomFieldsInfo,
        roundOffAmount,
        additionalExpenseKey1,
        additionalExpenseValue1,
        additionalExpenseKey2,
        additionalExpenseValue2,
        additionalExpenseKey3,
        additionalExpenseValue3,
        additionalExpenseKey4,
        additionalExpenseValue4,
        finalTotal,
        expenseCategoryId,
        expenseFor,
        commissionAgent,
        document,
        isDirectSale,
        isSuspend,
        exchangeRate,
        totalAmountRecovered,
        transferParentId,
        returnParentId,
        openingStockProductId,
        createdBy,
        crmIsOrderRequest,
        preferPaymentMethod,
        preferPaymentAccount,
        salesOrderIds,
        purchaseOrderIds,
        customField1,
        customField2,
        customField3,
        customField4,
        mfgParentProductionPurchaseId,
        mfgWastedUnits,
        mfgProductionCost,
        mfgIsFinal,
        repairCompletedOn,
        repairWarrantyId,
        repairBrandId,
        repairStatusId,
        repairModelId,
        repairJobSheetId,
        repairDefects,
        repairSerialNo,
        repairChecklist,
        repairSecurityPwd,
        repairSecurityPattern,
        repairDueDate,
        repairDeviceId,
        repairUpdatesNotif,
        essentialsDuration,
        essentialsDurationUnit,
        essentialsAmountPerUnitDuration,
        essentialsAllowances,
        essentialsDeductions,
        woocommerceOrderId,
        importBatch,
        importTime,
        typesOfServiceId,
        packingCharge,
        packingChargeType,
        serviceCustomField1,
        serviceCustomField2,
        serviceCustomField3,
        serviceCustomField4,
        serviceCustomField5,
        serviceCustomField6,
        isCreatedFromApi,
        rpEarned,
        orderAddresses,
        isRecurring,
        recurInterval,
        recurIntervalType,
        recurRepetitions,
        recurStoppedOn,
        recurParentId,
        invoiceToken,
        payTermNumber,
        payTermType,
        pjtProjectId,
        pjtTitle,
        sellingPriceGroupId,
        createdAt,
        updatedAt,
        endDate,
        shippingCompanyId,
        packageCount,
        paymobOrderId,
        prevBalance,
        subCounter,
        customerId,
        supplierId,
        sellId,
        purchaseId,
        etaSubmissionId,
        etaUuid,
        etaLongId,
        etaHashKey,
        etaStatus,
        etaNotes,
        invoiceSchemeId,
        customStatus,
        isReservation,
        reviewStatus,
        reviewDetails,
        settlementPurchaseId,
        settlementSellId,
        invoiceLayoutId,
        invoiceCommision,
        commisionAsLeader,
        leaderId,
        offline,
        sellReturnToAPI,
        locationInfo,
      ];
}
