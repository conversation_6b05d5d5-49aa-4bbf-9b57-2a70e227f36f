// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'new_product_to_api.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class NewProductToAPIAdapter extends TypeAdapter<NewProductToAPI> {
  @override
  final int typeId = 42;

  @override
  NewProductToAPI read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return NewProductToAPI(
      name: fields[0] as String?,
      sku: fields[1] as String?,
      type: fields[22] as String,
      barcodeType: fields[2] as String?,
      unitId: fields[3] as Unit,
      brandId: fields[4] as String?,
      categoryId: fields[5] as ProductCategory?,
      subCategoryId: fields[6] as String?,
      enableStock: fields[7] as bool,
      alertQuantity: fields[8] as String?,
      hasModuleData: fields[9] as String?,
      repairModelId: fields[10] as String?,
      productDescription: fields[11] as String?,
      expiryPeriod: fields[12] as String?,
      expiryPeriodType: fields[13] as ExpiryPeriodType?,
      weight: fields[14] as String?,
      productCustomField1: fields[15] as String?,
      productCustomField2: fields[16] as String?,
      productCustomField3: fields[17] as String?,
      productCustomField4: fields[18] as String?,
      tax: fields[20] as TaxRate?,
      taxType: fields[21] as TaxType,
      singleDpp: fields[23] as String?,
      singleDppIncTax: fields[24] as String?,
      profitPercent: fields[25] as String?,
      singleDsp: fields[26] as String?,
      singleDspIncTax: fields[27] as String?,
      locationInfo: fields[28] as LocationInfo?,
      imagePath: fields[31] as String?,
      productLocations: (fields[29] as List).cast<int>(),
      openingStock: (fields[30] as List)
          .map((dynamic e) => (e as Map).cast<String, dynamic>())
          .toList(),
    );
  }

  @override
  void write(BinaryWriter writer, NewProductToAPI obj) {
    writer
      ..writeByte(32)
      ..writeByte(0)
      ..write(obj.name)
      ..writeByte(1)
      ..write(obj.sku)
      ..writeByte(2)
      ..write(obj.barcodeType)
      ..writeByte(3)
      ..write(obj.unitId)
      ..writeByte(4)
      ..write(obj.brandId)
      ..writeByte(5)
      ..write(obj.categoryId)
      ..writeByte(6)
      ..write(obj.subCategoryId)
      ..writeByte(7)
      ..write(obj.enableStock)
      ..writeByte(8)
      ..write(obj.alertQuantity)
      ..writeByte(9)
      ..write(obj.hasModuleData)
      ..writeByte(10)
      ..write(obj.repairModelId)
      ..writeByte(11)
      ..write(obj.productDescription)
      ..writeByte(12)
      ..write(obj.expiryPeriod)
      ..writeByte(13)
      ..write(obj.expiryPeriodType)
      ..writeByte(14)
      ..write(obj.weight)
      ..writeByte(15)
      ..write(obj.productCustomField1)
      ..writeByte(16)
      ..write(obj.productCustomField2)
      ..writeByte(17)
      ..write(obj.productCustomField3)
      ..writeByte(18)
      ..write(obj.productCustomField4)
      ..writeByte(19)
      ..write(obj.woocommerceDisableSync)
      ..writeByte(20)
      ..write(obj.tax)
      ..writeByte(21)
      ..write(obj.taxType)
      ..writeByte(22)
      ..write(obj.type)
      ..writeByte(23)
      ..write(obj.singleDpp)
      ..writeByte(24)
      ..write(obj.singleDppIncTax)
      ..writeByte(25)
      ..write(obj.profitPercent)
      ..writeByte(26)
      ..write(obj.singleDsp)
      ..writeByte(27)
      ..write(obj.singleDspIncTax)
      ..writeByte(28)
      ..write(obj.locationInfo)
      ..writeByte(29)
      ..write(obj.productLocations)
      ..writeByte(30)
      ..write(obj.openingStock)
      ..writeByte(31)
      ..write(obj.imagePath);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is NewProductToAPIAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

NewProductToAPI _$NewProductToAPIFromJson(Map<String, dynamic> json) =>
    NewProductToAPI(
      name: json['name'] as String?,
      sku: json['sku'] as String?,
      type: json['type'] as String? ?? "single",
      barcodeType: json['barcode_type'] as String? ?? "C128",
      unitId: Unit.fromJson(json['unit_id'] as Map<String, dynamic>),
      brandId: json['brand_id'] as String?,
      categoryId: json['category_id'] == null
          ? null
          : ProductCategory.fromJson(
              json['category_id'] as Map<String, dynamic>),
      subCategoryId: json['sub_category_id'] as String?,
      enableStock: json['enable_stock'] as bool,
      alertQuantity: json['alert_quantity'] as String?,
      hasModuleData: json['has_module_data'] as String?,
      repairModelId: json['repair_model_id'] as String?,
      productDescription: json['product_description'] as String?,
      expiryPeriod: json['expiry_period'] as String?,
      expiryPeriodType: $enumDecodeNullable(
          _$ExpiryPeriodTypeEnumMap, json['expiry_period_type']),
      weight: json['weight'] as String?,
      productCustomField1: json['product_custom_field1'] as String?,
      productCustomField2: json['product_custom_field2'] as String?,
      productCustomField3: json['product_custom_field3'] as String?,
      productCustomField4: json['product_custom_field4'] as String?,
      tax: json['tax'] == null
          ? null
          : TaxRate.fromJson(json['tax'] as Map<String, dynamic>),
      taxType: $enumDecode(_$TaxTypeEnumMap, json['tax_type']),
      singleDpp: json['single_dpp'] as String?,
      singleDppIncTax: json['single_dpp_inc_tax'] as String?,
      profitPercent: json['profit_percent'] as String?,
      singleDsp: json['single_dsp'] as String?,
      singleDspIncTax: json['single_dsp_inc_tax'] as String?,
      imagePath: json['image'] as String?,
      productLocations: (json['product_locations'] as List<dynamic>)
          .map((e) => (e as num).toInt())
          .toList(),
      openingStock: (json['opening_stock'] as List<dynamic>)
          .map((e) => e as Map<String, dynamic>)
          .toList(),
    );

Map<String, dynamic> _$NewProductToAPIToJson(NewProductToAPI instance) =>
    <String, dynamic>{
      'name': instance.name,
      'sku': instance.sku,
      'barcode_type': instance.barcodeType,
      'unit_id': NewProductToAPI._unitIdToJson(instance.unitId),
      'brand_id': instance.brandId,
      'category_id': NewProductToAPI._categoryToJson(instance.categoryId),
      'sub_category_id': instance.subCategoryId,
      'enable_stock': NewProductToAPI._enableStockToJson(instance.enableStock),
      'alert_quantity': instance.alertQuantity,
      'has_module_data': instance.hasModuleData,
      'repair_model_id': instance.repairModelId,
      'product_description': instance.productDescription,
      'expiry_period': instance.expiryPeriod,
      'expiry_period_type':
          NewProductToAPI._expiryTypeToJson(instance.expiryPeriodType),
      'weight': instance.weight,
      'product_custom_field1': instance.productCustomField1,
      'product_custom_field2': instance.productCustomField2,
      'product_custom_field3': instance.productCustomField3,
      'product_custom_field4': instance.productCustomField4,
      'tax': NewProductToAPI._taxIdToJson(instance.tax),
      'tax_type': NewProductToAPI._taxTypeToJson(instance.taxType),
      'type': instance.type,
      'single_dpp': instance.singleDpp,
      'single_dpp_inc_tax': instance.singleDppIncTax,
      'profit_percent': instance.profitPercent,
      'single_dsp': instance.singleDsp,
      'single_dsp_inc_tax': instance.singleDspIncTax,
      'product_locations': instance.productLocations,
      'opening_stock': instance.openingStock,
      'image': instance.imagePath,
    };

const _$ExpiryPeriodTypeEnumMap = {
  ExpiryPeriodType.years: 'years',
  ExpiryPeriodType.months: 'months',
  ExpiryPeriodType.days: 'days',
};

const _$TaxTypeEnumMap = {
  TaxType.exclusive: 'exclusive',
  TaxType.inclusive: 'inclusive',
};
