class UserLoggedinData {
  final int? id;
  final bool overSelling;
  final bool locationRequired;
  final int? defaultAccount;
  final List<int> permittedLocations;

  UserLoggedinData({
    required this.id,
    required this.overSelling,
    required this.locationRequired,
    required this.defaultAccount,
    required this.permittedLocations,
  });

  factory UserLoggedinData.fromJson(Map<String, dynamic> json) {
    return UserLoggedinData(
      id: json['data']['id'] as int?,
      overSelling: _isOversellingAllowed(json),
      locationRequired: _isLocationRequired(json),
      defaultAccount: int.tryParse(json['data']['default_account'] ?? ''),
      permittedLocations:
          (json['data']['permitted_locations'] as List<dynamic>).cast<int>(),
    );
  }

  static bool _isOversellingAllowed(Map<String, dynamic> json) {
    return json['data'] != null &&
        json['data']['business'] != null &&
        json['data']['business']['pos_settings'] != null &&
        json['data']['business']['pos_settings']['allow_overselling'] == "1";
  }

  static bool _isLocationRequired(Map<String, dynamic> json) {
    return json['data'] != null &&
        json['data']['business'] != null &&
        json['data']['business']['common_settings'] != null &&
        json['data']['business']['common_settings']
                ['location_setting_required'] ==
            "1";
  }
}
