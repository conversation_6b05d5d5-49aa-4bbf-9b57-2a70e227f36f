import 'package:equatable/equatable.dart';
import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';

part 'expense_category.g.dart';

@JsonSerializable()
@HiveType(typeId: 53)
class ExpenseCategory extends Equatable {
  @HiveField(0)
  @Json<PERSON>ey(name: 'name')
  final String name;

  @HiveField(1)
  @<PERSON>son<PERSON>ey(name: 'code')
  final String? code;

  @HiveField(2)
  @<PERSON>son<PERSON><PERSON>(name: 'id')
  final int id;

  const ExpenseCategory({
    required this.name,
    this.code,
    required this.id,
  });

  factory ExpenseCategory.fromJson(Map<String, dynamic> json) =>
      _$ExpenseCategoryFromJson(json);

  Map<String, dynamic> toJson() => _$ExpenseCategoryToJson(this);

  @override
  List<Object?> get props => [name, code, id];
}