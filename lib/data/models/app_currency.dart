import 'package:currency_picker/currency_picker.dart';
import 'package:hive/hive.dart';

part 'app_currency.g.dart';

@HiveType(typeId: 60)
class AppCurrency {
  ///The currency code
  @HiveField(0)
  final String code;

  ///The currency name in English
  @HiveField(1)
  final String name;

  ///The currency symbol
  @HiveField(2)
  final String symbol;

  ///The currency flag code
  ///
  /// To get flag unicode(Emoji) use [CurrencyUtils.currencyToEmoji]
  @HiveField(3)
  final String? flag;

  ///The currency number
  @HiveField(4)
  final int number;

  ///The currency decimal digits
  @HiveField(5)
  final int decimalDigits;

  ///The currency plural name in English
  @HiveField(6)
  final String namePlural;

  ///The decimal separator
  @HiveField(7)
  final String decimalSeparator;

  ///The thousands separator
  @HiveField(8)
  final String thousandsSeparator;

  ///True if symbol is on the Left of the amount
  @HiveField(9)
  final bool symbolOnLeft;

  ///True if symbol has space with amount
  @HiveField(10)
  final bool spaceBetweenAmountAndSymbol;

  bool get isFlagImage => flag?.endsWith('.png') ?? false;

  AppCurrency({
    required this.code,
    required this.name,
    required this.symbol,
    required this.flag,
    required this.number,
    required this.decimalDigits,
    required this.namePlural,
    required this.symbolOnLeft,
    required this.decimalSeparator,
    required this.thousandsSeparator,
    required this.spaceBetweenAmountAndSymbol,
  });

  AppCurrency.fromJson(Map<String, dynamic> json)
      : code = json['code'],
        name = json['name'],
        symbol = json['symbol'],
        number = json['number'],
        flag = json['flag'],
        decimalDigits = json['decimal_digits'],
        namePlural = json['name_plural'],
        symbolOnLeft = json['symbol_on_left'],
        decimalSeparator = json['decimal_separator'],
        thousandsSeparator = json['thousands_separator'],
        spaceBetweenAmountAndSymbol = json['space_between_amount_and_symbol'];

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'name': name,
      'symbol': symbol,
      'flag': flag,
      'number': number,
      'decimal_digits': decimalDigits,
      'name_plural': namePlural,
      'symbol_on_left': symbolOnLeft,
      'decimal_separator': decimalSeparator,
      'thousands_separator': thousandsSeparator,
      'space_between_amount_and_symbol': spaceBetweenAmountAndSymbol,
    };
  }

  AppCurrency.fromCurrency(Currency currency)
      : code = currency.code,
        name = currency.name,
        symbol = currency.symbol,
        number = currency.number,
        flag = currency.flag,
        decimalDigits = currency.decimalDigits,
        namePlural = currency.namePlural,
        symbolOnLeft = currency.symbolOnLeft,
        decimalSeparator = currency.decimalSeparator,
        thousandsSeparator = currency.thousandsSeparator,
        spaceBetweenAmountAndSymbol = currency.spaceBetweenAmountAndSymbol;
}
