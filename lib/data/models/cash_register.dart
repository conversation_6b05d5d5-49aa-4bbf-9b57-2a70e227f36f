import 'package:equatable/equatable.dart';
import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:we2up/data/models/location_info.dart';

part 'cash_register.g.dart';

@HiveType(typeId: 47)
@JsonSerializable(explicitToJson: true, createToJson: false)
class CashRegister extends Equatable {
  @HiveField(0)
  @Json<PERSON>ey(name: 'id')
  final int id;

  @HiveField(1)
  @JsonKey(name: 'business_id')
  final int? businessId;

  @HiveField(2)
  @JsonKey(name: 'location_id')
  final int? locationId;

  @HiveField(3)
  @Json<PERSON>ey(name: 'user_id')
  final int? userId;

  @HiveField(4)
  @JsonKey(name: 'status')
  final String? status;

  @HiveField(5)
  @Json<PERSON>ey(name: 'closed_at')
  final String? closedAt;

  @HiveField(6)
  @<PERSON>son<PERSON><PERSON>(name: 'closing_amount')
  final String? closingAmount;

  @HiveField(7)
  @Json<PERSON>ey(name: 'total_card_slips')
  final int? totalCardSlips;

  @HiveField(8)
  @<PERSON>son<PERSON><PERSON>(name: 'total_cheques')
  final int? totalCheques;

  @HiveField(9)
  @JsonKey(name: 'denominations')
  final dynamic denominations;

  @HiveField(10)
  @JsonKey(name: 'closing_note')
  final String? closingNote;

  @HiveField(11)
  @JsonKey(name: 'created_at', fromJson: DateTime.parse)
  final DateTime createdAt;

  @HiveField(12)
  @JsonKey(name: 'updated_at', fromJson: DateTime.parse)
  final DateTime updatedAt;

  @HiveField(13)
  @JsonKey(name: 'location_info', fromJson: locationInfoFromListJson)
  final LocationInfo? locationInfo;

  const CashRegister({
    required this.id,
    this.businessId,
    this.locationId,
    this.userId,
    this.status,
    required this.closedAt,
    this.closingAmount,
    this.totalCardSlips,
    this.totalCheques,
    this.denominations,
    this.closingNote,
    required this.createdAt,
    required this.updatedAt,
    this.locationInfo,
  });

  factory CashRegister.fromJson(Map<String, dynamic> json) =>
      _$CashRegisterFromJson(json);

  @override
  List<Object?> get props => [
        id,
        businessId,
        locationId,
        userId,
        status,
        closedAt,
        closingAmount,
        totalCardSlips,
        totalCheques,
        denominations,
        closingNote,
        createdAt,
        updatedAt,
      ];
}
