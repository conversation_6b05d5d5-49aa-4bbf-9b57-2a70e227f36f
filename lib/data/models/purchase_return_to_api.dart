import 'package:hive/hive.dart';
import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:we2up/data/models/location_info.dart';

import '../../utils/we2up_constants.dart';

part 'purchase_return_to_api.g.dart';

@HiveType(typeId: 39)
@JsonSerializable()
class PurchaseReturnToAPI extends Equatable {
  @HiveField(0)
  @<PERSON>sonKey(name: 'transaction_id')
  final String transactionId;

  @HiveField(1)
  @JsonKey(name: 'ref_no')
  final String? refNo;

  @HiveField(2)
  @JsonKey(name: 'products', toJson: _toJsonList)
  final List<ProductPurchaseReturn> products;

  @HiveField(3)
  @JsonKey(name: 'tax_id')
  final int? taxId;

  @HiveField(4)
  @JsonKey(name: 'tax_amount')
  final String? taxAmount;

  @HiveField(5)
  @JsonKey(name: 'tax_percent')
  final String? taxPercent;

  @HiveField(7)
  @JsonKey(name: 'location_info', toJson: locationInfoToJson)
  final LocationInfo? locationInfo;

  const PurchaseReturnToAPI({
    required this.transactionId,
    this.refNo,
    required this.products,
    this.taxId,
    this.taxAmount,
    this.taxPercent,
    this.locationInfo,
  });

  static List<Map<String, dynamic>> _toJsonList(
      List<ProductPurchaseReturn>? list) {
    return list
            ?.map((productPurchaseReturn) => productPurchaseReturn.toJson())
            .toList() ??
        [];
  }

  factory PurchaseReturnToAPI.fromJson(Map<String, dynamic> json) =>
      _$PurchaseReturnToAPIFromJson(json);

  Map<String, dynamic> toJson() => _$PurchaseReturnToAPIToJson(this);

  @override
  List<Object?> get props => [
        transactionId,
        refNo,
        products,
        taxId,
        taxAmount,
        taxPercent,
      ];
}

@HiveType(typeId: 40)
@JsonSerializable()
class ProductPurchaseReturn extends Equatable {
  @HiveField(0)
  @JsonKey(name: 'purchase_line_id')
  final String purchaseLineId;

  @HiveField(1)
  @JsonKey(name: 'returned_quantity')
  final String returnedQuantity;

  const ProductPurchaseReturn({
    required this.purchaseLineId,
    required this.returnedQuantity,
  });

  factory ProductPurchaseReturn.fromJson(Map<String, dynamic> json) =>
      _$ProductPurchaseReturnFromJson(json);

  Map<String, dynamic> toJson() => _$ProductPurchaseReturnToJson(this);

  @override
  List<Object?> get props => [purchaseLineId, returnedQuantity];
}
