// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'purchase_return.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class PurchaseReturnAdapter extends TypeAdapter<PurchaseReturn> {
  @override
  final int typeId = 41;

  @override
  PurchaseReturn read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return PurchaseReturn(
      id: fields[0] as int?,
      offline: fields[157] as bool,
      purchaseReturnToAPI: fields[158] as PurchaseReturnToAPI?,
      businessId: fields[1] as int?,
      locationId: fields[2] as int?,
      resTableId: fields[3] as int?,
      resWaiterId: fields[4] as int?,
      resOrderStatus: fields[5] as int?,
      type: fields[6] as String?,
      subType: fields[7] as String?,
      status: fields[8] as String?,
      subStatus: fields[9] as String?,
      isQuotation: fields[10] as int?,
      paymentStatus: fields[11] as String?,
      adjustmentType: fields[12] as String?,
      contactId: fields[13] as int?,
      customerGroupId: fields[14] as int?,
      invoiceNo: fields[15] as String?,
      refNo: fields[16] as String?,
      source: fields[17] as String?,
      subscriptionNo: fields[18] as String?,
      subscriptionRepeatOn: fields[19] as String?,
      transactionDate: fields[20] as DateTime,
      totalBeforeTax: fields[21] as String?,
      taxId: fields[22] as int?,
      taxAmount: fields[23] as String?,
      discountType: fields[24] as String?,
      discountAmount: fields[25] as String?,
      rpRedeemed: fields[26] as int?,
      rpRedeemedAmount: fields[27] as String?,
      shippingDetails: fields[28] as String?,
      shippingAddress: fields[29] as String?,
      deliveryDate: fields[30] as String?,
      shippingStatus: fields[31] as String?,
      deliveredTo: fields[32] as String?,
      shippingCharges: fields[33] as String?,
      shippingCustomField1: fields[34] as String?,
      shippingCustomField2: fields[35] as String?,
      shippingCustomField3: fields[36] as String?,
      shippingCustomField4: fields[37] as String?,
      shippingCustomField5: fields[38] as String?,
      additionalNotes: fields[39] as String?,
      staffNote: fields[40] as String?,
      isExport: fields[41] as int?,
      exportCustomFieldsInfo: fields[42] as String?,
      roundOffAmount: fields[43] as String?,
      additionalExpenseKey1: fields[44] as String?,
      additionalExpenseValue1: fields[45] as String?,
      additionalExpenseKey2: fields[46] as String?,
      additionalExpenseValue2: fields[47] as String?,
      additionalExpenseKey3: fields[48] as String?,
      additionalExpenseValue3: fields[49] as String?,
      additionalExpenseKey4: fields[50] as String?,
      additionalExpenseValue4: fields[51] as String?,
      finalTotal: fields[52] as String?,
      expenseCategoryId: fields[53] as int?,
      expenseFor: fields[54] as String?,
      commissionAgent: fields[55] as String?,
      document: fields[56] as String?,
      isDirectSale: fields[57] as int?,
      isSuspend: fields[58] as int?,
      exchangeRate: fields[59] as String?,
      totalAmountRecovered: fields[60] as String?,
      transferParentId: fields[61] as int?,
      returnParentId: fields[62] as int?,
      openingStockProductId: fields[63] as int?,
      createdBy: fields[64] as int?,
      purchaseRequisitionIds: (fields[65] as List?)?.cast<int>(),
      crmIsOrderRequest: fields[66] as int?,
      preferPaymentMethod: fields[67] as String?,
      preferPaymentAccount: fields[68] as String?,
      salesOrderIds: (fields[69] as List?)?.cast<int>(),
      purchaseOrderIds: (fields[70] as List?)?.cast<int>(),
      customField1: fields[71] as String?,
      customField2: fields[72] as String?,
      customField3: fields[73] as String?,
      customField4: fields[74] as String?,
      mfgParentProductionPurchaseId: fields[75] as int?,
      mfgWastedUnits: fields[76] as int?,
      mfgProductionCost: fields[77] as String?,
      mfgIsFinal: fields[78] as int?,
      repairCompletedOn: fields[79] as String?,
      repairWarrantyId: fields[80] as int?,
      repairBrandId: fields[81] as int?,
      repairStatusId: fields[82] as int?,
      repairModelId: fields[83] as int?,
      repairJobSheetId: fields[84] as int?,
      repairDefects: fields[85] as String?,
      repairSerialNo: fields[86] as String?,
      repairChecklist: fields[87] as String?,
      repairSecurityPwd: fields[88] as String?,
      repairSecurityPattern: fields[89] as String?,
      repairDueDate: fields[90] as String?,
      repairDeviceId: fields[91] as int?,
      repairUpdatesNotif: fields[92] as int?,
      essentialsDuration: fields[93] as String?,
      essentialsDurationUnit: fields[94] as String?,
      essentialsAmountPerUnitDuration: fields[95] as String?,
      essentialsAllowances: fields[96] as String?,
      essentialsDeductions: fields[97] as String?,
      woocommerceOrderId: fields[98] as int?,
      importBatch: fields[99] as String?,
      importTime: fields[100] as String?,
      typesOfServiceId: fields[101] as int?,
      packingCharge: fields[102] as String?,
      packingChargeType: fields[103] as String?,
      serviceCustomField1: fields[104] as String?,
      serviceCustomField2: fields[105] as String?,
      serviceCustomField3: fields[106] as String?,
      serviceCustomField4: fields[107] as String?,
      serviceCustomField5: fields[108] as String?,
      serviceCustomField6: fields[109] as String?,
      isCreatedFromApi: fields[110] as int?,
      rpEarned: fields[111] as int?,
      orderAddresses: fields[112] as String?,
      isRecurring: fields[113] as int?,
      recurInterval: fields[114] as int?,
      recurIntervalType: fields[115] as String?,
      recurRepetitions: fields[116] as int?,
      recurStoppedOn: fields[117] as String?,
      recurParentId: fields[118] as int?,
      invoiceToken: fields[119] as String?,
      payTermNumber: fields[120] as int?,
      payTermType: fields[121] as String?,
      pjtProjectId: fields[122] as int?,
      pjtTitle: fields[123] as String?,
      sellingPriceGroupId: fields[124] as int?,
      createdAt: fields[125] as String?,
      updatedAt: fields[126] as String?,
      endDate: fields[127] as String?,
      shippingCompanyId: fields[128] as int?,
      packageCount: fields[129] as int?,
      paymobOrderId: fields[130] as int?,
      prevBalance: fields[131] as String?,
      subCounter: fields[132] as int?,
      customerId: fields[133] as int?,
      supplierId: fields[134] as int?,
      sellId: fields[135] as int?,
      purchaseId: fields[136] as int?,
      etaSubmissionId: fields[137] as int?,
      etaUuid: fields[138] as String?,
      etaLongId: fields[139] as int?,
      etaHashKey: fields[140] as String?,
      etaStatus: fields[141] as String?,
      etaNotes: fields[142] as String?,
      invoiceSchemeId: fields[143] as int?,
      customStatus: fields[144] as String?,
      isReservation: fields[145] as int?,
      reviewStatus: fields[146] as String?,
      reviewDetails: fields[147] as String?,
      settlementPurchaseId: fields[148] as int?,
      settlementSellId: fields[149] as int?,
      invoiceLayoutId: fields[150] as int?,
      invoiceCommision: fields[151] as String?,
      commisionAsLeader: fields[152] as String?,
      leaderId: fields[153] as int?,
      paymentLines: (fields[154] as List?)?.cast<PaymentLine>(),
      purchaseLines: (fields[155] as List?)?.cast<PurchaseLine>(),
      returnParentPurchase: fields[156] as Purchase?,
      locationInfo: fields[159] as LocationInfo?,
    );
  }

  @override
  void write(BinaryWriter writer, PurchaseReturn obj) {
    writer
      ..writeByte(160)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.businessId)
      ..writeByte(2)
      ..write(obj.locationId)
      ..writeByte(3)
      ..write(obj.resTableId)
      ..writeByte(4)
      ..write(obj.resWaiterId)
      ..writeByte(5)
      ..write(obj.resOrderStatus)
      ..writeByte(6)
      ..write(obj.type)
      ..writeByte(7)
      ..write(obj.subType)
      ..writeByte(8)
      ..write(obj.status)
      ..writeByte(9)
      ..write(obj.subStatus)
      ..writeByte(10)
      ..write(obj.isQuotation)
      ..writeByte(11)
      ..write(obj.paymentStatus)
      ..writeByte(12)
      ..write(obj.adjustmentType)
      ..writeByte(13)
      ..write(obj.contactId)
      ..writeByte(14)
      ..write(obj.customerGroupId)
      ..writeByte(15)
      ..write(obj.invoiceNo)
      ..writeByte(16)
      ..write(obj.refNo)
      ..writeByte(17)
      ..write(obj.source)
      ..writeByte(18)
      ..write(obj.subscriptionNo)
      ..writeByte(19)
      ..write(obj.subscriptionRepeatOn)
      ..writeByte(20)
      ..write(obj.transactionDate)
      ..writeByte(21)
      ..write(obj.totalBeforeTax)
      ..writeByte(22)
      ..write(obj.taxId)
      ..writeByte(23)
      ..write(obj.taxAmount)
      ..writeByte(24)
      ..write(obj.discountType)
      ..writeByte(25)
      ..write(obj.discountAmount)
      ..writeByte(26)
      ..write(obj.rpRedeemed)
      ..writeByte(27)
      ..write(obj.rpRedeemedAmount)
      ..writeByte(28)
      ..write(obj.shippingDetails)
      ..writeByte(29)
      ..write(obj.shippingAddress)
      ..writeByte(30)
      ..write(obj.deliveryDate)
      ..writeByte(31)
      ..write(obj.shippingStatus)
      ..writeByte(32)
      ..write(obj.deliveredTo)
      ..writeByte(33)
      ..write(obj.shippingCharges)
      ..writeByte(34)
      ..write(obj.shippingCustomField1)
      ..writeByte(35)
      ..write(obj.shippingCustomField2)
      ..writeByte(36)
      ..write(obj.shippingCustomField3)
      ..writeByte(37)
      ..write(obj.shippingCustomField4)
      ..writeByte(38)
      ..write(obj.shippingCustomField5)
      ..writeByte(39)
      ..write(obj.additionalNotes)
      ..writeByte(40)
      ..write(obj.staffNote)
      ..writeByte(41)
      ..write(obj.isExport)
      ..writeByte(42)
      ..write(obj.exportCustomFieldsInfo)
      ..writeByte(43)
      ..write(obj.roundOffAmount)
      ..writeByte(44)
      ..write(obj.additionalExpenseKey1)
      ..writeByte(45)
      ..write(obj.additionalExpenseValue1)
      ..writeByte(46)
      ..write(obj.additionalExpenseKey2)
      ..writeByte(47)
      ..write(obj.additionalExpenseValue2)
      ..writeByte(48)
      ..write(obj.additionalExpenseKey3)
      ..writeByte(49)
      ..write(obj.additionalExpenseValue3)
      ..writeByte(50)
      ..write(obj.additionalExpenseKey4)
      ..writeByte(51)
      ..write(obj.additionalExpenseValue4)
      ..writeByte(52)
      ..write(obj.finalTotal)
      ..writeByte(53)
      ..write(obj.expenseCategoryId)
      ..writeByte(54)
      ..write(obj.expenseFor)
      ..writeByte(55)
      ..write(obj.commissionAgent)
      ..writeByte(56)
      ..write(obj.document)
      ..writeByte(57)
      ..write(obj.isDirectSale)
      ..writeByte(58)
      ..write(obj.isSuspend)
      ..writeByte(59)
      ..write(obj.exchangeRate)
      ..writeByte(60)
      ..write(obj.totalAmountRecovered)
      ..writeByte(61)
      ..write(obj.transferParentId)
      ..writeByte(62)
      ..write(obj.returnParentId)
      ..writeByte(63)
      ..write(obj.openingStockProductId)
      ..writeByte(64)
      ..write(obj.createdBy)
      ..writeByte(65)
      ..write(obj.purchaseRequisitionIds)
      ..writeByte(66)
      ..write(obj.crmIsOrderRequest)
      ..writeByte(67)
      ..write(obj.preferPaymentMethod)
      ..writeByte(68)
      ..write(obj.preferPaymentAccount)
      ..writeByte(69)
      ..write(obj.salesOrderIds)
      ..writeByte(70)
      ..write(obj.purchaseOrderIds)
      ..writeByte(71)
      ..write(obj.customField1)
      ..writeByte(72)
      ..write(obj.customField2)
      ..writeByte(73)
      ..write(obj.customField3)
      ..writeByte(74)
      ..write(obj.customField4)
      ..writeByte(75)
      ..write(obj.mfgParentProductionPurchaseId)
      ..writeByte(76)
      ..write(obj.mfgWastedUnits)
      ..writeByte(77)
      ..write(obj.mfgProductionCost)
      ..writeByte(78)
      ..write(obj.mfgIsFinal)
      ..writeByte(79)
      ..write(obj.repairCompletedOn)
      ..writeByte(80)
      ..write(obj.repairWarrantyId)
      ..writeByte(81)
      ..write(obj.repairBrandId)
      ..writeByte(82)
      ..write(obj.repairStatusId)
      ..writeByte(83)
      ..write(obj.repairModelId)
      ..writeByte(84)
      ..write(obj.repairJobSheetId)
      ..writeByte(85)
      ..write(obj.repairDefects)
      ..writeByte(86)
      ..write(obj.repairSerialNo)
      ..writeByte(87)
      ..write(obj.repairChecklist)
      ..writeByte(88)
      ..write(obj.repairSecurityPwd)
      ..writeByte(89)
      ..write(obj.repairSecurityPattern)
      ..writeByte(90)
      ..write(obj.repairDueDate)
      ..writeByte(91)
      ..write(obj.repairDeviceId)
      ..writeByte(92)
      ..write(obj.repairUpdatesNotif)
      ..writeByte(93)
      ..write(obj.essentialsDuration)
      ..writeByte(94)
      ..write(obj.essentialsDurationUnit)
      ..writeByte(95)
      ..write(obj.essentialsAmountPerUnitDuration)
      ..writeByte(96)
      ..write(obj.essentialsAllowances)
      ..writeByte(97)
      ..write(obj.essentialsDeductions)
      ..writeByte(98)
      ..write(obj.woocommerceOrderId)
      ..writeByte(99)
      ..write(obj.importBatch)
      ..writeByte(100)
      ..write(obj.importTime)
      ..writeByte(101)
      ..write(obj.typesOfServiceId)
      ..writeByte(102)
      ..write(obj.packingCharge)
      ..writeByte(103)
      ..write(obj.packingChargeType)
      ..writeByte(104)
      ..write(obj.serviceCustomField1)
      ..writeByte(105)
      ..write(obj.serviceCustomField2)
      ..writeByte(106)
      ..write(obj.serviceCustomField3)
      ..writeByte(107)
      ..write(obj.serviceCustomField4)
      ..writeByte(108)
      ..write(obj.serviceCustomField5)
      ..writeByte(109)
      ..write(obj.serviceCustomField6)
      ..writeByte(110)
      ..write(obj.isCreatedFromApi)
      ..writeByte(111)
      ..write(obj.rpEarned)
      ..writeByte(112)
      ..write(obj.orderAddresses)
      ..writeByte(113)
      ..write(obj.isRecurring)
      ..writeByte(114)
      ..write(obj.recurInterval)
      ..writeByte(115)
      ..write(obj.recurIntervalType)
      ..writeByte(116)
      ..write(obj.recurRepetitions)
      ..writeByte(117)
      ..write(obj.recurStoppedOn)
      ..writeByte(118)
      ..write(obj.recurParentId)
      ..writeByte(119)
      ..write(obj.invoiceToken)
      ..writeByte(120)
      ..write(obj.payTermNumber)
      ..writeByte(121)
      ..write(obj.payTermType)
      ..writeByte(122)
      ..write(obj.pjtProjectId)
      ..writeByte(123)
      ..write(obj.pjtTitle)
      ..writeByte(124)
      ..write(obj.sellingPriceGroupId)
      ..writeByte(125)
      ..write(obj.createdAt)
      ..writeByte(126)
      ..write(obj.updatedAt)
      ..writeByte(127)
      ..write(obj.endDate)
      ..writeByte(128)
      ..write(obj.shippingCompanyId)
      ..writeByte(129)
      ..write(obj.packageCount)
      ..writeByte(130)
      ..write(obj.paymobOrderId)
      ..writeByte(131)
      ..write(obj.prevBalance)
      ..writeByte(132)
      ..write(obj.subCounter)
      ..writeByte(133)
      ..write(obj.customerId)
      ..writeByte(134)
      ..write(obj.supplierId)
      ..writeByte(135)
      ..write(obj.sellId)
      ..writeByte(136)
      ..write(obj.purchaseId)
      ..writeByte(137)
      ..write(obj.etaSubmissionId)
      ..writeByte(138)
      ..write(obj.etaUuid)
      ..writeByte(139)
      ..write(obj.etaLongId)
      ..writeByte(140)
      ..write(obj.etaHashKey)
      ..writeByte(141)
      ..write(obj.etaStatus)
      ..writeByte(142)
      ..write(obj.etaNotes)
      ..writeByte(143)
      ..write(obj.invoiceSchemeId)
      ..writeByte(144)
      ..write(obj.customStatus)
      ..writeByte(145)
      ..write(obj.isReservation)
      ..writeByte(146)
      ..write(obj.reviewStatus)
      ..writeByte(147)
      ..write(obj.reviewDetails)
      ..writeByte(148)
      ..write(obj.settlementPurchaseId)
      ..writeByte(149)
      ..write(obj.settlementSellId)
      ..writeByte(150)
      ..write(obj.invoiceLayoutId)
      ..writeByte(151)
      ..write(obj.invoiceCommision)
      ..writeByte(152)
      ..write(obj.commisionAsLeader)
      ..writeByte(153)
      ..write(obj.leaderId)
      ..writeByte(154)
      ..write(obj.paymentLines)
      ..writeByte(155)
      ..write(obj.purchaseLines)
      ..writeByte(156)
      ..write(obj.returnParentPurchase)
      ..writeByte(157)
      ..write(obj.offline)
      ..writeByte(158)
      ..write(obj.purchaseReturnToAPI)
      ..writeByte(159)
      ..write(obj.locationInfo);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PurchaseReturnAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PurchaseReturn _$PurchaseReturnFromJson(Map<String, dynamic> json) =>
    PurchaseReturn(
      id: (json['id'] as num?)?.toInt(),
      businessId: (json['business_id'] as num?)?.toInt(),
      locationId: (json['location_id'] as num?)?.toInt(),
      resTableId: (json['res_table_id'] as num?)?.toInt(),
      resWaiterId: (json['res_waiter_id'] as num?)?.toInt(),
      resOrderStatus: (json['res_order_status'] as num?)?.toInt(),
      type: json['type'] as String?,
      subType: json['sub_type'] as String?,
      status: json['status'] as String?,
      subStatus: json['sub_status'] as String?,
      isQuotation: (json['is_quotation'] as num?)?.toInt(),
      paymentStatus: json['payment_status'] as String?,
      adjustmentType: json['adjustment_type'] as String?,
      contactId: (json['contact_id'] as num?)?.toInt(),
      customerGroupId: (json['customer_group_id'] as num?)?.toInt(),
      invoiceNo: json['invoice_no'] as String?,
      refNo: json['ref_no'] as String?,
      source: json['source'] as String?,
      subscriptionNo: json['subscription_no'] as String?,
      subscriptionRepeatOn: json['subscription_repeat_on'] as String?,
      transactionDate: DateTime.parse(json['transaction_date'] as String),
      totalBeforeTax: json['total_before_tax'] as String?,
      taxId: (json['tax_id'] as num?)?.toInt(),
      taxAmount: json['tax_amount'] as String?,
      discountType: json['discount_type'] as String?,
      discountAmount: json['discount_amount'] as String?,
      rpRedeemed: (json['rp_redeemed'] as num?)?.toInt(),
      rpRedeemedAmount: json['rp_redeemed_amount'] as String?,
      shippingDetails: json['shipping_details'] as String?,
      shippingAddress: json['shipping_address'] as String?,
      deliveryDate: json['delivery_date'] as String?,
      shippingStatus: json['shipping_status'] as String?,
      deliveredTo: json['delivered_to'] as String?,
      shippingCharges: json['shipping_charges'] as String?,
      shippingCustomField1: json['shipping_custom_field_1'] as String?,
      shippingCustomField2: json['shipping_custom_field_2'] as String?,
      shippingCustomField3: json['shipping_custom_field_3'] as String?,
      shippingCustomField4: json['shipping_custom_field_4'] as String?,
      shippingCustomField5: json['shipping_custom_field_5'] as String?,
      additionalNotes: json['additional_notes'] as String?,
      staffNote: json['staff_note'] as String?,
      isExport: (json['is_export'] as num?)?.toInt(),
      exportCustomFieldsInfo: json['export_custom_fields_info'] as String?,
      roundOffAmount: json['round_off_amount'] as String?,
      additionalExpenseKey1: json['additional_expense_key_1'] as String?,
      additionalExpenseValue1: json['additional_expense_value_1'] as String?,
      additionalExpenseKey2: json['additional_expense_key_2'] as String?,
      additionalExpenseValue2: json['additional_expense_value_2'] as String?,
      additionalExpenseKey3: json['additional_expense_key_3'] as String?,
      additionalExpenseValue3: json['additional_expense_value_3'] as String?,
      additionalExpenseKey4: json['additional_expense_key_4'] as String?,
      additionalExpenseValue4: json['additional_expense_value_4'] as String?,
      finalTotal: json['final_total'] as String?,
      expenseCategoryId: (json['expense_category_id'] as num?)?.toInt(),
      expenseFor: json['expense_for'] as String?,
      commissionAgent: json['commission_agent'] as String?,
      document: json['document'] as String?,
      isDirectSale: (json['is_direct_sale'] as num?)?.toInt(),
      isSuspend: (json['is_suspend'] as num?)?.toInt(),
      exchangeRate: json['exchange_rate'] as String?,
      totalAmountRecovered: json['total_amount_recovered'] as String?,
      transferParentId: (json['transfer_parent_id'] as num?)?.toInt(),
      returnParentId: (json['return_parent_id'] as num?)?.toInt(),
      openingStockProductId:
          (json['opening_stock_product_id'] as num?)?.toInt(),
      createdBy: (json['created_by'] as num?)?.toInt(),
      purchaseRequisitionIds:
          (json['purchase_requisition_ids'] as List<dynamic>?)
              ?.map((e) => (e as num).toInt())
              .toList(),
      crmIsOrderRequest: (json['crm_is_order_request'] as num?)?.toInt(),
      preferPaymentMethod: json['prefer_payment_method'] as String?,
      preferPaymentAccount: json['prefer_payment_account'] as String?,
      salesOrderIds: (json['sales_order_ids'] as List<dynamic>?)
          ?.map((e) => (e as num).toInt())
          .toList(),
      purchaseOrderIds: (json['purchase_order_ids'] as List<dynamic>?)
          ?.map((e) => (e as num).toInt())
          .toList(),
      customField1: json['custom_field_1'] as String?,
      customField2: json['custom_field_2'] as String?,
      customField3: json['custom_field_3'] as String?,
      customField4: json['custom_field_4'] as String?,
      mfgParentProductionPurchaseId:
          (json['mfg_parent_production_purchase_id'] as num?)?.toInt(),
      mfgWastedUnits: (json['mfg_wasted_units'] as num?)?.toInt(),
      mfgProductionCost: json['mfg_production_cost'] as String?,
      mfgIsFinal: (json['mfg_is_final'] as num?)?.toInt(),
      repairCompletedOn: json['repair_completed_on'] as String?,
      repairWarrantyId: (json['repair_warranty_id'] as num?)?.toInt(),
      repairBrandId: (json['repair_brand_id'] as num?)?.toInt(),
      repairStatusId: (json['repair_status_id'] as num?)?.toInt(),
      repairModelId: (json['repair_model_id'] as num?)?.toInt(),
      repairJobSheetId: (json['repair_job_sheet_id'] as num?)?.toInt(),
      repairDefects: json['repair_defects'] as String?,
      repairSerialNo: json['repair_serial_no'] as String?,
      repairChecklist: json['repair_checklist'] as String?,
      repairSecurityPwd: json['repair_security_pwd'] as String?,
      repairSecurityPattern: json['repair_security_pattern'] as String?,
      repairDueDate: json['repair_due_date'] as String?,
      repairDeviceId: (json['repair_device_id'] as num?)?.toInt(),
      repairUpdatesNotif: (json['repair_updates_notif'] as num?)?.toInt(),
      essentialsDuration: json['essentials_duration'] as String?,
      essentialsDurationUnit: json['essentials_duration_unit'] as String?,
      essentialsAmountPerUnitDuration:
          json['essentials_amount_per_unit_duration'] as String?,
      essentialsAllowances: json['essentials_allowances'] as String?,
      essentialsDeductions: json['essentials_deductions'] as String?,
      woocommerceOrderId: (json['woocommerce_order_id'] as num?)?.toInt(),
      importBatch: json['import_batch'] as String?,
      importTime: json['import_time'] as String?,
      typesOfServiceId: (json['types_of_service_id'] as num?)?.toInt(),
      packingCharge: json['packing_charge'] as String?,
      packingChargeType: json['packing_charge_type'] as String?,
      serviceCustomField1: json['service_custom_field_1'] as String?,
      serviceCustomField2: json['service_custom_field_2'] as String?,
      serviceCustomField3: json['service_custom_field_3'] as String?,
      serviceCustomField4: json['service_custom_field_4'] as String?,
      serviceCustomField5: json['service_custom_field_5'] as String?,
      serviceCustomField6: json['service_custom_field_6'] as String?,
      isCreatedFromApi: (json['is_created_from_api'] as num?)?.toInt(),
      rpEarned: (json['rp_earned'] as num?)?.toInt(),
      orderAddresses: json['order_addresses'] as String?,
      isRecurring: (json['is_recurring'] as num?)?.toInt(),
      recurInterval: (json['recur_interval'] as num?)?.toInt(),
      recurIntervalType: json['recur_interval_type'] as String?,
      recurRepetitions: (json['recur_repetitions'] as num?)?.toInt(),
      recurStoppedOn: json['recur_stopped_on'] as String?,
      recurParentId: (json['recur_parent_id'] as num?)?.toInt(),
      invoiceToken: json['invoice_token'] as String?,
      payTermNumber: (json['pay_term_number'] as num?)?.toInt(),
      payTermType: json['pay_term_type'] as String?,
      pjtProjectId: (json['pjt_project_id'] as num?)?.toInt(),
      pjtTitle: json['pjt_title'] as String?,
      sellingPriceGroupId: (json['selling_price_group_id'] as num?)?.toInt(),
      createdAt: json['created_at'] as String?,
      updatedAt: json['updated_at'] as String?,
      endDate: json['end_date'] as String?,
      shippingCompanyId: (json['shipping_company_id'] as num?)?.toInt(),
      packageCount: (json['package_count'] as num?)?.toInt(),
      paymobOrderId: (json['paymob_order_id'] as num?)?.toInt(),
      prevBalance: json['prev_balance'] as String?,
      subCounter: (json['sub_counter'] as num?)?.toInt(),
      customerId: (json['customer_id'] as num?)?.toInt(),
      supplierId: (json['supplier_id'] as num?)?.toInt(),
      sellId: (json['sell_id'] as num?)?.toInt(),
      purchaseId: (json['purchase_id'] as num?)?.toInt(),
      etaSubmissionId: (json['eta_submissionId'] as num?)?.toInt(),
      etaUuid: json['eta_uuid'] as String?,
      etaLongId: (json['eta_longId'] as num?)?.toInt(),
      etaHashKey: json['eta_hashKey'] as String?,
      etaStatus: json['eta_status'] as String?,
      etaNotes: json['eta_notes'] as String?,
      invoiceSchemeId: (json['invoice_scheme_id'] as num?)?.toInt(),
      customStatus: json['custom_status'] as String?,
      isReservation: (json['is_reservation'] as num?)?.toInt(),
      reviewStatus: json['review_status'] as String?,
      reviewDetails: json['review_details'] as String?,
      settlementPurchaseId: (json['settlement_purchase_id'] as num?)?.toInt(),
      settlementSellId: (json['settlement_sell_id'] as num?)?.toInt(),
      invoiceLayoutId: (json['invoice_layout_id'] as num?)?.toInt(),
      invoiceCommision: json['invoice_commision'] as String?,
      commisionAsLeader: json['commision_as_leader'] as String?,
      leaderId: (json['leader_id'] as num?)?.toInt(),
      paymentLines: PurchaseReturn._paymentLinesFromJsonList(
          json['payment_lines'] as List?),
      purchaseLines: PurchaseReturn._purchaseLinesFromJsonList(
          json['purchase_lines'] as List?),
      returnParentPurchase:
          PurchaseReturn._returnPPFromJsonList(json['return_parent_purchase']),
      locationInfo: locationInfoFromListJson(json['location_info'] as List?),
    );

Map<String, dynamic> _$PurchaseReturnToJson(PurchaseReturn instance) =>
    <String, dynamic>{
      'id': instance.id,
      'business_id': instance.businessId,
      'location_id': instance.locationId,
      'res_table_id': instance.resTableId,
      'res_waiter_id': instance.resWaiterId,
      'res_order_status': instance.resOrderStatus,
      'type': instance.type,
      'sub_type': instance.subType,
      'status': instance.status,
      'sub_status': instance.subStatus,
      'is_quotation': instance.isQuotation,
      'payment_status': instance.paymentStatus,
      'adjustment_type': instance.adjustmentType,
      'contact_id': instance.contactId,
      'customer_group_id': instance.customerGroupId,
      'invoice_no': instance.invoiceNo,
      'ref_no': instance.refNo,
      'source': instance.source,
      'subscription_no': instance.subscriptionNo,
      'subscription_repeat_on': instance.subscriptionRepeatOn,
      'transaction_date': dateToString(instance.transactionDate),
      'total_before_tax': instance.totalBeforeTax,
      'tax_id': instance.taxId,
      'tax_amount': instance.taxAmount,
      'discount_type': instance.discountType,
      'discount_amount': instance.discountAmount,
      'rp_redeemed': instance.rpRedeemed,
      'rp_redeemed_amount': instance.rpRedeemedAmount,
      'shipping_details': instance.shippingDetails,
      'shipping_address': instance.shippingAddress,
      'delivery_date': instance.deliveryDate,
      'shipping_status': instance.shippingStatus,
      'delivered_to': instance.deliveredTo,
      'shipping_charges': instance.shippingCharges,
      'shipping_custom_field_1': instance.shippingCustomField1,
      'shipping_custom_field_2': instance.shippingCustomField2,
      'shipping_custom_field_3': instance.shippingCustomField3,
      'shipping_custom_field_4': instance.shippingCustomField4,
      'shipping_custom_field_5': instance.shippingCustomField5,
      'additional_notes': instance.additionalNotes,
      'staff_note': instance.staffNote,
      'is_export': instance.isExport,
      'export_custom_fields_info': instance.exportCustomFieldsInfo,
      'round_off_amount': instance.roundOffAmount,
      'additional_expense_key_1': instance.additionalExpenseKey1,
      'additional_expense_value_1': instance.additionalExpenseValue1,
      'additional_expense_key_2': instance.additionalExpenseKey2,
      'additional_expense_value_2': instance.additionalExpenseValue2,
      'additional_expense_key_3': instance.additionalExpenseKey3,
      'additional_expense_value_3': instance.additionalExpenseValue3,
      'additional_expense_key_4': instance.additionalExpenseKey4,
      'additional_expense_value_4': instance.additionalExpenseValue4,
      'final_total': instance.finalTotal,
      'expense_category_id': instance.expenseCategoryId,
      'expense_for': instance.expenseFor,
      'commission_agent': instance.commissionAgent,
      'document': instance.document,
      'is_direct_sale': instance.isDirectSale,
      'is_suspend': instance.isSuspend,
      'exchange_rate': instance.exchangeRate,
      'total_amount_recovered': instance.totalAmountRecovered,
      'transfer_parent_id': instance.transferParentId,
      'return_parent_id': instance.returnParentId,
      'opening_stock_product_id': instance.openingStockProductId,
      'created_by': instance.createdBy,
      'purchase_requisition_ids': instance.purchaseRequisitionIds,
      'crm_is_order_request': instance.crmIsOrderRequest,
      'prefer_payment_method': instance.preferPaymentMethod,
      'prefer_payment_account': instance.preferPaymentAccount,
      'sales_order_ids': instance.salesOrderIds,
      'purchase_order_ids': instance.purchaseOrderIds,
      'custom_field_1': instance.customField1,
      'custom_field_2': instance.customField2,
      'custom_field_3': instance.customField3,
      'custom_field_4': instance.customField4,
      'mfg_parent_production_purchase_id':
          instance.mfgParentProductionPurchaseId,
      'mfg_wasted_units': instance.mfgWastedUnits,
      'mfg_production_cost': instance.mfgProductionCost,
      'mfg_is_final': instance.mfgIsFinal,
      'repair_completed_on': instance.repairCompletedOn,
      'repair_warranty_id': instance.repairWarrantyId,
      'repair_brand_id': instance.repairBrandId,
      'repair_status_id': instance.repairStatusId,
      'repair_model_id': instance.repairModelId,
      'repair_job_sheet_id': instance.repairJobSheetId,
      'repair_defects': instance.repairDefects,
      'repair_serial_no': instance.repairSerialNo,
      'repair_checklist': instance.repairChecklist,
      'repair_security_pwd': instance.repairSecurityPwd,
      'repair_security_pattern': instance.repairSecurityPattern,
      'repair_due_date': instance.repairDueDate,
      'repair_device_id': instance.repairDeviceId,
      'repair_updates_notif': instance.repairUpdatesNotif,
      'essentials_duration': instance.essentialsDuration,
      'essentials_duration_unit': instance.essentialsDurationUnit,
      'essentials_amount_per_unit_duration':
          instance.essentialsAmountPerUnitDuration,
      'essentials_allowances': instance.essentialsAllowances,
      'essentials_deductions': instance.essentialsDeductions,
      'woocommerce_order_id': instance.woocommerceOrderId,
      'import_batch': instance.importBatch,
      'import_time': instance.importTime,
      'types_of_service_id': instance.typesOfServiceId,
      'packing_charge': instance.packingCharge,
      'packing_charge_type': instance.packingChargeType,
      'service_custom_field_1': instance.serviceCustomField1,
      'service_custom_field_2': instance.serviceCustomField2,
      'service_custom_field_3': instance.serviceCustomField3,
      'service_custom_field_4': instance.serviceCustomField4,
      'service_custom_field_5': instance.serviceCustomField5,
      'service_custom_field_6': instance.serviceCustomField6,
      'is_created_from_api': instance.isCreatedFromApi,
      'rp_earned': instance.rpEarned,
      'order_addresses': instance.orderAddresses,
      'is_recurring': instance.isRecurring,
      'recur_interval': instance.recurInterval,
      'recur_interval_type': instance.recurIntervalType,
      'recur_repetitions': instance.recurRepetitions,
      'recur_stopped_on': instance.recurStoppedOn,
      'recur_parent_id': instance.recurParentId,
      'invoice_token': instance.invoiceToken,
      'pay_term_number': instance.payTermNumber,
      'pay_term_type': instance.payTermType,
      'pjt_project_id': instance.pjtProjectId,
      'pjt_title': instance.pjtTitle,
      'selling_price_group_id': instance.sellingPriceGroupId,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
      'end_date': instance.endDate,
      'shipping_company_id': instance.shippingCompanyId,
      'package_count': instance.packageCount,
      'paymob_order_id': instance.paymobOrderId,
      'prev_balance': instance.prevBalance,
      'sub_counter': instance.subCounter,
      'customer_id': instance.customerId,
      'supplier_id': instance.supplierId,
      'sell_id': instance.sellId,
      'purchase_id': instance.purchaseId,
      'eta_submissionId': instance.etaSubmissionId,
      'eta_uuid': instance.etaUuid,
      'eta_longId': instance.etaLongId,
      'eta_hashKey': instance.etaHashKey,
      'eta_status': instance.etaStatus,
      'eta_notes': instance.etaNotes,
      'invoice_scheme_id': instance.invoiceSchemeId,
      'custom_status': instance.customStatus,
      'is_reservation': instance.isReservation,
      'review_status': instance.reviewStatus,
      'review_details': instance.reviewDetails,
      'settlement_purchase_id': instance.settlementPurchaseId,
      'settlement_sell_id': instance.settlementSellId,
      'invoice_layout_id': instance.invoiceLayoutId,
      'invoice_commision': instance.invoiceCommision,
      'commision_as_leader': instance.commisionAsLeader,
      'leader_id': instance.leaderId,
      'payment_lines':
          PurchaseReturn._paymentLinesToJsonList(instance.paymentLines),
      'purchase_lines':
          PurchaseReturn._purchaseLinesToJsonList(instance.purchaseLines),
      'return_parent_purchase': instance.returnParentPurchase,
      'location_info': instance.locationInfo,
    };
