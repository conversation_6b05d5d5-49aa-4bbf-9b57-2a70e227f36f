// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'sell_to_api.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class SellToAPIAdapter extends TypeAdapter<SellToAPI> {
  @override
  final int typeId = 22;

  @override
  SellToAPI read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return SellToAPI(
      locationId: fields[0] as int,
      contactId: fields[1] as int,
      transactionDate: fields[2] as String,
      invoiceNo: fields[3] as String?,
      source: fields[4] as String?,
      status: fields[5] as String,
      subStatus: fields[6] as String?,
      isQuotation: fields[7] as int?,
      taxRateId: fields[8] as int?,
      discountAmount: fields[9] as double,
      discountType: fields[10] as String?,
      saleNote: fields[11] as String?,
      staffNote: fields[12] as String?,
      commissionAgent: fields[13] as int?,
      shippingDetails: fields[14] as String?,
      shippingAddress: fields[15] as String?,
      shippingStatus: fields[16] as String?,
      deliveredTo: fields[17] as String?,
      shippingCharges: fields[18] as double?,
      packingCharge: fields[19] as double?,
      exchangeRate: fields[20] as int?,
      sellingPriceGroupId: fields[21] as int?,
      payTermNumber: fields[22] as int?,
      payTermType: fields[23] as String?,
      isSuspend: fields[24] as bool?,
      isRecurring: fields[25] as int?,
      recurInterval: fields[26] as int?,
      recurIntervalType: fields[27] as String?,
      subscriptionRepeatOn: fields[28] as int?,
      subscriptionNo: fields[29] as String?,
      recurRepetitions: fields[30] as int?,
      rpRedeemed: fields[31] as int?,
      rpRedeemedAmount: fields[32] as double?,
      typesOfServiceId: fields[33] as int?,
      serviceCustomField1: fields[34] as String?,
      serviceCustomField2: fields[35] as String?,
      serviceCustomField3: fields[36] as String?,
      serviceCustomField4: fields[37] as String?,
      serviceCustomField5: fields[38] as String?,
      serviceCustomField6: fields[39] as String?,
      roundOffAmount: fields[40] as double?,
      tableId: fields[41] as int?,
      serviceStaffId: fields[42] as int?,
      changeReturn: fields[43] as double?,
      products: (fields[44] as List).cast<ProductToAPI>(),
      payments: (fields[45] as List).cast<Payment>(),
      invoiceAmount: fields[46] as String,
      offlineID: fields[47] as String?,
      locationInfo: fields[48] as LocationInfo?,
      packingChargeType: fields[49] as String?,
      shippingCompanyId: fields[50] as int?,
    );
  }

  @override
  void write(BinaryWriter writer, SellToAPI obj) {
    writer
      ..writeByte(51)
      ..writeByte(0)
      ..write(obj.locationId)
      ..writeByte(1)
      ..write(obj.contactId)
      ..writeByte(2)
      ..write(obj.transactionDate)
      ..writeByte(3)
      ..write(obj.invoiceNo)
      ..writeByte(4)
      ..write(obj.source)
      ..writeByte(5)
      ..write(obj.status)
      ..writeByte(6)
      ..write(obj.subStatus)
      ..writeByte(7)
      ..write(obj.isQuotation)
      ..writeByte(8)
      ..write(obj.taxRateId)
      ..writeByte(9)
      ..write(obj.discountAmount)
      ..writeByte(10)
      ..write(obj.discountType)
      ..writeByte(11)
      ..write(obj.saleNote)
      ..writeByte(12)
      ..write(obj.staffNote)
      ..writeByte(13)
      ..write(obj.commissionAgent)
      ..writeByte(14)
      ..write(obj.shippingDetails)
      ..writeByte(15)
      ..write(obj.shippingAddress)
      ..writeByte(16)
      ..write(obj.shippingStatus)
      ..writeByte(17)
      ..write(obj.deliveredTo)
      ..writeByte(18)
      ..write(obj.shippingCharges)
      ..writeByte(19)
      ..write(obj.packingCharge)
      ..writeByte(20)
      ..write(obj.exchangeRate)
      ..writeByte(21)
      ..write(obj.sellingPriceGroupId)
      ..writeByte(22)
      ..write(obj.payTermNumber)
      ..writeByte(23)
      ..write(obj.payTermType)
      ..writeByte(24)
      ..write(obj.isSuspend)
      ..writeByte(25)
      ..write(obj.isRecurring)
      ..writeByte(26)
      ..write(obj.recurInterval)
      ..writeByte(27)
      ..write(obj.recurIntervalType)
      ..writeByte(28)
      ..write(obj.subscriptionRepeatOn)
      ..writeByte(29)
      ..write(obj.subscriptionNo)
      ..writeByte(30)
      ..write(obj.recurRepetitions)
      ..writeByte(31)
      ..write(obj.rpRedeemed)
      ..writeByte(32)
      ..write(obj.rpRedeemedAmount)
      ..writeByte(33)
      ..write(obj.typesOfServiceId)
      ..writeByte(34)
      ..write(obj.serviceCustomField1)
      ..writeByte(35)
      ..write(obj.serviceCustomField2)
      ..writeByte(36)
      ..write(obj.serviceCustomField3)
      ..writeByte(37)
      ..write(obj.serviceCustomField4)
      ..writeByte(38)
      ..write(obj.serviceCustomField5)
      ..writeByte(39)
      ..write(obj.serviceCustomField6)
      ..writeByte(40)
      ..write(obj.roundOffAmount)
      ..writeByte(41)
      ..write(obj.tableId)
      ..writeByte(42)
      ..write(obj.serviceStaffId)
      ..writeByte(43)
      ..write(obj.changeReturn)
      ..writeByte(44)
      ..write(obj.products)
      ..writeByte(45)
      ..write(obj.payments)
      ..writeByte(46)
      ..write(obj.invoiceAmount)
      ..writeByte(47)
      ..write(obj.offlineID)
      ..writeByte(48)
      ..write(obj.locationInfo)
      ..writeByte(49)
      ..write(obj.packingChargeType)
      ..writeByte(50)
      ..write(obj.shippingCompanyId);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SellToAPIAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ProductToAPIAdapter extends TypeAdapter<ProductToAPI> {
  @override
  final int typeId = 23;

  @override
  ProductToAPI read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ProductToAPI(
      productId: fields[0] as int,
      variationId: fields[1] as int?,
      quantity: fields[2] as double,
      unitPrice: fields[3] as double,
      taxRateId: fields[4] as int?,
      discountAmount: fields[5] as double,
      discountType: fields[6] as String?,
      subUnitId: fields[7] as int,
      note: fields[8] as String,
      unitWeight: fields[9] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, ProductToAPI obj) {
    writer
      ..writeByte(10)
      ..writeByte(0)
      ..write(obj.productId)
      ..writeByte(1)
      ..write(obj.variationId)
      ..writeByte(2)
      ..write(obj.quantity)
      ..writeByte(3)
      ..write(obj.unitPrice)
      ..writeByte(4)
      ..write(obj.taxRateId)
      ..writeByte(5)
      ..write(obj.discountAmount)
      ..writeByte(6)
      ..write(obj.discountType)
      ..writeByte(7)
      ..write(obj.subUnitId)
      ..writeByte(8)
      ..write(obj.note)
      ..writeByte(9)
      ..write(obj.unitWeight);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ProductToAPIAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SellToAPI _$SellToAPIFromJson(Map<String, dynamic> json) => SellToAPI(
      locationId: (json['location_id'] as num).toInt(),
      contactId: (json['contact_id'] as num).toInt(),
      transactionDate: json['transaction_date'] as String,
      invoiceNo: json['invoice_no'] as String?,
      source: json['source'] as String?,
      status: json['status'] as String,
      subStatus: json['sub_status'] as String?,
      isQuotation: (json['is_quotation'] as num?)?.toInt(),
      taxRateId: (json['tax_rate_id'] as num?)?.toInt(),
      discountAmount: (json['discount_amount'] as num).toDouble(),
      discountType: json['discount_type'] as String?,
      saleNote: json['sale_note'] as String?,
      staffNote: json['staff_note'] as String?,
      commissionAgent: (json['commission_agent'] as num?)?.toInt(),
      shippingDetails: json['shipping_details'] as String?,
      shippingAddress: json['shipping_address'] as String?,
      shippingStatus: json['shipping_status'] as String?,
      deliveredTo: json['delivered_to'] as String?,
      shippingCharges: (json['shipping_charges'] as num?)?.toDouble(),
      packingCharge: (json['packing_charge'] as num?)?.toDouble(),
      exchangeRate: (json['exchange_rate'] as num?)?.toInt(),
      sellingPriceGroupId: (json['selling_price_group_id'] as num?)?.toInt(),
      payTermNumber: (json['pay_term_number'] as num?)?.toInt(),
      payTermType: json['pay_term_type'] as String?,
      isSuspend: json['is_suspend'] as bool?,
      isRecurring: (json['is_recurring'] as num?)?.toInt(),
      recurInterval: (json['recur_interval'] as num?)?.toInt(),
      recurIntervalType: json['recur_interval_type'] as String?,
      subscriptionRepeatOn: (json['subscription_repeat_on'] as num?)?.toInt(),
      subscriptionNo: json['subscription_no'] as String?,
      recurRepetitions: (json['recur_repetitions'] as num?)?.toInt(),
      rpRedeemed: (json['rp_redeemed'] as num?)?.toInt(),
      rpRedeemedAmount: (json['rp_redeemed_amount'] as num?)?.toDouble(),
      typesOfServiceId: (json['types_of_service_id'] as num?)?.toInt(),
      serviceCustomField1: json['service_custom_field_1'] as String?,
      serviceCustomField2: json['service_custom_field_2'] as String?,
      serviceCustomField3: json['service_custom_field_3'] as String?,
      serviceCustomField4: json['service_custom_field_4'] as String?,
      serviceCustomField5: json['service_custom_field_5'] as String?,
      serviceCustomField6: json['service_custom_field_6'] as String?,
      roundOffAmount: (json['round_off_amount'] as num?)?.toDouble(),
      tableId: (json['table_id'] as num?)?.toInt(),
      serviceStaffId: (json['service_staff_id'] as num?)?.toInt(),
      changeReturn: (json['change_return'] as num?)?.toDouble(),
      products: SellToAPI._productsFromJson(
          json['products'] as List<Map<String, dynamic>>),
      payments: SellToAPI._fromJsonList(
          json['payments'] as List<Map<String, dynamic>>),
      locationInfo: json['location_info'] == null
          ? null
          : LocationInfo.fromJson(
              json['location_info'] as Map<String, dynamic>),
      packingChargeType: json['packing_charge_type'] as String?,
      shippingCompanyId: (json['shipping_company_id'] as num?)?.toInt(),
    );

Map<String, dynamic> _$SellToAPIToJson(SellToAPI instance) => <String, dynamic>{
      'location_id': instance.locationId,
      'contact_id': instance.contactId,
      'transaction_date': instance.transactionDate,
      'invoice_no': instance.invoiceNo,
      'source': instance.source,
      'status': instance.status,
      'sub_status': instance.subStatus,
      'is_quotation': instance.isQuotation,
      'tax_rate_id': instance.taxRateId,
      'discount_amount': instance.discountAmount,
      'discount_type': instance.discountType,
      'sale_note': instance.saleNote,
      'staff_note': instance.staffNote,
      'commission_agent': instance.commissionAgent,
      'shipping_details': instance.shippingDetails,
      'shipping_address': instance.shippingAddress,
      'shipping_status': instance.shippingStatus,
      'delivered_to': instance.deliveredTo,
      'shipping_charges': instance.shippingCharges,
      'packing_charge': instance.packingCharge,
      'exchange_rate': instance.exchangeRate,
      'selling_price_group_id': instance.sellingPriceGroupId,
      'pay_term_number': instance.payTermNumber,
      'pay_term_type': instance.payTermType,
      'is_suspend': instance.isSuspend,
      'is_recurring': instance.isRecurring,
      'recur_interval': instance.recurInterval,
      'recur_interval_type': instance.recurIntervalType,
      'subscription_repeat_on': instance.subscriptionRepeatOn,
      'subscription_no': instance.subscriptionNo,
      'recur_repetitions': instance.recurRepetitions,
      'rp_redeemed': instance.rpRedeemed,
      'rp_redeemed_amount': instance.rpRedeemedAmount,
      'types_of_service_id': instance.typesOfServiceId,
      'service_custom_field_1': instance.serviceCustomField1,
      'service_custom_field_2': instance.serviceCustomField2,
      'service_custom_field_3': instance.serviceCustomField3,
      'service_custom_field_4': instance.serviceCustomField4,
      'service_custom_field_5': instance.serviceCustomField5,
      'service_custom_field_6': instance.serviceCustomField6,
      'round_off_amount': instance.roundOffAmount,
      'table_id': instance.tableId,
      'service_staff_id': instance.serviceStaffId,
      'change_return': instance.changeReturn,
      'products': SellToAPI._productsToJson(instance.products),
      'payments': SellToAPI._toJsonList(instance.payments),
      'location_info': locationInfoToJson(instance.locationInfo),
      'packing_charge_type': instance.packingChargeType,
      'shipping_company_id': instance.shippingCompanyId,
    };

ProductToAPI _$ProductToAPIFromJson(Map<String, dynamic> json) => ProductToAPI(
      productId: (json['product_id'] as num).toInt(),
      variationId: (json['variation_id'] as num?)?.toInt(),
      quantity: (json['quantity'] as num).toDouble(),
      unitPrice: (json['unit_price'] as num).toDouble(),
      taxRateId: (json['tax_rate_id'] as num?)?.toInt(),
      discountAmount: (json['discount_amount'] as num).toDouble(),
      discountType: json['discount_type'] as String?,
      subUnitId: (json['sub_unit_id'] as num).toInt(),
      note: json['note'] as String? ?? "",
      unitWeight: json['unit_weight'] as String?,
    );

Map<String, dynamic> _$ProductToAPIToJson(ProductToAPI instance) =>
    <String, dynamic>{
      'product_id': removeLastDigit(instance.productId),
      'variation_id': instance.variationId,
      'quantity': instance.quantity,
      'unit_price': instance.unitPrice,
      'tax_rate_id': instance.taxRateId,
      'discount_amount': instance.discountAmount,
      'discount_type': instance.discountType,
      'sub_unit_id': instance.subUnitId,
      'note': instance.note,
      'unit_weight': instance.unitWeight,
    };
