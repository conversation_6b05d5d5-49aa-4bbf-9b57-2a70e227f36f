// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'shift_details.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ShiftDetails _$ShiftDetailsFromJson(Map<String, dynamic> json) => ShiftDetails(
      payments: (json['payments'] as List<dynamic>)
          .map((e) => ShiftPayment.fromJson(e as Map<String, dynamic>))
          .toList(),
      transactions: (json['transactions'] as List<dynamic>)
          .map((e) => Transaction.fromJson(e as Map<String, dynamic>))
          .toList(),
      notRelatedPayments: (json['not_related_payments'] as List<dynamic>)
          .map((e) => NotRelatedPayment.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$ShiftDetailsToJson(ShiftDetails instance) =>
    <String, dynamic>{
      'payments': instance.payments,
      'transactions': instance.transactions,
      'not_related_payments': instance.notRelatedPayments,
    };

ShiftPayment _$ShiftPaymentFromJson(Map<String, dynamic> json) => ShiftPayment(
      totalSellReturnPaid: json['total_sell_return_paid'] as String,
      totalPurchaseReturnPaid: json['total_purchase_return_paid'] as String,
      totalSellPaid: json['total_sell_paid'] as String,
      totalPurchasePaid: json['total_purchase_paid'] as String,
      totalExpensePaid: json['total_expense_paid'] as String,
      totalExpenseRefundPaid: json['total_expense_refund_paid'] as String,
    );

Map<String, dynamic> _$ShiftPaymentToJson(ShiftPayment instance) =>
    <String, dynamic>{
      'total_sell_return_paid': instance.totalSellReturnPaid,
      'total_purchase_return_paid': instance.totalPurchaseReturnPaid,
      'total_sell_paid': instance.totalSellPaid,
      'total_purchase_paid': instance.totalPurchasePaid,
      'total_expense_paid': instance.totalExpensePaid,
      'total_expense_refund_paid': instance.totalExpenseRefundPaid,
    };

Transaction _$TransactionFromJson(Map<String, dynamic> json) => Transaction(
      totalSellReturn: json['total_sell_return'] as String,
      totalPurchaseReturn: json['total_purchase_return'] as String,
      totalSell: json['total_sell'] as String,
      totalPurchase: json['total_purchase'] as String,
      totalExpense: json['total_expense'] as String,
      totalExpenseRefund: json['total_expense_refund'] as String,
    );

Map<String, dynamic> _$TransactionToJson(Transaction instance) =>
    <String, dynamic>{
      'total_sell_return': instance.totalSellReturn,
      'total_purchase_return': instance.totalPurchaseReturn,
      'total_sell': instance.totalSell,
      'total_purchase': instance.totalPurchase,
      'total_expense': instance.totalExpense,
      'total_expense_refund': instance.totalExpenseRefund,
    };

NotRelatedPayment _$NotRelatedPaymentFromJson(Map<String, dynamic> json) =>
    NotRelatedPayment(
      totalSupplierPaid: json['total_supplier_paid'] as String,
      totalCustomerPaid: json['total_customer_paid'] as String,
    );

Map<String, dynamic> _$NotRelatedPaymentToJson(NotRelatedPayment instance) =>
    <String, dynamic>{
      'total_supplier_paid': instance.totalSupplierPaid,
      'total_customer_paid': instance.totalCustomerPaid,
    };
