// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'selling_price_group.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class SellingPriceGroupAdapter extends TypeAdapter<SellingPriceGroup> {
  @override
  final int typeId = 17;

  @override
  SellingPriceGroup read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return SellingPriceGroup(
      id: fields[0] as int,
      name: fields[1] as String,
      description: fields[2] as String?,
      businessId: fields[3] as int,
      isActive: fields[4] as int,
      deletedAt: fields[5] as DateTime?,
      createdAt: fields[6] as DateTime,
      updatedAt: fields[7] as DateTime,
    );
  }

  @override
  void write(BinaryWriter writer, SellingPriceGroup obj) {
    writer
      ..writeByte(8)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.description)
      ..writeByte(3)
      ..write(obj.businessId)
      ..writeByte(4)
      ..write(obj.isActive)
      ..writeByte(5)
      ..write(obj.deletedAt)
      ..writeByte(6)
      ..write(obj.createdAt)
      ..writeByte(7)
      ..write(obj.updatedAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SellingPriceGroupAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
