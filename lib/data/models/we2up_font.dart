class We2upFont {
  final String arabicName;
  final String englishName;
  final String fontPath;
  final String fontFamily;

  We2upFont({
    required this.arabicName,
    required this.englishName,
    required this.fontPath,
    required this.fontFamily,
  });
}

List<We2upFont> we2upFonts = [
  We2upFont(
    arabicName: "المراعي",
    englishName: "Almarai",
    fontPath: 'fonts/Almarai-Regular.ttf',
    fontFamily: 'Almarai',
  ),
  We2upFont(
    arabicName: "الأميري",
    englishName: "Amiri",
    fontPath: 'fonts/Amiri-Regular.ttf',
    fontFamily: 'Amiri',
  ),
  We2upFont(
    arabicName: "إيريال",
    englishName: "Arial",
    fontPath: 'fonts/arial.ttf',
    fontFamily: 'Arial',
  ),
  We2upFont(
    arabicName: "إيريال عريض",
    englishName: "Arial Bold",
    fontPath: 'fonts/arial-bold.ttf',
    fontFamily: 'Arial',
  ),
  We2upFont(
    arabicName: "تشانجا",
    englishName: "Changa",
    fontPath: 'fonts/Changa-Regular.ttf',
    fontFamily: 'Changa',
  ),
  We2upFont(
    arabicName: "المصري",
    englishName: "ElMessiri",
    fontPath: 'fonts/ElMessiri-Regular.ttf',
    fontFamily: 'ElMessiri',
  ),
  We2upFont(
    arabicName: "لاليزار",
    englishName: "Lalezar",
    fontPath: 'fonts/Lalezar-Regular.ttf',
    fontFamily: 'Lalezar',
  ),
];
