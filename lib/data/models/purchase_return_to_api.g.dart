// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'purchase_return_to_api.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class PurchaseReturnToAPIAdapter extends TypeAdapter<PurchaseReturnToAPI> {
  @override
  final int typeId = 39;

  @override
  PurchaseReturnToAPI read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return PurchaseReturnToAPI(
      transactionId: fields[0] as String,
      refNo: fields[1] as String?,
      products: (fields[2] as List).cast<ProductPurchaseReturn>(),
      taxId: fields[3] as int?,
      taxAmount: fields[4] as String?,
      taxPercent: fields[5] as String?,
      locationInfo: fields[7] as LocationInfo?,
    );
  }

  @override
  void write(BinaryWriter writer, PurchaseReturnToAPI obj) {
    writer
      ..writeByte(7)
      ..writeByte(0)
      ..write(obj.transactionId)
      ..writeByte(1)
      ..write(obj.refNo)
      ..writeByte(2)
      ..write(obj.products)
      ..writeByte(3)
      ..write(obj.taxId)
      ..writeByte(4)
      ..write(obj.taxAmount)
      ..writeByte(5)
      ..write(obj.taxPercent)
      ..writeByte(7)
      ..write(obj.locationInfo);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PurchaseReturnToAPIAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ProductPurchaseReturnAdapter extends TypeAdapter<ProductPurchaseReturn> {
  @override
  final int typeId = 40;

  @override
  ProductPurchaseReturn read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ProductPurchaseReturn(
      purchaseLineId: fields[0] as String,
      returnedQuantity: fields[1] as String,
    );
  }

  @override
  void write(BinaryWriter writer, ProductPurchaseReturn obj) {
    writer
      ..writeByte(2)
      ..writeByte(0)
      ..write(obj.purchaseLineId)
      ..writeByte(1)
      ..write(obj.returnedQuantity);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ProductPurchaseReturnAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PurchaseReturnToAPI _$PurchaseReturnToAPIFromJson(Map<String, dynamic> json) =>
    PurchaseReturnToAPI(
      transactionId: json['transaction_id'] as String,
      refNo: json['ref_no'] as String?,
      products: (json['products'] as List<dynamic>)
          .map((e) => ProductPurchaseReturn.fromJson(e as Map<String, dynamic>))
          .toList(),
      taxId: (json['tax_id'] as num?)?.toInt(),
      taxAmount: json['tax_amount'] as String?,
      taxPercent: json['tax_percent'] as String?,
      locationInfo: json['location_info'] == null
          ? null
          : LocationInfo.fromJson(
              json['location_info'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$PurchaseReturnToAPIToJson(
        PurchaseReturnToAPI instance) =>
    <String, dynamic>{
      'transaction_id': instance.transactionId,
      'ref_no': instance.refNo,
      'products': PurchaseReturnToAPI._toJsonList(instance.products),
      'tax_id': instance.taxId,
      'tax_amount': instance.taxAmount,
      'tax_percent': instance.taxPercent,
      'location_info': locationInfoToJson(instance.locationInfo),
    };

ProductPurchaseReturn _$ProductPurchaseReturnFromJson(
        Map<String, dynamic> json) =>
    ProductPurchaseReturn(
      purchaseLineId: json['purchase_line_id'] as String,
      returnedQuantity: json['returned_quantity'] as String,
    );

Map<String, dynamic> _$ProductPurchaseReturnToJson(
        ProductPurchaseReturn instance) =>
    <String, dynamic>{
      'purchase_line_id': instance.purchaseLineId,
      'returned_quantity': instance.returnedQuantity,
    };
