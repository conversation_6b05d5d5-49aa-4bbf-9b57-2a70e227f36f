import 'package:we2up/data/models/location_info.dart';

import '../../presentation/widgets/location_range_dropdown_button.dart';

class SearchContact {
  final String contactName;
  final LocationRange locationRange;
  final double distance;
  final String phoneNumber;
  final LocationInfo? userLocation;

  SearchContact({
    required this.contactName,
    required this.locationRange,
    required this.distance,
    this.phoneNumber = "N/A",
    this.userLocation,
  });
}
