import 'package:we2up/data/models/location_info.dart';
import 'package:we2up/presentation/widgets/location_range_dropdown_button.dart';

import 'contact.dart';

class LocationTransaction {
  final DateTime dateTime;
  final String userName;
  final String transactionType;
  final String contactName;
  final LocationInfo? locationInfo;
  final LocationRange distanceFromCompany;
  final LocationRange distanceFromContact;
  final String? amountEntered;
  final String? amountWithdrawn;
  final LocationRange locationRange;
  final Contact? closestContact;

  LocationTransaction({
    required this.dateTime,
    required this.userName,
    required this.transactionType,
    required this.contactName,
    this.locationInfo,
    this.locationRange = LocationRange.none,
    required this.distanceFromCompany,
    required this.distanceFromContact,
    this.amountEntered,
    this.amountWithdrawn,
    this.closestContact,
  });
}
