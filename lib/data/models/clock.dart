import 'package:hive/hive.dart';
import 'package:we2up/data/models/location_info.dart';
import '../db/db_manager.dart';
import '../../utils/we2up_constants.dart';

part 'clock.g.dart';

@HiveType(typeId: 27)
class Clock {
  @HiveField(0)
  final String? clockNote;
  @HiveField(1)
  final String? ipAddress;
  @HiveField(2)
  final String? latitude;
  @HiveField(3)
  final String? longitude;
  @HiveField(4)
  final LocationInfo? locationInfo;

  const Clock({
    this.clockNote,
    this.ipAddress,
    this.latitude,
    this.longitude,
    this.locationInfo,
  });

  Map<String, dynamic> clockInToJson() => <String, dynamic>{
        'user_id': loginData.userId,
        'clock_in_time': DateTime.now().toLocal().toString().split('.').first,
        'clock_in_note': clockNote,
        'ip_address': ipAddress,
        'latitude': latitude,
        'longitude': longitude,
        'location_info': locationInfoToJson(locationInfo),
      };

  Map<String, dynamic> clockOutToJson() => <String, dynamic>{
        'user_id': loginData.userId,
        'clock_out_time': DateTime.now().toLocal().toString().split('.').first,
        'clock_out_note': clockNote,
        'latitude': latitude,
        'longitude': longitude,
        'location_info': locationInfoToJson(locationInfo),
      };
}

@HiveType(typeId: 28)
class ClockResponse {
  @HiveField(0)
  final bool success;

  @HiveField(1)
  final String message;

  @HiveField(2)
  final String? type;

  @HiveField(3)
  final String? currentShift;

  ClockResponse({
    required this.success,
    required this.message,
    this.type,
    this.currentShift,
  });
}
