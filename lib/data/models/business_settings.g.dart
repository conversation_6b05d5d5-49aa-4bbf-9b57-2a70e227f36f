// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'business_settings.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class BusinessSettingsAdapter extends TypeAdapter<BusinessSettings> {
  @override
  final int typeId = 63;

  @override
  BusinessSettings read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return BusinessSettings(
      businessName: fields[0] as String,
      businessMobileNumber: fields[1] as String,
      businessFooterText: fields[2] as String,
      imagePath: fields[3] as String?,
      bluetoothDeviceName: fields[4] as String?,
      bluetoothDeviceAddress: fields[5] as String?,
      currentPaperSize: fields[6] as String,
      currentFont: fields[7] as String,
      currentFontSize: fields[8] as int,
      playSoundWhenAddingProduct: fields[9] as bool,
      printQRCode: fields[10] as bool,
      printSequenceFirstAddedFirst: fields[11] as bool,
      showUnit: fields[12] as bool,
      isDiscountOptionAmount: fields[13] as bool,
      defaultServiceId: fields[14] as int?,
      currency: fields[15] as AppCurrency,
      useArabicNumbers: fields[16] as bool,
      stopSyncing: fields[17] as bool,
      showShopProductsGridView: fields[18] as bool,
      showPopupMenuToSelectNumber: fields[19] as bool,
      includeServiceInTaxes: fields[20] as bool,
    );
  }

  @override
  void write(BinaryWriter writer, BusinessSettings obj) {
    writer
      ..writeByte(21)
      ..writeByte(0)
      ..write(obj.businessName)
      ..writeByte(1)
      ..write(obj.businessMobileNumber)
      ..writeByte(2)
      ..write(obj.businessFooterText)
      ..writeByte(3)
      ..write(obj.imagePath)
      ..writeByte(4)
      ..write(obj.bluetoothDeviceName)
      ..writeByte(5)
      ..write(obj.bluetoothDeviceAddress)
      ..writeByte(6)
      ..write(obj.currentPaperSize)
      ..writeByte(7)
      ..write(obj.currentFont)
      ..writeByte(8)
      ..write(obj.currentFontSize)
      ..writeByte(9)
      ..write(obj.playSoundWhenAddingProduct)
      ..writeByte(10)
      ..write(obj.printQRCode)
      ..writeByte(11)
      ..write(obj.printSequenceFirstAddedFirst)
      ..writeByte(12)
      ..write(obj.showUnit)
      ..writeByte(13)
      ..write(obj.isDiscountOptionAmount)
      ..writeByte(14)
      ..write(obj.defaultServiceId)
      ..writeByte(15)
      ..write(obj.currency)
      ..writeByte(16)
      ..write(obj.useArabicNumbers)
      ..writeByte(17)
      ..write(obj.stopSyncing)
      ..writeByte(18)
      ..write(obj.showShopProductsGridView)
      ..writeByte(19)
      ..write(obj.showPopupMenuToSelectNumber)
      ..writeByte(20)
      ..write(obj.includeServiceInTaxes);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is BusinessSettingsAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$BusinessSettingsImpl _$$BusinessSettingsImplFromJson(
        Map<String, dynamic> json) =>
    _$BusinessSettingsImpl(
      businessName: json['businessName'] as String,
      businessMobileNumber: json['businessMobileNumber'] as String,
      businessFooterText: json['businessFooterText'] as String,
      imagePath: json['imagePath'] as String?,
      bluetoothDeviceName: json['bluetoothDeviceName'] as String?,
      bluetoothDeviceAddress: json['bluetoothDeviceAddress'] as String?,
      currentPaperSize: json['currentPaperSize'] as String? ?? "roll57",
      currentFont: json['currentFont'] as String? ?? "ElMessiri",
      currentFontSize: (json['currentFontSize'] as num?)?.toInt() ?? 9,
      playSoundWhenAddingProduct:
          json['playSoundWhenAddingProduct'] as bool? ?? true,
      printQRCode: json['printQRCode'] as bool? ?? true,
      printSequenceFirstAddedFirst:
          json['printSequenceFirstAddedFirst'] as bool? ?? false,
      showUnit: json['showUnit'] as bool? ?? false,
      isDiscountOptionAmount: json['isDiscountOptionAmount'] as bool? ?? true,
      defaultServiceId: (json['defaultServiceId'] as num?)?.toInt(),
      currency: AppCurrency.fromJson(json['currency'] as Map<String, dynamic>),
      useArabicNumbers: json['useArabicNumbers'] as bool? ?? false,
      stopSyncing: json['stopSyncing'] as bool? ?? false,
      showShopProductsGridView:
          json['showShopProductsGridView'] as bool? ?? true,
      showPopupMenuToSelectNumber:
          json['showPopupMenuToSelectNumber'] as bool? ?? false,
      includeServiceInTaxes: json['includeServiceInTaxes'] as bool? ?? false,
    );

Map<String, dynamic> _$$BusinessSettingsImplToJson(
        _$BusinessSettingsImpl instance) =>
    <String, dynamic>{
      'businessName': instance.businessName,
      'businessMobileNumber': instance.businessMobileNumber,
      'businessFooterText': instance.businessFooterText,
      'imagePath': instance.imagePath,
      'bluetoothDeviceName': instance.bluetoothDeviceName,
      'bluetoothDeviceAddress': instance.bluetoothDeviceAddress,
      'currentPaperSize': instance.currentPaperSize,
      'currentFont': instance.currentFont,
      'currentFontSize': instance.currentFontSize,
      'playSoundWhenAddingProduct': instance.playSoundWhenAddingProduct,
      'printQRCode': instance.printQRCode,
      'printSequenceFirstAddedFirst': instance.printSequenceFirstAddedFirst,
      'showUnit': instance.showUnit,
      'isDiscountOptionAmount': instance.isDiscountOptionAmount,
      'defaultServiceId': instance.defaultServiceId,
      'currency': instance.currency,
      'useArabicNumbers': instance.useArabicNumbers,
      'stopSyncing': instance.stopSyncing,
      'showShopProductsGridView': instance.showShopProductsGridView,
      'showPopupMenuToSelectNumber': instance.showPopupMenuToSelectNumber,
      'includeServiceInTaxes': instance.includeServiceInTaxes,
    };
