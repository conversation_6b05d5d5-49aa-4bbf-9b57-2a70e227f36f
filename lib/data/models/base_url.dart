import 'package:equatable/equatable.dart';
import 'package:hive/hive.dart';

part 'base_url.g.dart';

@HiveType(typeId: 19)
class BaseUrl extends Equatable {
  @HiveField(0)
  final String name;

  @HiveField(1)
  final String url;

  @HiveField(2)
  final String id;

  @HiveField(3)
  final String secret;

  const BaseUrl({
    required this.name,
    required this.url,
    required this.id,
    required this.secret,
  });

  @override
  List<Object?> get props => [name, url, id, secret];
}
