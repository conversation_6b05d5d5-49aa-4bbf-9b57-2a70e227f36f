import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:hive/hive.dart';
import 'package:we2up/data/models/service_model.dart';
import 'package:we2up/data/models/we2up_font.dart';

import '../../utils/we2up_constants.dart';
import '../db/db_manager.dart';
import 'app_currency.dart';

part 'business_settings.freezed.dart';

part 'business_settings.g.dart';

@freezed
@HiveType(typeId: 63)
class BusinessSettings with _$BusinessSettings {
  const factory BusinessSettings({
    @HiveField(0) required String businessName,
    @HiveField(1) required String businessMobileNumber,
    @HiveField(2) required String businessFooterText,
    @HiveField(3) String? imagePath,
    @HiveField(4) String? bluetoothDeviceName,
    @HiveField(5) String? bluetoothDeviceAddress,
    @HiveField(6) @Default("roll57") String currentPaperSize,
    @HiveField(7) @Default("ElMessiri") String currentFont,
    @HiveField(8) @Default(9) int currentFontSize,
    @HiveField(9) @Default(true) bool playSoundWhenAddingProduct,
    @HiveField(10) @Default(true) bool printQRCode,
    @HiveField(11) @Default(false) bool printSequenceFirstAddedFirst,
    @HiveField(12) @Default(false) bool showUnit,
    @HiveField(13) @Default(true) bool isDiscountOptionAmount,
    @HiveField(14) int? defaultServiceId,
    @HiveField(15) required AppCurrency currency,
    @HiveField(16) @Default(false) bool useArabicNumbers,
    @HiveField(17) @Default(false) bool stopSyncing,
    @HiveField(18) @Default(true) bool showShopProductsGridView,
    @HiveField(19) @Default(false) bool showPopupMenuToSelectNumber,
    @HiveField(20) @Default(false) bool includeServiceInTaxes,
  }) = _BusinessSettings;

  factory BusinessSettings.fromJson(Map<String, dynamic> json) =>
      _$BusinessSettingsFromJson(json);

  factory BusinessSettings.initial() => BusinessSettings(
        businessName: '',
        businessMobileNumber: '',
        businessFooterText: '',
        currency: egpCurrency(),
      );
}

AppCurrency egpCurrency() {
  return AppCurrency.fromJson(
    {
      "code": "EGP",
      "name": "Egypt Pound",
      "symbol": "£",
      "flag": "EGP",
      "decimal_digits": 2,
      "number": 818,
      "name_plural": "Egyptian pounds",
      "thousands_separator": ",",
      "decimal_separator": ".",
      "space_between_amount_and_symbol": true,
      "symbol_on_left": true,
    },
  );
}

BusinessSettings get businessSettings => businessDetailsBox.get(
  businessSettingsDataKey,
  defaultValue: BusinessSettings.initial(),
);

AppCurrency currentCurrency() => businessSettings.currency;

String? getImagePath() => businessSettings.imagePath;

String? getBusinessName() => businessSettings.businessName;

String? getBusinessMobileNumber() => businessSettings.businessMobileNumber;

String currencySymbol() => currentCurrency().code;

bool useArabicNumbers() => businessSettings.useArabicNumbers;

bool stopSyncingTransactions() => businessSettings.stopSyncing;

bool showShopProductsGridView() => businessSettings.showShopProductsGridView;

bool showPopupMenuToSelectNumber() =>
    businessSettings.showPopupMenuToSelectNumber;

bool playSoundWhenAddingProduct() =>
    businessSettings.playSoundWhenAddingProduct;

bool printQRCodeAtReceipt() => businessSettings.printQRCode;

bool getPrintSequence() => businessSettings.printSequenceFirstAddedFirst;

bool showUnitInReceipt() => businessSettings.showUnit;

bool isDiscountOptionAmount() => businessSettings.isDiscountOptionAmount;

ServiceModel? defaultServiceModel() =>
    serviceModelBox.get(businessSettings.defaultServiceId);

String? getBluetoothDeviceName() => businessSettings.bluetoothDeviceName;

String? getBluetoothDeviceAddress() => businessSettings.bluetoothDeviceAddress;

String? getCurrentPaperSize() => businessSettings.currentPaperSize;

String getCurrentFont() => businessSettings.currentFont;

String getCurrentFontPath() => we2upFonts
    .firstWhere((element) => element.englishName == getCurrentFont())
    .fontPath;

int getCurrentFontSize() => businessSettings.currentFontSize;
