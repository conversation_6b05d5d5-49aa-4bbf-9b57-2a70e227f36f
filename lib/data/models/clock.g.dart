// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'clock.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class ClockAdapter extends TypeAdapter<Clock> {
  @override
  final int typeId = 27;

  @override
  Clock read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Clock(
      clockNote: fields[0] as String?,
      ipAddress: fields[1] as String?,
      latitude: fields[2] as String?,
      longitude: fields[3] as String?,
      locationInfo: fields[4] as LocationInfo?,
    );
  }

  @override
  void write(BinaryWriter writer, Clock obj) {
    writer
      ..writeByte(5)
      ..writeByte(0)
      ..write(obj.clockNote)
      ..writeByte(1)
      ..write(obj.ipAddress)
      ..writeByte(2)
      ..write(obj.latitude)
      ..writeByte(3)
      ..write(obj.longitude)
      ..writeByte(4)
      ..write(obj.locationInfo);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ClockAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ClockResponseAdapter extends TypeAdapter<ClockResponse> {
  @override
  final int typeId = 28;

  @override
  ClockResponse read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ClockResponse(
      success: fields[0] as bool,
      message: fields[1] as String,
      type: fields[2] as String?,
      currentShift: fields[3] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, ClockResponse obj) {
    writer
      ..writeByte(4)
      ..writeByte(0)
      ..write(obj.success)
      ..writeByte(1)
      ..write(obj.message)
      ..writeByte(2)
      ..write(obj.type)
      ..writeByte(3)
      ..write(obj.currentShift);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ClockResponseAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
