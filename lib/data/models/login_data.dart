import 'package:hive/hive.dart';
import 'package:we2up/data/models/user_permissions.dart';

part 'login_data.g.dart';

@HiveType(typeId: 26)
class LoginData extends HiveObject {
  @HiveField(0)
  String? tokenType;

  @HiveField(1)
  int? expiresIn;

  @HiveField(2)
  String? accessToken;

  @HiveField(3)
  String? refreshToken;

  @HiveField(4)
  int? userId;

  @HiveField(5)
  bool clockedIn = false;

  @HiveField(6)
  DateTime? clockedInTime;

  @HiveField(7)
  bool isAdmin = false;

  @HiveField(8)
  UserPermissions permissions = UserPermissions.fromPermissionsList(const []);

  @HiveField(9)
  bool cashRegistered = false;

  @HiveField(10)
  DateTime? cashRegisteredTime;

  @HiveField(11)
  int? cashRegisteredId;

  @HiveField(12)
  bool allowOverSelling = false;

  @HiveField(13)
  bool locationRequired = false;

  @HiveField(14)
  String username = "";

  @HiveField(15)
  String password = "";

  @HiveField(16)
  int? defaultAccount;

  @HiveField(17)
  bool inSync = true;

  @HiveField(18)
  List<int> permittedLocations;

  @HiveField(19)
  DateTime? lastUpdateTime;

  @HiveField(20)
  bool isProductsReady = false;

  @HiveField(21)
  bool isContactsReady = false;

  @HiveField(22)
  String? commAgntAllowed;

  @HiveField(23)
  bool tablesAllowed = false;

  @HiveField(24)
  bool serviceStaffAllowed = false;

  @HiveField(25)
  int? transactionEditDays;

  @HiveField(26)
  bool? isCommissionAgentRequired;

  LoginData(
    this.tokenType,
    this.expiresIn,
    this.accessToken,
    this.refreshToken,
    this.userId,
    this.clockedIn,
    this.clockedInTime,
    this.isAdmin,
    this.permissions,
    this.cashRegistered,
    this.cashRegisteredTime,
    this.cashRegisteredId,
    this.allowOverSelling,
    this.locationRequired,
    this.username,
    this.password,
    this.defaultAccount,
    this.inSync,
    this.permittedLocations,
    this.lastUpdateTime,
    this.isProductsReady,
    this.isContactsReady,
    this.commAgntAllowed,
    this.tablesAllowed,
    this.serviceStaffAllowed,
    this.transactionEditDays,
    this.isCommissionAgentRequired,
  );

  LoginData.empty()
      : this(
          null,
          null,
          null,
          null,
          null,
          false,
          null,
          false,
          UserPermissions.fromPermissionsList(const []),
          false,
          null,
          null,
          false,
          false,
          "",
          "",
          null,
          true,
          [],
          null,
          false,
          false,
          null,
          false,
          false,
          null,
          null,
        );

  void resetValues() {
    tokenType = null;
    expiresIn = null;
    accessToken = null;
    refreshToken = null;
    userId = null;
    clockedIn = false;
    clockedInTime = null;
    isAdmin = false;
    permissions = UserPermissions.fromPermissionsList(const []);
    cashRegistered = false;
    cashRegisteredTime = null;
    cashRegisteredId = null;
    allowOverSelling = false;
    locationRequired = false;
    username = "";
    password = "";
    defaultAccount = null;
    inSync = true;
    permittedLocations = [];
    commAgntAllowed = null;
    tablesAllowed = false;
    serviceStaffAllowed = false;
    transactionEditDays= null;
    isCommissionAgentRequired = null;
    // don't reset lastUpdateTime when signing out.
  }
}
