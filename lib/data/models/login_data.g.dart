// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'login_data.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class LoginDataAdapter extends TypeAdapter<LoginData> {
  @override
  final int typeId = 26;

  @override
  LoginData read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return LoginData(
      fields[0] as String?,
      fields[1] as int?,
      fields[2] as String?,
      fields[3] as String?,
      fields[4] as int?,
      fields[5] as bool,
      fields[6] as DateTime?,
      fields[7] as bool,
      fields[8] as UserPermissions,
      fields[9] as bool,
      fields[10] as DateTime?,
      fields[11] as int?,
      fields[12] as bool,
      fields[13] as bool,
      fields[14] as String,
      fields[15] as String,
      fields[16] as int?,
      fields[17] as bool,
      (fields[18] as List).cast<int>(),
      fields[19] as DateTime?,
      fields[20] as bool,
      fields[21] as bool,
      fields[22] as String?,
      fields[23] as bool,
      fields[24] as bool,
      fields[25] as int?,
      fields[26] as bool?,
    );
  }

  @override
  void write(BinaryWriter writer, LoginData obj) {
    writer
      ..writeByte(27)
      ..writeByte(0)
      ..write(obj.tokenType)
      ..writeByte(1)
      ..write(obj.expiresIn)
      ..writeByte(2)
      ..write(obj.accessToken)
      ..writeByte(3)
      ..write(obj.refreshToken)
      ..writeByte(4)
      ..write(obj.userId)
      ..writeByte(5)
      ..write(obj.clockedIn)
      ..writeByte(6)
      ..write(obj.clockedInTime)
      ..writeByte(7)
      ..write(obj.isAdmin)
      ..writeByte(8)
      ..write(obj.permissions)
      ..writeByte(9)
      ..write(obj.cashRegistered)
      ..writeByte(10)
      ..write(obj.cashRegisteredTime)
      ..writeByte(11)
      ..write(obj.cashRegisteredId)
      ..writeByte(12)
      ..write(obj.allowOverSelling)
      ..writeByte(13)
      ..write(obj.locationRequired)
      ..writeByte(14)
      ..write(obj.username)
      ..writeByte(15)
      ..write(obj.password)
      ..writeByte(16)
      ..write(obj.defaultAccount)
      ..writeByte(17)
      ..write(obj.inSync)
      ..writeByte(18)
      ..write(obj.permittedLocations)
      ..writeByte(19)
      ..write(obj.lastUpdateTime)
      ..writeByte(20)
      ..write(obj.isProductsReady)
      ..writeByte(21)
      ..write(obj.isContactsReady)
      ..writeByte(22)
      ..write(obj.commAgntAllowed)
      ..writeByte(23)
      ..write(obj.tablesAllowed)
      ..writeByte(24)
      ..write(obj.serviceStaffAllowed)
      ..writeByte(25)
      ..write(obj.transactionEditDays)
      ..writeByte(26)
      ..write(obj.isCommissionAgentRequired);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is LoginDataAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
