// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'business_settings.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

BusinessSettings _$BusinessSettingsFromJson(Map<String, dynamic> json) {
  return _BusinessSettings.fromJson(json);
}

/// @nodoc
mixin _$BusinessSettings {
  @HiveField(0)
  String get businessName => throw _privateConstructorUsedError;
  @HiveField(1)
  String get businessMobileNumber => throw _privateConstructorUsedError;
  @HiveField(2)
  String get businessFooterText => throw _privateConstructorUsedError;
  @HiveField(3)
  String? get imagePath => throw _privateConstructorUsedError;
  @HiveField(4)
  String? get bluetoothDeviceName => throw _privateConstructorUsedError;
  @HiveField(5)
  String? get bluetoothDeviceAddress => throw _privateConstructorUsedError;
  @HiveField(6)
  String get currentPaperSize => throw _privateConstructorUsedError;
  @HiveField(7)
  String get currentFont => throw _privateConstructorUsedError;
  @HiveField(8)
  int get currentFontSize => throw _privateConstructorUsedError;
  @HiveField(9)
  bool get playSoundWhenAddingProduct => throw _privateConstructorUsedError;
  @HiveField(10)
  bool get printQRCode => throw _privateConstructorUsedError;
  @HiveField(11)
  bool get printSequenceFirstAddedFirst => throw _privateConstructorUsedError;
  @HiveField(12)
  bool get showUnit => throw _privateConstructorUsedError;
  @HiveField(13)
  bool get isDiscountOptionAmount => throw _privateConstructorUsedError;
  @HiveField(14)
  int? get defaultServiceId => throw _privateConstructorUsedError;
  @HiveField(15)
  AppCurrency get currency => throw _privateConstructorUsedError;
  @HiveField(16)
  bool get useArabicNumbers => throw _privateConstructorUsedError;
  @HiveField(17)
  bool get stopSyncing => throw _privateConstructorUsedError;
  @HiveField(18)
  bool get showShopProductsGridView => throw _privateConstructorUsedError;
  @HiveField(19)
  bool get showPopupMenuToSelectNumber => throw _privateConstructorUsedError;
  @HiveField(20)
  bool get includeServiceInTaxes => throw _privateConstructorUsedError;

  /// Serializes this BusinessSettings to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of BusinessSettings
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BusinessSettingsCopyWith<BusinessSettings> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BusinessSettingsCopyWith<$Res> {
  factory $BusinessSettingsCopyWith(
          BusinessSettings value, $Res Function(BusinessSettings) then) =
      _$BusinessSettingsCopyWithImpl<$Res, BusinessSettings>;
  @useResult
  $Res call(
      {@HiveField(0) String businessName,
      @HiveField(1) String businessMobileNumber,
      @HiveField(2) String businessFooterText,
      @HiveField(3) String? imagePath,
      @HiveField(4) String? bluetoothDeviceName,
      @HiveField(5) String? bluetoothDeviceAddress,
      @HiveField(6) String currentPaperSize,
      @HiveField(7) String currentFont,
      @HiveField(8) int currentFontSize,
      @HiveField(9) bool playSoundWhenAddingProduct,
      @HiveField(10) bool printQRCode,
      @HiveField(11) bool printSequenceFirstAddedFirst,
      @HiveField(12) bool showUnit,
      @HiveField(13) bool isDiscountOptionAmount,
      @HiveField(14) int? defaultServiceId,
      @HiveField(15) AppCurrency currency,
      @HiveField(16) bool useArabicNumbers,
      @HiveField(17) bool stopSyncing,
      @HiveField(18) bool showShopProductsGridView,
      @HiveField(19) bool showPopupMenuToSelectNumber,
      @HiveField(20) bool includeServiceInTaxes});
}

/// @nodoc
class _$BusinessSettingsCopyWithImpl<$Res, $Val extends BusinessSettings>
    implements $BusinessSettingsCopyWith<$Res> {
  _$BusinessSettingsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BusinessSettings
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? businessName = null,
    Object? businessMobileNumber = null,
    Object? businessFooterText = null,
    Object? imagePath = freezed,
    Object? bluetoothDeviceName = freezed,
    Object? bluetoothDeviceAddress = freezed,
    Object? currentPaperSize = null,
    Object? currentFont = null,
    Object? currentFontSize = null,
    Object? playSoundWhenAddingProduct = null,
    Object? printQRCode = null,
    Object? printSequenceFirstAddedFirst = null,
    Object? showUnit = null,
    Object? isDiscountOptionAmount = null,
    Object? defaultServiceId = freezed,
    Object? currency = null,
    Object? useArabicNumbers = null,
    Object? stopSyncing = null,
    Object? showShopProductsGridView = null,
    Object? showPopupMenuToSelectNumber = null,
    Object? includeServiceInTaxes = null,
  }) {
    return _then(_value.copyWith(
      businessName: null == businessName
          ? _value.businessName
          : businessName // ignore: cast_nullable_to_non_nullable
              as String,
      businessMobileNumber: null == businessMobileNumber
          ? _value.businessMobileNumber
          : businessMobileNumber // ignore: cast_nullable_to_non_nullable
              as String,
      businessFooterText: null == businessFooterText
          ? _value.businessFooterText
          : businessFooterText // ignore: cast_nullable_to_non_nullable
              as String,
      imagePath: freezed == imagePath
          ? _value.imagePath
          : imagePath // ignore: cast_nullable_to_non_nullable
              as String?,
      bluetoothDeviceName: freezed == bluetoothDeviceName
          ? _value.bluetoothDeviceName
          : bluetoothDeviceName // ignore: cast_nullable_to_non_nullable
              as String?,
      bluetoothDeviceAddress: freezed == bluetoothDeviceAddress
          ? _value.bluetoothDeviceAddress
          : bluetoothDeviceAddress // ignore: cast_nullable_to_non_nullable
              as String?,
      currentPaperSize: null == currentPaperSize
          ? _value.currentPaperSize
          : currentPaperSize // ignore: cast_nullable_to_non_nullable
              as String,
      currentFont: null == currentFont
          ? _value.currentFont
          : currentFont // ignore: cast_nullable_to_non_nullable
              as String,
      currentFontSize: null == currentFontSize
          ? _value.currentFontSize
          : currentFontSize // ignore: cast_nullable_to_non_nullable
              as int,
      playSoundWhenAddingProduct: null == playSoundWhenAddingProduct
          ? _value.playSoundWhenAddingProduct
          : playSoundWhenAddingProduct // ignore: cast_nullable_to_non_nullable
              as bool,
      printQRCode: null == printQRCode
          ? _value.printQRCode
          : printQRCode // ignore: cast_nullable_to_non_nullable
              as bool,
      printSequenceFirstAddedFirst: null == printSequenceFirstAddedFirst
          ? _value.printSequenceFirstAddedFirst
          : printSequenceFirstAddedFirst // ignore: cast_nullable_to_non_nullable
              as bool,
      showUnit: null == showUnit
          ? _value.showUnit
          : showUnit // ignore: cast_nullable_to_non_nullable
              as bool,
      isDiscountOptionAmount: null == isDiscountOptionAmount
          ? _value.isDiscountOptionAmount
          : isDiscountOptionAmount // ignore: cast_nullable_to_non_nullable
              as bool,
      defaultServiceId: freezed == defaultServiceId
          ? _value.defaultServiceId
          : defaultServiceId // ignore: cast_nullable_to_non_nullable
              as int?,
      currency: null == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as AppCurrency,
      useArabicNumbers: null == useArabicNumbers
          ? _value.useArabicNumbers
          : useArabicNumbers // ignore: cast_nullable_to_non_nullable
              as bool,
      stopSyncing: null == stopSyncing
          ? _value.stopSyncing
          : stopSyncing // ignore: cast_nullable_to_non_nullable
              as bool,
      showShopProductsGridView: null == showShopProductsGridView
          ? _value.showShopProductsGridView
          : showShopProductsGridView // ignore: cast_nullable_to_non_nullable
              as bool,
      showPopupMenuToSelectNumber: null == showPopupMenuToSelectNumber
          ? _value.showPopupMenuToSelectNumber
          : showPopupMenuToSelectNumber // ignore: cast_nullable_to_non_nullable
              as bool,
      includeServiceInTaxes: null == includeServiceInTaxes
          ? _value.includeServiceInTaxes
          : includeServiceInTaxes // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$BusinessSettingsImplCopyWith<$Res>
    implements $BusinessSettingsCopyWith<$Res> {
  factory _$$BusinessSettingsImplCopyWith(_$BusinessSettingsImpl value,
          $Res Function(_$BusinessSettingsImpl) then) =
      __$$BusinessSettingsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@HiveField(0) String businessName,
      @HiveField(1) String businessMobileNumber,
      @HiveField(2) String businessFooterText,
      @HiveField(3) String? imagePath,
      @HiveField(4) String? bluetoothDeviceName,
      @HiveField(5) String? bluetoothDeviceAddress,
      @HiveField(6) String currentPaperSize,
      @HiveField(7) String currentFont,
      @HiveField(8) int currentFontSize,
      @HiveField(9) bool playSoundWhenAddingProduct,
      @HiveField(10) bool printQRCode,
      @HiveField(11) bool printSequenceFirstAddedFirst,
      @HiveField(12) bool showUnit,
      @HiveField(13) bool isDiscountOptionAmount,
      @HiveField(14) int? defaultServiceId,
      @HiveField(15) AppCurrency currency,
      @HiveField(16) bool useArabicNumbers,
      @HiveField(17) bool stopSyncing,
      @HiveField(18) bool showShopProductsGridView,
      @HiveField(19) bool showPopupMenuToSelectNumber,
      @HiveField(20) bool includeServiceInTaxes});
}

/// @nodoc
class __$$BusinessSettingsImplCopyWithImpl<$Res>
    extends _$BusinessSettingsCopyWithImpl<$Res, _$BusinessSettingsImpl>
    implements _$$BusinessSettingsImplCopyWith<$Res> {
  __$$BusinessSettingsImplCopyWithImpl(_$BusinessSettingsImpl _value,
      $Res Function(_$BusinessSettingsImpl) _then)
      : super(_value, _then);

  /// Create a copy of BusinessSettings
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? businessName = null,
    Object? businessMobileNumber = null,
    Object? businessFooterText = null,
    Object? imagePath = freezed,
    Object? bluetoothDeviceName = freezed,
    Object? bluetoothDeviceAddress = freezed,
    Object? currentPaperSize = null,
    Object? currentFont = null,
    Object? currentFontSize = null,
    Object? playSoundWhenAddingProduct = null,
    Object? printQRCode = null,
    Object? printSequenceFirstAddedFirst = null,
    Object? showUnit = null,
    Object? isDiscountOptionAmount = null,
    Object? defaultServiceId = freezed,
    Object? currency = null,
    Object? useArabicNumbers = null,
    Object? stopSyncing = null,
    Object? showShopProductsGridView = null,
    Object? showPopupMenuToSelectNumber = null,
    Object? includeServiceInTaxes = null,
  }) {
    return _then(_$BusinessSettingsImpl(
      businessName: null == businessName
          ? _value.businessName
          : businessName // ignore: cast_nullable_to_non_nullable
              as String,
      businessMobileNumber: null == businessMobileNumber
          ? _value.businessMobileNumber
          : businessMobileNumber // ignore: cast_nullable_to_non_nullable
              as String,
      businessFooterText: null == businessFooterText
          ? _value.businessFooterText
          : businessFooterText // ignore: cast_nullable_to_non_nullable
              as String,
      imagePath: freezed == imagePath
          ? _value.imagePath
          : imagePath // ignore: cast_nullable_to_non_nullable
              as String?,
      bluetoothDeviceName: freezed == bluetoothDeviceName
          ? _value.bluetoothDeviceName
          : bluetoothDeviceName // ignore: cast_nullable_to_non_nullable
              as String?,
      bluetoothDeviceAddress: freezed == bluetoothDeviceAddress
          ? _value.bluetoothDeviceAddress
          : bluetoothDeviceAddress // ignore: cast_nullable_to_non_nullable
              as String?,
      currentPaperSize: null == currentPaperSize
          ? _value.currentPaperSize
          : currentPaperSize // ignore: cast_nullable_to_non_nullable
              as String,
      currentFont: null == currentFont
          ? _value.currentFont
          : currentFont // ignore: cast_nullable_to_non_nullable
              as String,
      currentFontSize: null == currentFontSize
          ? _value.currentFontSize
          : currentFontSize // ignore: cast_nullable_to_non_nullable
              as int,
      playSoundWhenAddingProduct: null == playSoundWhenAddingProduct
          ? _value.playSoundWhenAddingProduct
          : playSoundWhenAddingProduct // ignore: cast_nullable_to_non_nullable
              as bool,
      printQRCode: null == printQRCode
          ? _value.printQRCode
          : printQRCode // ignore: cast_nullable_to_non_nullable
              as bool,
      printSequenceFirstAddedFirst: null == printSequenceFirstAddedFirst
          ? _value.printSequenceFirstAddedFirst
          : printSequenceFirstAddedFirst // ignore: cast_nullable_to_non_nullable
              as bool,
      showUnit: null == showUnit
          ? _value.showUnit
          : showUnit // ignore: cast_nullable_to_non_nullable
              as bool,
      isDiscountOptionAmount: null == isDiscountOptionAmount
          ? _value.isDiscountOptionAmount
          : isDiscountOptionAmount // ignore: cast_nullable_to_non_nullable
              as bool,
      defaultServiceId: freezed == defaultServiceId
          ? _value.defaultServiceId
          : defaultServiceId // ignore: cast_nullable_to_non_nullable
              as int?,
      currency: null == currency
          ? _value.currency
          : currency // ignore: cast_nullable_to_non_nullable
              as AppCurrency,
      useArabicNumbers: null == useArabicNumbers
          ? _value.useArabicNumbers
          : useArabicNumbers // ignore: cast_nullable_to_non_nullable
              as bool,
      stopSyncing: null == stopSyncing
          ? _value.stopSyncing
          : stopSyncing // ignore: cast_nullable_to_non_nullable
              as bool,
      showShopProductsGridView: null == showShopProductsGridView
          ? _value.showShopProductsGridView
          : showShopProductsGridView // ignore: cast_nullable_to_non_nullable
              as bool,
      showPopupMenuToSelectNumber: null == showPopupMenuToSelectNumber
          ? _value.showPopupMenuToSelectNumber
          : showPopupMenuToSelectNumber // ignore: cast_nullable_to_non_nullable
              as bool,
      includeServiceInTaxes: null == includeServiceInTaxes
          ? _value.includeServiceInTaxes
          : includeServiceInTaxes // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$BusinessSettingsImpl implements _BusinessSettings {
  const _$BusinessSettingsImpl(
      {@HiveField(0) required this.businessName,
      @HiveField(1) required this.businessMobileNumber,
      @HiveField(2) required this.businessFooterText,
      @HiveField(3) this.imagePath,
      @HiveField(4) this.bluetoothDeviceName,
      @HiveField(5) this.bluetoothDeviceAddress,
      @HiveField(6) this.currentPaperSize = "roll57",
      @HiveField(7) this.currentFont = "ElMessiri",
      @HiveField(8) this.currentFontSize = 9,
      @HiveField(9) this.playSoundWhenAddingProduct = true,
      @HiveField(10) this.printQRCode = true,
      @HiveField(11) this.printSequenceFirstAddedFirst = false,
      @HiveField(12) this.showUnit = false,
      @HiveField(13) this.isDiscountOptionAmount = true,
      @HiveField(14) this.defaultServiceId,
      @HiveField(15) required this.currency,
      @HiveField(16) this.useArabicNumbers = false,
      @HiveField(17) this.stopSyncing = false,
      @HiveField(18) this.showShopProductsGridView = true,
      @HiveField(19) this.showPopupMenuToSelectNumber = false,
      @HiveField(20) this.includeServiceInTaxes = false});

  factory _$BusinessSettingsImpl.fromJson(Map<String, dynamic> json) =>
      _$$BusinessSettingsImplFromJson(json);

  @override
  @HiveField(0)
  final String businessName;
  @override
  @HiveField(1)
  final String businessMobileNumber;
  @override
  @HiveField(2)
  final String businessFooterText;
  @override
  @HiveField(3)
  final String? imagePath;
  @override
  @HiveField(4)
  final String? bluetoothDeviceName;
  @override
  @HiveField(5)
  final String? bluetoothDeviceAddress;
  @override
  @JsonKey()
  @HiveField(6)
  final String currentPaperSize;
  @override
  @JsonKey()
  @HiveField(7)
  final String currentFont;
  @override
  @JsonKey()
  @HiveField(8)
  final int currentFontSize;
  @override
  @JsonKey()
  @HiveField(9)
  final bool playSoundWhenAddingProduct;
  @override
  @JsonKey()
  @HiveField(10)
  final bool printQRCode;
  @override
  @JsonKey()
  @HiveField(11)
  final bool printSequenceFirstAddedFirst;
  @override
  @JsonKey()
  @HiveField(12)
  final bool showUnit;
  @override
  @JsonKey()
  @HiveField(13)
  final bool isDiscountOptionAmount;
  @override
  @HiveField(14)
  final int? defaultServiceId;
  @override
  @HiveField(15)
  final AppCurrency currency;
  @override
  @JsonKey()
  @HiveField(16)
  final bool useArabicNumbers;
  @override
  @JsonKey()
  @HiveField(17)
  final bool stopSyncing;
  @override
  @JsonKey()
  @HiveField(18)
  final bool showShopProductsGridView;
  @override
  @JsonKey()
  @HiveField(19)
  final bool showPopupMenuToSelectNumber;
  @override
  @JsonKey()
  @HiveField(20)
  final bool includeServiceInTaxes;

  @override
  String toString() {
    return 'BusinessSettings(businessName: $businessName, businessMobileNumber: $businessMobileNumber, businessFooterText: $businessFooterText, imagePath: $imagePath, bluetoothDeviceName: $bluetoothDeviceName, bluetoothDeviceAddress: $bluetoothDeviceAddress, currentPaperSize: $currentPaperSize, currentFont: $currentFont, currentFontSize: $currentFontSize, playSoundWhenAddingProduct: $playSoundWhenAddingProduct, printQRCode: $printQRCode, printSequenceFirstAddedFirst: $printSequenceFirstAddedFirst, showUnit: $showUnit, isDiscountOptionAmount: $isDiscountOptionAmount, defaultServiceId: $defaultServiceId, currency: $currency, useArabicNumbers: $useArabicNumbers, stopSyncing: $stopSyncing, showShopProductsGridView: $showShopProductsGridView, showPopupMenuToSelectNumber: $showPopupMenuToSelectNumber, includeServiceInTaxes: $includeServiceInTaxes)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BusinessSettingsImpl &&
            (identical(other.businessName, businessName) ||
                other.businessName == businessName) &&
            (identical(other.businessMobileNumber, businessMobileNumber) ||
                other.businessMobileNumber == businessMobileNumber) &&
            (identical(other.businessFooterText, businessFooterText) ||
                other.businessFooterText == businessFooterText) &&
            (identical(other.imagePath, imagePath) ||
                other.imagePath == imagePath) &&
            (identical(other.bluetoothDeviceName, bluetoothDeviceName) ||
                other.bluetoothDeviceName == bluetoothDeviceName) &&
            (identical(other.bluetoothDeviceAddress, bluetoothDeviceAddress) ||
                other.bluetoothDeviceAddress == bluetoothDeviceAddress) &&
            (identical(other.currentPaperSize, currentPaperSize) ||
                other.currentPaperSize == currentPaperSize) &&
            (identical(other.currentFont, currentFont) ||
                other.currentFont == currentFont) &&
            (identical(other.currentFontSize, currentFontSize) ||
                other.currentFontSize == currentFontSize) &&
            (identical(other.playSoundWhenAddingProduct,
                    playSoundWhenAddingProduct) ||
                other.playSoundWhenAddingProduct ==
                    playSoundWhenAddingProduct) &&
            (identical(other.printQRCode, printQRCode) ||
                other.printQRCode == printQRCode) &&
            (identical(other.printSequenceFirstAddedFirst,
                    printSequenceFirstAddedFirst) ||
                other.printSequenceFirstAddedFirst ==
                    printSequenceFirstAddedFirst) &&
            (identical(other.showUnit, showUnit) ||
                other.showUnit == showUnit) &&
            (identical(other.isDiscountOptionAmount, isDiscountOptionAmount) ||
                other.isDiscountOptionAmount == isDiscountOptionAmount) &&
            (identical(other.defaultServiceId, defaultServiceId) ||
                other.defaultServiceId == defaultServiceId) &&
            (identical(other.currency, currency) ||
                other.currency == currency) &&
            (identical(other.useArabicNumbers, useArabicNumbers) ||
                other.useArabicNumbers == useArabicNumbers) &&
            (identical(other.stopSyncing, stopSyncing) ||
                other.stopSyncing == stopSyncing) &&
            (identical(other.showShopProductsGridView, showShopProductsGridView) ||
                other.showShopProductsGridView == showShopProductsGridView) &&
            (identical(other.showPopupMenuToSelectNumber,
                    showPopupMenuToSelectNumber) ||
                other.showPopupMenuToSelectNumber ==
                    showPopupMenuToSelectNumber) &&
            (identical(other.includeServiceInTaxes, includeServiceInTaxes) ||
                other.includeServiceInTaxes == includeServiceInTaxes));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        businessName,
        businessMobileNumber,
        businessFooterText,
        imagePath,
        bluetoothDeviceName,
        bluetoothDeviceAddress,
        currentPaperSize,
        currentFont,
        currentFontSize,
        playSoundWhenAddingProduct,
        printQRCode,
        printSequenceFirstAddedFirst,
        showUnit,
        isDiscountOptionAmount,
        defaultServiceId,
        currency,
        useArabicNumbers,
        stopSyncing,
        showShopProductsGridView,
        showPopupMenuToSelectNumber,
        includeServiceInTaxes
      ]);

  /// Create a copy of BusinessSettings
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BusinessSettingsImplCopyWith<_$BusinessSettingsImpl> get copyWith =>
      __$$BusinessSettingsImplCopyWithImpl<_$BusinessSettingsImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$BusinessSettingsImplToJson(
      this,
    );
  }
}

abstract class _BusinessSettings implements BusinessSettings {
  const factory _BusinessSettings(
          {@HiveField(0) required final String businessName,
          @HiveField(1) required final String businessMobileNumber,
          @HiveField(2) required final String businessFooterText,
          @HiveField(3) final String? imagePath,
          @HiveField(4) final String? bluetoothDeviceName,
          @HiveField(5) final String? bluetoothDeviceAddress,
          @HiveField(6) final String currentPaperSize,
          @HiveField(7) final String currentFont,
          @HiveField(8) final int currentFontSize,
          @HiveField(9) final bool playSoundWhenAddingProduct,
          @HiveField(10) final bool printQRCode,
          @HiveField(11) final bool printSequenceFirstAddedFirst,
          @HiveField(12) final bool showUnit,
          @HiveField(13) final bool isDiscountOptionAmount,
          @HiveField(14) final int? defaultServiceId,
          @HiveField(15) required final AppCurrency currency,
          @HiveField(16) final bool useArabicNumbers,
          @HiveField(17) final bool stopSyncing,
          @HiveField(18) final bool showShopProductsGridView,
          @HiveField(19) final bool showPopupMenuToSelectNumber,
          @HiveField(20) final bool includeServiceInTaxes}) =
      _$BusinessSettingsImpl;

  factory _BusinessSettings.fromJson(Map<String, dynamic> json) =
      _$BusinessSettingsImpl.fromJson;

  @override
  @HiveField(0)
  String get businessName;
  @override
  @HiveField(1)
  String get businessMobileNumber;
  @override
  @HiveField(2)
  String get businessFooterText;
  @override
  @HiveField(3)
  String? get imagePath;
  @override
  @HiveField(4)
  String? get bluetoothDeviceName;
  @override
  @HiveField(5)
  String? get bluetoothDeviceAddress;
  @override
  @HiveField(6)
  String get currentPaperSize;
  @override
  @HiveField(7)
  String get currentFont;
  @override
  @HiveField(8)
  int get currentFontSize;
  @override
  @HiveField(9)
  bool get playSoundWhenAddingProduct;
  @override
  @HiveField(10)
  bool get printQRCode;
  @override
  @HiveField(11)
  bool get printSequenceFirstAddedFirst;
  @override
  @HiveField(12)
  bool get showUnit;
  @override
  @HiveField(13)
  bool get isDiscountOptionAmount;
  @override
  @HiveField(14)
  int? get defaultServiceId;
  @override
  @HiveField(15)
  AppCurrency get currency;
  @override
  @HiveField(16)
  bool get useArabicNumbers;
  @override
  @HiveField(17)
  bool get stopSyncing;
  @override
  @HiveField(18)
  bool get showShopProductsGridView;
  @override
  @HiveField(19)
  bool get showPopupMenuToSelectNumber;
  @override
  @HiveField(20)
  bool get includeServiceInTaxes;

  /// Create a copy of BusinessSettings
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BusinessSettingsImplCopyWith<_$BusinessSettingsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
