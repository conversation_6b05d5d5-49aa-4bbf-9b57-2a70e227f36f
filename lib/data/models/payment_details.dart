import 'package:flutter/material.dart';
import 'package:we2up/data/models/payment_account.dart';
import 'package:we2up/data/models/sell.dart';

import '../db/db_manager.dart';

class PaymentDetails {
  final TextEditingController invoiceController;
  final TextEditingController paymentNoteController;
  final int? paymentId;
  String paymentMethod;
  PaymentAccount? paymentAccount;

  factory PaymentDetails.fromPayment(PaymentLine payment) {
    return PaymentDetails(
      paymentId: payment.id,
      invoiceController: TextEditingController(text: payment.amount),
      paymentNoteController: TextEditingController(text: payment.note),
      paymentAccount: paymentAccountsBox.get(payment.accountId),
      paymentMethod: payment.method,
    );
  }

  PaymentDetails({
    required this.invoiceController,
    required this.paymentNoteController,
    String? paymentMethod,
    this.paymentId,
    PaymentAccount? paymentAccount,
  })  : paymentMethod = paymentMethod ?? "cash",
        paymentAccount = paymentAccount ??
            defaultPaymentAccount(
              businessLocationsBox.values.first,
            );
}
