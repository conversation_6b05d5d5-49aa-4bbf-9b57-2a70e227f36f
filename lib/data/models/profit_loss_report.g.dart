// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'profit_loss_report.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class ProfitLossReportAdapter extends TypeAdapter<ProfitLossReport> {
  @override
  final int typeId = 15;

  @override
  ProfitLossReport read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ProfitLossReport(
      totalPurchaseShippingCharge: fields[0] as double,
      totalSellShippingCharge: fields[1] as double,
      totalPurchaseAdditionalExpense: fields[2] as double,
      totalSellAdditionalExpense: fields[3] as double,
      totalTransferShippingCharges: fields[4] as double,
      openingStock: fields[5] as double,
      closingStock: fields[6] as double,
      totalPurchase: fields[7] as double,
      totalPurchaseDiscount: fields[8] as double,
      totalPurchaseReturn: fields[9] as double,
      totalSell: fields[10] as double,
      totalSellDiscount: fields[11] as double,
      totalSellReturn: fields[12] as double,
      totalSellRoundOff: fields[13] as double,
      totalExpense: fields[14] as double,
      totalAdjustment: fields[15] as double,
      totalRecovered: fields[16] as double,
      totalRewardAmount: fields[17] as double,
      leftSideModuleData: (fields[18] as List).cast<ModuleData>(),
      rightSideModuleData: (fields[19] as List).cast<ModuleData>(),
      netProfit: fields[20] as double,
      grossProfit: fields[21] as double,
      totalSellBySubtype: (fields[22] as List)
          .map((dynamic e) => (e as Map).cast<String, dynamic>())
          .toList(),
    );
  }

  @override
  void write(BinaryWriter writer, ProfitLossReport obj) {
    writer
      ..writeByte(23)
      ..writeByte(0)
      ..write(obj.totalPurchaseShippingCharge)
      ..writeByte(1)
      ..write(obj.totalSellShippingCharge)
      ..writeByte(2)
      ..write(obj.totalPurchaseAdditionalExpense)
      ..writeByte(3)
      ..write(obj.totalSellAdditionalExpense)
      ..writeByte(4)
      ..write(obj.totalTransferShippingCharges)
      ..writeByte(5)
      ..write(obj.openingStock)
      ..writeByte(6)
      ..write(obj.closingStock)
      ..writeByte(7)
      ..write(obj.totalPurchase)
      ..writeByte(8)
      ..write(obj.totalPurchaseDiscount)
      ..writeByte(9)
      ..write(obj.totalPurchaseReturn)
      ..writeByte(10)
      ..write(obj.totalSell)
      ..writeByte(11)
      ..write(obj.totalSellDiscount)
      ..writeByte(12)
      ..write(obj.totalSellReturn)
      ..writeByte(13)
      ..write(obj.totalSellRoundOff)
      ..writeByte(14)
      ..write(obj.totalExpense)
      ..writeByte(15)
      ..write(obj.totalAdjustment)
      ..writeByte(16)
      ..write(obj.totalRecovered)
      ..writeByte(17)
      ..write(obj.totalRewardAmount)
      ..writeByte(18)
      ..write(obj.leftSideModuleData)
      ..writeByte(19)
      ..write(obj.rightSideModuleData)
      ..writeByte(20)
      ..write(obj.netProfit)
      ..writeByte(21)
      ..write(obj.grossProfit)
      ..writeByte(22)
      ..write(obj.totalSellBySubtype);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ProfitLossReportAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ModuleDataAdapter extends TypeAdapter<ModuleData> {
  @override
  final int typeId = 16;

  @override
  ModuleData read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ModuleData(
      value: fields[0] as double,
      label: fields[1] as String,
      addToNetProfit: fields[2] as bool,
    );
  }

  @override
  void write(BinaryWriter writer, ModuleData obj) {
    writer
      ..writeByte(3)
      ..writeByte(0)
      ..write(obj.value)
      ..writeByte(1)
      ..write(obj.label)
      ..writeByte(2)
      ..write(obj.addToNetProfit);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ModuleDataAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
