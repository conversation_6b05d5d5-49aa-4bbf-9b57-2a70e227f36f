import 'package:json_annotation/json_annotation.dart';
import 'package:hive/hive.dart';
import 'package:equatable/equatable.dart';
import 'package:we2up/data/db/db_manager.dart';
import 'package:we2up/data/models/payment.dart';
import 'package:we2up/data/models/sell.dart';

import '../../utils/we2up_constants.dart';
import 'location_info.dart';

part 'contact_payment_model.g.dart';

@JsonSerializable(createToJson: false)
@HiveType(typeId: 56)
class ContactPaymentModel extends Equatable {
  @JsonKey(name: 'id')
  @HiveField(0)
  final int? id;

  @JsonKey(name: 'payment_ref_no')
  @HiveField(1)
  final String? refNo;

  @JsonKey(
    name: 'created_at',
    fromJson: DateTime.parse,
    toJson: dateToString,
  )
  @HiveField(2)
  final DateTime createdAt;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'amount', fromJson: double.parse)
  @HiveField(3)
  final double? amount;

  @Json<PERSON>ey(name: 'method')
  @HiveField(4)
  final String? method;

  @JsonKey(name: 'contact_name')
  @HiveField(5)
  final String? contactName;

  @JsonKey(name: 'contact_id')
  @HiveField(6)
  final int contactId;

  @JsonKey(name: 'customer_group')
  @HiveField(7)
  final dynamic customerGroup;

  @JsonKey(name: 'first_name')
  @HiveField(8)
  final String? firstName;

  @JsonKey(name: 'last_name')
  @HiveField(9)
  final String? lastName;

  @JsonKey(name: 'user_id')
  @HiveField(10)
  final int userId;

  @HiveField(11)
  @JsonKey(includeFromJson: false, includeToJson: false)
  final bool offline;

  @HiveField(12)
  @JsonKey(includeFromJson: false, includeToJson: false)
  final Payment? payment;

  @JsonKey(name: 'transaction_type')
  @HiveField(13)
  final String? transactionType;

  @HiveField(14)
  @JsonKey(
    name: 'location_info',
    toJson: locationInfoToJson,
    fromJson: locationInfoFromListJson,
  )
  final LocationInfo? locationInfo;

  const ContactPaymentModel({
    this.id,
    this.refNo,
    required this.createdAt,
    this.amount,
    this.method,
    this.contactName,
    required this.contactId,
    this.customerGroup,
    this.firstName,
    this.lastName,
    this.payment,
    required this.userId,
    this.offline = false,
    this.transactionType,
    this.locationInfo,
  });

  factory ContactPaymentModel.fromJson(Map<String, dynamic> json) =>
      _$ContactPaymentModelFromJson(json);

  factory ContactPaymentModel.fromOfflinePayment(
    int contactID,
    Payment payment,
    LocationInfo? locationInfo,
  ) {
    final now = DateTime.now();
    return ContactPaymentModel(
      refNo: payment.refNo,
      createdAt: now,
      contactId: contactID,
      userId: loginData.userId!,
      amount: payment.amount,
      contactName: contactsBox.get(contactID)!.name,
      firstName: loginData.username,
      method: payment.method,
      customerGroup: null,
      offline: true,
      payment: payment,
      transactionType: null,
      locationInfo: locationInfo,
    );
  }

  factory ContactPaymentModel.fromPaymentLine(PaymentLine line) {
    final now = DateTime.now();
    return ContactPaymentModel(
      id: line.id,
      refNo: line.paymentRefNo,
      createdAt: DateTime.tryParse(line.paidOn ?? "") ?? now,
      contactId: line.paymentFor!,
      userId: line.userId ?? loginData.userId!,
      amount: double.parse(line.amount ?? "0"),
      contactName: line.contactName,
      firstName: line.firstName,
      lastName: line.lastName,
      method: line.method,
      customerGroup: line.customerGroup,
      transactionType: "offline",
    );
  }

  @override
  List<Object?> get props => [
        id,
        refNo,
        createdAt,
        amount,
        method,
        contactName,
        contactId,
        customerGroup,
        firstName,
        lastName,
        userId,
        offline,
      ];
}
