// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'follow_up.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class FollowUpAdapter extends TypeAdapter<FollowUp> {
  @override
  final int typeId = 37;

  @override
  FollowUp read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return FollowUp(
      id: fields[0] as int?,
      businessId: fields[1] as int?,
      contactId: fields[2] as int?,
      title: fields[3] as String?,
      status: fields[4] as String?,
      startDatetime: fields[5] as String?,
      endDatetime: fields[6] as String?,
      description: fields[7] as String?,
      scheduleType: fields[8] as String?,
      allowNotification: fields[9] as int?,
      notifyVia: (fields[10] as Map?)?.cast<String, int>(),
      notifyBefore: fields[11] as int?,
      notifyType: fields[12] as String?,
      createdBy: fields[13] as int?,
      followupAdditionalInfo: fields[14] as String?,
      createdAt: fields[15] as String?,
      updatedAt: fields[16] as String?,
      customer: fields[17] as Customer?,
      offline: fields[18] as bool,
      followUpToAPI: fields[19] as FollowUpToAPI?,
      refNo: fields[20] as String?,
      users: (fields[21] as List?)?.cast<UserModel>(),
    );
  }

  @override
  void write(BinaryWriter writer, FollowUp obj) {
    writer
      ..writeByte(22)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.businessId)
      ..writeByte(2)
      ..write(obj.contactId)
      ..writeByte(3)
      ..write(obj.title)
      ..writeByte(4)
      ..write(obj.status)
      ..writeByte(5)
      ..write(obj.startDatetime)
      ..writeByte(6)
      ..write(obj.endDatetime)
      ..writeByte(7)
      ..write(obj.description)
      ..writeByte(8)
      ..write(obj.scheduleType)
      ..writeByte(9)
      ..write(obj.allowNotification)
      ..writeByte(10)
      ..write(obj.notifyVia)
      ..writeByte(11)
      ..write(obj.notifyBefore)
      ..writeByte(12)
      ..write(obj.notifyType)
      ..writeByte(13)
      ..write(obj.createdBy)
      ..writeByte(14)
      ..write(obj.followupAdditionalInfo)
      ..writeByte(15)
      ..write(obj.createdAt)
      ..writeByte(16)
      ..write(obj.updatedAt)
      ..writeByte(17)
      ..write(obj.customer)
      ..writeByte(18)
      ..write(obj.offline)
      ..writeByte(19)
      ..write(obj.followUpToAPI)
      ..writeByte(20)
      ..write(obj.refNo)
      ..writeByte(21)
      ..write(obj.users);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is FollowUpAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class CustomerAdapter extends TypeAdapter<Customer> {
  @override
  final int typeId = 38;

  @override
  Customer read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Customer(
      id: fields[0] as int,
      businessId: fields[1] as int,
      type: fields[2] as String,
      supplierBusinessName: fields[3] as String?,
      name: fields[4] as String?,
      prefix: fields[5] as String?,
      firstName: fields[6] as String?,
      middleName: fields[7] as String?,
      lastName: fields[8] as String?,
      email: fields[9] as String?,
      contactId: fields[10] as String?,
      contactStatus: fields[11] as String?,
      taxNumber: fields[12] as String?,
      city: fields[13] as String?,
      state: fields[14] as String?,
      country: fields[15] as String?,
      addressLine1: fields[16] as String?,
      addressLine2: fields[17] as String?,
      zipCode: fields[18] as String?,
      dob: fields[19] as String?,
      mobile: fields[20] as String?,
      landline: fields[21] as String?,
      alternateNumber: fields[22] as String?,
      payTermNumber: fields[23] as String?,
      payTermType: fields[24] as String?,
      creditLimit: fields[25] as String?,
      createdBy: fields[26] as int,
      balance: fields[27] as String?,
      totalRp: fields[28] as int?,
      totalRpUsed: fields[29] as int?,
      totalRpExpired: fields[30] as int?,
      isDefault: fields[31] as int?,
      shippingAddress: fields[32] as String?,
      position: fields[33] as String?,
      customerGroupId: fields[34] as int?,
      crmSource: fields[35] as String?,
      crmLifeStage: fields[36] as String?,
      customField1: fields[37] as String?,
      customField2: fields[38] as String?,
      customField3: fields[39] as String?,
      customField4: fields[40] as String?,
      customField5: fields[41] as String?,
      customField6: fields[42] as String?,
      customField7: fields[43] as String?,
      customField8: fields[44] as String?,
      customField9: fields[45] as String?,
      customField10: fields[46] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, Customer obj) {
    writer
      ..writeByte(47)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.businessId)
      ..writeByte(2)
      ..write(obj.type)
      ..writeByte(3)
      ..write(obj.supplierBusinessName)
      ..writeByte(4)
      ..write(obj.name)
      ..writeByte(5)
      ..write(obj.prefix)
      ..writeByte(6)
      ..write(obj.firstName)
      ..writeByte(7)
      ..write(obj.middleName)
      ..writeByte(8)
      ..write(obj.lastName)
      ..writeByte(9)
      ..write(obj.email)
      ..writeByte(10)
      ..write(obj.contactId)
      ..writeByte(11)
      ..write(obj.contactStatus)
      ..writeByte(12)
      ..write(obj.taxNumber)
      ..writeByte(13)
      ..write(obj.city)
      ..writeByte(14)
      ..write(obj.state)
      ..writeByte(15)
      ..write(obj.country)
      ..writeByte(16)
      ..write(obj.addressLine1)
      ..writeByte(17)
      ..write(obj.addressLine2)
      ..writeByte(18)
      ..write(obj.zipCode)
      ..writeByte(19)
      ..write(obj.dob)
      ..writeByte(20)
      ..write(obj.mobile)
      ..writeByte(21)
      ..write(obj.landline)
      ..writeByte(22)
      ..write(obj.alternateNumber)
      ..writeByte(23)
      ..write(obj.payTermNumber)
      ..writeByte(24)
      ..write(obj.payTermType)
      ..writeByte(25)
      ..write(obj.creditLimit)
      ..writeByte(26)
      ..write(obj.createdBy)
      ..writeByte(27)
      ..write(obj.balance)
      ..writeByte(28)
      ..write(obj.totalRp)
      ..writeByte(29)
      ..write(obj.totalRpUsed)
      ..writeByte(30)
      ..write(obj.totalRpExpired)
      ..writeByte(31)
      ..write(obj.isDefault)
      ..writeByte(32)
      ..write(obj.shippingAddress)
      ..writeByte(33)
      ..write(obj.position)
      ..writeByte(34)
      ..write(obj.customerGroupId)
      ..writeByte(35)
      ..write(obj.crmSource)
      ..writeByte(36)
      ..write(obj.crmLifeStage)
      ..writeByte(37)
      ..write(obj.customField1)
      ..writeByte(38)
      ..write(obj.customField2)
      ..writeByte(39)
      ..write(obj.customField3)
      ..writeByte(40)
      ..write(obj.customField4)
      ..writeByte(41)
      ..write(obj.customField5)
      ..writeByte(42)
      ..write(obj.customField6)
      ..writeByte(43)
      ..write(obj.customField7)
      ..writeByte(44)
      ..write(obj.customField8)
      ..writeByte(45)
      ..write(obj.customField9)
      ..writeByte(46)
      ..write(obj.customField10);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CustomerAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FollowUp _$FollowUpFromJson(Map<String, dynamic> json) => FollowUp(
      id: (json['id'] as num?)?.toInt(),
      businessId: (json['business_id'] as num?)?.toInt(),
      contactId: (json['contact_id'] as num?)?.toInt(),
      title: json['title'] as String?,
      status: json['status'] as String?,
      startDatetime: json['start_datetime'] as String?,
      endDatetime: json['end_datetime'] as String?,
      description: json['description'] as String?,
      scheduleType: json['schedule_type'] as String?,
      allowNotification: (json['allow_notification'] as num?)?.toInt(),
      notifyVia: (json['notify_via'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(k, (e as num).toInt()),
      ),
      notifyBefore: (json['notify_before'] as num?)?.toInt(),
      notifyType: json['notify_type'] as String?,
      createdBy: (json['created_by'] as num?)?.toInt(),
      followupAdditionalInfo: json['followup_additional_info'] as String?,
      createdAt: json['created_at'] as String?,
      updatedAt: json['updated_at'] as String?,
      customer:
          FollowUp._customerFromJson(json['customer'] as Map<String, dynamic>?),
      users: FollowUp._usersFromJson(json['users'] as List?),
    );

Map<String, dynamic> _$FollowUpToJson(FollowUp instance) => <String, dynamic>{
      'id': instance.id,
      'business_id': instance.businessId,
      'contact_id': instance.contactId,
      'title': instance.title,
      'status': instance.status,
      'start_datetime': instance.startDatetime,
      'end_datetime': instance.endDatetime,
      'description': instance.description,
      'schedule_type': instance.scheduleType,
      'allow_notification': instance.allowNotification,
      'notify_via': instance.notifyVia,
      'notify_before': instance.notifyBefore,
      'notify_type': instance.notifyType,
      'created_by': instance.createdBy,
      'followup_additional_info': instance.followupAdditionalInfo,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
      'customer': FollowUp._customerToJson(instance.customer),
      'users': instance.users?.map((e) => e.toJson()).toList(),
    };

Customer _$CustomerFromJson(Map<String, dynamic> json) => Customer(
      id: (json['id'] as num).toInt(),
      businessId: (json['business_id'] as num).toInt(),
      type: json['type'] as String,
      supplierBusinessName: json['supplier_business_name'] as String?,
      name: json['name'] as String?,
      prefix: json['prefix'] as String?,
      firstName: json['first_name'] as String?,
      middleName: json['middle_name'] as String?,
      lastName: json['last_name'] as String?,
      email: json['email'] as String?,
      contactId: json['contact_id'] as String?,
      contactStatus: json['contact_status'] as String?,
      taxNumber: json['tax_number'] as String?,
      city: json['city'] as String?,
      state: json['state'] as String?,
      country: json['country'] as String?,
      addressLine1: json['address_line_1'] as String?,
      addressLine2: json['address_line_2'] as String?,
      zipCode: json['zip_code'] as String?,
      dob: json['dob'] as String?,
      mobile: json['mobile'] as String?,
      landline: json['landline'] as String?,
      alternateNumber: json['alternate_number'] as String?,
      payTermNumber: json['pay_term_number'] as String?,
      payTermType: json['pay_term_type'] as String?,
      creditLimit: json['credit_limit'] as String?,
      createdBy: (json['created_by'] as num).toInt(),
      balance: json['balance'] as String?,
      totalRp: (json['total_rp'] as num?)?.toInt(),
      totalRpUsed: (json['total_rp_used'] as num?)?.toInt(),
      totalRpExpired: (json['total_rp_expired'] as num?)?.toInt(),
      isDefault: (json['is_default'] as num?)?.toInt(),
      shippingAddress: json['shipping_address'] as String?,
      position: json['position'] as String?,
      customerGroupId: (json['customer_group_id'] as num?)?.toInt(),
      crmSource: json['crm_source'] as String?,
      crmLifeStage: json['crm_life_stage'] as String?,
      customField1: json['custom_field1'] as String?,
      customField2: json['custom_field2'] as String?,
      customField3: json['custom_field3'] as String?,
      customField4: json['custom_field4'] as String?,
      customField5: json['custom_field5'] as String?,
      customField6: json['custom_field6'] as String?,
      customField7: json['custom_field7'] as String?,
      customField8: json['custom_field8'] as String?,
      customField9: json['custom_field9'] as String?,
      customField10: json['custom_field10'] as String?,
    );

Map<String, dynamic> _$CustomerToJson(Customer instance) => <String, dynamic>{
      'id': instance.id,
      'business_id': instance.businessId,
      'type': instance.type,
      'supplier_business_name': instance.supplierBusinessName,
      'name': instance.name,
      'prefix': instance.prefix,
      'first_name': instance.firstName,
      'middle_name': instance.middleName,
      'last_name': instance.lastName,
      'email': instance.email,
      'contact_id': instance.contactId,
      'contact_status': instance.contactStatus,
      'tax_number': instance.taxNumber,
      'city': instance.city,
      'state': instance.state,
      'country': instance.country,
      'address_line_1': instance.addressLine1,
      'address_line_2': instance.addressLine2,
      'zip_code': instance.zipCode,
      'dob': instance.dob,
      'mobile': instance.mobile,
      'landline': instance.landline,
      'alternate_number': instance.alternateNumber,
      'pay_term_number': instance.payTermNumber,
      'pay_term_type': instance.payTermType,
      'credit_limit': instance.creditLimit,
      'created_by': instance.createdBy,
      'balance': instance.balance,
      'total_rp': instance.totalRp,
      'total_rp_used': instance.totalRpUsed,
      'total_rp_expired': instance.totalRpExpired,
      'is_default': instance.isDefault,
      'shipping_address': instance.shippingAddress,
      'position': instance.position,
      'customer_group_id': instance.customerGroupId,
      'crm_source': instance.crmSource,
      'crm_life_stage': instance.crmLifeStage,
      'custom_field1': instance.customField1,
      'custom_field2': instance.customField2,
      'custom_field3': instance.customField3,
      'custom_field4': instance.customField4,
      'custom_field5': instance.customField5,
      'custom_field6': instance.customField6,
      'custom_field7': instance.customField7,
      'custom_field8': instance.customField8,
      'custom_field9': instance.customField9,
      'custom_field10': instance.customField10,
    };
