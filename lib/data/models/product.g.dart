// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'product.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class ProductAdapter extends TypeAdapter<Product> {
  @override
  final int typeId = 13;

  @override
  Product read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Product(
      id: fields[0] as int,
      name: fields[1] as String,
      businessId: fields[2] as int,
      type: fields[3] as String?,
      subUnitIds: (fields[4] as List?)?.cast<String>(),
      enableStock: fields[5] as int,
      alertQuantity: fields[6] as String?,
      sku: fields[7] as String?,
      barcodeType: fields[8] as String?,
      expiryPeriod: fields[9] as String?,
      expiryPeriodType: fields[10] as String?,
      weight: fields[11] as String?,
      productDescription: fields[12] as String?,
      createdBy: fields[13] as int?,
      isInactive: fields[14] as int?,
      notForSelling: fields[15] as int?,
      maxInInvoice: fields[16] as int?,
      maxDiscount: fields[17] as int?,
      inOffer: fields[18] as int?,
      imageUrl: fields[19] as String,
      productVariations: (fields[20] as List).cast<ProductVariation>(),
      brand: fields[21] as Brand?,
      unit: fields[22] as Unit,
      category: fields[23] as ProductCategory?,
      subCategory: fields[24] as ProductCategory?,
      productTax: fields[25] as TaxRate?,
      productLocations: (fields[26] as List).cast<ProductLocation>(),
    );
  }

  @override
  void write(BinaryWriter writer, Product obj) {
    writer
      ..writeByte(27)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.businessId)
      ..writeByte(3)
      ..write(obj.type)
      ..writeByte(4)
      ..write(obj.subUnitIds)
      ..writeByte(5)
      ..write(obj.enableStock)
      ..writeByte(6)
      ..write(obj.alertQuantity)
      ..writeByte(7)
      ..write(obj.sku)
      ..writeByte(8)
      ..write(obj.barcodeType)
      ..writeByte(9)
      ..write(obj.expiryPeriod)
      ..writeByte(10)
      ..write(obj.expiryPeriodType)
      ..writeByte(11)
      ..write(obj.weight)
      ..writeByte(12)
      ..write(obj.productDescription)
      ..writeByte(13)
      ..write(obj.createdBy)
      ..writeByte(14)
      ..write(obj.isInactive)
      ..writeByte(15)
      ..write(obj.notForSelling)
      ..writeByte(16)
      ..write(obj.maxInInvoice)
      ..writeByte(17)
      ..write(obj.maxDiscount)
      ..writeByte(18)
      ..write(obj.inOffer)
      ..writeByte(19)
      ..write(obj.imageUrl)
      ..writeByte(20)
      ..write(obj.productVariations)
      ..writeByte(21)
      ..write(obj.brand)
      ..writeByte(22)
      ..write(obj.unit)
      ..writeByte(23)
      ..write(obj.category)
      ..writeByte(24)
      ..write(obj.subCategory)
      ..writeByte(25)
      ..write(obj.productTax)
      ..writeByte(26)
      ..write(obj.productLocations);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ProductAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ProductCategoryAdapter extends TypeAdapter<ProductCategory> {
  @override
  final int typeId = 1;

  @override
  ProductCategory read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ProductCategory(
      id: fields[0] as int,
      businessId: fields[1] as int,
      name: fields[2] as String,
      shortCode: fields[3] as String?,
      parentId: fields[4] as int?,
      createdBy: fields[5] as int?,
      categoryType: fields[6] as String?,
      description: fields[7] as String?,
      imageUrl: fields[8] as String,
      subCategories: (fields[9] as List).cast<ProductCategory>(),
    );
  }

  @override
  void write(BinaryWriter writer, ProductCategory obj) {
    writer
      ..writeByte(10)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.businessId)
      ..writeByte(2)
      ..write(obj.name)
      ..writeByte(3)
      ..write(obj.shortCode)
      ..writeByte(4)
      ..write(obj.parentId)
      ..writeByte(5)
      ..write(obj.createdBy)
      ..writeByte(6)
      ..write(obj.categoryType)
      ..writeByte(7)
      ..write(obj.description)
      ..writeByte(8)
      ..write(obj.imageUrl)
      ..writeByte(9)
      ..write(obj.subCategories);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ProductCategoryAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class BrandAdapter extends TypeAdapter<Brand> {
  @override
  final int typeId = 3;

  @override
  Brand read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Brand(
      id: fields[0] as int?,
      businessId: fields[1] as int?,
      name: fields[2] as String?,
      description: fields[3] as String?,
      createdBy: fields[4] as int?,
      deletedAt: fields[5] as DateTime?,
      createdAt: fields[6] as DateTime?,
      updatedAt: fields[7] as DateTime?,
    );
  }

  @override
  void write(BinaryWriter writer, Brand obj) {
    writer
      ..writeByte(8)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.businessId)
      ..writeByte(2)
      ..write(obj.name)
      ..writeByte(3)
      ..write(obj.description)
      ..writeByte(4)
      ..write(obj.createdBy)
      ..writeByte(5)
      ..write(obj.deletedAt)
      ..writeByte(6)
      ..write(obj.createdAt)
      ..writeByte(7)
      ..write(obj.updatedAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is BrandAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ProductLocationAdapter extends TypeAdapter<ProductLocation> {
  @override
  final int typeId = 12;

  @override
  ProductLocation read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ProductLocation(
      id: fields[0] as int,
      businessId: fields[1] as int?,
      locationId: fields[2] as String?,
      name: fields[3] as String,
      isActive: fields[4] as int?,
      defaultPaymentAccounts: (fields[5] as Map?)?.cast<String, dynamic>(),
      createdAt: fields[6] as DateTime?,
      updatedAt: fields[7] as DateTime?,
      purchaseAccountId: fields[8] as String?,
      showQtyInfo: fields[9] as int?,
      activityCode: fields[10] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, ProductLocation obj) {
    writer
      ..writeByte(11)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.businessId)
      ..writeByte(2)
      ..write(obj.locationId)
      ..writeByte(3)
      ..write(obj.name)
      ..writeByte(4)
      ..write(obj.isActive)
      ..writeByte(5)
      ..write(obj.defaultPaymentAccounts)
      ..writeByte(6)
      ..write(obj.createdAt)
      ..writeByte(7)
      ..write(obj.updatedAt)
      ..writeByte(8)
      ..write(obj.purchaseAccountId)
      ..writeByte(9)
      ..write(obj.showQtyInfo)
      ..writeByte(10)
      ..write(obj.activityCode);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ProductLocationAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ProductVariationAdapter extends TypeAdapter<ProductVariation> {
  @override
  final int typeId = 8;

  @override
  ProductVariation read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ProductVariation(
      id: fields[0] as int?,
      name: fields[1] as String?,
      productId: fields[2] as int?,
      createdAt: fields[3] as DateTime?,
      updatedAt: fields[4] as DateTime?,
      variations: (fields[5] as List?)?.cast<Variation>(),
    );
  }

  @override
  void write(BinaryWriter writer, ProductVariation obj) {
    writer
      ..writeByte(6)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.productId)
      ..writeByte(3)
      ..write(obj.createdAt)
      ..writeByte(4)
      ..write(obj.updatedAt)
      ..writeByte(5)
      ..write(obj.variations);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ProductVariationAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class VariationAdapter extends TypeAdapter<Variation> {
  @override
  final int typeId = 9;

  @override
  Variation read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Variation(
      id: fields[0] as int,
      productId: fields[1] as int,
      name: fields[2] as String,
      subSku: fields[3] as String,
      productVariationId: fields[4] as int,
      defaultPurchasePrice: fields[5] as double,
      dppIncTax: fields[6] as double,
      profitPercent: fields[7] as double,
      defaultSellPrice: fields[8] as double,
      sellPriceIncTax: fields[9] as double,
      createdAt: fields[10] as DateTime,
      updatedAt: fields[11] as DateTime,
      variationLocationDetails:
          (fields[12] as List).cast<VariationLocationDetails>(),
      media: (fields[13] as List).cast<Media>(),
      discounts: (fields[14] as List).cast<Discount>(),
      sellingPriceGroups: (fields[15] as List).cast<ProductPriceGroup?>(),
      comboVariations: (fields[16] as List).cast<ComboVariation?>(),
    );
  }

  @override
  void write(BinaryWriter writer, Variation obj) {
    writer
      ..writeByte(17)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.productId)
      ..writeByte(2)
      ..write(obj.name)
      ..writeByte(3)
      ..write(obj.subSku)
      ..writeByte(4)
      ..write(obj.productVariationId)
      ..writeByte(5)
      ..write(obj.defaultPurchasePrice)
      ..writeByte(6)
      ..write(obj.dppIncTax)
      ..writeByte(7)
      ..write(obj.profitPercent)
      ..writeByte(8)
      ..write(obj.defaultSellPrice)
      ..writeByte(9)
      ..write(obj.sellPriceIncTax)
      ..writeByte(10)
      ..write(obj.createdAt)
      ..writeByte(11)
      ..write(obj.updatedAt)
      ..writeByte(12)
      ..write(obj.variationLocationDetails)
      ..writeByte(13)
      ..write(obj.media)
      ..writeByte(14)
      ..write(obj.discounts)
      ..writeByte(15)
      ..write(obj.sellingPriceGroups)
      ..writeByte(16)
      ..write(obj.comboVariations);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is VariationAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class VariationLocationDetailsAdapter
    extends TypeAdapter<VariationLocationDetails> {
  @override
  final int typeId = 10;

  @override
  VariationLocationDetails read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return VariationLocationDetails(
      id: fields[0] as int,
      productId: fields[1] as int,
      productVariationId: fields[2] as int,
      variationId: fields[3] as int,
      locationId: fields[4] as int,
      qtyAvailable: fields[5] as double,
      createdAt: fields[6] as DateTime,
      updatedAt: fields[7] as DateTime,
    );
  }

  @override
  void write(BinaryWriter writer, VariationLocationDetails obj) {
    writer
      ..writeByte(8)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.productId)
      ..writeByte(2)
      ..write(obj.productVariationId)
      ..writeByte(3)
      ..write(obj.variationId)
      ..writeByte(4)
      ..write(obj.locationId)
      ..writeByte(5)
      ..write(obj.qtyAvailable)
      ..writeByte(6)
      ..write(obj.createdAt)
      ..writeByte(7)
      ..write(obj.updatedAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is VariationLocationDetailsAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class MediaAdapter extends TypeAdapter<Media> {
  @override
  final int typeId = 11;

  @override
  Media read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Media(
      id: fields[0] as int,
      businessId: fields[1] as int,
      mediaName: fields[2] as String?,
      modelMediaType: fields[3] as String?,
      displayUrl: fields[4] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, Media obj) {
    writer
      ..writeByte(5)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.businessId)
      ..writeByte(2)
      ..write(obj.mediaName)
      ..writeByte(3)
      ..write(obj.modelMediaType)
      ..writeByte(4)
      ..write(obj.displayUrl);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MediaAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ComboVariationAdapter extends TypeAdapter<ComboVariation> {
  @override
  final int typeId = 50;

  @override
  ComboVariation read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ComboVariation(
      variationId: fields[0] as int,
      quantity: fields[1] as double,
      unitId: fields[2] as int,
    );
  }

  @override
  void write(BinaryWriter writer, ComboVariation obj) {
    writer
      ..writeByte(3)
      ..writeByte(0)
      ..write(obj.variationId)
      ..writeByte(1)
      ..write(obj.quantity)
      ..writeByte(2)
      ..write(obj.unitId);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ComboVariationAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class ProductPriceGroupAdapter extends TypeAdapter<ProductPriceGroup> {
  @override
  final int typeId = 48;

  @override
  ProductPriceGroup read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ProductPriceGroup(
      id: fields[0] as int,
      variationId: fields[1] as int,
      priceGroupId: fields[2] as int,
      priceIncTax: fields[3] as double,
      priceType: fields[4] as String,
      createdAt: fields[5] as DateTime,
      updatedAt: fields[6] as DateTime,
    );
  }

  @override
  void write(BinaryWriter writer, ProductPriceGroup obj) {
    writer
      ..writeByte(7)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.variationId)
      ..writeByte(2)
      ..write(obj.priceGroupId)
      ..writeByte(3)
      ..write(obj.priceIncTax)
      ..writeByte(4)
      ..write(obj.priceType)
      ..writeByte(5)
      ..write(obj.createdAt)
      ..writeByte(6)
      ..write(obj.updatedAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ProductPriceGroupAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class TaxRateAdapter extends TypeAdapter<TaxRate> {
  @override
  final int typeId = 0;

  @override
  TaxRate read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return TaxRate(
      id: fields[0] as int?,
      businessId: fields[1] as int?,
      name: fields[2] as String?,
      amount: fields[3] as double?,
      createdBy: fields[4] as int?,
      deletedAt: fields[5] as DateTime?,
      createdAt: fields[6] as DateTime?,
      updatedAt: fields[7] as DateTime?,
    );
  }

  @override
  void write(BinaryWriter writer, TaxRate obj) {
    writer
      ..writeByte(8)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.businessId)
      ..writeByte(2)
      ..write(obj.name)
      ..writeByte(3)
      ..write(obj.amount)
      ..writeByte(4)
      ..write(obj.createdBy)
      ..writeByte(5)
      ..write(obj.deletedAt)
      ..writeByte(6)
      ..write(obj.createdAt)
      ..writeByte(7)
      ..write(obj.updatedAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TaxRateAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class UnitAdapter extends TypeAdapter<Unit> {
  @override
  final int typeId = 7;

  @override
  Unit read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Unit(
      id: fields[0] as int,
      businessId: fields[1] as int,
      actualName: fields[2] as String,
      shortName: fields[3] as String,
      allowDecimal: fields[4] as int,
      baseUnitMultiplier: fields[5] as double,
      baseUnitId: fields[6] as int?,
      baseUnit: fields[7] as Unit?,
    );
  }

  @override
  void write(BinaryWriter writer, Unit obj) {
    writer
      ..writeByte(8)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.businessId)
      ..writeByte(2)
      ..write(obj.actualName)
      ..writeByte(3)
      ..write(obj.shortName)
      ..writeByte(4)
      ..write(obj.allowDecimal)
      ..writeByte(5)
      ..write(obj.baseUnitMultiplier)
      ..writeByte(6)
      ..write(obj.baseUnitId)
      ..writeByte(7)
      ..write(obj.baseUnit);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UnitAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$ProductImpl _$$ProductImplFromJson(Map<String, dynamic> json) =>
    _$ProductImpl(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String? ?? "N/A",
      businessId: (json['business_id'] as num).toInt(),
      type: json['type'] as String?,
      subUnitIds: (json['sub_unit_ids'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      enableStock: (json['enable_stock'] as num).toInt(),
      alertQuantity: json['alert_quantity'] as String?,
      sku: json['sku'] as String?,
      barcodeType: json['barcode_type'] as String?,
      expiryPeriod: json['expiry_period'] as String?,
      expiryPeriodType: json['expiry_period_type'] as String?,
      weight: json['weight'] as String?,
      productDescription: json['product_description'] as String?,
      createdBy: (json['created_by'] as num?)?.toInt(),
      isInactive: (json['is_inactive'] as num?)?.toInt(),
      notForSelling: (json['not_for_selling'] as num?)?.toInt(),
      maxInInvoice: (json['max_in_invoice'] as num?)?.toInt(),
      maxDiscount: (json['max_discount'] as num?)?.toInt(),
      inOffer: (json['in_offer'] as num?)?.toInt(),
      imageUrl: json['image_url'] as String,
      productVariations: (json['product_variations'] as List<dynamic>)
          .map((e) => ProductVariation.fromJson(e as Map<String, dynamic>))
          .toList(),
      brand: json['brand'] == null
          ? null
          : Brand.fromJson(json['brand'] as Map<String, dynamic>),
      unit: Unit.fromJson(json['unit'] as Map<String, dynamic>),
      category: json['category'] == null
          ? null
          : ProductCategory.fromJson(json['category'] as Map<String, dynamic>),
      subCategory: json['sub_category'] == null
          ? null
          : ProductCategory.fromJson(
              json['sub_category'] as Map<String, dynamic>),
      productTax: json['product_tax'] == null
          ? null
          : TaxRate.fromJson(json['product_tax'] as Map<String, dynamic>),
      productLocations: (json['product_locations'] as List<dynamic>)
          .map((e) => ProductLocation.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$ProductImplToJson(_$ProductImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'business_id': instance.businessId,
      'type': instance.type,
      'sub_unit_ids': instance.subUnitIds,
      'enable_stock': instance.enableStock,
      'alert_quantity': instance.alertQuantity,
      'sku': instance.sku,
      'barcode_type': instance.barcodeType,
      'expiry_period': instance.expiryPeriod,
      'expiry_period_type': instance.expiryPeriodType,
      'weight': instance.weight,
      'product_description': instance.productDescription,
      'created_by': instance.createdBy,
      'is_inactive': instance.isInactive,
      'not_for_selling': instance.notForSelling,
      'max_in_invoice': instance.maxInInvoice,
      'max_discount': instance.maxDiscount,
      'in_offer': instance.inOffer,
      'image_url': instance.imageUrl,
      'product_variations': instance.productVariations,
      'brand': instance.brand,
      'unit': instance.unit,
      'category': instance.category,
      'sub_category': instance.subCategory,
      'product_tax': instance.productTax,
      'product_locations': instance.productLocations,
    };

_$ProductCategoryImpl _$$ProductCategoryImplFromJson(
        Map<String, dynamic> json) =>
    _$ProductCategoryImpl(
      id: (json['id'] as num).toInt(),
      businessId: (json['business_id'] as num).toInt(),
      name: json['name'] as String? ?? "N/A",
      shortCode: json['short_code'] as String?,
      parentId: (json['parent_id'] as num?)?.toInt(),
      createdBy: (json['created_by'] as num?)?.toInt(),
      categoryType: json['category_type'] as String?,
      description: json['description'] as String?,
      imageUrl: json['image_url'] as String? ?? _placeholderImageUrl,
      subCategories: (json['sub_categories'] as List<dynamic>?)
              ?.map((e) => ProductCategory.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
    );

Map<String, dynamic> _$$ProductCategoryImplToJson(
        _$ProductCategoryImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'business_id': instance.businessId,
      'name': instance.name,
      'short_code': instance.shortCode,
      'parent_id': instance.parentId,
      'created_by': instance.createdBy,
      'category_type': instance.categoryType,
      'description': instance.description,
      'image_url': instance.imageUrl,
      'sub_categories': instance.subCategories,
    };

_$BrandImpl _$$BrandImplFromJson(Map<String, dynamic> json) => _$BrandImpl(
      id: (json['id'] as num?)?.toInt(),
      businessId: (json['business_id'] as num?)?.toInt(),
      name: json['name'] as String?,
      description: json['description'] as String?,
      createdBy: (json['created_by'] as num?)?.toInt(),
      deletedAt: json['deleted_at'] == null
          ? null
          : DateTime.parse(json['deleted_at'] as String),
      createdAt: json['created_at'] == null
          ? null
          : DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] == null
          ? null
          : DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$$BrandImplToJson(_$BrandImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'business_id': instance.businessId,
      'name': instance.name,
      'description': instance.description,
      'created_by': instance.createdBy,
      'deleted_at': instance.deletedAt?.toIso8601String(),
      'created_at': instance.createdAt?.toIso8601String(),
      'updated_at': instance.updatedAt?.toIso8601String(),
    };

_$ProductLocationImpl _$$ProductLocationImplFromJson(
        Map<String, dynamic> json) =>
    _$ProductLocationImpl(
      id: (json['id'] as num).toInt(),
      businessId: (json['business_id'] as num?)?.toInt(),
      locationId: json['location_id'] as String?,
      name: json['name'] as String? ?? "N/A",
      isActive: (json['is_active'] as num?)?.toInt(),
      defaultPaymentAccounts:
          _decodeJsonString(json['default_payment_accounts']),
      createdAt: json['created_at'] == null
          ? null
          : DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] == null
          ? null
          : DateTime.parse(json['updated_at'] as String),
      purchaseAccountId: json['purchase_account_id'] as String?,
      showQtyInfo: (json['show_qty_info'] as num?)?.toInt(),
      activityCode: json['activity_code'] as String?,
    );

Map<String, dynamic> _$$ProductLocationImplToJson(
        _$ProductLocationImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'business_id': instance.businessId,
      'location_id': instance.locationId,
      'name': instance.name,
      'is_active': instance.isActive,
      'default_payment_accounts': instance.defaultPaymentAccounts,
      'created_at': instance.createdAt?.toIso8601String(),
      'updated_at': instance.updatedAt?.toIso8601String(),
      'purchase_account_id': instance.purchaseAccountId,
      'show_qty_info': instance.showQtyInfo,
      'activity_code': instance.activityCode,
    };

_$ProductVariationImpl _$$ProductVariationImplFromJson(
        Map<String, dynamic> json) =>
    _$ProductVariationImpl(
      id: (json['id'] as num?)?.toInt(),
      name: json['name'] as String?,
      productId: (json['product_id'] as num?)?.toInt(),
      createdAt: json['created_at'] == null
          ? null
          : DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] == null
          ? null
          : DateTime.parse(json['updated_at'] as String),
      variations: (json['variations'] as List<dynamic>?)
          ?.map((e) => Variation.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$$ProductVariationImplToJson(
        _$ProductVariationImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'product_id': instance.productId,
      'created_at': instance.createdAt?.toIso8601String(),
      'updated_at': instance.updatedAt?.toIso8601String(),
      'variations': instance.variations,
    };

_$VariationImpl _$$VariationImplFromJson(Map<String, dynamic> json) =>
    _$VariationImpl(
      id: (json['id'] as num).toInt(),
      productId: (json['product_id'] as num).toInt(),
      name: json['name'] as String,
      subSku: json['sub_sku'] as String,
      productVariationId: (json['product_variation_id'] as num).toInt(),
      defaultPurchasePrice:
          const DoubleConverter().fromJson(json['default_purchase_price']),
      dppIncTax: const DoubleConverter().fromJson(json['dpp_inc_tax']),
      profitPercent: const DoubleConverter().fromJson(json['profit_percent']),
      defaultSellPrice:
          const DoubleConverter().fromJson(json['default_sell_price']),
      sellPriceIncTax:
          const DoubleConverter().fromJson(json['sell_price_inc_tax']),
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      variationLocationDetails:
          (json['variation_location_details'] as List<dynamic>)
              .map((e) =>
                  VariationLocationDetails.fromJson(e as Map<String, dynamic>))
              .toList(),
      media: (json['media'] as List<dynamic>)
          .map((e) => Media.fromJson(e as Map<String, dynamic>))
          .toList(),
      discounts: (json['discounts'] as List<dynamic>?)
              ?.map((e) => Discount.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      sellingPriceGroups: (json['selling_price_group'] as List<dynamic>?)
              ?.map((e) => e == null
                  ? null
                  : ProductPriceGroup.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      comboVariations: (json['combo_variations'] as List<dynamic>?)
              ?.map((e) => e == null
                  ? null
                  : ComboVariation.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
    );

Map<String, dynamic> _$$VariationImplToJson(_$VariationImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'product_id': instance.productId,
      'name': instance.name,
      'sub_sku': instance.subSku,
      'product_variation_id': instance.productVariationId,
      'default_purchase_price':
          const DoubleConverter().toJson(instance.defaultPurchasePrice),
      'dpp_inc_tax': const DoubleConverter().toJson(instance.dppIncTax),
      'profit_percent': const DoubleConverter().toJson(instance.profitPercent),
      'default_sell_price':
          const DoubleConverter().toJson(instance.defaultSellPrice),
      'sell_price_inc_tax':
          const DoubleConverter().toJson(instance.sellPriceIncTax),
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
      'variation_location_details': instance.variationLocationDetails,
      'media': instance.media,
      'discounts': instance.discounts,
      'selling_price_group': instance.sellingPriceGroups,
      'combo_variations': instance.comboVariations,
    };

_$VariationLocationDetailsImpl _$$VariationLocationDetailsImplFromJson(
        Map<String, dynamic> json) =>
    _$VariationLocationDetailsImpl(
      id: (json['id'] as num).toInt(),
      productId: (json['product_id'] as num).toInt(),
      productVariationId: (json['product_variation_id'] as num).toInt(),
      variationId: (json['variation_id'] as num).toInt(),
      locationId: (json['location_id'] as num).toInt(),
      qtyAvailable: _parseDouble(json['qty_available']),
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$$VariationLocationDetailsImplToJson(
        _$VariationLocationDetailsImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'product_id': instance.productId,
      'product_variation_id': instance.productVariationId,
      'variation_id': instance.variationId,
      'location_id': instance.locationId,
      'qty_available': instance.qtyAvailable,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
    };

_$DiscountImpl _$$DiscountImplFromJson(Map<String, dynamic> json) =>
    _$DiscountImpl(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String? ?? "N/A",
      businessId: (json['business_id'] as num).toInt(),
      brandId: (json['brand_id'] as num?)?.toInt(),
      categoryId: (json['category_id'] as num?)?.toInt(),
      locationId: (json['location_id'] as num).toInt(),
      priority: (json['priority'] as num?)?.toInt() ?? 1,
      discountType:
          $enumDecodeNullable(_$DiscountTypeEnumMap, json['discount_type']) ??
              DiscountType.percentage,
      discountAmount: json['discount_amount'] == null
          ? 0.0
          : const DoubleConverter().fromJson(json['discount_amount']),
      startsAt: const DateTimeConverter().fromJson(json['starts_at']),
      endsAt: const DateTimeConverter().fromJson(json['ends_at']),
      isActive: json['is_active'] == null
          ? true
          : const BooleanConverter().fromJson(json['is_active']),
      spg: json['spg'] as String?,
      applicableInCg: json['applicable_in_cg'] == null
          ? false
          : const BooleanConverter().fromJson(json['applicable_in_cg']),
      createdAt: const DateTimeConverter().fromJson(json['created_at']),
      updatedAt: const DateTimeConverter().fromJson(json['updated_at']),
      priceGroupId: json['price_group_id'] as String? ?? "0",
      applicableInSpg: json['applicable_in_spg'] == null
          ? false
          : const BooleanConverter().fromJson(json['applicable_in_spg']),
      maxQty: json['max_qty'] == null
          ? double.infinity
          : const DoubleConverter().fromJson(json['max_qty']),
      coupon: json['coupon'] as String?,
      formatedStartsAt: json['formated_starts_at'] as String? ?? "",
      formatedEndsAt: json['formated_ends_at'] as String? ?? "",
    );

Map<String, dynamic> _$$DiscountImplToJson(_$DiscountImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'business_id': instance.businessId,
      'brand_id': instance.brandId,
      'category_id': instance.categoryId,
      'location_id': instance.locationId,
      'priority': instance.priority,
      'discount_type': _$DiscountTypeEnumMap[instance.discountType]!,
      'discount_amount':
          const DoubleConverter().toJson(instance.discountAmount),
      'starts_at': const DateTimeConverter().toJson(instance.startsAt),
      'ends_at': const DateTimeConverter().toJson(instance.endsAt),
      'is_active': const BooleanConverter().toJson(instance.isActive),
      'spg': instance.spg,
      'applicable_in_cg':
          const BooleanConverter().toJson(instance.applicableInCg),
      'created_at': const DateTimeConverter().toJson(instance.createdAt),
      'updated_at': const DateTimeConverter().toJson(instance.updatedAt),
      'price_group_id': instance.priceGroupId,
      'applicable_in_spg':
          const BooleanConverter().toJson(instance.applicableInSpg),
      'max_qty': const DoubleConverter().toJson(instance.maxQty),
      'coupon': instance.coupon,
      'formated_starts_at': instance.formatedStartsAt,
      'formated_ends_at': instance.formatedEndsAt,
    };

const _$DiscountTypeEnumMap = {
  DiscountType.percentage: 'percentage',
  DiscountType.fixed: 'fixed',
};

_$MediaImpl _$$MediaImplFromJson(Map<String, dynamic> json) => _$MediaImpl(
      id: (json['id'] as num).toInt(),
      businessId: (json['business_id'] as num).toInt(),
      mediaName: json['media_name'] as String?,
      modelMediaType: json['model_media_type'] as String?,
      displayUrl: json['display_url'] as String?,
    );

Map<String, dynamic> _$$MediaImplToJson(_$MediaImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'business_id': instance.businessId,
      'media_name': instance.mediaName,
      'model_media_type': instance.modelMediaType,
      'display_url': instance.displayUrl,
    };

_$ComboVariationImpl _$$ComboVariationImplFromJson(Map<String, dynamic> json) =>
    _$ComboVariationImpl(
      variationId: int.parse(json['variation_id'] as String),
      quantity: _parseDouble(json['quantity']),
      unitId: int.parse(json['unit_id'] as String),
    );

Map<String, dynamic> _$$ComboVariationImplToJson(
        _$ComboVariationImpl instance) =>
    <String, dynamic>{
      'variation_id': instance.variationId,
      'quantity': instance.quantity,
      'unit_id': instance.unitId,
    };

_$ProductPriceGroupImpl _$$ProductPriceGroupImplFromJson(
        Map<String, dynamic> json) =>
    _$ProductPriceGroupImpl(
      id: (json['id'] as num).toInt(),
      variationId: (json['variation_id'] as num).toInt(),
      priceGroupId: (json['price_group_id'] as num).toInt(),
      priceIncTax: double.parse(json['price_inc_tax'] as String),
      priceType: json['price_type'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$$ProductPriceGroupImplToJson(
        _$ProductPriceGroupImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'variation_id': instance.variationId,
      'price_group_id': instance.priceGroupId,
      'price_inc_tax': instance.priceIncTax,
      'price_type': instance.priceType,
      'created_at': instance.createdAt.toIso8601String(),
      'updated_at': instance.updatedAt.toIso8601String(),
    };

_$TaxRateImpl _$$TaxRateImplFromJson(Map<String, dynamic> json) =>
    _$TaxRateImpl(
      id: (json['id'] as num?)?.toInt(),
      businessId: (json['business_id'] as num?)?.toInt(),
      name: json['name'] as String?,
      amount: (json['amount'] as num?)?.toDouble(),
      createdBy: (json['created_by'] as num?)?.toInt(),
      deletedAt: json['deleted_at'] == null
          ? null
          : DateTime.parse(json['deleted_at'] as String),
      createdAt: json['created_at'] == null
          ? null
          : DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] == null
          ? null
          : DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$$TaxRateImplToJson(_$TaxRateImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'business_id': instance.businessId,
      'name': instance.name,
      'amount': instance.amount,
      'created_by': instance.createdBy,
      'deleted_at': instance.deletedAt?.toIso8601String(),
      'created_at': instance.createdAt?.toIso8601String(),
      'updated_at': instance.updatedAt?.toIso8601String(),
    };

_$UnitImpl _$$UnitImplFromJson(Map<String, dynamic> json) => _$UnitImpl(
      id: (json['id'] as num).toInt(),
      businessId: (json['business_id'] as num).toInt(),
      actualName: json['actual_name'] as String,
      shortName: json['short_name'] as String,
      allowDecimal: (json['allow_decimal'] as num).toInt(),
      baseUnitMultiplier: json['base_unit_multiplier'] == null
          ? 1
          : _parseUnitMultiplierDouble(json['base_unit_multiplier']),
      baseUnitId: _tryParseInt(json['base_unit_id']),
      baseUnit: json['base_unit'] == null
          ? null
          : Unit.fromJson(json['base_unit'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$UnitImplToJson(_$UnitImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'business_id': instance.businessId,
      'actual_name': instance.actualName,
      'short_name': instance.shortName,
      'allow_decimal': instance.allowDecimal,
      'base_unit_multiplier': instance.baseUnitMultiplier,
      'base_unit_id': instance.baseUnitId,
      'base_unit': instance.baseUnit,
    };
