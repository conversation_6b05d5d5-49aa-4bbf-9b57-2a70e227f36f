import 'package:json_annotation/json_annotation.dart';
import 'package:hive/hive.dart';
import 'package:equatable/equatable.dart';
import 'package:we2up/data/db/db_manager.dart';
import 'package:we2up/data/models/payment.dart';
import 'package:we2up/data/models/payment_status.dart';
import 'package:we2up/data/models/sell_to_api.dart';
import 'package:we2up/data/models/shipment_status.dart';

import '../../utils/we2up_constants.dart';
import 'location_info.dart';

part 'sell.g.dart';

@JsonSerializable()
@HiveType(typeId: 20)
class Sell extends Equatable {
  @JsonKey(name: 'id')
  @HiveField(0)
  final int? id;

  @JsonKey(name: 'business_id')
  @HiveField(1)
  final int? businessId;

  @JsonKey(name: 'location_id')
  @HiveField(2)
  final int? locationId;

  @<PERSON>son<PERSON>ey(name: 'res_table_id')
  @HiveField(3)
  final int? resTableId;

  @Json<PERSON>ey(name: 'res_waiter_id')
  @HiveField(4)
  final int? resWaiterId;

  @JsonKey(name: 'res_order_status')
  @HiveField(5)
  final String? resOrderStatus;

  @JsonKey(name: 'type')
  @HiveField(6)
  final String? type;

  @JsonKey(name: 'sub_type')
  @HiveField(7)
  final String? subType;

  @JsonKey(name: 'status')
  @HiveField(8)
  final String? status;

  @JsonKey(name: 'sub_status')
  @HiveField(9)
  final String? subStatus;

  @JsonKey(name: 'is_quotation')
  @HiveField(10)
  final int? isQuotation;

  @JsonKey(name: 'payment_status', fromJson: paymentStatusFromJson)
  @HiveField(11)
  final PaymentStatus paymentStatus;

  @JsonKey(name: 'adjustment_type')
  @HiveField(12)
  final String? adjustmentType;

  @JsonKey(name: 'contact_id')
  @HiveField(13)
  final int? contactId;

  @JsonKey(name: 'customer_group_id')
  @HiveField(14)
  final int? customerGroupId;

  @JsonKey(name: 'invoice_no')
  @HiveField(15)
  final String? invoiceNo;

  @JsonKey(name: 'ref_no')
  @HiveField(16)
  final String? refNo;

  @JsonKey(name: 'source')
  @HiveField(17)
  final String? source;

  @JsonKey(name: 'subscription_no')
  @HiveField(18)
  final String? subscriptionNo;

  @JsonKey(name: 'subscription_repeat_on')
  @HiveField(19)
  final String? subscriptionRepeatOn;

  @JsonKey(
    name: 'transaction_date',
    fromJson: DateTime.parse,
    toJson: dateToString,
  )
  @HiveField(20)
  final DateTime transactionDate;

  @JsonKey(name: 'total_before_tax', fromJson: _toString)
  @HiveField(21)
  final String? totalBeforeTax;

  @JsonKey(name: 'tax_id')
  @HiveField(22)
  final int? taxId;

  @JsonKey(name: 'tax_amount', fromJson: _toString)
  @HiveField(23)
  final String? taxAmount;

  @JsonKey(name: 'discount_type')
  @HiveField(24)
  final String? discountType;

  @JsonKey(name: 'discount_amount', fromJson: _toString)
  @HiveField(25)
  final String? discountAmount;

  @JsonKey(name: 'rp_redeemed')
  @HiveField(26)
  final int? rpRedeemed;

  @JsonKey(name: 'rp_redeemed_amount', fromJson: _toString)
  @HiveField(27)
  final String? rpRedeemedAmount;

  @JsonKey(name: 'shipping_details')
  @HiveField(28)
  final String? shippingDetails;

  @JsonKey(name: 'shipping_address')
  @HiveField(29)
  final String? shippingAddress;

  @JsonKey(
    name: 'shipping_status',
    fromJson: shipmentStatusFromJson,
    toJson: shipmentStatusToJson,
  )
  @HiveField(30)
  final ShipmentStatus? shippingStatus;

  @JsonKey(name: 'delivered_to')
  @HiveField(31)
  final String? deliveredTo;

  @JsonKey(name: 'shipping_charges', fromJson: _toString)
  @HiveField(32)
  final String? shippingCharges;

  @JsonKey(name: 'shipping_custom_field_1')
  @HiveField(33)
  final String? shippingCustomField1;

  @JsonKey(name: 'shipping_custom_field_2')
  @HiveField(34)
  final String? shippingCustomField2;

  @JsonKey(name: 'shipping_custom_field_3')
  @HiveField(35)
  final String? shippingCustomField3;

  @JsonKey(name: 'shipping_custom_field_4')
  @HiveField(36)
  final String? shippingCustomField4;

  @JsonKey(name: 'shipping_custom_field_5')
  @HiveField(37)
  final String? shippingCustomField5;

  @JsonKey(name: 'additional_notes')
  @HiveField(38)
  final String? additionalNotes;

  @JsonKey(name: 'staff_note')
  @HiveField(39)
  final String? staffNote;

  @JsonKey(name: 'is_export')
  @HiveField(40)
  final int? isExport;

  @JsonKey(name: 'export_custom_fields_info')
  @HiveField(41)
  final String? exportCustomFieldsInfo;

  @JsonKey(name: 'round_off_amount', fromJson: _toString)
  @HiveField(42)
  final String? roundOffAmount;

  @JsonKey(name: 'additional_expense_key_1')
  @HiveField(43)
  final String? additionalExpenseKey1;

  @JsonKey(name: 'additional_expense_value_1')
  @HiveField(44)
  final String? additionalExpenseValue1;

  @JsonKey(name: 'additional_expense_key_2')
  @HiveField(45)
  final String? additionalExpenseKey2;

  @JsonKey(name: 'additional_expense_value_2')
  @HiveField(46)
  final String? additionalExpenseValue2;

  @JsonKey(name: 'additional_expense_key_3')
  @HiveField(47)
  final String? additionalExpenseKey3;

  @JsonKey(name: 'additional_expense_value_3')
  @HiveField(48)
  final String? additionalExpenseValue3;

  @JsonKey(name: 'additional_expense_key_4')
  @HiveField(49)
  final String? additionalExpenseKey4;

  @JsonKey(name: 'additional_expense_value_4')
  @HiveField(50)
  final String? additionalExpenseValue4;

  @JsonKey(name: 'final_total', fromJson: _toString)
  @HiveField(51)
  final String? finalTotal;

  @JsonKey(name: 'expense_category_id')
  @HiveField(52)
  final int? expenseCategoryId;

  @JsonKey(name: 'expense_for')
  @HiveField(53)
  final String? expenseFor;

  @JsonKey(name: 'commission_agent')
  @HiveField(54)
  final int? commissionAgent;

  @JsonKey(name: 'document')
  @HiveField(55)
  final String? document;

  @JsonKey(name: 'is_direct_sale')
  @HiveField(56)
  final int? isDirectSale;

  @JsonKey(name: 'is_suspend')
  @HiveField(57)
  final int? isSuspend;

  @JsonKey(name: 'exchange_rate', fromJson: _toString)
  @HiveField(58)
  final String? exchangeRate;

  @JsonKey(name: 'total_amount_recovered', fromJson: _toString)
  @HiveField(59)
  final String? totalAmountRecovered;

  @JsonKey(name: 'transfer_parent_id')
  @HiveField(60)
  final int? transferParentId;

  @JsonKey(name: 'return_parent_id')
  @HiveField(61)
  final int? returnParentId;

  @JsonKey(name: 'opening_stock_product_id')
  @HiveField(62)
  final int? openingStockProductId;

  @JsonKey(name: 'created_by')
  @HiveField(63)
  final int? createdBy;

  @JsonKey(name: 'crm_is_order_request')
  @HiveField(64)
  final int? crmIsOrderRequest;

  @JsonKey(name: 'prefer_payment_method')
  @HiveField(65)
  final String? preferPaymentMethod;

  @JsonKey(name: 'prefer_payment_account')
  @HiveField(66)
  final String? preferPaymentAccount;

  @JsonKey(name: 'sales_order_ids')
  @HiveField(67)
  final String? salesOrderIds;

  @JsonKey(name: 'purchase_order_ids')
  @HiveField(68)
  final String? purchaseOrderIds;

  @JsonKey(name: 'custom_field_1')
  @HiveField(69)
  final String? customField1;

  @JsonKey(name: 'custom_field_2')
  @HiveField(70)
  final String? customField2;

  @JsonKey(name: 'custom_field_3')
  @HiveField(71)
  final String? customField3;

  @JsonKey(name: 'custom_field_4')
  @HiveField(72)
  final String? customField4;

  @JsonKey(name: 'mfg_parent_production_purchase_id')
  @HiveField(73)
  final int? mfgParentProductionPurchaseId;

  @JsonKey(name: 'mfg_wasted_units')
  @HiveField(74)
  final String? mfgWastedUnits;

  @JsonKey(name: 'mfg_production_cost')
  @HiveField(75)
  final String? mfgProductionCost;

  @JsonKey(name: 'mfg_is_final')
  @HiveField(76)
  final int? mfgIsFinal;

  @JsonKey(name: 'repair_completed_on')
  @HiveField(77)
  final String? repairCompletedOn;

  @JsonKey(name: 'repair_warranty_id')
  @HiveField(78)
  final int? repairWarrantyId;

  @JsonKey(name: 'repair_brand_id')
  @HiveField(79)
  final int? repairBrandId;

  @JsonKey(name: 'repair_status_id')
  @HiveField(80)
  final int? repairStatusId;

  @JsonKey(name: 'repair_model_id')
  @HiveField(81)
  final int? repairModelId;

  @JsonKey(name: 'repair_job_sheet_id')
  @HiveField(82)
  final int? repairJobSheetId;

  @JsonKey(name: 'repair_defects')
  @HiveField(83)
  final String? repairDefects;

  @JsonKey(name: 'repair_serial_no')
  @HiveField(84)
  final String? repairSerialNo;

  @JsonKey(name: 'repair_checklist')
  @HiveField(85)
  final String? repairChecklist;

  @JsonKey(name: 'repair_security_pwd')
  @HiveField(86)
  final String? repairSecurityPwd;

  @JsonKey(name: 'repair_security_pattern')
  @HiveField(87)
  final String? repairSecurityPattern;

  @JsonKey(name: 'repair_due_date')
  @HiveField(88)
  final String? repairDueDate;

  @JsonKey(name: 'repair_device_id')
  @HiveField(89)
  final int? repairDeviceId;

  @JsonKey(name: 'repair_updates_notif')
  @HiveField(90)
  final int? repairUpdatesNotif;

  @JsonKey(name: 'essentials_duration')
  @HiveField(91)
  final String? essentialsDuration;

  @JsonKey(name: 'essentials_duration_unit')
  @HiveField(92)
  final String? essentialsDurationUnit;

  @JsonKey(name: 'essentials_amount_per_unit_duration', fromJson: _toString)
  @HiveField(93)
  final String? essentialsAmountPerUnitDuration;

  @JsonKey(name: 'essentials_allowances')
  @HiveField(94)
  final String? essentialsAllowances;

  @JsonKey(name: 'essentials_deductions')
  @HiveField(95)
  final String? essentialsDeductions;

  @JsonKey(name: 'woocommerce_order_id')
  @HiveField(96)
  final int? woocommerceOrderId;

  @JsonKey(name: 'import_batch')
  @HiveField(97)
  final String? importBatch;

  @JsonKey(name: 'import_time')
  @HiveField(98)
  final String? importTime;

  @JsonKey(name: 'types_of_service_id')
  @HiveField(99)
  final int? typesOfServiceId;

  @JsonKey(name: 'packing_id')
  @HiveField(100)
  final int? packingId;

  @JsonKey(name: 'terminal_id')
  @HiveField(101)
  final int? terminalId;

  @JsonKey(name: 'is_complementary')
  @HiveField(102)
  final int? isComplementary;

  @JsonKey(name: 'complementary_details')
  @HiveField(103)
  final String? complementaryDetails;

  @JsonKey(name: 'void_amount', fromJson: _toString)
  @HiveField(104)
  final String? voidAmount;

  @JsonKey(name: 'change_returned')
  @HiveField(105)
  final String? changeReturned;

  @JsonKey(name: 'voucher_id')
  @HiveField(106)
  final int? voucherId;

  @JsonKey(name: 'paying_due')
  @HiveField(107)
  final String? payingDue;

  @JsonKey(name: 'customers_voucher_no')
  @HiveField(108)
  final String? customersVoucherNo;

  @JsonKey(name: 'customers_voucher_id')
  @HiveField(109)
  final int? customersVoucherId;

  @JsonKey(name: 'is_integrated')
  @HiveField(110)
  final int? isIntegrated;

  @JsonKey(name: 'is_euvat_invoice')
  @HiveField(111)
  final int? isEuvatInvoice;

  @JsonKey(name: 'is_abandoned')
  @HiveField(112)
  final int? isAbandoned;

  @JsonKey(name: 'sellers_invoice_id')
  @HiveField(113)
  final int? sellersInvoiceId;

  @JsonKey(name: 'is_igs')
  @HiveField(114)
  final int? isIgs;

  @JsonKey(name: 'is_pos')
  @HiveField(115)
  final int? isPos;

  @JsonKey(name: 'is_tds')
  @HiveField(116)
  final int? isTds;

  @JsonKey(name: 'is_dine_in')
  @HiveField(117)
  final int? isDineIn;

  @JsonKey(name: 'created_on')
  @HiveField(118)
  final String? createdOn;

  @JsonKey(name: 'synced_on')
  @HiveField(119)
  final String? syncedOn;

  @JsonKey(name: 'payment_type')
  @HiveField(120)
  final String? paymentType;

  @JsonKey(name: 'payment_lines')
  @HiveField(121)
  final List<PaymentLine>? paymentLines;

  @JsonKey(name: 'sell_lines')
  @HiveField(122)
  final List<SellLine>? sellLines;

  @HiveField(123)
  @JsonKey(includeFromJson: false, includeToJson: false)
  final bool offline;

  @HiveField(124)
  @JsonKey(includeFromJson: false, includeToJson: false)
  final SellToAPI? sellToAPI;

  @HiveField(125)
  @JsonKey(name: 'location_info', fromJson: locationInfoFromListJson)
  final LocationInfo? locationInfo;

  @JsonKey(name: 'qr_code')
  @HiveField(126)
  final String? qrCode;

  @JsonKey(name: 'qr_code_img')
  @HiveField(127)
  final String? qrCodeImage;

  @JsonKey(name: 'invoice_url')
  @HiveField(128)
  final String? invoiceUrl;

  @JsonKey(name: 'shipping_company_id')
  @HiveField(129)
  final int? shippingCompanyId;

  const Sell({
    this.id,
    this.businessId,
    this.locationId,
    this.resTableId,
    this.resWaiterId,
    this.resOrderStatus,
    this.type,
    this.subType,
    required this.status,
    this.subStatus,
    this.isQuotation,
    required this.paymentStatus,
    this.adjustmentType,
    this.contactId,
    this.customerGroupId,
    this.invoiceNo,
    this.refNo,
    this.source,
    this.subscriptionNo,
    this.subscriptionRepeatOn,
    required this.transactionDate,
    this.totalBeforeTax,
    this.taxId,
    this.taxAmount,
    this.discountType = "percentage",
    this.discountAmount,
    this.rpRedeemed,
    this.rpRedeemedAmount,
    this.shippingDetails,
    this.shippingAddress,
    this.shippingStatus,
    this.deliveredTo,
    this.shippingCharges,
    this.shippingCustomField1,
    this.shippingCustomField2,
    this.shippingCustomField3,
    this.shippingCustomField4,
    this.shippingCustomField5,
    this.additionalNotes,
    this.staffNote,
    this.isExport,
    this.exportCustomFieldsInfo,
    this.roundOffAmount,
    this.additionalExpenseKey1,
    this.additionalExpenseValue1,
    this.additionalExpenseKey2,
    this.additionalExpenseValue2,
    this.additionalExpenseKey3,
    this.additionalExpenseValue3,
    this.additionalExpenseKey4,
    this.additionalExpenseValue4,
    this.finalTotal,
    this.expenseCategoryId,
    this.expenseFor,
    this.commissionAgent,
    this.document,
    this.isDirectSale,
    this.isSuspend,
    this.exchangeRate,
    this.totalAmountRecovered,
    this.transferParentId,
    this.returnParentId,
    this.openingStockProductId,
    this.createdBy,
    this.crmIsOrderRequest,
    this.preferPaymentMethod,
    this.preferPaymentAccount,
    this.salesOrderIds,
    this.purchaseOrderIds,
    this.customField1,
    this.customField2,
    this.customField3,
    this.customField4,
    this.mfgParentProductionPurchaseId,
    this.mfgWastedUnits,
    this.mfgProductionCost,
    this.mfgIsFinal,
    this.repairCompletedOn,
    this.repairWarrantyId,
    this.repairBrandId,
    this.repairStatusId,
    this.repairModelId,
    this.repairJobSheetId,
    this.repairDefects,
    this.repairSerialNo,
    this.repairChecklist,
    this.repairSecurityPwd,
    this.repairSecurityPattern,
    this.repairDueDate,
    this.repairDeviceId,
    this.repairUpdatesNotif,
    this.essentialsDuration,
    this.essentialsDurationUnit,
    this.essentialsAmountPerUnitDuration,
    this.essentialsAllowances,
    this.essentialsDeductions,
    this.woocommerceOrderId,
    this.importBatch,
    this.importTime,
    this.typesOfServiceId,
    this.packingId,
    this.terminalId,
    this.isComplementary,
    this.complementaryDetails,
    this.voidAmount,
    this.changeReturned,
    this.voucherId,
    this.payingDue,
    this.customersVoucherNo,
    this.customersVoucherId,
    this.isIntegrated,
    this.isEuvatInvoice,
    this.isAbandoned,
    this.sellersInvoiceId,
    this.isIgs,
    this.isPos,
    this.isTds,
    this.isDineIn,
    this.createdOn,
    this.syncedOn,
    this.paymentType,
    this.paymentLines,
    this.sellLines,
    this.offline = false,
    this.sellToAPI,
    this.qrCode,
    this.qrCodeImage,
    this.invoiceUrl,
    required this.locationInfo,
    this.shippingCompanyId,
  });

  static String? _toString(dynamic number) => number.toString();

  factory Sell.fromJson(Map<String, dynamic> json) => _$SellFromJson(json);

  Map<String, dynamic> toJson() => _$SellToJson(this);

  @override
  List<Object?> get props => [
        id,
        businessId,
        locationId,
        resTableId,
        resWaiterId,
        resOrderStatus,
        type,
        subType,
        status,
        subStatus,
        isQuotation,
        paymentStatus,
        adjustmentType,
        contactId,
        customerGroupId,
        invoiceNo,
        refNo,
        source,
        subscriptionNo,
        subscriptionRepeatOn,
        transactionDate,
        totalBeforeTax,
        taxId,
        taxAmount,
        discountType,
        discountAmount,
        rpRedeemed,
        rpRedeemedAmount,
        shippingDetails,
        shippingAddress,
        shippingStatus,
        deliveredTo,
        shippingCharges,
        shippingCustomField1,
        shippingCustomField2,
        shippingCustomField3,
        shippingCustomField4,
        shippingCustomField5,
        additionalNotes,
        staffNote,
        isExport,
        exportCustomFieldsInfo,
        roundOffAmount,
        additionalExpenseKey1,
        additionalExpenseValue1,
        additionalExpenseKey2,
        additionalExpenseValue2,
        additionalExpenseKey3,
        additionalExpenseValue3,
        additionalExpenseKey4,
        additionalExpenseValue4,
        finalTotal,
        expenseCategoryId,
        expenseFor,
        commissionAgent,
        document,
        isDirectSale,
        isSuspend,
        exchangeRate,
        totalAmountRecovered,
        transferParentId,
        returnParentId,
        openingStockProductId,
        createdBy,
        crmIsOrderRequest,
        preferPaymentMethod,
        preferPaymentAccount,
        salesOrderIds,
        purchaseOrderIds,
        customField1,
        customField2,
        customField3,
        customField4,
        mfgParentProductionPurchaseId,
        mfgWastedUnits,
        mfgProductionCost,
        mfgIsFinal,
        repairCompletedOn,
        repairWarrantyId,
        repairBrandId,
        repairStatusId,
        repairModelId,
        repairJobSheetId,
        repairDefects,
        repairSerialNo,
        repairChecklist,
        repairSecurityPwd,
        repairSecurityPattern,
        repairDueDate,
        repairDeviceId,
        repairUpdatesNotif,
        essentialsDuration,
        essentialsDurationUnit,
        essentialsAmountPerUnitDuration,
        essentialsAllowances,
        essentialsDeductions,
        woocommerceOrderId,
        importBatch,
        importTime,
        typesOfServiceId,
        packingId,
        terminalId,
        isComplementary,
        complementaryDetails,
        voidAmount,
        changeReturned,
        voucherId,
        payingDue,
        customersVoucherNo,
        customersVoucherId,
        isIntegrated,
        isEuvatInvoice,
        isAbandoned,
        sellersInvoiceId,
        isIgs,
        isPos,
        isTds,
        isDineIn,
        createdOn,
        syncedOn,
        paymentType,
        paymentLines,
        sellLines,
        offline,
        locationInfo,
        shippingCompanyId,
      ];
}

@JsonSerializable()
@HiveType(typeId: 28)
class PaymentLine extends Equatable {
  @JsonKey(name: 'id')
  @HiveField(0)
  final int? id;

  @JsonKey(name: 'transaction_id')
  @HiveField(1)
  final int? transactionId;

  @JsonKey(name: 'business_id')
  @HiveField(2)
  final int? businessId;

  @JsonKey(name: 'is_return')
  @HiveField(3)
  final int? isReturn;

  @JsonKey(name: 'amount')
  @HiveField(4)
  final String? amount;

  @JsonKey(name: 'method')
  @HiveField(5)
  final String? method;

  @JsonKey(name: 'card_transaction_number')
  @HiveField(6)
  final String? cardTransactionNumber;

  @JsonKey(name: 'card_number')
  @HiveField(7)
  final String? cardNumber;

  @JsonKey(name: 'card_type')
  @HiveField(8)
  final String? cardType;

  @JsonKey(name: 'card_holder_name')
  @HiveField(9)
  final String? cardHolderName;

  @JsonKey(name: 'card_month')
  @HiveField(10)
  final String? cardMonth;

  @JsonKey(name: 'card_year')
  @HiveField(11)
  final String? cardYear;

  @JsonKey(name: 'card_security')
  @HiveField(12)
  final String? cardSecurity;

  @JsonKey(name: 'cheque_number')
  @HiveField(13)
  final String? chequeNumber;

  @JsonKey(name: 'bank_account_number')
  @HiveField(14)
  final String? bankAccountNumber;

  @JsonKey(name: 'paid_on')
  @HiveField(15)
  final String? paidOn;

  @JsonKey(name: 'created_by')
  @HiveField(16)
  final int? createdBy;

  @JsonKey(name: 'paid_through_link')
  @HiveField(17)
  final int? paidThroughLink;

  @JsonKey(name: 'gateway')
  @HiveField(18)
  final String? gateway;

  @JsonKey(name: 'is_advance')
  @HiveField(19)
  final int? isAdvance;

  @JsonKey(name: 'payment_for')
  @HiveField(20)
  final int? paymentFor;

  @JsonKey(name: 'parent_id')
  @HiveField(21)
  final int? parentId;

  @JsonKey(name: 'note')
  @HiveField(22)
  final String? note;

  @JsonKey(name: 'document')
  @HiveField(23)
  final String? document;

  @JsonKey(name: 'payment_ref_no')
  @HiveField(24)
  final String? paymentRefNo;

  @JsonKey(name: 'account_id')
  @HiveField(25)
  final int? accountId;

  @JsonKey(name: 'created_at')
  @HiveField(26)
  final String? createdAt;

  @JsonKey(name: 'updated_at')
  @HiveField(27)
  final String? updatedAt;

  @JsonKey(name: 'contact_type')
  @HiveField(28)
  final String? contactType;

  @JsonKey(name: 'prepaid')
  @HiveField(29)
  final String? prepaid;

  @JsonKey(name: 'amount_second_curr')
  @HiveField(30)
  final String? amountSecondCurr;

  @JsonKey(name: 'payout_status')
  @HiveField(31)
  final String? payoutStatus;

  @JsonKey(name: 'installment_id')
  @HiveField(32)
  final int? installmentId;

  @JsonKey(name: 'payment_sub_type')
  @HiveField(33)
  final String? paymentSubType;

  @JsonKey(name: 'contact_name')
  @HiveField(34)
  final String? contactName;

  @JsonKey(name: 'first_name')
  @HiveField(35)
  final String? firstName;

  @JsonKey(name: 'last_name')
  @HiveField(36)
  final String? lastName;

  @JsonKey(name: 'user_id')
  @HiveField(37)
  final int? userId;

  @JsonKey(name: 'customer_group')
  @HiveField(38)
  final String? customerGroup;

  @HiveField(39)
  @JsonKey(includeFromJson: false, includeToJson: false)
  final bool offline;

  @HiveField(40)
  @JsonKey(includeFromJson: false, includeToJson: false)
  final String? refNo;

  const PaymentLine({
    this.id,
    this.transactionId,
    this.businessId,
    this.isReturn,
    this.amount,
    this.method,
    this.cardTransactionNumber,
    this.cardNumber,
    this.cardType,
    this.cardHolderName,
    this.cardMonth,
    this.cardYear,
    this.cardSecurity,
    this.chequeNumber,
    this.bankAccountNumber,
    this.paidOn,
    this.createdBy,
    this.paidThroughLink,
    this.gateway,
    this.isAdvance,
    this.paymentFor,
    this.parentId,
    this.note,
    this.document,
    this.paymentRefNo,
    this.accountId,
    this.createdAt,
    this.updatedAt,
    this.contactType,
    this.prepaid,
    this.amountSecondCurr,
    this.payoutStatus,
    this.installmentId,
    this.paymentSubType,
    this.contactName,
    this.userId,
    this.firstName,
    this.customerGroup,
    this.lastName,
    this.offline = false,
    this.refNo,
  });

  factory PaymentLine.fromPayment(Payment payment) {
    final contact = contactsBox.get(payment.paymentFor)!;
    return PaymentLine(
      method: payment.method,
      amount: payment.amount.toString(),
      accountId: payment.accountId,
      note: payment.note,
      contactName: contact.name,
      firstName: contact.firstName,
      lastName: contact.lastName,
      userId: loginData.userId!,
      paymentFor: payment.paymentFor,
      createdBy: loginData.userId!,
      paidOn: dateToString(payment.transactionDate ?? DateTime.now()),
      contactType: contact.type,
      customerGroup: contact.customerGroup,
      offline: payment.offline,
      paymentRefNo: "Offline",
    );
  }

  factory PaymentLine.fromJson(Map<String, dynamic> json) =>
      _$PaymentLineFromJson(json);

  Map<String, dynamic> toJson() => _$PaymentLineToJson(this);

  @override
  List<Object?> get props => [
        id,
        transactionId,
        businessId,
        isReturn,
        amount,
        method,
        cardTransactionNumber,
        cardNumber,
        cardType,
        cardHolderName,
        cardMonth,
        cardYear,
        cardSecurity,
        chequeNumber,
        bankAccountNumber,
        paidOn,
        createdBy,
        paidThroughLink,
        gateway,
        isAdvance,
        paymentFor,
        parentId,
        note,
        document,
        paymentRefNo,
        accountId,
        createdAt,
        updatedAt,
        contactType,
        prepaid,
        amountSecondCurr,
        payoutStatus,
        installmentId,
        paymentSubType,
        customerGroup,
        userId,
        firstName,
        lastName,
        contactName,
      ];
}

@JsonSerializable()
@HiveType(typeId: 29)
class SellLine extends HiveObject with EquatableMixin {
  @JsonKey(name: 'id')
  @HiveField(0)
  final int? id;

  @JsonKey(name: 'transaction_id')
  @HiveField(1)
  final int? transactionId;

  @JsonKey(name: 'product_id')
  @HiveField(2)
  final int? productId;

  @JsonKey(name: 'variation_id')
  @HiveField(3)
  final int? variationId;

  @JsonKey(name: 'quantity')
  @HiveField(4)
  final double? quantity;

  @JsonKey(name: 'secondary_unit_quantity')
  @HiveField(5)
  final String? secondaryUnitQuantity;

  @JsonKey(name: 'mfg_waste_percent')
  @HiveField(6)
  final String? mfgWastePercent;

  @JsonKey(name: 'quantity_returned')
  @HiveField(7)
  String? quantityReturned;

  @JsonKey(name: 'unit_price_before_discount')
  @HiveField(8)
  final String? unitPriceBeforeDiscount;

  @JsonKey(name: 'unit_price')
  @HiveField(9)
  final String? unitPrice;

  @JsonKey(name: 'line_discount_type')
  @HiveField(10)
  final String? lineDiscountType;

  @JsonKey(name: 'line_discount_amount')
  @HiveField(11)
  final String? lineDiscountAmount;

  @JsonKey(name: 'unit_price_inc_tax')
  @HiveField(12)
  final String? unitPriceIncTax;

  @JsonKey(name: 'item_tax')
  @HiveField(13)
  final String? itemTax;

  @JsonKey(name: 'tax_id')
  @HiveField(14)
  final int? taxId;

  @JsonKey(name: 'discount_id')
  @HiveField(15)
  final int? discountId;

  @JsonKey(name: 'lot_no_line_id')
  @HiveField(16)
  final int? lotNoLineId;

  @JsonKey(name: 'sell_line_note')
  @HiveField(17)
  final String? sellLineNote;

  @JsonKey(name: 'so_line_id')
  @HiveField(18)
  final int? soLineId;

  @JsonKey(name: 'so_quantity_invoiced')
  @HiveField(19)
  final String? soQuantityInvoiced;

  @JsonKey(name: 'woocommerce_line_items_id')
  @HiveField(20)
  final int? woocommerceLineItemsId;

  @JsonKey(name: 'res_service_staff_id')
  @HiveField(21)
  final int? resServiceStaffId;

  @JsonKey(name: 'res_line_order_status')
  @HiveField(22)
  final String? resLineOrderStatus;

  @JsonKey(name: 'parent_sell_line_id')
  @HiveField(23)
  final int? parentSellLineId;

  @JsonKey(name: 'children_type')
  @HiveField(24)
  final String? childrenType;

  @JsonKey(name: 'sub_unit_id')
  @HiveField(25)
  final int? subUnitId;

  @JsonKey(name: 'created_at')
  @HiveField(26)
  final String? createdAt;

  @JsonKey(name: 'updated_at')
  @HiveField(27)
  final String? updatedAt;

  @JsonKey(name: 'packages_counter')
  @HiveField(28)
  final String? packagesCounter;

  @JsonKey(name: 'unit_weight')
  @HiveField(29)
  final String? unitWeight;

  @JsonKey(name: 'is_bonus')
  @HiveField(30)
  final int? isBonus;

  @JsonKey(name: 'bonus_qty')
  @HiveField(31)
  final int? bonusQty;

  @JsonKey(name: 'is_reservation')
  @HiveField(32)
  final String? isReservation;

  @JsonKey(name: 'reservation_status')
  @HiveField(33)
  final String? reservationStatus;

  @JsonKey(name: 'x')
  @HiveField(34)
  final String? x;

  @JsonKey(name: 'y')
  @HiveField(35)
  final String? y;

  @JsonKey(name: 'z')
  @HiveField(36)
  final String? z;

  @JsonKey(name: 'num')
  @HiveField(37)
  final String? num;

  SellLine({
    this.id,
    this.transactionId,
    this.productId,
    this.variationId,
    this.quantity,
    this.secondaryUnitQuantity,
    this.mfgWastePercent,
    this.quantityReturned,
    this.unitPriceBeforeDiscount,
    this.unitPrice,
    this.lineDiscountType = "percentage",
    this.lineDiscountAmount,
    this.unitPriceIncTax,
    this.itemTax,
    this.taxId,
    this.discountId,
    this.lotNoLineId,
    this.sellLineNote,
    this.soLineId,
    this.soQuantityInvoiced,
    this.woocommerceLineItemsId,
    this.resServiceStaffId,
    this.resLineOrderStatus,
    this.parentSellLineId,
    this.childrenType,
    this.subUnitId,
    this.createdAt,
    this.updatedAt,
    this.packagesCounter,
    this.unitWeight,
    this.isBonus,
    this.bonusQty,
    this.isReservation,
    this.reservationStatus,
    this.x,
    this.y,
    this.z,
    this.num,
  });

  factory SellLine.fromJson(Map<String, dynamic> json) =>
      _$SellLineFromJson(json);

  Map<String, dynamic> toJson() => _$SellLineToJson(this);

  @override
  List<Object?> get props => [
        id,
        transactionId,
        productId,
        variationId,
        quantity,
        secondaryUnitQuantity,
        mfgWastePercent,
        quantityReturned,
        unitPriceBeforeDiscount,
        unitPrice,
        lineDiscountType,
        lineDiscountAmount,
        unitPriceIncTax,
        itemTax,
        taxId,
        discountId,
        lotNoLineId,
        sellLineNote,
        soLineId,
        soQuantityInvoiced,
        woocommerceLineItemsId,
        resServiceStaffId,
        resLineOrderStatus,
        parentSellLineId,
        childrenType,
        subUnitId,
        createdAt,
        updatedAt,
        packagesCounter,
        unitWeight,
        isBonus,
        bonusQty,
        isReservation,
        reservationStatus,
        x,
        y,
        z,
        num,
      ];
}
