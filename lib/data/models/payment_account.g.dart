// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'payment_account.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class PaymentAccountAdapter extends TypeAdapter<PaymentAccount> {
  @override
  final int typeId = 6;

  @override
  PaymentAccount read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return PaymentAccount(
      id: fields[0] as int,
      businessId: fields[1] as int,
      name: fields[2] as String,
      accountNumber: fields[3] as String,
      accountTypeId: fields[4] as int,
      createdBy: fields[5] as int,
      isClosed: fields[6] as int,
    );
  }

  @override
  void write(BinaryWriter writer, PaymentAccount obj) {
    writer
      ..writeByte(7)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.businessId)
      ..writeByte(2)
      ..write(obj.name)
      ..writeByte(3)
      ..write(obj.accountNumber)
      ..writeByte(4)
      ..write(obj.accountTypeId)
      ..writeByte(5)
      ..write(obj.createdBy)
      ..writeByte(6)
      ..write(obj.isClosed);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PaymentAccountAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
