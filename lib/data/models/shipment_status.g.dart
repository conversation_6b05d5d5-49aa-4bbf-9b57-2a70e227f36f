// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'shipment_status.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class ShipmentStatusAdapter extends TypeAdapter<ShipmentStatus> {
  @override
  final int typeId = 52;

  @override
  ShipmentStatus read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return ShipmentStatus.shipped;
      case 1:
        return ShipmentStatus.ordered;
      case 2:
        return ShipmentStatus.packed;
      case 3:
        return ShipmentStatus.delivered;
      case 4:
        return ShipmentStatus.cancelled;
      default:
        return ShipmentStatus.shipped;
    }
  }

  @override
  void write(BinaryWriter writer, ShipmentStatus obj) {
    switch (obj) {
      case ShipmentStatus.shipped:
        writer.writeByte(0);
        break;
      case ShipmentStatus.ordered:
        writer.writeByte(1);
        break;
      case ShipmentStatus.packed:
        writer.writeByte(2);
        break;
      case ShipmentStatus.delivered:
        writer.writeByte(3);
        break;
      case ShipmentStatus.cancelled:
        writer.writeByte(4);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ShipmentStatusAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
