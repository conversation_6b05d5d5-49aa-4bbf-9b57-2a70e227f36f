import 'package:equatable/equatable.dart';
import 'package:hive/hive.dart';

part 'selling_price_group.g.dart';

@HiveType(typeId: 17)
class SellingPriceGroup extends Equatable {
  @HiveField(0)
  final int id;

  @HiveField(1)
  final String name;

  @HiveField(2)
  final String? description;

  @HiveField(3)
  final int businessId;

  @HiveField(4)
  final int isActive;

  @HiveField(5)
  final DateTime? deletedAt;

  @HiveField(6)
  final DateTime createdAt;

  @HiveField(7)
  final DateTime updatedAt;

  const SellingPriceGroup({
    required this.id,
    required this.name,
    this.description,
    required this.businessId,
    required this.isActive,
    this.deletedAt,
    required this.createdAt,
    required this.updatedAt,
  });

  factory SellingPriceGroup.fromJson(Map<String, dynamic> json) {
    return SellingPriceGroup(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      businessId: json['business_id'],
      isActive: json['is_active'],
      deletedAt: json['deleted_at'] != null ? DateTime.parse(json['deleted_at']) : null,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  @override
  List<Object?> get props => [
    id,
    name,
    description,
    businessId,
    isActive,
    deletedAt,
    createdAt,
    updatedAt,
  ];
}