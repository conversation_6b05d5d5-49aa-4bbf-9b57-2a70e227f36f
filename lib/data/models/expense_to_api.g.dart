// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'expense_to_api.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class ExpenseToAPIAdapter extends TypeAdapter<ExpenseToAPI> {
  @override
  final int typeId = 24;

  @override
  ExpenseToAPI read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ExpenseToAPI(
      locationId: fields[0] as int?,
      finalTotal: fields[1] as double?,
      transactionDate: fields[2] as String?,
      taxRateId: fields[3] as int?,
      expenseFor: fields[4] as int?,
      contactId: fields[5] as int?,
      additionalNotes: fields[6] as String?,
      isRefund: fields[7] as int,
      isRecurring: fields[8] as int?,
      recurInterval: fields[9] as int?,
      recurIntervalType: fields[10] as String?,
      subscriptionRepeatOn: fields[11] as int?,
      subscriptionNo: fields[12] as String?,
      recurRepetitions: fields[13] as int?,
      payment: (fields[14] as List?)?.cast<Payment>(),
      offlineID: fields[15] as String?,
      locationInfo: fields[16] as LocationInfo?,
      expenseCategoryId: fields[17] as int?,
      transactionNo4: fields[18] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, ExpenseToAPI obj) {
    writer
      ..writeByte(19)
      ..writeByte(0)
      ..write(obj.locationId)
      ..writeByte(1)
      ..write(obj.finalTotal)
      ..writeByte(2)
      ..write(obj.transactionDate)
      ..writeByte(3)
      ..write(obj.taxRateId)
      ..writeByte(4)
      ..write(obj.expenseFor)
      ..writeByte(5)
      ..write(obj.contactId)
      ..writeByte(6)
      ..write(obj.additionalNotes)
      ..writeByte(7)
      ..write(obj.isRefund)
      ..writeByte(8)
      ..write(obj.isRecurring)
      ..writeByte(9)
      ..write(obj.recurInterval)
      ..writeByte(10)
      ..write(obj.recurIntervalType)
      ..writeByte(11)
      ..write(obj.subscriptionRepeatOn)
      ..writeByte(12)
      ..write(obj.subscriptionNo)
      ..writeByte(13)
      ..write(obj.recurRepetitions)
      ..writeByte(14)
      ..write(obj.payment)
      ..writeByte(15)
      ..write(obj.offlineID)
      ..writeByte(16)
      ..write(obj.locationInfo)
      ..writeByte(17)
      ..write(obj.expenseCategoryId)
      ..writeByte(18)
      ..write(obj.transactionNo4);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ExpenseToAPIAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ExpenseToAPI _$ExpenseToAPIFromJson(Map<String, dynamic> json) => ExpenseToAPI(
      locationId: (json['location_id'] as num?)?.toInt(),
      finalTotal: (json['final_total'] as num?)?.toDouble(),
      transactionDate: json['transaction_date'] as String?,
      taxRateId: (json['tax_id'] as num?)?.toInt(),
      expenseFor: (json['expense_for'] as num?)?.toInt(),
      contactId: (json['contact_id'] as num?)?.toInt(),
      additionalNotes: json['additional_notes'] as String?,
      isRefund: (json['is_refund'] as num?)?.toInt() ?? 0,
      isRecurring: (json['is_recurring'] as num?)?.toInt(),
      recurInterval: (json['recur_interval'] as num?)?.toInt(),
      recurIntervalType: json['recur_interval_type'] as String?,
      subscriptionRepeatOn: (json['subscription_repeat_on'] as num?)?.toInt(),
      subscriptionNo: json['subscription_no'] as String?,
      recurRepetitions: (json['recur_repetitions'] as num?)?.toInt(),
      payment: ExpenseToAPI._fromJsonList(json['payment'] as List?),
      locationInfo: json['location_info'] == null
          ? null
          : LocationInfo.fromJson(
              json['location_info'] as Map<String, dynamic>),
      expenseCategoryId: (json['expense_category_id'] as num?)?.toInt(),
      transactionNo4: json['transaction_no_4'] as String?,
    );

Map<String, dynamic> _$ExpenseToAPIToJson(ExpenseToAPI instance) =>
    <String, dynamic>{
      'location_id': instance.locationId,
      'final_total': instance.finalTotal,
      'transaction_date': instance.transactionDate,
      'tax_id': instance.taxRateId,
      'expense_for': instance.expenseFor,
      'contact_id': instance.contactId,
      'additional_notes': instance.additionalNotes,
      'is_refund': instance.isRefund,
      'is_recurring': instance.isRecurring,
      'recur_interval': instance.recurInterval,
      'recur_interval_type': instance.recurIntervalType,
      'subscription_repeat_on': instance.subscriptionRepeatOn,
      'subscription_no': instance.subscriptionNo,
      'recur_repetitions': instance.recurRepetitions,
      'payment': ExpenseToAPI._toJsonList(instance.payment),
      'location_info': locationInfoToJson(instance.locationInfo),
      'expense_category_id': instance.expenseCategoryId,
      'transaction_no_4': instance.transactionNo4,
    };
