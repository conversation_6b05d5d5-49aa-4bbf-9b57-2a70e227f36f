// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'shipping_company.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class ShippingCompanyAdapter extends TypeAdapter<ShippingCompany> {
  @override
  final int typeId = 62;

  @override
  ShippingCompany read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ShippingCompany(
      id: fields[0] as int,
      name: fields[1] as String?,
      mobile: fields[2] as String?,
      createdAt: fields[3] as String?,
      updatedAt: fields[4] as String?,
      businessId: fields[5] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, ShippingCompany obj) {
    writer
      ..writeByte(6)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.mobile)
      ..writeByte(3)
      ..write(obj.createdAt)
      ..writeByte(4)
      ..write(obj.updatedAt)
      ..writeByte(5)
      ..write(obj.businessId);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ShippingCompanyAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ShippingCompany _$ShippingCompanyFromJson(Map<String, dynamic> json) =>
    ShippingCompany(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String?,
      mobile: json['mobile'] as String?,
      createdAt: json['created_at'] as String?,
      updatedAt: json['updated_at'] as String?,
      businessId: json['business_id'] as String?,
    );

Map<String, dynamic> _$ShippingCompanyToJson(ShippingCompany instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'mobile': instance.mobile,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
      'business_id': instance.businessId,
    };
