// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'follow_up_to_api.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class FollowUpToAPIAdapter extends TypeAdapter<FollowUpToAPI> {
  @override
  final int typeId = 36;

  @override
  FollowUpToAPI read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return FollowUpToAPI(
      title: fields[0] as String,
      contactId: fields[1] as int,
      description: fields[2] as String,
      scheduleType: fields[3] as String,
      userId: (fields[4] as List).cast<int>(),
      notifyBefore: fields[5] as int,
      notifyType: fields[6] as String,
      status: fields[7] as String,
      notifyVia: (fields[8] as Map).cast<String, int>(),
      startDatetime: fields[9] as String?,
      endDatetime: fields[10] as String?,
      followupAdditionalInfo: (fields[11] as Map?)?.cast<String, int>(),
      allowNotification: fields[12] as bool,
      offlineID: fields[13] as String?,
      locationInfo: fields[14] as LocationInfo?,
    );
  }

  @override
  void write(BinaryWriter writer, FollowUpToAPI obj) {
    writer
      ..writeByte(15)
      ..writeByte(0)
      ..write(obj.title)
      ..writeByte(1)
      ..write(obj.contactId)
      ..writeByte(2)
      ..write(obj.description)
      ..writeByte(3)
      ..write(obj.scheduleType)
      ..writeByte(4)
      ..write(obj.userId)
      ..writeByte(5)
      ..write(obj.notifyBefore)
      ..writeByte(6)
      ..write(obj.notifyType)
      ..writeByte(7)
      ..write(obj.status)
      ..writeByte(8)
      ..write(obj.notifyVia)
      ..writeByte(9)
      ..write(obj.startDatetime)
      ..writeByte(10)
      ..write(obj.endDatetime)
      ..writeByte(11)
      ..write(obj.followupAdditionalInfo)
      ..writeByte(12)
      ..write(obj.allowNotification)
      ..writeByte(13)
      ..write(obj.offlineID)
      ..writeByte(14)
      ..write(obj.locationInfo);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is FollowUpToAPIAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FollowUpToAPI _$FollowUpToAPIFromJson(Map<String, dynamic> json) =>
    FollowUpToAPI(
      title: json['title'] as String,
      contactId: (json['contact_id'] as num).toInt(),
      description: json['description'] as String,
      scheduleType: json['schedule_type'] as String,
      userId: (json['user_id'] as List<dynamic>)
          .map((e) => (e as num).toInt())
          .toList(),
      notifyBefore: (json['notify_before'] as num).toInt(),
      notifyType: json['notify_type'] as String,
      status: json['status'] as String,
      notifyVia: Map<String, int>.from(json['notify_via'] as Map),
      startDatetime: json['start_datetime'] as String?,
      endDatetime: json['end_datetime'] as String?,
      followupAdditionalInfo:
          (json['followup_additional_info'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(k, (e as num).toInt()),
      ),
      allowNotification: json['allow_notification'] as bool,
      locationInfo: json['location_info'] == null
          ? null
          : LocationInfo.fromJson(
              json['location_info'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$FollowUpToAPIToJson(FollowUpToAPI instance) =>
    <String, dynamic>{
      'title': instance.title,
      'contact_id': instance.contactId,
      'description': instance.description,
      'schedule_type': instance.scheduleType,
      'user_id': instance.userId,
      'notify_before': instance.notifyBefore,
      'notify_type': instance.notifyType,
      'status': instance.status,
      'notify_via': instance.notifyVia,
      'start_datetime': instance.startDatetime,
      'end_datetime': instance.endDatetime,
      'followup_additional_info':
          FollowUpToAPI._customToJson(instance.followupAdditionalInfo),
      'allow_notification': instance.allowNotification,
      'location_info': locationInfoToJson(instance.locationInfo),
    };
