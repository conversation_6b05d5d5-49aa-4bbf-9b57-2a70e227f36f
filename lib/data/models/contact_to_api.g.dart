// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'contact_to_api.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class ContactToAPIAdapter extends TypeAdapter<ContactToAPI> {
  @override
  final int typeId = 25;

  @override
  ContactToAPI read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ContactToAPI(
      type: fields[0] as String,
      supplierBusinessName: fields[1] as String?,
      prefix: fields[2] as String?,
      firstName: fields[3] as String?,
      middleName: fields[4] as String?,
      lastName: fields[5] as String?,
      taxNumber: fields[6] as String?,
      payTermNumber: fields[7] as int?,
      payTermType: fields[8] as String?,
      mobile: fields[9] as String,
      landline: fields[10] as String?,
      alternateNumber: fields[11] as String?,
      addressLine1: fields[12] as String?,
      addressLine2: fields[13] as String?,
      city: fields[14] as String?,
      state: fields[15] as String?,
      country: fields[16] as String?,
      zipCode: fields[17] as String?,
      customerGroupId: fields[18] as String?,
      contactId: fields[19] as String?,
      dob: fields[20] as String?,
      customField1: fields[21] as String?,
      customField2: fields[22] as String?,
      customField3: fields[23] as String?,
      customField4: fields[24] as String?,
      email: fields[25] as String?,
      shippingAddress: fields[26] as String?,
      position: fields[27] as String?,
      openingBalance: fields[28] as double,
      sourceId: fields[29] as int?,
      lifeStageId: fields[30] as int?,
      assignedTo: (fields[31] as List).cast<dynamic>(),
      locationPosition: fields[32] as LocationInfo?,
      priceGroupId: fields[33] as int?,
    );
  }

  @override
  void write(BinaryWriter writer, ContactToAPI obj) {
    writer
      ..writeByte(34)
      ..writeByte(0)
      ..write(obj.type)
      ..writeByte(1)
      ..write(obj.supplierBusinessName)
      ..writeByte(2)
      ..write(obj.prefix)
      ..writeByte(3)
      ..write(obj.firstName)
      ..writeByte(4)
      ..write(obj.middleName)
      ..writeByte(5)
      ..write(obj.lastName)
      ..writeByte(6)
      ..write(obj.taxNumber)
      ..writeByte(7)
      ..write(obj.payTermNumber)
      ..writeByte(8)
      ..write(obj.payTermType)
      ..writeByte(9)
      ..write(obj.mobile)
      ..writeByte(10)
      ..write(obj.landline)
      ..writeByte(11)
      ..write(obj.alternateNumber)
      ..writeByte(12)
      ..write(obj.addressLine1)
      ..writeByte(13)
      ..write(obj.addressLine2)
      ..writeByte(14)
      ..write(obj.city)
      ..writeByte(15)
      ..write(obj.state)
      ..writeByte(16)
      ..write(obj.country)
      ..writeByte(17)
      ..write(obj.zipCode)
      ..writeByte(18)
      ..write(obj.customerGroupId)
      ..writeByte(19)
      ..write(obj.contactId)
      ..writeByte(20)
      ..write(obj.dob)
      ..writeByte(21)
      ..write(obj.customField1)
      ..writeByte(22)
      ..write(obj.customField2)
      ..writeByte(23)
      ..write(obj.customField3)
      ..writeByte(24)
      ..write(obj.customField4)
      ..writeByte(25)
      ..write(obj.email)
      ..writeByte(26)
      ..write(obj.shippingAddress)
      ..writeByte(27)
      ..write(obj.position)
      ..writeByte(28)
      ..write(obj.openingBalance)
      ..writeByte(29)
      ..write(obj.sourceId)
      ..writeByte(30)
      ..write(obj.lifeStageId)
      ..writeByte(31)
      ..write(obj.assignedTo)
      ..writeByte(32)
      ..write(obj.locationPosition)
      ..writeByte(33)
      ..write(obj.priceGroupId);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ContactToAPIAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ContactToAPI _$ContactToAPIFromJson(Map<String, dynamic> json) => ContactToAPI(
      type: json['type'] as String,
      supplierBusinessName: json['supplier_business_name'] as String?,
      prefix: json['prefix'] as String?,
      firstName: json['first_name'] as String?,
      middleName: json['middle_name'] as String?,
      lastName: json['last_name'] as String?,
      taxNumber: json['tax_number'] as String?,
      payTermNumber: (json['pay_term_number'] as num?)?.toInt(),
      payTermType: json['pay_term_type'] as String?,
      mobile: json['mobile'] as String,
      landline: json['landline'] as String?,
      alternateNumber: json['alternate_number'] as String?,
      addressLine1: json['address_line_1'] as String?,
      addressLine2: json['address_line_2'] as String?,
      city: json['city'] as String?,
      state: json['state'] as String?,
      country: json['country'] as String?,
      zipCode: json['zip_code'] as String?,
      customerGroupId: json['customer_group_id'] as String?,
      contactId: json['contact_id'] as String?,
      dob: json['dob'] as String?,
      customField1: json['custom_field1'] as String?,
      customField2: json['custom_field2'] as String?,
      customField3: json['custom_field3'] as String?,
      customField4: json['custom_field4'] as String?,
      email: json['email'] as String?,
      shippingAddress: json['shipping_address'] as String?,
      position: json['position'] as String?,
      openingBalance: (json['opening_balance'] as num?)?.toDouble() ?? 0,
      sourceId: (json['source_id'] as num?)?.toInt(),
      lifeStageId: (json['life_stage_id'] as num?)?.toInt(),
      assignedTo: json['assigned_to'] as List<dynamic>? ?? const [],
      locationPosition: json['location_info'] == null
          ? null
          : LocationInfo.fromJson(
              json['location_info'] as Map<String, dynamic>),
      priceGroupId: (json['price_group_id'] as num?)?.toInt(),
    );

Map<String, dynamic> _$ContactToAPIToJson(ContactToAPI instance) =>
    <String, dynamic>{
      'type': instance.type,
      'supplier_business_name': instance.supplierBusinessName,
      'prefix': instance.prefix,
      'first_name': instance.firstName,
      'middle_name': instance.middleName,
      'last_name': instance.lastName,
      'tax_number': instance.taxNumber,
      'pay_term_number': instance.payTermNumber,
      'pay_term_type': instance.payTermType,
      'mobile': instance.mobile,
      'landline': instance.landline,
      'alternate_number': instance.alternateNumber,
      'address_line_1': instance.addressLine1,
      'address_line_2': instance.addressLine2,
      'city': instance.city,
      'state': instance.state,
      'country': instance.country,
      'zip_code': instance.zipCode,
      'customer_group_id': instance.customerGroupId,
      'contact_id': instance.contactId,
      'dob': instance.dob,
      'custom_field1': instance.customField1,
      'custom_field2': instance.customField2,
      'custom_field3': instance.customField3,
      'custom_field4': instance.customField4,
      'email': instance.email,
      'shipping_address': instance.shippingAddress,
      'position': instance.position,
      'opening_balance': instance.openingBalance,
      'source_id': instance.sourceId,
      'life_stage_id': instance.lifeStageId,
      'assigned_to': instance.assignedTo,
      'location_info': locationInfoToJson(instance.locationPosition),
      'price_group_id': instance.priceGroupId,
    };
