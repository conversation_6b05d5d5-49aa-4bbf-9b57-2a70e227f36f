import 'package:equatable/equatable.dart';
import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';

import '../../utils/we2up_constants.dart';
import 'location_info.dart';

part 'user_model.g.dart';

@HiveType(typeId: 45)
@JsonSerializable(explicitToJson: true)
class UserModel extends Equatable {
  @HiveField(0)
  @Json<PERSON>ey(name: 'id')
  final int id;

  @HiveField(1)
  @Json<PERSON>ey(name: 'user_type')
  final String? userType;

  @HiveField(2)
  @<PERSON><PERSON><PERSON><PERSON>(name: 'surname')
  final String? surname;

  @HiveField(3)
  @J<PERSON><PERSON><PERSON>(name: 'first_name')
  final String? firstName;

  @HiveField(4)
  @Json<PERSON>ey(name: 'last_name')
  final String? lastName;

  @HiveField(5)
  @<PERSON>son<PERSON><PERSON>(name: 'username')
  final String? username;

  @HiveField(6)
  @<PERSON><PERSON><PERSON><PERSON>(name: 'email')
  final String? email;

  @HiveField(7)
  @Json<PERSON><PERSON>(name: 'language')
  final String? language;

  @HiveField(8)
  @Json<PERSON>ey(name: 'contact_no')
  final String? contactNo;

  @HiveField(9)
  @Json<PERSON>ey(name: 'address')
  final String? address;

  @HiveField(10)
  @JsonKey(name: 'business_id')
  final int? businessId;

  @HiveField(11)
  @JsonKey(name: 'available_at')
  final String? availableAt;

  @HiveField(12)
  @JsonKey(name: 'paused_at')
  final String? pausedAt;

  @HiveField(13)
  @JsonKey(name: 'essentials_department_id')
  final int? essentialsDepartmentId;

  @HiveField(14)
  @JsonKey(name: 'essentials_designation_id')
  final int? essentialsDesignationId;

  @HiveField(15)
  @JsonKey(name: 'essentials_salary')
  final String? essentialsSalary;

  @HiveField(16)
  @JsonKey(name: 'essentials_pay_period')
  final dynamic essentialsPayPeriod;

  @HiveField(17)
  @JsonKey(name: 'essentials_pay_cycle')
  final String? essentialsPayCycle;

  @HiveField(18)
  @JsonKey(name: 'max_sales_discount_percent')
  final String? maxSalesDiscountPercent;

  @HiveField(19)
  @JsonKey(name: 'allow_login')
  final int? allowLogin;

  @HiveField(20)
  @JsonKey(name: 'status')
  final String? status;

  @HiveField(21)
  @JsonKey(name: 'crm_contact_id')
  final int? crmContactId;

  @HiveField(22)
  @JsonKey(name: 'is_cmmsn_agnt')
  final int? isCommissionAgent;

  @HiveField(23)
  @JsonKey(name: 'cmmsn_percent')
  final String? commissionPercent;

  @HiveField(24)
  @JsonKey(name: 'selected_contacts')
  final int? selectedContacts;

  @HiveField(25)
  @JsonKey(name: 'dob')
  final String? dob;

  @HiveField(26)
  @JsonKey(name: 'gender')
  final String? gender;

  @HiveField(27)
  @JsonKey(name: 'marital_status')
  final String? maritalStatus;

  @HiveField(28)
  @JsonKey(name: 'blood_group')
  final String? bloodGroup;

  @HiveField(29)
  @JsonKey(name: 'contact_number')
  final String? contactNumber;

  @HiveField(30)
  @JsonKey(name: 'alt_number')
  final String? altNumber;

  @HiveField(31)
  @JsonKey(name: 'family_number')
  final String? familyNumber;

  @HiveField(32)
  @JsonKey(name: 'fb_link')
  final String? facebookLink;

  @HiveField(33)
  @JsonKey(name: 'twitter_link')
  final String? twitterLink;

  @HiveField(34)
  @JsonKey(name: 'social_media_1')
  final String? socialMedia1;

  @HiveField(35)
  @JsonKey(name: 'social_media_2')
  final String? socialMedia2;

  @HiveField(36)
  @JsonKey(name: 'permanent_address')
  final String? permanentAddress;

  @HiveField(37)
  @JsonKey(name: 'current_address')
  final String? currentAddress;

  @HiveField(38)
  @JsonKey(name: 'guardian_name')
  final String? guardianName;

  @HiveField(39)
  @JsonKey(name: 'custom_field_1')
  final String? customField1;

  @HiveField(40)
  @JsonKey(name: 'custom_field_2')
  final String? customField2;

  @HiveField(41)
  @JsonKey(name: 'custom_field_3')
  final String? customField3;

  @HiveField(42)
  @JsonKey(name: 'custom_field_4')
  final String? customField4;

  @HiveField(43)
  @JsonKey(name: 'bank_details')
  final String? bankDetails;

  @HiveField(44)
  @JsonKey(name: 'id_proof_name')
  final String? idProofName;

  @HiveField(45)
  @JsonKey(name: 'id_proof_number')
  final String? idProofNumber;

  @HiveField(46)
  @JsonKey(name: 'location_id')
  final int? locationId;

  @HiveField(47)
  @JsonKey(name: 'crm_department')
  final String? crmDepartment;

  @HiveField(48)
  @JsonKey(name: 'crm_designation')
  final String? crmDesignation;

  @HiveField(49)
  @JsonKey(name: 'deleted_at')
  final String? deletedAt;

  @HiveField(50)
  @JsonKey(name: 'created_at')
  final String? createdAt;

  @HiveField(51)
  @JsonKey(name: 'updated_at')
  final String? updatedAt;

  @HiveField(52)
  @JsonKey(name: 'fixed_deductions')
  final String? fixedDeductions;

  @HiveField(53)
  @JsonKey(name: 'contact_group')
  final String? contactGroup;

  @HiveField(54)
  @JsonKey(name: 'supplier_group')
  final String? supplierGroup;

  @HiveField(55)
  @JsonKey(name: 'session_token')
  final String? sessionToken;

  @HiveField(56)
  @JsonKey(name: 'leader_id', fromJson: stringToInt)
  final int? leaderId;

  @HiveField(57)
  @JsonKey(name: 'comm_invoice')
  final String? commissionInvoice;

  @HiveField(58)
  @JsonKey(name: 'shipping_status')
  final String? shippingStatus;

  @HiveField(59)
  @JsonKey(name: 'comm_as_leader')
  final String? commissionAsLeader;

  @HiveField(60)
  @JsonKey(name: 'categories_ids')
  final List<dynamic>? categoriesIds;

  @HiveField(61)
  @JsonKey(name: 'users_ids')
  final dynamic usersIds;

  @HiveField(62)
  @JsonKey(name: 'credit_limit_cashier')
  final String? creditLimitCashier;

  @HiveField(63)
  @JsonKey(name: 'credit_limit_agent')
  final String? creditLimitAgent;

  @HiveField(64)
  @JsonKey(name: 'default_account')
  final String? defaultAccount;

  @HiveField(65)
  @JsonKey(
      name: 'location_info',
      fromJson: locationInfoFromListJson,
      toJson: locationInfoToJson)
  final LocationInfo? locationInfo;

  const UserModel({
    required this.id,
    this.userType,
    this.surname,
    this.firstName,
    this.lastName,
    this.username,
    this.email,
    this.language,
    this.contactNo,
    this.address,
    this.businessId,
    this.availableAt,
    this.pausedAt,
    this.essentialsDepartmentId,
    this.essentialsDesignationId,
    this.essentialsSalary,
    this.essentialsPayPeriod,
    this.essentialsPayCycle,
    this.maxSalesDiscountPercent,
    this.allowLogin,
    this.status,
    this.crmContactId,
    this.isCommissionAgent,
    this.commissionPercent,
    this.selectedContacts,
    this.dob,
    this.gender,
    this.maritalStatus,
    this.bloodGroup,
    this.contactNumber,
    this.altNumber,
    this.familyNumber,
    this.facebookLink,
    this.twitterLink,
    this.socialMedia1,
    this.socialMedia2,
    this.permanentAddress,
    this.currentAddress,
    this.guardianName,
    this.customField1,
    this.customField2,
    this.customField3,
    this.customField4,
    this.bankDetails,
    this.idProofName,
    this.idProofNumber,
    this.locationId,
    this.crmDepartment,
    this.crmDesignation,
    this.deletedAt,
    this.createdAt,
    this.updatedAt,
    this.fixedDeductions,
    this.contactGroup,
    this.supplierGroup,
    this.sessionToken,
    this.leaderId,
    this.commissionInvoice,
    this.shippingStatus,
    this.commissionAsLeader,
    this.categoriesIds,
    this.usersIds,
    this.creditLimitCashier,
    this.creditLimitAgent,
    this.defaultAccount,
    this.locationInfo,
  });

  static int? stringToInt(String? text) => int.tryParse(text ?? '');

  factory UserModel.fromJson(Map<String, dynamic> json) =>
      _$UserModelFromJson(json);

  Map<String, dynamic> toJson() => _$UserModelToJson(this);

  @override
  List<Object?> get props => [
        id,
        userType,
        surname,
        firstName,
        lastName,
        username,
        email,
        language,
        contactNo,
        address,
        businessId,
        availableAt,
        pausedAt,
        essentialsDepartmentId,
        essentialsDesignationId,
        essentialsSalary,
        essentialsPayPeriod,
        essentialsPayCycle,
        maxSalesDiscountPercent,
        allowLogin,
        status,
        crmContactId,
        isCommissionAgent,
        commissionPercent,
        selectedContacts,
        dob,
        gender,
        maritalStatus,
        bloodGroup,
        contactNumber,
        altNumber,
        familyNumber,
        facebookLink,
        twitterLink,
        socialMedia1,
        socialMedia2,
        permanentAddress,
        currentAddress,
        guardianName,
        customField1,
        customField2,
        customField3,
        customField4,
        bankDetails,
        idProofName,
        idProofNumber,
        locationId,
        crmDepartment,
        crmDesignation,
        deletedAt,
        createdAt,
        updatedAt,
        fixedDeductions,
        contactGroup,
        supplierGroup,
        sessionToken,
        leaderId,
        commissionInvoice,
        shippingStatus,
        commissionAsLeader,
        categoriesIds,
        usersIds,
        creditLimitCashier,
        creditLimitAgent,
        defaultAccount,
      ];
}
