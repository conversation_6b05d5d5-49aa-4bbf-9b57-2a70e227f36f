// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'purchase_to_api.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class PurchaseToAPIAdapter extends TypeAdapter<PurchaseToAPI> {
  @override
  final int typeId = 33;

  @override
  PurchaseToAPI read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return PurchaseToAPI(
      contactId: fields[0] as int,
      refNo: fields[1] as String?,
      transactionDate: fields[2] as DateTime,
      status: fields[3] as String?,
      locationId: fields[4] as int,
      exchangeRate: fields[5] as String?,
      payTermNumber: fields[6] as String?,
      payTermType: fields[7] as String?,
      totalBeforeTax: fields[8] as String,
      discountType: fields[9] as String?,
      discountAmount: fields[10] as String?,
      taxId: fields[11] as String?,
      taxAmount: fields[12] as String?,
      additionalNotes: fields[13] as String?,
      shippingDetails: fields[14] as String?,
      shippingCharges: fields[15] as String?,
      additionalExpenseKey1: fields[16] as String?,
      additionalExpenseValue1: fields[17] as String?,
      additionalExpenseKey2: fields[18] as String?,
      additionalExpenseValue2: fields[19] as String?,
      additionalExpenseKey3: fields[20] as String?,
      additionalExpenseValue3: fields[21] as String?,
      additionalExpenseKey4: fields[22] as String?,
      additionalExpenseValue4: fields[23] as String?,
      finalTotal: fields[24] as String,
      source: fields[25] as String?,
      payment: (fields[26] as List).cast<Payment>(),
      products: (fields[27] as List).cast<PurchaseProductToAPI>(),
      offlineID: fields[28] as String?,
      locationInfo: fields[29] as LocationInfo?,
    );
  }

  @override
  void write(BinaryWriter writer, PurchaseToAPI obj) {
    writer
      ..writeByte(30)
      ..writeByte(0)
      ..write(obj.contactId)
      ..writeByte(1)
      ..write(obj.refNo)
      ..writeByte(2)
      ..write(obj.transactionDate)
      ..writeByte(3)
      ..write(obj.status)
      ..writeByte(4)
      ..write(obj.locationId)
      ..writeByte(5)
      ..write(obj.exchangeRate)
      ..writeByte(6)
      ..write(obj.payTermNumber)
      ..writeByte(7)
      ..write(obj.payTermType)
      ..writeByte(8)
      ..write(obj.totalBeforeTax)
      ..writeByte(9)
      ..write(obj.discountType)
      ..writeByte(10)
      ..write(obj.discountAmount)
      ..writeByte(11)
      ..write(obj.taxId)
      ..writeByte(12)
      ..write(obj.taxAmount)
      ..writeByte(13)
      ..write(obj.additionalNotes)
      ..writeByte(14)
      ..write(obj.shippingDetails)
      ..writeByte(15)
      ..write(obj.shippingCharges)
      ..writeByte(16)
      ..write(obj.additionalExpenseKey1)
      ..writeByte(17)
      ..write(obj.additionalExpenseValue1)
      ..writeByte(18)
      ..write(obj.additionalExpenseKey2)
      ..writeByte(19)
      ..write(obj.additionalExpenseValue2)
      ..writeByte(20)
      ..write(obj.additionalExpenseKey3)
      ..writeByte(21)
      ..write(obj.additionalExpenseValue3)
      ..writeByte(22)
      ..write(obj.additionalExpenseKey4)
      ..writeByte(23)
      ..write(obj.additionalExpenseValue4)
      ..writeByte(24)
      ..write(obj.finalTotal)
      ..writeByte(25)
      ..write(obj.source)
      ..writeByte(26)
      ..write(obj.payment)
      ..writeByte(27)
      ..write(obj.products)
      ..writeByte(28)
      ..write(obj.offlineID)
      ..writeByte(29)
      ..write(obj.locationInfo);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PurchaseToAPIAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class PurchaseProductToAPIAdapter extends TypeAdapter<PurchaseProductToAPI> {
  @override
  final int typeId = 43;

  @override
  PurchaseProductToAPI read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return PurchaseProductToAPI(
      productId: fields[0] as int,
      variationId: fields[1] as int?,
      quantity: fields[2] as String?,
      productUnitId: fields[3] as String,
      subUnitId: fields[4] as String?,
      ppWithoutDiscount: fields[5] as String?,
      discountPercent: fields[6] as String?,
      purchasePrice: fields[7] as String,
      purchaseLineTaxId: fields[8] as int?,
      itemTax: fields[9] as String?,
      purchasePriceIncTax: fields[10] as String?,
      profitPercent: fields[11] as String?,
      defaultSellPrice: fields[12] as String,
      mfgDate: fields[13] as String?,
      expDate: fields[14] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, PurchaseProductToAPI obj) {
    writer
      ..writeByte(15)
      ..writeByte(0)
      ..write(obj.productId)
      ..writeByte(1)
      ..write(obj.variationId)
      ..writeByte(2)
      ..write(obj.quantity)
      ..writeByte(3)
      ..write(obj.productUnitId)
      ..writeByte(4)
      ..write(obj.subUnitId)
      ..writeByte(5)
      ..write(obj.ppWithoutDiscount)
      ..writeByte(6)
      ..write(obj.discountPercent)
      ..writeByte(7)
      ..write(obj.purchasePrice)
      ..writeByte(8)
      ..write(obj.purchaseLineTaxId)
      ..writeByte(9)
      ..write(obj.itemTax)
      ..writeByte(10)
      ..write(obj.purchasePriceIncTax)
      ..writeByte(11)
      ..write(obj.profitPercent)
      ..writeByte(12)
      ..write(obj.defaultSellPrice)
      ..writeByte(13)
      ..write(obj.mfgDate)
      ..writeByte(14)
      ..write(obj.expDate);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PurchaseProductToAPIAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PurchaseToAPI _$PurchaseToAPIFromJson(Map<String, dynamic> json) =>
    PurchaseToAPI(
      contactId: (json['contact_id'] as num).toInt(),
      refNo: json['ref_no'] as String?,
      transactionDate: DateTime.parse(json['transaction_date'] as String),
      status: json['status'] as String?,
      locationId: (json['location_id'] as num).toInt(),
      exchangeRate: json['exchange_rate'] as String? ?? "1",
      payTermNumber: json['pay_term_number'] as String?,
      payTermType: json['pay_term_type'] as String?,
      totalBeforeTax: json['total_before_tax'] as String,
      discountType: json['discount_type'] as String?,
      discountAmount: json['discount_amount'] as String?,
      taxId: json['tax_id'] as String?,
      taxAmount: json['tax_amount'] as String?,
      additionalNotes: json['additional_notes'] as String?,
      shippingDetails: json['shipping_details'] as String?,
      shippingCharges: json['shipping_charges'] as String?,
      additionalExpenseKey1: json['additional_expense_key_1'] as String? ??
          "additional_expense_key_1",
      additionalExpenseValue1: json['additional_expense_value_1'] as String? ??
          "additional_expense_value_1",
      additionalExpenseKey2: json['additional_expense_key_2'] as String? ??
          "additional_expense_key_2",
      additionalExpenseValue2: json['additional_expense_value_2'] as String? ??
          "additional_expense_value_2",
      additionalExpenseKey3: json['additional_expense_key_3'] as String? ??
          "additional_expense_key_3",
      additionalExpenseValue3: json['additional_expense_value_3'] as String? ??
          "additional_expense_value_3",
      additionalExpenseKey4: json['additional_expense_key_4'] as String? ??
          "additional_expense_key_3",
      additionalExpenseValue4: json['additional_expense_value_4'] as String? ??
          "additional_expense_value_4",
      finalTotal: json['final_total'] as String,
      source: json['source'] as String?,
      payment: PurchaseToAPI._fromJsonList(
          json['payment'] as List<Map<String, dynamic>>),
      products: PurchaseToAPI._productsFromJson(
          json['products'] as List<Map<String, dynamic>>),
      locationInfo: json['location_info'] == null
          ? null
          : LocationInfo.fromJson(
              json['location_info'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$PurchaseToAPIToJson(PurchaseToAPI instance) =>
    <String, dynamic>{
      'contact_id': intToString(instance.contactId),
      'ref_no': instance.refNo,
      'transaction_date': dateToString(instance.transactionDate),
      'status': instance.status,
      'location_id': intToString(instance.locationId),
      'exchange_rate': instance.exchangeRate,
      'pay_term_number': instance.payTermNumber,
      'pay_term_type': instance.payTermType,
      'total_before_tax': instance.totalBeforeTax,
      'discount_type': instance.discountType,
      'discount_amount': instance.discountAmount,
      'tax_id': instance.taxId,
      'tax_amount': instance.taxAmount,
      'additional_notes': instance.additionalNotes,
      'shipping_details': instance.shippingDetails,
      'shipping_charges': instance.shippingCharges,
      'additional_expense_key_1': instance.additionalExpenseKey1,
      'additional_expense_value_1': instance.additionalExpenseValue1,
      'additional_expense_key_2': instance.additionalExpenseKey2,
      'additional_expense_value_2': instance.additionalExpenseValue2,
      'additional_expense_key_3': instance.additionalExpenseKey3,
      'additional_expense_value_3': instance.additionalExpenseValue3,
      'additional_expense_key_4': instance.additionalExpenseKey4,
      'additional_expense_value_4': instance.additionalExpenseValue4,
      'final_total': instance.finalTotal,
      'source': instance.source,
      'payment': PurchaseToAPI._toJsonList(instance.payment),
      'products': PurchaseToAPI._productsToJson(instance.products),
      'location_info': locationInfoToJson(instance.locationInfo),
    };

PurchaseProductToAPI _$PurchaseProductToAPIFromJson(
        Map<String, dynamic> json) =>
    PurchaseProductToAPI(
      productId: (json['product_id'] as num).toInt(),
      variationId: (json['variation_id'] as num?)?.toInt(),
      quantity: json['quantity'] as String?,
      productUnitId: json['product_unit_id'] as String,
      subUnitId: json['sub_unit_id'] as String?,
      ppWithoutDiscount: json['pp_without_discount'] as String?,
      discountPercent: json['discount_percent'] as String?,
      purchasePrice: json['purchase_price'] as String,
      purchaseLineTaxId: (json['purchase_line_tax_id'] as num?)?.toInt(),
      itemTax: json['item_tax'] as String?,
      purchasePriceIncTax: json['purchase_price_inc_tax'] as String?,
      profitPercent: json['profit_percent'] as String?,
      defaultSellPrice: json['default_sell_price'] as String,
      mfgDate: json['mfg_date'] as String?,
      expDate: json['exp_date'] as String?,
    );

Map<String, dynamic> _$PurchaseProductToAPIToJson(
        PurchaseProductToAPI instance) =>
    <String, dynamic>{
      'product_id': removeLastDigit(instance.productId),
      'variation_id': instance.variationId,
      'quantity': instance.quantity,
      'product_unit_id': instance.productUnitId,
      'sub_unit_id': instance.subUnitId,
      'pp_without_discount': instance.ppWithoutDiscount,
      'discount_percent': instance.discountPercent,
      'purchase_price': instance.purchasePrice,
      'purchase_line_tax_id': instance.purchaseLineTaxId,
      'item_tax': instance.itemTax,
      'purchase_price_inc_tax': instance.purchasePriceIncTax,
      'profit_percent': instance.profitPercent,
      'default_sell_price': instance.defaultSellPrice,
      'mfg_date': instance.mfgDate,
      'exp_date': instance.expDate,
    };
