// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class UserModelAdapter extends TypeAdapter<UserModel> {
  @override
  final int typeId = 45;

  @override
  UserModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return UserModel(
      id: fields[0] as int,
      userType: fields[1] as String?,
      surname: fields[2] as String?,
      firstName: fields[3] as String?,
      lastName: fields[4] as String?,
      username: fields[5] as String?,
      email: fields[6] as String?,
      language: fields[7] as String?,
      contactNo: fields[8] as String?,
      address: fields[9] as String?,
      businessId: fields[10] as int?,
      availableAt: fields[11] as String?,
      pausedAt: fields[12] as String?,
      essentialsDepartmentId: fields[13] as int?,
      essentialsDesignationId: fields[14] as int?,
      essentialsSalary: fields[15] as String?,
      essentialsPayPeriod: fields[16] as dynamic,
      essentialsPayCycle: fields[17] as String?,
      maxSalesDiscountPercent: fields[18] as String?,
      allowLogin: fields[19] as int?,
      status: fields[20] as String?,
      crmContactId: fields[21] as int?,
      isCommissionAgent: fields[22] as int?,
      commissionPercent: fields[23] as String?,
      selectedContacts: fields[24] as int?,
      dob: fields[25] as String?,
      gender: fields[26] as String?,
      maritalStatus: fields[27] as String?,
      bloodGroup: fields[28] as String?,
      contactNumber: fields[29] as String?,
      altNumber: fields[30] as String?,
      familyNumber: fields[31] as String?,
      facebookLink: fields[32] as String?,
      twitterLink: fields[33] as String?,
      socialMedia1: fields[34] as String?,
      socialMedia2: fields[35] as String?,
      permanentAddress: fields[36] as String?,
      currentAddress: fields[37] as String?,
      guardianName: fields[38] as String?,
      customField1: fields[39] as String?,
      customField2: fields[40] as String?,
      customField3: fields[41] as String?,
      customField4: fields[42] as String?,
      bankDetails: fields[43] as String?,
      idProofName: fields[44] as String?,
      idProofNumber: fields[45] as String?,
      locationId: fields[46] as int?,
      crmDepartment: fields[47] as String?,
      crmDesignation: fields[48] as String?,
      deletedAt: fields[49] as String?,
      createdAt: fields[50] as String?,
      updatedAt: fields[51] as String?,
      fixedDeductions: fields[52] as String?,
      contactGroup: fields[53] as String?,
      supplierGroup: fields[54] as String?,
      sessionToken: fields[55] as String?,
      leaderId: fields[56] as int?,
      commissionInvoice: fields[57] as String?,
      shippingStatus: fields[58] as String?,
      commissionAsLeader: fields[59] as String?,
      categoriesIds: (fields[60] as List?)?.cast<dynamic>(),
      usersIds: fields[61] as dynamic,
      creditLimitCashier: fields[62] as String?,
      creditLimitAgent: fields[63] as String?,
      defaultAccount: fields[64] as String?,
      locationInfo: fields[65] as LocationInfo?,
    );
  }

  @override
  void write(BinaryWriter writer, UserModel obj) {
    writer
      ..writeByte(66)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.userType)
      ..writeByte(2)
      ..write(obj.surname)
      ..writeByte(3)
      ..write(obj.firstName)
      ..writeByte(4)
      ..write(obj.lastName)
      ..writeByte(5)
      ..write(obj.username)
      ..writeByte(6)
      ..write(obj.email)
      ..writeByte(7)
      ..write(obj.language)
      ..writeByte(8)
      ..write(obj.contactNo)
      ..writeByte(9)
      ..write(obj.address)
      ..writeByte(10)
      ..write(obj.businessId)
      ..writeByte(11)
      ..write(obj.availableAt)
      ..writeByte(12)
      ..write(obj.pausedAt)
      ..writeByte(13)
      ..write(obj.essentialsDepartmentId)
      ..writeByte(14)
      ..write(obj.essentialsDesignationId)
      ..writeByte(15)
      ..write(obj.essentialsSalary)
      ..writeByte(16)
      ..write(obj.essentialsPayPeriod)
      ..writeByte(17)
      ..write(obj.essentialsPayCycle)
      ..writeByte(18)
      ..write(obj.maxSalesDiscountPercent)
      ..writeByte(19)
      ..write(obj.allowLogin)
      ..writeByte(20)
      ..write(obj.status)
      ..writeByte(21)
      ..write(obj.crmContactId)
      ..writeByte(22)
      ..write(obj.isCommissionAgent)
      ..writeByte(23)
      ..write(obj.commissionPercent)
      ..writeByte(24)
      ..write(obj.selectedContacts)
      ..writeByte(25)
      ..write(obj.dob)
      ..writeByte(26)
      ..write(obj.gender)
      ..writeByte(27)
      ..write(obj.maritalStatus)
      ..writeByte(28)
      ..write(obj.bloodGroup)
      ..writeByte(29)
      ..write(obj.contactNumber)
      ..writeByte(30)
      ..write(obj.altNumber)
      ..writeByte(31)
      ..write(obj.familyNumber)
      ..writeByte(32)
      ..write(obj.facebookLink)
      ..writeByte(33)
      ..write(obj.twitterLink)
      ..writeByte(34)
      ..write(obj.socialMedia1)
      ..writeByte(35)
      ..write(obj.socialMedia2)
      ..writeByte(36)
      ..write(obj.permanentAddress)
      ..writeByte(37)
      ..write(obj.currentAddress)
      ..writeByte(38)
      ..write(obj.guardianName)
      ..writeByte(39)
      ..write(obj.customField1)
      ..writeByte(40)
      ..write(obj.customField2)
      ..writeByte(41)
      ..write(obj.customField3)
      ..writeByte(42)
      ..write(obj.customField4)
      ..writeByte(43)
      ..write(obj.bankDetails)
      ..writeByte(44)
      ..write(obj.idProofName)
      ..writeByte(45)
      ..write(obj.idProofNumber)
      ..writeByte(46)
      ..write(obj.locationId)
      ..writeByte(47)
      ..write(obj.crmDepartment)
      ..writeByte(48)
      ..write(obj.crmDesignation)
      ..writeByte(49)
      ..write(obj.deletedAt)
      ..writeByte(50)
      ..write(obj.createdAt)
      ..writeByte(51)
      ..write(obj.updatedAt)
      ..writeByte(52)
      ..write(obj.fixedDeductions)
      ..writeByte(53)
      ..write(obj.contactGroup)
      ..writeByte(54)
      ..write(obj.supplierGroup)
      ..writeByte(55)
      ..write(obj.sessionToken)
      ..writeByte(56)
      ..write(obj.leaderId)
      ..writeByte(57)
      ..write(obj.commissionInvoice)
      ..writeByte(58)
      ..write(obj.shippingStatus)
      ..writeByte(59)
      ..write(obj.commissionAsLeader)
      ..writeByte(60)
      ..write(obj.categoriesIds)
      ..writeByte(61)
      ..write(obj.usersIds)
      ..writeByte(62)
      ..write(obj.creditLimitCashier)
      ..writeByte(63)
      ..write(obj.creditLimitAgent)
      ..writeByte(64)
      ..write(obj.defaultAccount)
      ..writeByte(65)
      ..write(obj.locationInfo);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserModel _$UserModelFromJson(Map<String, dynamic> json) => UserModel(
      id: (json['id'] as num).toInt(),
      userType: json['user_type'] as String?,
      surname: json['surname'] as String?,
      firstName: json['first_name'] as String?,
      lastName: json['last_name'] as String?,
      username: json['username'] as String?,
      email: json['email'] as String?,
      language: json['language'] as String?,
      contactNo: json['contact_no'] as String?,
      address: json['address'] as String?,
      businessId: (json['business_id'] as num?)?.toInt(),
      availableAt: json['available_at'] as String?,
      pausedAt: json['paused_at'] as String?,
      essentialsDepartmentId:
          (json['essentials_department_id'] as num?)?.toInt(),
      essentialsDesignationId:
          (json['essentials_designation_id'] as num?)?.toInt(),
      essentialsSalary: json['essentials_salary'] as String?,
      essentialsPayPeriod: json['essentials_pay_period'],
      essentialsPayCycle: json['essentials_pay_cycle'] as String?,
      maxSalesDiscountPercent: json['max_sales_discount_percent'] as String?,
      allowLogin: (json['allow_login'] as num?)?.toInt(),
      status: json['status'] as String?,
      crmContactId: (json['crm_contact_id'] as num?)?.toInt(),
      isCommissionAgent: (json['is_cmmsn_agnt'] as num?)?.toInt(),
      commissionPercent: json['cmmsn_percent'] as String?,
      selectedContacts: (json['selected_contacts'] as num?)?.toInt(),
      dob: json['dob'] as String?,
      gender: json['gender'] as String?,
      maritalStatus: json['marital_status'] as String?,
      bloodGroup: json['blood_group'] as String?,
      contactNumber: json['contact_number'] as String?,
      altNumber: json['alt_number'] as String?,
      familyNumber: json['family_number'] as String?,
      facebookLink: json['fb_link'] as String?,
      twitterLink: json['twitter_link'] as String?,
      socialMedia1: json['social_media_1'] as String?,
      socialMedia2: json['social_media_2'] as String?,
      permanentAddress: json['permanent_address'] as String?,
      currentAddress: json['current_address'] as String?,
      guardianName: json['guardian_name'] as String?,
      customField1: json['custom_field_1'] as String?,
      customField2: json['custom_field_2'] as String?,
      customField3: json['custom_field_3'] as String?,
      customField4: json['custom_field_4'] as String?,
      bankDetails: json['bank_details'] as String?,
      idProofName: json['id_proof_name'] as String?,
      idProofNumber: json['id_proof_number'] as String?,
      locationId: (json['location_id'] as num?)?.toInt(),
      crmDepartment: json['crm_department'] as String?,
      crmDesignation: json['crm_designation'] as String?,
      deletedAt: json['deleted_at'] as String?,
      createdAt: json['created_at'] as String?,
      updatedAt: json['updated_at'] as String?,
      fixedDeductions: json['fixed_deductions'] as String?,
      contactGroup: json['contact_group'] as String?,
      supplierGroup: json['supplier_group'] as String?,
      sessionToken: json['session_token'] as String?,
      leaderId: UserModel.stringToInt(json['leader_id'] as String?),
      commissionInvoice: json['comm_invoice'] as String?,
      shippingStatus: json['shipping_status'] as String?,
      commissionAsLeader: json['comm_as_leader'] as String?,
      categoriesIds: json['categories_ids'] as List<dynamic>?,
      usersIds: json['users_ids'],
      creditLimitCashier: json['credit_limit_cashier'] as String?,
      creditLimitAgent: json['credit_limit_agent'] as String?,
      defaultAccount: json['default_account'] as String?,
      locationInfo: locationInfoFromListJson(json['location_info'] as List?),
    );

Map<String, dynamic> _$UserModelToJson(UserModel instance) => <String, dynamic>{
      'id': instance.id,
      'user_type': instance.userType,
      'surname': instance.surname,
      'first_name': instance.firstName,
      'last_name': instance.lastName,
      'username': instance.username,
      'email': instance.email,
      'language': instance.language,
      'contact_no': instance.contactNo,
      'address': instance.address,
      'business_id': instance.businessId,
      'available_at': instance.availableAt,
      'paused_at': instance.pausedAt,
      'essentials_department_id': instance.essentialsDepartmentId,
      'essentials_designation_id': instance.essentialsDesignationId,
      'essentials_salary': instance.essentialsSalary,
      'essentials_pay_period': instance.essentialsPayPeriod,
      'essentials_pay_cycle': instance.essentialsPayCycle,
      'max_sales_discount_percent': instance.maxSalesDiscountPercent,
      'allow_login': instance.allowLogin,
      'status': instance.status,
      'crm_contact_id': instance.crmContactId,
      'is_cmmsn_agnt': instance.isCommissionAgent,
      'cmmsn_percent': instance.commissionPercent,
      'selected_contacts': instance.selectedContacts,
      'dob': instance.dob,
      'gender': instance.gender,
      'marital_status': instance.maritalStatus,
      'blood_group': instance.bloodGroup,
      'contact_number': instance.contactNumber,
      'alt_number': instance.altNumber,
      'family_number': instance.familyNumber,
      'fb_link': instance.facebookLink,
      'twitter_link': instance.twitterLink,
      'social_media_1': instance.socialMedia1,
      'social_media_2': instance.socialMedia2,
      'permanent_address': instance.permanentAddress,
      'current_address': instance.currentAddress,
      'guardian_name': instance.guardianName,
      'custom_field_1': instance.customField1,
      'custom_field_2': instance.customField2,
      'custom_field_3': instance.customField3,
      'custom_field_4': instance.customField4,
      'bank_details': instance.bankDetails,
      'id_proof_name': instance.idProofName,
      'id_proof_number': instance.idProofNumber,
      'location_id': instance.locationId,
      'crm_department': instance.crmDepartment,
      'crm_designation': instance.crmDesignation,
      'deleted_at': instance.deletedAt,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
      'fixed_deductions': instance.fixedDeductions,
      'contact_group': instance.contactGroup,
      'supplier_group': instance.supplierGroup,
      'session_token': instance.sessionToken,
      'leader_id': instance.leaderId,
      'comm_invoice': instance.commissionInvoice,
      'shipping_status': instance.shippingStatus,
      'comm_as_leader': instance.commissionAsLeader,
      'categories_ids': instance.categoriesIds,
      'users_ids': instance.usersIds,
      'credit_limit_cashier': instance.creditLimitCashier,
      'credit_limit_agent': instance.creditLimitAgent,
      'default_account': instance.defaultAccount,
      'location_info': locationInfoToJson(instance.locationInfo),
    };
