import '../../presentation/widgets/location_range_dropdown_button.dart';
import 'contact.dart';
import 'location_info.dart';

class UserLocationInfo {
  final String username;
  final LocationInfo? userLocation;
  final LocationInfo? openShiftLocation;
  final DateTime? openShiftDateTime;
  final LocationInfo? lastTransactionLocation;
  final DateTime? lastTransactionDateTime;
  final LocationRange shiftOpeningRange;
  final LocationRange lastTransactionRange;
  final Contact? closestContact;

  UserLocationInfo({
    required this.username,
    this.openShiftDateTime,
    this.lastTransactionDateTime,
    this.userLocation,
    this.closestContact,
    this.openShiftLocation,
    this.lastTransactionLocation,
    this.shiftOpeningRange = LocationRange.none,
    this.lastTransactionRange = LocationRange.none,
  });
}
