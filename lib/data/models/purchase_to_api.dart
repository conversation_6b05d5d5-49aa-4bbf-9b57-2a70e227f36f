import 'package:equatable/equatable.dart';
import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:we2up/data/models/payment.dart';

import '../../utils/we2up_constants.dart';
import 'location_info.dart';

part 'purchase_to_api.g.dart';

@JsonSerializable()
@HiveType(typeId: 33)
class PurchaseToAPI extends Equatable {
  @HiveField(0)
  @Json<PERSON>ey(name: 'contact_id', toJson: intToString)
  final int contactId;

  @HiveField(1)
  @JsonKey(name: 'ref_no')
  final String? refNo;

  @HiveField(2)
  @Json<PERSON>ey(name: 'transaction_date', toJson: dateToString)
  final DateTime transactionDate;

  @HiveField(3)
  final String? status;

  @HiveField(4)
  @JsonKey(name: 'location_id', toJson: intToString)
  final int locationId;

  @HiveField(5)
  @Json<PERSON>ey(name: 'exchange_rate')
  final String? exchangeRate;

  @HiveField(6)
  @Json<PERSON>ey(name: 'pay_term_number')
  final String? payTermNumber;

  @HiveField(7)
  @JsonKey(name: 'pay_term_type')
  final String? payTermType;

  @HiveField(8)
  @JsonKey(name: 'total_before_tax')
  final String totalBeforeTax;

  @HiveField(9)
  @JsonKey(name: 'discount_type')
  final String? discountType;

  @HiveField(10)
  @JsonKey(name: 'discount_amount')
  final String? discountAmount;

  @HiveField(11)
  @JsonKey(name: 'tax_id')
  final String? taxId;

  @HiveField(12)
  @JsonKey(name: 'tax_amount')
  final String? taxAmount;

  @HiveField(13)
  @JsonKey(name: 'additional_notes')
  final String? additionalNotes;

  @HiveField(14)
  @JsonKey(name: 'shipping_details')
  final String? shippingDetails;

  @HiveField(15)
  @JsonKey(name: 'shipping_charges')
  final String? shippingCharges;

  @HiveField(16)
  @JsonKey(name: 'additional_expense_key_1')
  final String? additionalExpenseKey1;

  @HiveField(17)
  @JsonKey(name: 'additional_expense_value_1')
  final String? additionalExpenseValue1;

  @HiveField(18)
  @JsonKey(name: 'additional_expense_key_2')
  final String? additionalExpenseKey2;

  @HiveField(19)
  @JsonKey(name: 'additional_expense_value_2')
  final String? additionalExpenseValue2;

  @HiveField(20)
  @JsonKey(name: 'additional_expense_key_3')
  final String? additionalExpenseKey3;

  @HiveField(21)
  @JsonKey(name: 'additional_expense_value_3')
  final String? additionalExpenseValue3;

  @HiveField(22)
  @JsonKey(name: 'additional_expense_key_4')
  final String? additionalExpenseKey4;

  @HiveField(23)
  @JsonKey(name: 'additional_expense_value_4')
  final String? additionalExpenseValue4;

  @HiveField(24)
  @JsonKey(name: 'final_total')
  final String finalTotal;

  @JsonKey(name: 'source')
  @HiveField(25)
  final String? source;

  @HiveField(26)
  @JsonKey(toJson: _toJsonList, fromJson: _fromJsonList)
  final List<Payment> payment;

  @HiveField(27)
  @JsonKey(toJson: _productsToJson, fromJson: _productsFromJson)
  final List<PurchaseProductToAPI> products;

  @HiveField(28)
  @JsonKey(includeFromJson: false, includeToJson: false)
  final String? offlineID;

  @HiveField(29)
  @JsonKey(name: 'location_info', toJson: locationInfoToJson)
  final LocationInfo? locationInfo;

  const PurchaseToAPI({
    required this.contactId,
    this.refNo,
    required this.transactionDate,
    this.status,
    required this.locationId,
    this.exchangeRate = "1",
    this.payTermNumber,
    this.payTermType,
    required this.totalBeforeTax,
    this.discountType,
    this.discountAmount,
    this.taxId,
    this.taxAmount,
    this.additionalNotes,
    this.shippingDetails,
    this.shippingCharges,
    this.additionalExpenseKey1 = "additional_expense_key_1",
    this.additionalExpenseValue1 = "additional_expense_value_1",
    this.additionalExpenseKey2 = "additional_expense_key_2",
    this.additionalExpenseValue2 = "additional_expense_value_2",
    this.additionalExpenseKey3 = "additional_expense_key_3",
    this.additionalExpenseValue3 = "additional_expense_value_3",
    this.additionalExpenseKey4 = "additional_expense_key_3",
    this.additionalExpenseValue4 = "additional_expense_value_4",
    required this.finalTotal,
    this.source,
    required this.payment,
    required this.products,
    this.offlineID,
    this.locationInfo,
  });

  static List<Map<String, dynamic>> _productsToJson(
      List<PurchaseProductToAPI> products) {
    return products.map((product) => product.toJson()).toList();
  }

  static List<PurchaseProductToAPI> _productsFromJson(
      List<Map<String, dynamic>> json) {
    return json.map((map) => PurchaseProductToAPI.fromJson(map)).toList();
  }

  static List<Map<String, dynamic>> _toJsonList(List<Payment> list) {
    return list.map((item) => item.toJson()).toList();
  }

  static List<Payment> _fromJsonList(List<Map<String, dynamic>> json) {
    return json.map((map) => Payment.fromJson(map)).toList();
  }

  Map<String, dynamic> toJson() => _$PurchaseToAPIToJson(this);

  factory PurchaseToAPI.fromJson(Map<String, dynamic> json) =>
      _$PurchaseToAPIFromJson(json);

  @override
  List<Object?> get props => [
        contactId,
        refNo,
        transactionDate,
        status,
        locationId,
        exchangeRate,
        payTermNumber,
        payTermType,
        totalBeforeTax,
        discountType,
        discountAmount,
        taxId,
        taxAmount,
        additionalNotes,
        shippingDetails,
        shippingCharges,
        additionalExpenseKey1,
        additionalExpenseValue1,
        additionalExpenseKey2,
        additionalExpenseValue2,
        additionalExpenseKey3,
        additionalExpenseValue3,
        additionalExpenseKey4,
        additionalExpenseValue4,
        finalTotal,
        source,
        products,
        offlineID,
      ];
}

@HiveType(typeId: 43)
@JsonSerializable(explicitToJson: true)
class PurchaseProductToAPI extends Equatable {
  @HiveField(0)
  @JsonKey(name: 'product_id', toJson: removeLastDigit)
  final int productId;

  @HiveField(1)
  @JsonKey(name: 'variation_id')
  final int? variationId;

  @HiveField(2)
  @JsonKey(name: 'quantity')
  final String? quantity;

  @HiveField(3)
  @JsonKey(name: 'product_unit_id')
  final String productUnitId;

  @HiveField(4)
  @JsonKey(name: 'sub_unit_id')
  final String? subUnitId;

  @HiveField(5)
  @JsonKey(name: 'pp_without_discount')
  final String? ppWithoutDiscount;

  @HiveField(6)
  @JsonKey(name: 'discount_percent')
  final String? discountPercent;

  @HiveField(7)
  @JsonKey(name: 'purchase_price')
  final String purchasePrice;

  @HiveField(8)
  @JsonKey(name: 'purchase_line_tax_id')
  final int? purchaseLineTaxId;

  @HiveField(9)
  @JsonKey(name: 'item_tax')
  final String? itemTax;

  @HiveField(10)
  @JsonKey(name: 'purchase_price_inc_tax')
  final String? purchasePriceIncTax;

  @HiveField(11)
  @JsonKey(name: 'profit_percent')
  final String? profitPercent;

  @HiveField(12)
  @JsonKey(name: 'default_sell_price')
  final String defaultSellPrice;

  @HiveField(13)
  @JsonKey(name: 'mfg_date')
  final String? mfgDate;

  @HiveField(14)
  @JsonKey(name: 'exp_date')
  final String? expDate;

  const PurchaseProductToAPI({
    required this.productId,
    this.variationId,
    required this.quantity,
    required this.productUnitId,
    this.subUnitId,
    this.ppWithoutDiscount,
    this.discountPercent,
    required this.purchasePrice,
    this.purchaseLineTaxId,
    this.itemTax,
    this.purchasePriceIncTax,
    this.profitPercent,
    required this.defaultSellPrice,
    this.mfgDate,
    this.expDate,
  });

  factory PurchaseProductToAPI.fromJson(Map<String, dynamic> json) =>
      _$PurchaseProductToAPIFromJson(json);

  Map<String, dynamic> toJson() => _$PurchaseProductToAPIToJson(this);

  @override
  List<Object?> get props => [
        productId,
        variationId,
        quantity,
        productUnitId,
        subUnitId,
        ppWithoutDiscount,
        discountPercent,
        purchasePrice,
        purchaseLineTaxId,
        itemTax,
        purchasePriceIncTax,
        profitPercent,
        defaultSellPrice,
        mfgDate,
        expDate,
      ];
}
