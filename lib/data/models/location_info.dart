import 'package:equatable/equatable.dart';
import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';

part 'location_info.g.dart';

@HiveType(typeId: 46)
@JsonSerializable(explicitToJson: true)
class LocationInfo extends Equatable {
  @HiveField(0)
  @Json<PERSON>ey(name: 'id')
  final int? id;

  @HiveField(1)
  @JsonKey(name: 'log_name')
  final String? logName;

  @HiveField(2)
  @JsonKey(name: 'description')
  final String? description;

  @HiveField(3)
  @JsonKey(name: 'subject_id')
  final int? subjectId;

  @HiveField(4)
  @JsonKey(name: 'subject_type')
  final String? subjectType;

  @HiveField(5)
  @JsonKey(name: 'event')
  final dynamic event;

  @HiveField(6)
  @<PERSON>son<PERSON>ey(name: 'business_id')
  final int? businessId;

  @HiveField(7)
  @<PERSON>son<PERSON><PERSON>(name: 'causer_id')
  final int? causerId;

  @HiveField(8)
  @<PERSON>sonKey(name: 'causer_type')
  final String? causerType;

  @HiveField(9)
  @Json<PERSON><PERSON>(name: 'properties')
  final dynamic properties;

  @HiveField(10)
  @JsonKey(name: 'longitude', fromJson: double.parse)
  final double longitude;

  @HiveField(11)
  @JsonKey(name: 'latitude', fromJson: double.parse)
  final double latitude;

  @HiveField(12)
  @JsonKey(name: 'batch_uuid')
  final dynamic batchUuid;

  @HiveField(13)
  @JsonKey(name: 'created_at')
  final String? createdAt;

  @HiveField(14)
  @JsonKey(name: 'updated_at')
  final String? updatedAt;

  const LocationInfo({
    this.id,
    this.logName,
    this.description,
    this.subjectId,
    this.subjectType,
    this.event,
    this.businessId,
    this.causerId,
    this.causerType,
    this.properties,
    required this.longitude,
    required this.latitude,
    this.batchUuid,
    this.createdAt,
    this.updatedAt,
  });

  factory LocationInfo.fromJson(Map<String, dynamic> json) =>
      _$LocationInfoFromJson(json);

  Map<String, dynamic> toJson() => _$LocationInfoToJson(this);

  @override
  List<Object?> get props => [
        id,
        logName,
        description,
        subjectId,
        subjectType,
        event,
        businessId,
        causerId,
        causerType,
        properties,
        longitude,
        latitude,
        batchUuid,
        createdAt,
        updatedAt,
      ];
}

LocationInfo? locationInfoFromListJson(List<dynamic>? jsonList) {
  if (jsonList?.isNotEmpty ?? false) {
    return LocationInfo.fromJson(jsonList!.last);
  }
  return null;
}
