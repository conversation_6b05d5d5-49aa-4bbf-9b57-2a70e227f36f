// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'table.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class BusinessTableAdapter extends TypeAdapter<BusinessTable> {
  @override
  final int typeId = 61;

  @override
  BusinessTable read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return BusinessTable(
      id: fields[0] as int,
      businessId: fields[1] as int?,
      locationId: fields[2] as int?,
      name: fields[3] as String?,
      description: fields[4] as String?,
      createdBy: fields[5] as int?,
      deletedAt: fields[6] as String?,
      createdAt: fields[7] as DateTime,
      updatedAt: fields[8] as DateTime,
    );
  }

  @override
  void write(BinaryWriter writer, BusinessTable obj) {
    writer
      ..writeByte(9)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.businessId)
      ..writeByte(2)
      ..write(obj.locationId)
      ..writeByte(3)
      ..write(obj.name)
      ..writeByte(4)
      ..write(obj.description)
      ..writeByte(5)
      ..write(obj.createdBy)
      ..writeByte(6)
      ..write(obj.deletedAt)
      ..writeByte(7)
      ..write(obj.createdAt)
      ..writeByte(8)
      ..write(obj.updatedAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is BusinessTableAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BusinessTable _$BusinessTableFromJson(Map<String, dynamic> json) =>
    BusinessTable(
      id: (json['id'] as num).toInt(),
      businessId: (json['business_id'] as num?)?.toInt(),
      locationId: (json['location_id'] as num?)?.toInt(),
      name: json['name'] as String?,
      description: json['description'] as String?,
      createdBy: (json['created_by'] as num?)?.toInt(),
      deletedAt: json['deleted_at'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );

Map<String, dynamic> _$BusinessTableToJson(BusinessTable instance) =>
    <String, dynamic>{
      'id': instance.id,
      'business_id': instance.businessId,
      'location_id': instance.locationId,
      'name': instance.name,
      'description': instance.description,
      'created_by': instance.createdBy,
      'deleted_at': instance.deletedAt,
      'created_at': dateToString(instance.createdAt),
      'updated_at': dateToString(instance.updatedAt),
    };
