import 'package:equatable/equatable.dart';
import 'package:intl/intl.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:hive/hive.dart';
import 'package:we2up/data/models/location_info.dart';

import '../../utils/we2up_constants.dart';

part 'cash_register_to_api.g.dart';

@HiveType(typeId: 44)
@JsonSerializable(explicitToJson: true)
class CashRegisterToAPI extends Equatable {
  @HiveField(0)
  @JsonKey(name: 'location_info', toJson: locationInfoToJson)
  final LocationInfo? locationInfo;

  @HiveField(1)
  @JsonKey(name: 'location_id')
  final int? locationId;

  @HiveField(2)
  @JsonKey(name: 'initial_amount')
  final double? initialAmount;

  @HiveField(3)
  @JsonKey(
    name: "created_at",
    fromJson: _dateTimeFromString,
    toJson: _dateTimeToString,
  )
  final DateTime createdAt;

  @HiveField(4)
  @Json<PERSON>ey(
    name: "closed_at",
    fromJson: _dateTimeFromString,
    toJson: _dateTimeToString,
  )
  final DateTime? closedAt;

  @HiveField(5)
  @JsonKey(fromJson: _statusFromJson, toJson: _statusToJson)
  final CashRegisterStatus status;

  @HiveField(6)
  @JsonKey(name: 'closing_amount')
  final double closingAmount;

  @HiveField(7)
  @JsonKey(name: 'total_card_slips')
  final int totalCardSlips;

  @HiveField(8)
  @JsonKey(name: 'total_cheques')
  final int totalCheques;

  @HiveField(9)
  @JsonKey(name: 'closing_note')
  final String? closingNote;

  @HiveField(10)
  @JsonKey(
    name: "transaction_ids",
    fromJson: _transactionIdsFromJson,
    toJson: _transactionIdsToJson,
  )
  final List<int>? transactionIds;

  @HiveField(11)
  @JsonKey(name: 'id')
  final int? id;

  const CashRegisterToAPI({
    this.locationInfo,
    this.locationId,
    required this.createdAt,
    this.closedAt,
    required this.status,
    required this.closingAmount,
    this.initialAmount,
    this.totalCardSlips = 0,
    this.totalCheques = 0,
    this.closingNote,
    this.transactionIds,
    this.id,
  });

  factory CashRegisterToAPI.fromJson(Map<String, dynamic> json) =>
      _$CashRegisterToAPIFromJson(json);

  Map<String, dynamic> toJson() => _$CashRegisterToAPIToJson(this);

  @override
  List<Object?> get props => [
        locationInfo,
        locationId,
        initialAmount,
        createdAt,
        closedAt,
        status,
        closingAmount,
        totalCardSlips,
        totalCheques,
        closingNote,
        transactionIds,
        id,
      ];

  static CashRegisterStatus _statusFromJson(String status) {
    return status == 'open'
        ? CashRegisterStatus.open
        : CashRegisterStatus.close;
  }

  static String _statusToJson(CashRegisterStatus status) {
    return status.value;
  }

  static DateTime _dateTimeFromString(String dateTimeString) {
    return DateTime.parse(dateTimeString);
  }

  static String? _dateTimeToString(DateTime? dateTime) {
    if (dateTime == null) {
      return null;
    }
    return DateFormat('yyyy-M-d HH:mm:ss').format(dateTime);
  }

  static List<int>? _transactionIdsFromJson(String? ids) {
    if (ids != null) {
      return ids.split(',').map(int.parse).toList();
    }
    return null;
  }

  static String? _transactionIdsToJson(List<int>? ids) {
    if (ids != null) {
      return ids.join(',');
    }
    return null;
  }
}

enum CashRegisterStatus { open, close }

extension CashRegisterStatusExtension on CashRegisterStatus {
  String get value {
    return this == CashRegisterStatus.open ? 'open' : 'close';
  }
}
