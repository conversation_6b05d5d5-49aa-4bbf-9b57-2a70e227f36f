import 'package:equatable/equatable.dart';
import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:we2up/data/models/location_info.dart';
import 'package:we2up/data/models/product.dart';

import '../../utils/we2up_constants.dart';

part 'new_product_to_api.g.dart';

@HiveType(typeId: 42)
@JsonSerializable()
class NewProductToAPI extends Equatable {
  @HiveField(0)
  @Json<PERSON>ey(name: 'name')
  final String? name;

  @HiveField(1)
  @JsonKey(name: 'sku')
  final String? sku;

  @HiveField(2)
  @JsonKey(name: 'barcode_type')
  final String? barcodeType;

  @HiveField(3)
  @JsonKey(name: 'unit_id', toJson: _unitIdToJson)
  final Unit unitId;

  @HiveField(4)
  @JsonKey(name: 'brand_id')
  final String? brandId;

  @HiveField(5)
  @<PERSON>son<PERSON>ey(name: 'category_id', toJson: _categoryToJson)
  final ProductCategory? categoryId;

  @HiveField(6)
  @Json<PERSON>ey(name: 'sub_category_id')
  final String? subCategoryId;

  @HiveField(7)
  @JsonKey(name: 'enable_stock', toJson: _enableStockToJson)
  final bool enableStock;

  @HiveField(8)
  @JsonKey(name: 'alert_quantity')
  final String? alertQuantity;

  @HiveField(9)
  @JsonKey(name: 'has_module_data')
  final String? hasModuleData;

  @HiveField(10)
  @JsonKey(name: 'repair_model_id')
  final String? repairModelId;

  @HiveField(11)
  @JsonKey(name: 'product_description')
  final String? productDescription;

  @HiveField(12)
  @JsonKey(name: 'expiry_period')
  final String? expiryPeriod;

  @HiveField(13)
  @JsonKey(name: 'expiry_period_type', toJson: _expiryTypeToJson)
  final ExpiryPeriodType? expiryPeriodType;

  @HiveField(14)
  @JsonKey(name: 'weight')
  final String? weight;

  @HiveField(15)
  @JsonKey(name: 'product_custom_field1')
  final String? productCustomField1;

  @HiveField(16)
  @JsonKey(name: 'product_custom_field2')
  final String? productCustomField2;

  @HiveField(17)
  @JsonKey(name: 'product_custom_field3')
  final String? productCustomField3;

  @HiveField(18)
  @JsonKey(name: 'product_custom_field4')
  final String? productCustomField4;

  @HiveField(19)
  @JsonKey(name: 'woocommerce_disable_sync')
  final String woocommerceDisableSync = "0";

  @HiveField(20)
  @JsonKey(name: 'tax', toJson: _taxIdToJson)
  final TaxRate? tax;

  @HiveField(21)
  @JsonKey(name: 'tax_type', toJson: _taxTypeToJson)
  final TaxType taxType;

  @HiveField(22)
  @JsonKey(name: 'type')
  final String type;

  @HiveField(23)
  @JsonKey(name: 'single_dpp')
  final String? singleDpp;

  @HiveField(24)
  @JsonKey(name: 'single_dpp_inc_tax')
  final String? singleDppIncTax;

  @HiveField(25)
  @JsonKey(name: 'profit_percent')
  final String? profitPercent;

  @HiveField(26)
  @JsonKey(name: 'single_dsp')
  final String? singleDsp;

  @HiveField(27)
  @JsonKey(name: 'single_dsp_inc_tax')
  final String? singleDspIncTax;

  @HiveField(28)
  @JsonKey(
    name: 'location_info',
    includeFromJson: false,
    toJson: locationInfoToJson,
  )
  final LocationInfo? locationInfo;

  @HiveField(29)
  @JsonKey(name: 'product_locations')
  final List<int> productLocations;

  @HiveField(30)
  @JsonKey(name: 'opening_stock')
  final List<Map<String, dynamic>> openingStock;

  @HiveField(31)
  @JsonKey(name: 'image')
  final String? imagePath;

  const NewProductToAPI({
    this.name,
    this.sku,
    this.type = "single",
    this.barcodeType = "C128",
    required this.unitId,
    this.brandId,
    this.categoryId,
    this.subCategoryId,
    required this.enableStock,
    this.alertQuantity,
    this.hasModuleData,
    this.repairModelId,
    this.productDescription,
    this.expiryPeriod,
    this.expiryPeriodType,
    this.weight,
    this.productCustomField1,
    this.productCustomField2,
    this.productCustomField3,
    this.productCustomField4,
    this.tax,
    required this.taxType,
    this.singleDpp,
    this.singleDppIncTax,
    this.profitPercent,
    this.singleDsp,
    this.singleDspIncTax,
    this.locationInfo,
    this.imagePath,
    required this.productLocations,
    required this.openingStock,
  });

  static String _enableStockToJson(bool inStock) => inStock ? "1" : "0";

  static String _taxTypeToJson(TaxType taxType) => taxType.name;

  static String? _expiryTypeToJson(ExpiryPeriodType? ep) => ep?.name;

  static String _unitIdToJson(Unit unit) => unit.id.toString();

  static String? _categoryToJson(ProductCategory? pc) => pc?.id.toString();

  static String? _taxIdToJson(TaxRate? tax) => tax?.id.toString();

  factory NewProductToAPI.fromJson(Map<String, dynamic> json) =>
      _$NewProductToAPIFromJson(json);

  Map<String, dynamic> toJson() => _$NewProductToAPIToJson(this);

  @override
  List<Object?> get props => [
        name,
        sku,
        barcodeType,
        unitId,
        brandId,
        categoryId,
        subCategoryId,
        enableStock,
        alertQuantity,
        hasModuleData,
        repairModelId,
        productDescription,
        expiryPeriod,
        expiryPeriodType,
        weight,
        productCustomField1,
        productCustomField2,
        productCustomField3,
        productCustomField4,
        woocommerceDisableSync,
        tax,
        taxType,
        type,
        singleDpp,
        singleDppIncTax,
        profitPercent,
        singleDsp,
        singleDspIncTax,
        productLocations,
        imagePath,
      ];
}

enum TaxType { exclusive, inclusive }

enum ExpiryPeriodType { years, months, days }
