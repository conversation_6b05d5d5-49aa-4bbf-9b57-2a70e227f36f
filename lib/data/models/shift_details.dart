import 'package:json_annotation/json_annotation.dart';

part 'shift_details.g.dart';

@JsonSerializable()
class ShiftDetails {
  @Json<PERSON>ey(name: 'payments')
  List<ShiftPayment> payments;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'transactions')
  List<Transaction> transactions;

  @Json<PERSON>ey(name: 'not_related_payments')
  List<NotRelatedPayment> notRelatedPayments;

  ShiftDetails({
    required this.payments,
    required this.transactions,
    required this.notRelatedPayments,
  });

  factory ShiftDetails.fromJson(Map<String, dynamic> json) =>
      _$ShiftDetailsFromJson(json);

  Map<String, dynamic> toJson() => _$ShiftDetailsToJson(this);
}

@JsonSerializable()
class ShiftPayment {
  @Json<PERSON>ey(name: 'total_sell_return_paid')
  String totalSellReturnPaid;

  @JsonKey(name: 'total_purchase_return_paid')
  String totalPurchaseReturnPaid;

  @<PERSON>son<PERSON>ey(name: 'total_sell_paid')
  String totalSellPaid;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'total_purchase_paid')
  String totalPurchasePaid;

  @<PERSON>son<PERSON>ey(name: 'total_expense_paid')
  String totalExpensePaid;

  @JsonKey(name: 'total_expense_refund_paid')
  String totalExpenseRefundPaid;

  ShiftPayment({
    required this.totalSellReturnPaid,
    required this.totalPurchaseReturnPaid,
    required this.totalSellPaid,
    required this.totalPurchasePaid,
    required this.totalExpensePaid,
    required this.totalExpenseRefundPaid,
  });

  factory ShiftPayment.fromJson(Map<String, dynamic> json) =>
      _$ShiftPaymentFromJson(json);

  Map<String, dynamic> toJson() => _$ShiftPaymentToJson(this);
}

@JsonSerializable()
class Transaction {
  @JsonKey(name: 'total_sell_return')
  String totalSellReturn;

  @JsonKey(name: 'total_purchase_return')
  String totalPurchaseReturn;

  @JsonKey(name: 'total_sell')
  String totalSell;

  @JsonKey(name: 'total_purchase')
  String totalPurchase;

  @JsonKey(name: 'total_expense')
  String totalExpense;

  @JsonKey(name: 'total_expense_refund')
  String totalExpenseRefund;

  Transaction({
    required this.totalSellReturn,
    required this.totalPurchaseReturn,
    required this.totalSell,
    required this.totalPurchase,
    required this.totalExpense,
    required this.totalExpenseRefund,
  });

  factory Transaction.fromJson(Map<String, dynamic> json) =>
      _$TransactionFromJson(json);

  Map<String, dynamic> toJson() => _$TransactionToJson(this);
}

@JsonSerializable()
class NotRelatedPayment {
  @JsonKey(name: 'total_supplier_paid')
  String totalSupplierPaid;

  @JsonKey(name: 'total_customer_paid')
  String totalCustomerPaid;

  NotRelatedPayment({
    required this.totalSupplierPaid,
    required this.totalCustomerPaid,
  });

  factory NotRelatedPayment.fromJson(Map<String, dynamic> json) =>
      _$NotRelatedPaymentFromJson(json);

  Map<String, dynamic> toJson() => _$NotRelatedPaymentToJson(this);
}