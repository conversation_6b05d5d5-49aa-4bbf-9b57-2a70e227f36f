import 'package:equatable/equatable.dart';
import 'package:hive/hive.dart';

part 'profit_loss_report.g.dart';

@HiveType(typeId: 15)
class ProfitLossReport extends Equatable {
  @HiveField(0)
  final double totalPurchaseShippingCharge;

  @HiveField(1)
  final double totalSellShippingCharge;

  @HiveField(2)
  final double totalPurchaseAdditionalExpense;

  @HiveField(3)
  final double totalSellAdditionalExpense;

  @HiveField(4)
  final double totalTransferShippingCharges;

  @HiveField(5)
  final double openingStock;

  @HiveField(6)
  final double closingStock;

  @HiveField(7)
  final double totalPurchase;

  @HiveField(8)
  final double totalPurchaseDiscount;

  @HiveField(9)
  final double totalPurchaseReturn;

  @HiveField(10)
  final double totalSell;

  @HiveField(11)
  final double totalSellDiscount;

  @HiveField(12)
  final double totalSellReturn;

  @HiveField(13)
  final double totalSellRoundOff;

  @HiveField(14)
  final double totalExpense;

  @HiveField(15)
  final double totalAdjustment;

  @HiveField(16)
  final double totalRecovered;

  @HiveField(17)
  final double totalRewardAmount;

  @HiveField(18)
  final List<ModuleData> leftSideModuleData;

  @HiveField(19)
  final List<ModuleData> rightSideModuleData;

  @HiveField(20)
  final double netProfit;

  @HiveField(21)
  final double grossProfit;

  @HiveField(22)
  final List<Map<String, dynamic>> totalSellBySubtype;

  const ProfitLossReport({
    required this.totalPurchaseShippingCharge,
    required this.totalSellShippingCharge,
    required this.totalPurchaseAdditionalExpense,
    required this.totalSellAdditionalExpense,
    required this.totalTransferShippingCharges,
    required this.openingStock,
    required this.closingStock,
    required this.totalPurchase,
    required this.totalPurchaseDiscount,
    required this.totalPurchaseReturn,
    required this.totalSell,
    required this.totalSellDiscount,
    required this.totalSellReturn,
    required this.totalSellRoundOff,
    required this.totalExpense,
    required this.totalAdjustment,
    required this.totalRecovered,
    required this.totalRewardAmount,
    required this.leftSideModuleData,
    required this.rightSideModuleData,
    required this.netProfit,
    required this.grossProfit,
    required this.totalSellBySubtype,
  });

  factory ProfitLossReport.fromJson(Map<String, dynamic> json) {
    final leftSideModuleData = (json['left_side_module_data'] as List<dynamic>)
        .map((e) => ModuleData.fromJson(e))
        .toList();
    final rightSideModuleData =
        (json['right_side_module_data'] as List<dynamic>)
            .map((e) => ModuleData.fromJson(e))
            .toList();
    final totalSellBySubtype =
        List<Map<String, dynamic>>.from(json['total_sell_by_subtype'] ?? []);

    return ProfitLossReport(
      totalPurchaseShippingCharge:
          _parseDouble(json['total_purchase_shipping_charge']),
      totalSellShippingCharge: _parseDouble(json['total_sell_shipping_charge']),
      totalPurchaseAdditionalExpense:
          _parseDouble(json['total_purchase_additional_expense']),
      totalSellAdditionalExpense:
          _parseDouble(json['total_sell_additional_expense']),
      totalTransferShippingCharges:
          _parseDouble(json['total_transfer_shipping_charges']),
      openingStock: _parseDouble(json['opening_stock']),
      closingStock: _parseDouble(json['closing_stock']),
      totalPurchase: _parseDouble(json['total_purchase']),
      totalPurchaseDiscount: _parseDouble(json['total_purchase_discount']),
      totalPurchaseReturn: _parseDouble(json['total_purchase_return']),
      totalSell: _parseDouble(json['total_sell']),
      totalSellDiscount: _parseDouble(json['total_sell_discount']),
      totalSellReturn: _parseDouble(json['total_sell_return']),
      totalSellRoundOff: _parseDouble(json['total_sell_round_off']),
      totalExpense: _parseDouble(json['total_expense']),
      totalAdjustment: _parseDouble(json['total_adjustment']),
      totalRecovered: _parseDouble(json['total_recovered']),
      totalRewardAmount: _parseDouble(json['total_reward_amount']),
      leftSideModuleData: leftSideModuleData,
      rightSideModuleData: rightSideModuleData,
      netProfit: _parseDouble(json['net_profit']),
      grossProfit: _parseDouble(json['gross_profit']),
      totalSellBySubtype: totalSellBySubtype,
    );
  }

  static double _parseDouble(dynamic value) {
    if (value is double) {
      return value;
    } else if (value is int) {
      return value.toDouble();
    } else if (value is String) {
      return double.tryParse(value.replaceAll(",", "")) ?? 0.0;
    } else {
      return 0.0;
    }
  }

  @override
  List<Object?> get props => [
        totalPurchaseShippingCharge,
        totalSellShippingCharge,
        totalPurchaseAdditionalExpense,
        totalSellAdditionalExpense,
        totalTransferShippingCharges,
        openingStock,
        closingStock,
        totalPurchase,
        totalPurchaseDiscount,
        totalPurchaseReturn,
        totalSell,
        totalSellDiscount,
        totalSellReturn,
        totalSellRoundOff,
        totalExpense,
        totalAdjustment,
        totalRecovered,
        totalRewardAmount,
        leftSideModuleData,
        rightSideModuleData,
        netProfit,
        grossProfit,
        totalSellBySubtype,
      ];
}

@HiveType(typeId: 16)
class ModuleData extends Equatable {
  @HiveField(0)
  final double value;

  @HiveField(1)
  final String label;

  @HiveField(2)
  final bool addToNetProfit;

  const ModuleData({
    required this.value,
    required this.label,
    required this.addToNetProfit,
  });

  factory ModuleData.fromJson(Map<String, dynamic> json) {
    return ModuleData(
      value: _parseDouble(json['value']),
      label: json['label'] ?? '',
      addToNetProfit: json['add_to_net_profit'] ?? false,
    );
  }

  static double _parseDouble(dynamic value) {
    if (value is double) {
      return value;
    } else if (value is int) {
      return value.toDouble();
    } else if (value is String) {
      return double.tryParse(value) ?? 0.0;
    } else {
      return 0.0;
    }
  }

  @override
  List<Object?> get props => [value, label, addToNetProfit];
}
