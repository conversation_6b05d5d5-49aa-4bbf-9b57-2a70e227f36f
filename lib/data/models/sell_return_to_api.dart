import 'package:equatable/equatable.dart';
import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:we2up/data/models/location_info.dart';

import '../../utils/we2up_constants.dart';

part 'sell_return_to_api.g.dart';

@JsonSerializable()
@HiveType(typeId: 30)
class SellReturnToAPI extends Equatable {
  @HiveField(0)
  @<PERSON>son<PERSON>ey(name: 'transaction_id')
  final int transactionId;

  @HiveField(1)
  @<PERSON>sonKey(name: 'transaction_date')
  final String transactionDate;

  @HiveField(2)
  @JsonKey(name: 'invoice_no')
  final String? invoiceNo;

  @HiveField(3)
  @JsonKey(name: 'discount_amount')
  final double? discountAmount;

  @HiveField(4)
  @JsonKey(name: 'discount_type')
  final String? discountType;

  @HiveField(5)
  @<PERSON>son<PERSON>ey(name: 'products')
  final List<ReturnProductToAPI> products;

  @HiveField(6)
  @JsonKey(name: 'location_info', toJson: locationInfoToJson)
  final LocationInfo? locationInfo;

  const SellReturnToAPI({
    required this.transactionId,
    required this.transactionDate,
    this.invoiceNo,
    this.discountAmount,
    this.discountType,
    required this.products,
    this.locationInfo,
  });

  factory SellReturnToAPI.fromJson(Map<String, dynamic> json) =>
      _$SellReturnToAPIFromJson(json);

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = _$SellReturnToAPIToJson(this);
    final List<Map<String, dynamic>> productJsonList =
        products.map((product) => product.toJson()).toList();
    data['products'] = productJsonList;
    return data;
  }

  @override
  List<Object?> get props => [
        transactionId,
        transactionDate,
        invoiceNo,
        discountAmount,
        discountType,
        products,
      ];
}

@JsonSerializable()
@HiveType(typeId: 31)
class ReturnProductToAPI extends Equatable {
  @HiveField(0)
  @JsonKey(name: 'sell_line_id')
  final int sellLineId;
  @HiveField(1)
  @JsonKey(name: 'quantity')
  final double? quantity;
  @HiveField(2)
  @JsonKey(name: 'unit_price_inc_tax')
  final double unitPriceIncTax;

  const ReturnProductToAPI({
    required this.sellLineId,
    required this.quantity,
    required this.unitPriceIncTax,
  });

  factory ReturnProductToAPI.fromJson(Map<String, dynamic> json) =>
      _$ReturnProductToAPIFromJson(json);

  Map<String, dynamic> toJson() => _$ReturnProductToAPIToJson(this);

  @override
  List<Object?> get props => [sellLineId, quantity, unitPriceIncTax];
}
