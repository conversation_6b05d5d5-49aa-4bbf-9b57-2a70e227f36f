import 'package:equatable/equatable.dart';
import 'package:hive/hive.dart';

part 'payment_account.g.dart';

@HiveType(typeId: 6)
class PaymentAccount extends Equatable {
  @HiveField(0)
  final int id;

  @HiveField(1)
  final int businessId;

  @HiveField(2)
  final String name;

  @HiveField(3)
  final String accountNumber;

  @HiveField(4)
  final int accountTypeId;

  @HiveField(5)
  final int createdBy;

  @HiveField(6)
  final int isClosed;

  const PaymentAccount({
    required this.id,
    required this.businessId,
    required this.name,
    required this.accountNumber,
    required this.accountTypeId,
    required this.createdBy,
    required this.isClosed,
  });

  @override
  List<Object?> get props => [
        id,
        businessId,
        name,
        accountNumber,
        accountTypeId,
        createdBy,
        isClosed,
      ];

  factory PaymentAccount.fromJson(Map<String, dynamic> json) {
    return PaymentAccount(
      id: json['id'] ?? 0,
      businessId: json['business_id'] ?? 0,
      name: json['name'] ?? '',
      accountNumber: json['account_number'] ?? '',
      accountTypeId: json['account_type_id'] ?? 0,
      createdBy: json['created_by'] ?? 0,
      isClosed: json['is_closed'] ?? 0,
    );
  }
}
