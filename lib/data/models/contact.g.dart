// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'contact.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class ContactAdapter extends TypeAdapter<Contact> {
  @override
  final int typeId = 14;

  @override
  Contact read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Contact(
      id: fields[0] as int,
      businessId: fields[1] as int,
      type: fields[2] as String,
      supplierBusinessName: fields[3] as String?,
      name: fields[4] as String?,
      prefix: fields[5] as String?,
      firstName: fields[6] as String?,
      middleName: fields[7] as String?,
      lastName: fields[8] as String?,
      email: fields[9] as String?,
      contactId: fields[10] as String?,
      contactStatus: fields[11] as String?,
      taxNumber: fields[12] as String?,
      city: fields[13] as String?,
      state: fields[14] as String?,
      country: fields[15] as String?,
      addressLine1: fields[16] as String?,
      addressLine2: fields[17] as String?,
      zipCode: fields[18] as String?,
      dob: fields[19] as String?,
      mobile: fields[20] as String?,
      landline: fields[21] as String?,
      alternateNumber: fields[22] as String?,
      payTermNumber: fields[23] as int?,
      payTermType: fields[24] as String?,
      creditLimit: fields[25] as String?,
      createdBy: fields[26] as int?,
      convertedBy: fields[27] as int?,
      convertedOn: fields[28] as String?,
      balance: fields[29] as String?,
      totalRp: fields[30] as int?,
      totalRpUsed: fields[31] as int?,
      totalRpExpired: fields[32] as int?,
      isDefault: fields[33] as int?,
      shippingAddress: fields[34] as String?,
      shippingCustomFieldDetails: fields[35] as String?,
      isExport: fields[36] as int?,
      exportCustomField1: fields[37] as String?,
      exportCustomField2: fields[38] as String?,
      exportCustomField3: fields[39] as String?,
      exportCustomField4: fields[40] as String?,
      exportCustomField5: fields[41] as String?,
      exportCustomField6: fields[42] as String?,
      position: fields[43] as String?,
      customerGroupId: fields[44] as int?,
      crmSource: fields[45] as String?,
      crmLifeStage: fields[46] as String?,
      customField1: fields[47] as String?,
      customField2: fields[48] as String?,
      customField3: fields[49] as String?,
      customField4: fields[50] as String?,
      customField5: fields[51] as String?,
      customField6: fields[52] as String?,
      customField7: fields[53] as String?,
      customField8: fields[54] as String?,
      customField9: fields[55] as String?,
      customField10: fields[56] as String?,
      deletedAt: fields[57] as DateTime?,
      createdAt: fields[58] as DateTime?,
      updatedAt: fields[59] as DateTime?,
      priceGroupId: fields[60] as int?,
      lastSettlement: fields[61] as String?,
      isDefaultBefore: fields[62] as int?,
      isStocktaking: fields[63] as int?,
      posNote: fields[64] as String?,
      currentBalDue: fields[65] as String?,
      sgId: fields[66] as String?,
      isSettlement: fields[67] as int?,
      due: fields[68] as String?,
      customerGroup: fields[69] as String?,
      openingBalance: fields[70] as String?,
      openingBalancePaid: fields[71] as String?,
      maxTransactionDate: fields[72] as String?,
      transactionDate: fields[73] as String?,
      totalPurchase: fields[74] as String?,
      purchasePaid: fields[75] as String?,
      totalPurchaseReturn: fields[76] as String?,
      purchaseReturnPaid: fields[77] as String?,
      totalInvoice: fields[78] as String?,
      invoiceReceived: fields[79] as String?,
      totalSellReturn: fields[80] as String?,
      sellReturnPaid: fields[81] as String?,
      purchaseDue: fields[82] as double?,
      sellDue: fields[83] as double?,
      purchaseReturnDue: fields[84] as double?,
      sellReturnDue: fields[85] as double?,
      locationInfo: fields[86] as LocationInfo?,
    );
  }

  @override
  void write(BinaryWriter writer, Contact obj) {
    writer
      ..writeByte(87)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.businessId)
      ..writeByte(2)
      ..write(obj.type)
      ..writeByte(3)
      ..write(obj.supplierBusinessName)
      ..writeByte(4)
      ..write(obj.name)
      ..writeByte(5)
      ..write(obj.prefix)
      ..writeByte(6)
      ..write(obj.firstName)
      ..writeByte(7)
      ..write(obj.middleName)
      ..writeByte(8)
      ..write(obj.lastName)
      ..writeByte(9)
      ..write(obj.email)
      ..writeByte(10)
      ..write(obj.contactId)
      ..writeByte(11)
      ..write(obj.contactStatus)
      ..writeByte(12)
      ..write(obj.taxNumber)
      ..writeByte(13)
      ..write(obj.city)
      ..writeByte(14)
      ..write(obj.state)
      ..writeByte(15)
      ..write(obj.country)
      ..writeByte(16)
      ..write(obj.addressLine1)
      ..writeByte(17)
      ..write(obj.addressLine2)
      ..writeByte(18)
      ..write(obj.zipCode)
      ..writeByte(19)
      ..write(obj.dob)
      ..writeByte(20)
      ..write(obj.mobile)
      ..writeByte(21)
      ..write(obj.landline)
      ..writeByte(22)
      ..write(obj.alternateNumber)
      ..writeByte(23)
      ..write(obj.payTermNumber)
      ..writeByte(24)
      ..write(obj.payTermType)
      ..writeByte(25)
      ..write(obj.creditLimit)
      ..writeByte(26)
      ..write(obj.createdBy)
      ..writeByte(27)
      ..write(obj.convertedBy)
      ..writeByte(28)
      ..write(obj.convertedOn)
      ..writeByte(29)
      ..write(obj.balance)
      ..writeByte(30)
      ..write(obj.totalRp)
      ..writeByte(31)
      ..write(obj.totalRpUsed)
      ..writeByte(32)
      ..write(obj.totalRpExpired)
      ..writeByte(33)
      ..write(obj.isDefault)
      ..writeByte(34)
      ..write(obj.shippingAddress)
      ..writeByte(35)
      ..write(obj.shippingCustomFieldDetails)
      ..writeByte(36)
      ..write(obj.isExport)
      ..writeByte(37)
      ..write(obj.exportCustomField1)
      ..writeByte(38)
      ..write(obj.exportCustomField2)
      ..writeByte(39)
      ..write(obj.exportCustomField3)
      ..writeByte(40)
      ..write(obj.exportCustomField4)
      ..writeByte(41)
      ..write(obj.exportCustomField5)
      ..writeByte(42)
      ..write(obj.exportCustomField6)
      ..writeByte(43)
      ..write(obj.position)
      ..writeByte(44)
      ..write(obj.customerGroupId)
      ..writeByte(45)
      ..write(obj.crmSource)
      ..writeByte(46)
      ..write(obj.crmLifeStage)
      ..writeByte(47)
      ..write(obj.customField1)
      ..writeByte(48)
      ..write(obj.customField2)
      ..writeByte(49)
      ..write(obj.customField3)
      ..writeByte(50)
      ..write(obj.customField4)
      ..writeByte(51)
      ..write(obj.customField5)
      ..writeByte(52)
      ..write(obj.customField6)
      ..writeByte(53)
      ..write(obj.customField7)
      ..writeByte(54)
      ..write(obj.customField8)
      ..writeByte(55)
      ..write(obj.customField9)
      ..writeByte(56)
      ..write(obj.customField10)
      ..writeByte(57)
      ..write(obj.deletedAt)
      ..writeByte(58)
      ..write(obj.createdAt)
      ..writeByte(59)
      ..write(obj.updatedAt)
      ..writeByte(60)
      ..write(obj.priceGroupId)
      ..writeByte(61)
      ..write(obj.lastSettlement)
      ..writeByte(62)
      ..write(obj.isDefaultBefore)
      ..writeByte(63)
      ..write(obj.isStocktaking)
      ..writeByte(64)
      ..write(obj.posNote)
      ..writeByte(65)
      ..write(obj.currentBalDue)
      ..writeByte(66)
      ..write(obj.sgId)
      ..writeByte(67)
      ..write(obj.isSettlement)
      ..writeByte(68)
      ..write(obj.due)
      ..writeByte(69)
      ..write(obj.customerGroup)
      ..writeByte(70)
      ..write(obj.openingBalance)
      ..writeByte(71)
      ..write(obj.openingBalancePaid)
      ..writeByte(72)
      ..write(obj.maxTransactionDate)
      ..writeByte(73)
      ..write(obj.transactionDate)
      ..writeByte(74)
      ..write(obj.totalPurchase)
      ..writeByte(75)
      ..write(obj.purchasePaid)
      ..writeByte(76)
      ..write(obj.totalPurchaseReturn)
      ..writeByte(77)
      ..write(obj.purchaseReturnPaid)
      ..writeByte(78)
      ..write(obj.totalInvoice)
      ..writeByte(79)
      ..write(obj.invoiceReceived)
      ..writeByte(80)
      ..write(obj.totalSellReturn)
      ..writeByte(81)
      ..write(obj.sellReturnPaid)
      ..writeByte(82)
      ..write(obj.purchaseDue)
      ..writeByte(83)
      ..write(obj.sellDue)
      ..writeByte(84)
      ..write(obj.purchaseReturnDue)
      ..writeByte(85)
      ..write(obj.sellReturnDue)
      ..writeByte(86)
      ..write(obj.locationInfo);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ContactAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Contact _$ContactFromJson(Map<String, dynamic> json) => Contact(
      id: (json['id'] as num).toInt(),
      businessId: (json['business_id'] as num).toInt(),
      type: json['type'] as String,
      supplierBusinessName: json['supplier_business_name'] as String?,
      name: json['name'] as String?,
      prefix: json['prefix'] as String?,
      firstName: json['first_name'] as String?,
      middleName: json['middle_name'] as String?,
      lastName: json['last_name'] as String?,
      email: json['email'] as String?,
      contactId: Contact._toString(json['contact_id']),
      contactStatus: json['contact_status'] as String?,
      taxNumber: json['tax_number'] as String?,
      city: json['city'] as String?,
      state: json['state'] as String?,
      country: json['country'] as String?,
      addressLine1: json['address_line_1'] as String?,
      addressLine2: json['address_line_2'] as String?,
      zipCode: json['zip_code'] as String?,
      dob: json['dob'] as String?,
      mobile: json['mobile'] as String?,
      landline: json['landline'] as String?,
      alternateNumber: json['alternate_number'] as String?,
      payTermNumber: (json['pay_term_number'] as num?)?.toInt(),
      payTermType: json['pay_term_type'] as String?,
      creditLimit: json['credit_limit'] as String?,
      createdBy: (json['created_by'] as num?)?.toInt(),
      convertedBy: (json['converted_by'] as num?)?.toInt(),
      convertedOn: json['converted_on'] as String?,
      balance: json['balance'] as String?,
      totalRp: (json['total_rp'] as num?)?.toInt(),
      totalRpUsed: (json['total_rp_used'] as num?)?.toInt(),
      totalRpExpired: (json['total_rp_expired'] as num?)?.toInt(),
      isDefault: (json['is_default'] as num?)?.toInt(),
      shippingAddress: json['shipping_address'] as String?,
      shippingCustomFieldDetails:
          json['shipping_custom_field_details'] as String?,
      isExport: (json['is_export'] as num?)?.toInt(),
      exportCustomField1: json['export_custom_field_1'] as String?,
      exportCustomField2: json['export_custom_field_2'] as String?,
      exportCustomField3: json['export_custom_field_3'] as String?,
      exportCustomField4: json['export_custom_field_4'] as String?,
      exportCustomField5: json['export_custom_field_5'] as String?,
      exportCustomField6: json['export_custom_field_6'] as String?,
      position: json['position'] as String?,
      customerGroupId: (json['customer_group_id'] as num?)?.toInt(),
      crmSource: json['crm_source'] as String?,
      crmLifeStage: json['crm_life_stage'] as String?,
      customField1: json['custom_field1'] as String?,
      customField2: json['custom_field2'] as String?,
      customField3: json['custom_field3'] as String?,
      customField4: json['custom_field4'] as String?,
      customField5: json['custom_field5'] as String?,
      customField6: json['custom_field6'] as String?,
      customField7: json['custom_field7'] as String?,
      customField8: json['custom_field8'] as String?,
      customField9: json['custom_field9'] as String?,
      customField10: json['custom_field10'] as String?,
      deletedAt: json['deleted_at'] == null
          ? null
          : DateTime.parse(json['deleted_at'] as String),
      createdAt: json['created_at'] == null
          ? null
          : DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] == null
          ? null
          : DateTime.parse(json['updated_at'] as String),
      priceGroupId: (json['price_group_id'] as num?)?.toInt(),
      lastSettlement: json['last_settlement'] as String?,
      isDefaultBefore: (json['is_default_before'] as num?)?.toInt(),
      isStocktaking: (json['is_stocktaking'] as num?)?.toInt(),
      posNote: json['pos_note'] as String?,
      currentBalDue: json['current_bal_due'] as String?,
      sgId: json['sg_id'] as String?,
      isSettlement: (json['is_settlement'] as num?)?.toInt(),
      due: json['due'] as String?,
      customerGroup: json['customer_group'] as String?,
      openingBalance: json['opening_balance'] as String?,
      openingBalancePaid: json['opening_balance_paid'] as String?,
      maxTransactionDate: json['max_transaction_date'] as String?,
      transactionDate: json['transaction_date'] as String?,
      totalPurchase: json['total_purchase'] as String?,
      purchasePaid: json['purchase_paid'] as String?,
      totalPurchaseReturn: json['total_purchase_return'] as String?,
      purchaseReturnPaid: json['purchase_return_paid'] as String?,
      totalInvoice: json['total_invoice'] as String?,
      invoiceReceived: json['invoice_received'] as String?,
      totalSellReturn: json['total_sell_return'] as String?,
      sellReturnPaid: json['sell_return_paid'] as String?,
      purchaseDue: (json['purchase_due'] as num?)?.toDouble(),
      sellDue: (json['sell_due'] as num?)?.toDouble(),
      purchaseReturnDue: (json['purchase_return_due'] as num?)?.toDouble(),
      sellReturnDue: (json['sell_return_due'] as num?)?.toDouble(),
      locationInfo: locationInfoFromListJson(json['location_info'] as List?),
    );

Map<String, dynamic> _$ContactToJson(Contact instance) => <String, dynamic>{
      'id': instance.id,
      'business_id': instance.businessId,
      'type': instance.type,
      'supplier_business_name': instance.supplierBusinessName,
      'name': instance.name,
      'prefix': instance.prefix,
      'first_name': instance.firstName,
      'middle_name': instance.middleName,
      'last_name': instance.lastName,
      'email': instance.email,
      'contact_id': instance.contactId,
      'contact_status': instance.contactStatus,
      'tax_number': instance.taxNumber,
      'city': instance.city,
      'state': instance.state,
      'country': instance.country,
      'address_line_1': instance.addressLine1,
      'address_line_2': instance.addressLine2,
      'zip_code': instance.zipCode,
      'dob': instance.dob,
      'mobile': instance.mobile,
      'landline': instance.landline,
      'alternate_number': instance.alternateNumber,
      'pay_term_number': instance.payTermNumber,
      'pay_term_type': instance.payTermType,
      'credit_limit': instance.creditLimit,
      'created_by': instance.createdBy,
      'converted_by': instance.convertedBy,
      'converted_on': instance.convertedOn,
      'balance': instance.balance,
      'total_rp': instance.totalRp,
      'total_rp_used': instance.totalRpUsed,
      'total_rp_expired': instance.totalRpExpired,
      'is_default': instance.isDefault,
      'shipping_address': instance.shippingAddress,
      'shipping_custom_field_details': instance.shippingCustomFieldDetails,
      'is_export': instance.isExport,
      'export_custom_field_1': instance.exportCustomField1,
      'export_custom_field_2': instance.exportCustomField2,
      'export_custom_field_3': instance.exportCustomField3,
      'export_custom_field_4': instance.exportCustomField4,
      'export_custom_field_5': instance.exportCustomField5,
      'export_custom_field_6': instance.exportCustomField6,
      'position': instance.position,
      'customer_group_id': instance.customerGroupId,
      'crm_source': instance.crmSource,
      'crm_life_stage': instance.crmLifeStage,
      'custom_field1': instance.customField1,
      'custom_field2': instance.customField2,
      'custom_field3': instance.customField3,
      'custom_field4': instance.customField4,
      'custom_field5': instance.customField5,
      'custom_field6': instance.customField6,
      'custom_field7': instance.customField7,
      'custom_field8': instance.customField8,
      'custom_field9': instance.customField9,
      'custom_field10': instance.customField10,
      'deleted_at': instance.deletedAt?.toIso8601String(),
      'created_at': instance.createdAt?.toIso8601String(),
      'updated_at': instance.updatedAt?.toIso8601String(),
      'price_group_id': instance.priceGroupId,
      'last_settlement': instance.lastSettlement,
      'is_default_before': instance.isDefaultBefore,
      'is_stocktaking': instance.isStocktaking,
      'pos_note': instance.posNote,
      'current_bal_due': instance.currentBalDue,
      'sg_id': instance.sgId,
      'is_settlement': instance.isSettlement,
      'due': instance.due,
      'customer_group': instance.customerGroup,
      'opening_balance': instance.openingBalance,
      'opening_balance_paid': instance.openingBalancePaid,
      'max_transaction_date': instance.maxTransactionDate,
      'transaction_date': instance.transactionDate,
      'total_purchase': instance.totalPurchase,
      'purchase_paid': instance.purchasePaid,
      'total_purchase_return': instance.totalPurchaseReturn,
      'purchase_return_paid': instance.purchaseReturnPaid,
      'total_invoice': instance.totalInvoice,
      'invoice_received': instance.invoiceReceived,
      'total_sell_return': instance.totalSellReturn,
      'sell_return_paid': instance.sellReturnPaid,
      'purchase_due': instance.purchaseDue,
      'sell_due': instance.sellDue,
      'purchase_return_due': instance.purchaseReturnDue,
      'sell_return_due': instance.sellReturnDue,
      'location_info': instance.locationInfo?.toJson(),
    };
