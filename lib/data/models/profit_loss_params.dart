class ProfitLossParams {
  final int? locationID;
  final String? startDate;
  final String? endDate;
  final int? userID;

  ProfitLossParams({
    this.locationID,
    this.startDate,
    this.endDate,
    this.userID,
  });

  Map<String, dynamic> toJson() {
    return {
      "location_id": locationID,
      "start_date": startDate,
      "end_date": endDate,
      "user_id": userID,
    };
  }
}
