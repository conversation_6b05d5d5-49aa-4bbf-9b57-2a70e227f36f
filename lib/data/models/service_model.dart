import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:hive/hive.dart';

part 'service_model.g.dart';

@JsonSerializable()
@HiveType(typeId: 58)
class ServiceModel extends Equatable {
  @J<PERSON><PERSON><PERSON>(name: 'id')
  @HiveField(0)
  final int id;

  @JsonKey(name: 'name')
  @HiveField(1)
  final String name;

  @JsonKey(name: 'description')
  @HiveField(2)
  final String? description;

  @JsonKey(name: 'business_id')
  @HiveField(3)
  final int businessId;

  @JsonKey(name: 'location_price_group')
  @HiveField(4)
  final Map<String, dynamic>? locationPriceGroup;

  @<PERSON>son<PERSON><PERSON>(name: 'packing_charge', fromJson: double.parse)
  @HiveField(5)
  final double packingCharge;

  @<PERSON>son<PERSON>ey(
    name: 'packing_charge_type',
    fromJson: _$PackingChargeTypeFromJson,
  )
  @HiveField(6)
  final PackingChargeType packingChargeType;

  @Json<PERSON>ey(name: 'enable_custom_fields')
  @HiveField(7)
  final int enableCustomFields;

  @<PERSON>son<PERSON>ey(name: 'created_at', fromJson: DateTime.parse)
  @HiveField(8)
  final DateTime createdAt;

  @JsonKey(name: 'updated_at', fromJson: DateTime.parse)
  @HiveField(9)
  final DateTime updatedAt;

  const ServiceModel({
    required this.id,
    required this.name,
    required this.description,
    required this.businessId,
    required this.locationPriceGroup,
    required this.packingCharge,
    required this.packingChargeType,
    required this.enableCustomFields,
    required this.createdAt,
    required this.updatedAt,
  });

  factory ServiceModel.fromJson(Map<String, dynamic> json) =>
      _$ServiceModelFromJson(json);

  Map<String, dynamic> toJson() => _$ServiceModelToJson(this);

  @override
  List<Object?> get props => [
        id,
        name,
        description,
        businessId,
        locationPriceGroup,
        packingCharge,
        packingChargeType,
        enableCustomFields,
        createdAt,
        updatedAt,
      ];
}

@HiveType(typeId: 59)
enum PackingChargeType {
  @HiveField(0)
  fixed,
  @HiveField(1)
  weight,
  @HiveField(2)
  percentage,
}

PackingChargeType _$PackingChargeTypeFromJson(String value) {
  switch (value) {
    case 'fixed':
      return PackingChargeType.fixed;
    case 'weight':
      return PackingChargeType.weight;
    case 'percentage':
      return PackingChargeType.percentage;
    case 'percent':
      return PackingChargeType.percentage;
    default:
      throw ArgumentError('Unknown PackingChargeType: $value');
  }
}
