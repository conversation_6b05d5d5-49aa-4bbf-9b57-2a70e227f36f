import 'package:equatable/equatable.dart';
import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';

part 'payment.g.dart';

@JsonSerializable()
@HiveType(typeId: 21)
class Payment extends Equatable {
  @HiveField(0)
  @Json<PERSON><PERSON>(name: 'amount', fromJson: _amountFromJson)
  final double? amount;

  @HiveField(1)
  @JsonKey(name: 'method')
  final String? method;

  @HiveField(2)
  @JsonKey(name: 'account_id')
  final int? accountId;

  @HiveField(3)
  @JsonKey(name: 'card_number')
  final String? cardNumber;

  @HiveField(4)
  @Json<PERSON>ey(name: 'card_holder_name')
  final String? cardHolderName;

  @HiveField(5)
  @JsonKey(name: 'card_transaction_number')
  final String? cardTransactionNumber;

  @HiveField(6)
  @JsonKey(name: 'card_type')
  final String? cardType;

  @HiveField(7)
  @<PERSON>sonKey(name: 'card_month')
  final String? cardMonth;

  @HiveField(8)
  @Json<PERSON><PERSON>(name: 'card_year')
  final String? cardYear;

  @HiveField(9)
  @JsonKey(name: 'card_security')
  final String? cardSecurity;

  @HiveField(10)
  @JsonKey(name: 'transaction_no_1')
  final String? transactionNo1;

  @HiveField(11)
  @JsonKey(name: 'transaction_no_2')
  final String? transactionNo2;

  @HiveField(12)
  @JsonKey(name: 'transaction_no_3')
  final String? transactionNo3;

  @HiveField(13)
  @JsonKey(name: 'note')
  final String? note;

  @HiveField(14)
  @JsonKey(name: 'cheque_number')
  final String? chequeNumber;

  @HiveField(15)
  @JsonKey(name: 'payment_for', includeFromJson: false, includeToJson: false)
  final int? paymentFor;

  @HiveField(16)
  @JsonKey(includeFromJson: false, includeToJson: false)
  final bool offline;

  @HiveField(17)
  @JsonKey(includeFromJson: false, includeToJson: false)
  final DateTime? transactionDate;

  @HiveField(18)
  @JsonKey(includeFromJson: false, includeToJson: false)
  final String? refNo;

  @HiveField(19)
  @JsonKey(name: 'payment_id')
  final int? paymentId;

  const Payment({
    this.amount,
    this.method,
    this.accountId,
    this.cardNumber,
    this.cardHolderName,
    this.cardTransactionNumber,
    this.cardType,
    this.cardMonth,
    this.cardYear,
    this.cardSecurity,
    this.transactionNo1,
    this.transactionNo2,
    this.transactionNo3,
    this.note,
    this.chequeNumber,
    this.paymentFor,
    this.offline = false,
    this.transactionDate,
    this.refNo,
    this.paymentId,
  });

  static double? _amountFromJson(dynamic amount) {
    if (amount is String?) {
      return double.tryParse(amount ?? "");
    } else {
      return amount?.toDouble();
    }
  }

  factory Payment.fromJson(Map<String, dynamic> json) =>
      _$PaymentFromJson(json);

  Map<String, dynamic> toJson() => _$PaymentToJson(this);

  @override
  List<Object?> get props => [
        amount,
        method,
        accountId,
        cardNumber,
        cardHolderName,
        cardTransactionNumber,
        cardType,
        cardMonth,
        cardYear,
        cardSecurity,
        transactionNo1,
        transactionNo2,
        transactionNo3,
        note,
        chequeNumber,
        paymentFor,
        offline,
        transactionDate,
        refNo,
        paymentId,
      ];
}
