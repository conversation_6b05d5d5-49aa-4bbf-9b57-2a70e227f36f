part of "db_manager.dart";

Future<void> dbReset() async {
  await Future.wait([
    productsBox.clear(),
    categoriesBox.clear(),
    unitsBox.clear(),
    taxRatesBox.clear(),
    brandsBox.clear(),
    businessLocationsBox.clear(),
    paymentAccountsBox.clear(),
    contactsBox.clear(),
    sellsBox.clear(),
    purchasesBox.clear(),
    cashRegistersBox.clear(),
    sellsReturnsBox.clear(),
    purchaseReturnsBox.clear(),
    expensesBox.clear(),
    usersBox.clear(),
    serviceStaffBox.clear(),
    commAgentsBox.clear(),
    tablesBox.clear(),
    shippingCompaniesBox.clear(),
    sellingPriceGroupsBox.clear(),
    expenseCategoriesBox.clear(),
    followUpBox.clear(),
    paymentsReportBox.clear(),
    serviceModelBox.clear(),
    customerGroupBox.clear(),
    businessDetailsBox.clear(),
  ]);
  loginData.resetValues();
  loginData.isContactsReady = false;
  loginData.isProductsReady = false;
  loginData.lastUpdateTime = null;
  loginData.save();
  if (credentialsBox.get(baseUrl) == null) {
    credentialsBox.put(baseUrl, baseURLs[0]);
  }
}
