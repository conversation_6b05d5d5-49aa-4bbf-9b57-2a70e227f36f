part of "db_manager.dart";

LoginData loginData = credentialsBox.get(
  loginDataKey,
  defaultValue: LoginData.empty(),
);

DateTime cashRegisteredTime() =>
    loginData.cashRegistered ? loginData.cashRegisteredTime! : DateTime(2500);

bool isDataInSync() => loginData.inSync;

bool isWithinLast10Minutes() {
  DateTime currentTime = DateTime.now();
  DateTime? lastUpdateTime = loginData.lastUpdateTime;
  if (lastUpdateTime != null) {
    int differenceInMinutes = currentTime.difference(lastUpdateTime).inMinutes;
    return differenceInMinutes <= 10;
  } else {
    return false;
  }
}


bool isProductsAndContactsReady() =>
    loginData.isProductsReady && loginData.isContactsReady;

/// Permission: View all suppliers
// done
bool canViewSupplier() =>
    loginData.isAdmin || loginData.permissions.supplierView;

/// Permission: View own supplier
// done
bool canOnlyViewOwnSupplier() => loginData.permissions.supplierViewOwn;

/// Permission: Create supplier
//done
bool canCreateSupplier() =>
    loginData.isAdmin || loginData.permissions.supplierCreate;

/// Permission: Update supplier
// done
bool canUpdateSupplier() =>
    loginData.isAdmin || loginData.permissions.supplierUpdate;

/// Permission: Delete supplier
// not used
bool canDeleteSupplier() =>
    loginData.isAdmin || loginData.permissions.supplierDelete;

/// Permission: View all customers
// done
bool canViewCustomer() =>
    loginData.isAdmin || loginData.permissions.customerView;

/// Permission: View customer group
// not used
bool canViewCustomerGroup() =>
    loginData.isAdmin || loginData.permissions.customerGroupView;

/// Permission: Show customer
// done
bool canShowCustomer() =>
    loginData.isAdmin || loginData.permissions.customerShow;

/// Permission: View own customer
// done
bool canOnlyViewOwnCustomer() => loginData.permissions.customerViewOwn;

/// Permission: Create customer
// done
bool canCreateCustomer() =>
    loginData.isAdmin || loginData.permissions.customerCreate;

/// Permission: Update customer
// done
bool canUpdateCustomer() =>
    loginData.isAdmin || loginData.permissions.customerUpdate;

/// Permission: Delete customer
// not used
bool canDeleteCustomer() =>
    loginData.isAdmin || loginData.permissions.customerDelete;

/// Permission: Give customer cashback
bool canGiveCustomerCashback() =>
    loginData.isAdmin || loginData.permissions.customerCashback;

/// Permission: View product
// done
bool canViewProduct() => loginData.isAdmin || loginData.permissions.productView;

/// Permission: Create product
// done
bool canCreateProduct() =>
    loginData.isAdmin || loginData.permissions.productCreate;

/// Permission: Update all product prices
// not used
bool canUpdateAllProductPrices() =>
    loginData.isAdmin || loginData.permissions.productUpdateAllPrices;

/// Permission: Update product
// not used
bool canUpdateProduct() =>
    loginData.isAdmin || loginData.permissions.productUpdate;

/// Permission: Delete product
// not used
bool canDeleteProduct() =>
    loginData.isAdmin || loginData.permissions.productDelete;

/// Permission: Add opening stock for product
// done
bool canAddOpeningStockForProduct() =>
    loginData.isAdmin || loginData.permissions.productOpeningStock;

/// Permission: Compare stock levels
bool canCompareStockLevels() =>
    loginData.isAdmin || loginData.permissions.stockCompares;

/// Permission: View purchase price
bool canViewPurchasePrice() =>
    loginData.isAdmin || loginData.permissions.viewPurchasePrice;

/// Permission: View purchase
// done
bool canViewPurchase() =>
    loginData.isAdmin || loginData.permissions.purchaseView;

/// Permission: Create purchase
// done
bool canCreatePurchase() =>
    loginData.isAdmin || loginData.permissions.purchaseCreate;

/// Permission: Update purchase
// done
bool canUpdatePurchase() =>
    loginData.isAdmin || loginData.permissions.purchaseUpdate;

/// Permission: Delete purchase
// done
bool canDeletePurchase() =>
    loginData.isAdmin || loginData.permissions.purchaseDelete;

/// Permission: Create purchase payment
// done
bool canCreatePurchasePayment() =>
    loginData.isAdmin || loginData.permissions.purchasePaymentCreate;

/// Permission: Edit purchase payment
// not used
bool canEditPurchasePayment() =>
    loginData.isAdmin || loginData.permissions.purchasePaymentEdit;

/// Permission: Delete purchase payment
// not used
bool canDeletePurchasePayment() =>
    loginData.isAdmin || loginData.permissions.purchasePaymentDelete;

/// Permission: Update purchase status
bool canUpdatePurchaseStatus() =>
    loginData.isAdmin || loginData.permissions.purchaseUpdateStatus;

/// Permission: View own purchase
// done
bool canOnlyViewOwnPurchase() => loginData.permissions.viewOwnPurchase;

/// Permission: View purchase returns
// done
bool canViewPurchaseReturns() =>
    loginData.isAdmin || loginData.permissions.purchaseReturnView;

/// Permission: Create purchase return
// done
bool canCreatePurchaseReturn() =>
    loginData.isAdmin || loginData.permissions.purchaseReturnCreate;

/// Permission: Sell on credit
// done
bool canSellOnCredit() =>
    loginData.isAdmin || loginData.permissions.salesSellAgel;

/// Permission: Accept card payments
bool canAcceptCardPayments() =>
    loginData.isAdmin || loginData.permissions.salesPayCard;

/// Permission: Accept multiple payment methods
bool canAcceptMultiplePaymentMethods() =>
    loginData.isAdmin || loginData.permissions.salesMultiPayWays;

/// Permission: Sell in cash
bool canSellInCash() =>
    loginData.isAdmin || loginData.permissions.salesSellInCash;

/// Permission: Sell below purchase price
bool canSellBelowPurchasePrice() =>
    loginData.isAdmin || loginData.permissions.salesLessThanPurchasePrice;

/// Permission: View sales
// done
bool canViewSales() => loginData.isAdmin || loginData.permissions.salesShow;

/// Permission: List quotations
bool canListQuotations() =>
    loginData.isAdmin || loginData.permissions.listQuotations;

/// Permission: Offer price during sales
bool canOfferPriceDuringSales() =>
    loginData.isAdmin || loginData.permissions.salesPriceOffer;

/// Permission: Show current stock in POS
bool canShowCurrentStockInPos() =>
    loginData.isAdmin || loginData.permissions.salesShowCurrentStockInPos;

/// Permission: Show combo details in POS
// done
bool canShowComboDetailsInPos() =>
    loginData.isAdmin || loginData.permissions.salesShowComboDetails;

/// Permission: Show purchase price in POS
bool canShowPurchasePriceInPos() =>
    loginData.isAdmin || loginData.permissions.salesShowPurchasePriceInPos;

/// Permission: Show today's total sales
bool canShowTodaySellsTotal() =>
    loginData.isAdmin || loginData.permissions.todaySellsTotalShow;

/// Permission: Create sell
// done
bool canCreateSell() => loginData.isAdmin || loginData.permissions.sellCreate;

/// Permission: Update sell
// done
bool canUpdateSell() => loginData.isAdmin || loginData.permissions.sellUpdate;

/// Permission: Delete sell
// done
bool canDeleteSell() => loginData.isAdmin || loginData.permissions.sellDelete;

/// Permission: View own sell only
bool canViewOwnSellOnly() => loginData.permissions.viewOwnSellOnly;

/// Permission: Create sell payment
// done
bool canCreateSellPayment() =>
    loginData.isAdmin || loginData.permissions.sellPaymentCreate;

/// Permission: Edit sell payment
// not used
bool canEditSellPayment() =>
    loginData.isAdmin || loginData.permissions.sellPaymentEdit;

/// Permission: Delete sell payment
// not used
bool canDeleteSellPayment() =>
    loginData.isAdmin || loginData.permissions.sellPaymentDelete;

/// Permission: Edit product price from POS screen
// done
bool canEditProductPriceFromPosScreen() =>
    loginData.isAdmin || loginData.permissions.editProductPriceFromPosScreen;

/// Permission: Edit product discount from POS screen
// done
bool canEditProductDiscountFromPosScreen() =>
    loginData.isAdmin || loginData.permissions.editProductDiscountFromPosScreen;

/// Permission: Access discounts
bool canAccessDiscounts() =>
    loginData.isAdmin || loginData.permissions.discountAccess;

/// Permission: Access shipping
// done
bool canAccessShipping() =>
    loginData.isAdmin || loginData.permissions.accessShipping;

/// Permission: Access sell return
// done
bool canAccessSellReturn() =>
    loginData.isAdmin || loginData.permissions.accessSellReturn;

/// Permission: View customer balance due in POS
bool canViewCustomerBalanceDueInPos() =>
    loginData.isAdmin || loginData.permissions.customerBalanceDueInPos;

/// Permission: Access all expenses
// not used
bool canAccessAllExpenses() =>
    loginData.isAdmin || loginData.permissions.allExpenseAccess;

/// Permission: View own expense
// done
bool canViewOwnExpense() => loginData.permissions.viewOwnExpense;

/// Permission: View all expenses
// done
bool canViewAllExpenses() =>
    loginData.isAdmin || loginData.permissions.expensesView;

/// Permission: Manage expense categories
bool canManageExpenseCategories() =>
    loginData.isAdmin || loginData.permissions.expenseCategories;

/// Permission: Create expense
// done
bool canCreateExpense() =>
    loginData.isAdmin || loginData.permissions.expenseCreate;

/// Permission: Edit expense
// done
bool canEditExpense() => loginData.isAdmin || loginData.permissions.expenseEdit;

/// Permission: Delete expense
// not used
bool canDeleteExpense() =>
    loginData.isAdmin || loginData.permissions.expenseDelete;

/// Permission: View cash register details
bool canViewCashRegisterDetails() =>
    loginData.isAdmin || loginData.permissions.viewCashRegister;

/// Permission: Close cash register
bool canCloseCashRegister() =>
    loginData.isAdmin || loginData.permissions.closeCashRegister;

/// Permission: Access default selling price
// not used!!!
bool canAccessDefaultSellingPrice() =>
    loginData.isAdmin || loginData.permissions.accessDefaultSellingPrice;

/// Permission: can use selling price for specific id
// done
bool canUseSellingPrice(id) =>
    loginData.isAdmin ||
    loginData.permissions.hasSellingPriceGroupPermission(id);

/// Permission: View CRM details
// done
bool canViewCrmDetails() => loginData.isAdmin || loginData.permissions.crmShow;

/// Permission: Access all CRM schedules
bool canAccessAllCrmSchedules() =>
    loginData.isAdmin || loginData.permissions.crmAccessAllSchedule;

/// Permission: Access own CRM schedule
bool canAccessOwnCrmSchedule() => loginData.permissions.crmAccessOwnSchedule;

/// Permission: CRUD all attendance records
bool canCrudAllAttendance() =>
    loginData.isAdmin || loginData.permissions.essentialsCrudAllAttendance;

/// Permission: can Access Location User Places Page
bool canAccessLocationUserPlaces() =>
    loginData.isAdmin || loginData.permissions.locationUserPlaces;

/// Permission: can Access Location Walking Line
bool canAccessLocationWalkingLine() =>
    loginData.isAdmin || loginData.permissions.locationWalkingLine;

/// Permission: can Access Location Search
bool canAccessLocationSearch() =>
    loginData.isAdmin || loginData.permissions.locationSearch;

/// Permission: can Access Location Setting
bool canAccessLocationSetting() =>
    loginData.isAdmin || loginData.permissions.locationSetting;

/// Permission: can Access Purchase Payment report
bool canViewPurchasePaymentReport() =>
    loginData.isAdmin || loginData.permissions.purchasePaymentReportView;

/// Permission: can Access Sell Payment report
bool canViewSellPaymentReport() =>
    loginData.isAdmin || loginData.permissions.sellPaymentReportView;

/// Permission: can Access Customers Payment report
bool canViewCustomersPaymentReport() =>
    loginData.isAdmin || loginData.permissions.customersPaymentsReportView;

/// Permission: can Access Suppliers Payment report
bool canViewSuppliersPaymentReport() =>
    loginData.isAdmin || loginData.permissions.suppliersPaymentsReportView;

/// Permission: can Access Profit Loss report
bool canViewProfitLossReport() =>
    loginData.isAdmin || loginData.permissions.profitLossReportView;

bool canAccessLocationSection() =>
    canAccessLocationWalkingLine() ||
    canAccessLocationUserPlaces() ||
    canAccessLocationSearch() ||
    canAccessLocationSetting();

bool isWithinTransactionEditDays(DateTime transactionDate) {
  if (loginData.transactionEditDays != null) {
    final currentDate = DateTime.now();
    final differenceInDays = currentDate.difference(transactionDate).inDays;

    // Check if the difference is within the allowed edit days
    if (differenceInDays <= loginData.transactionEditDays!) {
      return true;
    } else {
      return false;
    }
  }

  // If transactionEditDays is null, assume it's always within the edit period
  return true;
}

