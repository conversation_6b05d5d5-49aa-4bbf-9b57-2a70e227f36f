part of "db_manager.dart";

Future<void> initHive() async {
  await Hive.initFlutter((await getApplicationSupportDirectory()).path);

  Hive.registerAdapter(BrandAdapter());
  Hive.registerAdapter(BusinessLocationAdapter());
  Hive.registerAdapter(ProductCategoryAdapter());
  Hive.registerAdapter(PaymentMethodAdapter());
  Hive.registerAdapter(PaymentAccountAdapter());
  Hive.registerAdapter(ProductAdapter());
  Hive.registerAdapter(ProductLocationAdapter());
  Hive.registerAdapter(ProductVariationAdapter());
  Hive.registerAdapter(VariationAdapter());
  Hive.registerAdapter(VariationLocationDetailsAdapter());
  Hive.registerAdapter(MediaAdapter());
  Hive.registerAdapter(TaxRateAdapter());
  Hive.registerAdapter(UnitAdapter());
  Hive.registerAdapter(ContactAdapter());
  Hive.registerAdapter(ExpenseAdapter());
  Hive.registerAdapter(ProfitLossReportAdapter());
  Hive.registerAdapter(ModuleDataAdapter());
  Hive.registerAdapter(BaseUrlAdapter());
  Hive.registerAdapter(SellingPriceGroupAdapter());
  Hive.registerAdapter(SellAdapter());
  Hive.registerAdapter(PaymentAdapter());
  Hive.registerAdapter(SellToAPIAdapter());
  Hive.registerAdapter(ProductToAPIAdapter());
  Hive.registerAdapter(ExpenseToAPIAdapter());
  Hive.registerAdapter(ContactToAPIAdapter());
  Hive.registerAdapter(LoginDataAdapter());
  Hive.registerAdapter(ClockAdapter());
  Hive.registerAdapter(SellLineAdapter());
  Hive.registerAdapter(PaymentLineAdapter());
  Hive.registerAdapter(SellReturnToAPIAdapter());
  Hive.registerAdapter(ReturnProductToAPIAdapter());
  Hive.registerAdapter(SellReturnAdapter());
  Hive.registerAdapter(PurchaseToAPIAdapter());
  Hive.registerAdapter(PurchaseAdapter());
  Hive.registerAdapter(PurchaseLineAdapter());
  Hive.registerAdapter(FollowUpToAPIAdapter());
  Hive.registerAdapter(FollowUpAdapter());
  Hive.registerAdapter(CustomerAdapter());
  Hive.registerAdapter(PurchaseReturnToAPIAdapter());
  Hive.registerAdapter(ProductPurchaseReturnAdapter());
  Hive.registerAdapter(PurchaseReturnAdapter());
  Hive.registerAdapter(NewProductToAPIAdapter());
  Hive.registerAdapter(PurchaseProductToAPIAdapter());
  Hive.registerAdapter(CashRegisterToAPIAdapter());
  Hive.registerAdapter(UserModelAdapter());
  Hive.registerAdapter(LocationInfoAdapter());
  Hive.registerAdapter(CashRegisterAdapter());
  Hive.registerAdapter(ProductPriceGroupAdapter());
  Hive.registerAdapter(UserPermissionsAdapter());
  Hive.registerAdapter(ComboVariationAdapter());
  Hive.registerAdapter(PaymentStatusAdapter());
  Hive.registerAdapter(ShipmentStatusAdapter());
  Hive.registerAdapter(ExpenseCategoryAdapter());
  Hive.registerAdapter(ContactPaymentModelAdapter());
  Hive.registerAdapter(CustomerGroupAdapter());
  Hive.registerAdapter(ServiceModelAdapter());
  Hive.registerAdapter(PackingChargeTypeAdapter());
  Hive.registerAdapter(AppCurrencyAdapter());
  Hive.registerAdapter(BusinessTableAdapter());
  Hive.registerAdapter(ShippingCompanyAdapter());
  Hive.registerAdapter(BusinessSettingsAdapter());
  //Last HiveId is 63

  await Hive.openBox(settingsBox);
  await Hive.openBox(userDetailsBox);
  await Hive.openBox(businessDetailsBoxKey);
  await Hive.openBox(userAndPasswordBox);
  await Hive.openBox<Product>("productsBox");
  await Hive.openBox<UserModel>("usersBox");
  await Hive.openBox<UserModel>("commAgentsBox");
  await Hive.openBox<UserModel>("serviceStaffBox");
  await Hive.openBox<ShippingCompany>("shippingCompaniesBox");
  await Hive.openBox<BusinessTable>("tablesBox");
  await Hive.openBox<ProductCategory>("categories");
  await Hive.openBox<Unit>("unitsBox");
  await Hive.openBox<TaxRate>("taxRatesBox");
  await Hive.openBox<Brand>("brandsBox");
  await Hive.openBox<BusinessLocation>("businessLocationsBox");
  await Hive.openBox<PaymentAccount>("paymentAccountsBox");
  await Hive.openBox<Contact>("contactsBox");
  await Hive.openBox<Sell>("sellsBox");
  await Hive.openBox<Purchase>("purchasesBox");
  await Hive.openBox<PurchaseReturn>("purchaseReturnsBox");
  await Hive.openBox<SellReturn>("sellsReturnsBox");
  await Hive.openBox<Expense>("expensesBox");
  await Hive.openBox<ProfitLossReport>("profitLossReportBox");
  await Hive.openBox<SellingPriceGroup>("sellingPriceGroupsBox");
  await Hive.openBox<FollowUp>("followUpBox");
  await Hive.openBox<CashRegister>("cashRegistersBox");
  await Hive.openBox<ExpenseCategory>("expenseCategoriesBox");
  await Hive.openBox<ContactPaymentModel>("paymentsReportBox");
  await Hive.openBox<ServiceModel>("serviceModelBox");
  await Hive.openBox<CustomerGroup>("customerGroupBox");
  await Hive.openBox<int>(locationRange);

  credentialsBox = Hive.box(userDetailsBox);
  if (credentialsBox.get(baseUrl) == null) {
    credentialsBox.put(baseUrl, baseURLs[0]);
  }

  _apiRepository = ApiRepository.get();

  // RESETTING THE APP DB IF NEW VERSION OR FRESH INSTALL
  PackageInfo packageInfo = await PackageInfo.fromPlatform();
  String version = packageInfo.version;
  var storedVersion = businessDetailsBox.get('storedVersion');
  if ((storedVersion == null || storedVersion != version) &&
      loginData.accessToken == null) {
    await dbReset();
    businessDetailsBox.put('storedVersion', version);
    debugPrint("APP DB RESET AS IT'S NEW VERSION");
  }
  // RESETTING THE APP DB IF NEW VERSION OR FRESH INSTALL
}
