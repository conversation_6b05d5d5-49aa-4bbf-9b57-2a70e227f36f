import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/foundation.dart';
// import 'package:we2up/data/db/firebase_constants.dart';

import '../../firebase_options.dart';

Future<void> initFirebase() async {
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  FlutterError.onError = (errorDetails) {
    FirebaseCrashlytics.instance.recordFlutterFatalError(errorDetails);
  };
  // Pass all uncaught asynchronous errors that aren't handled by the Flutter framework to Crashlytics
  PlatformDispatcher.instance.onError = (error, stack) {
    FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
    return true;
  };
}

// Future<void> initDesktopFirebase() async {
//   await Firebase.initializeApp(
//     options: const FirebaseOptions(
//       apiKey: webApiKey,
//       appId: appId,
//       projectId: projectId,
//       messagingSenderId: senderId,
//     ),
//   );
// }
