import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:go_router/go_router.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'package:we2up/bloc/products/products_cubit.dart';
import 'package:we2up/data/models/cash_register_to_api.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/data/models/clock.dart';
import 'package:we2up/data/models/contact.dart';
import 'package:we2up/data/models/contact_payment_model.dart';
import 'package:we2up/data/models/contact_to_api.dart';
import 'package:we2up/data/models/customer_group.dart';
import 'package:we2up/data/models/expense.dart';
import 'package:we2up/data/models/expense_category.dart';
import 'package:we2up/data/models/expense_to_api.dart';
import 'package:we2up/data/models/follow_up_to_api.dart';
import 'package:we2up/data/models/login_data.dart';
import 'package:we2up/data/models/new_product_to_api.dart';
import 'package:we2up/data/models/payment.dart';
import 'package:we2up/data/models/payment_status.dart';
import 'package:we2up/data/models/profit_loss_report.dart';
import 'package:we2up/data/models/purchase.dart';
import 'package:we2up/data/models/purchase_return.dart';
import 'package:we2up/data/models/purchase_return_to_api.dart';
import 'package:we2up/data/models/purchase_to_api.dart';
import 'package:we2up/data/models/sell.dart';
import 'package:we2up/data/models/sell_return.dart';
import 'package:we2up/data/models/sell_return_to_api.dart';
import 'package:we2up/data/models/sell_to_api.dart';
import 'package:we2up/data/models/selling_price_group.dart';
import 'package:we2up/data/models/service_model.dart';
import 'package:we2up/data/models/shipment_status.dart';
import 'package:we2up/data/models/user_model.dart';
import 'package:we2up/data/repository/api_repo.dart';
import 'package:we2up/utils/route_constants.dart';

import '../../bloc/landing/landing_cubit.dart';
import '../models/app_currency.dart';
import '../models/base_url.dart';
import '../models/business_location.dart';
import '../models/business_settings.dart';
import '../models/cash_register.dart';
import '../models/follow_up.dart';
import '../models/location_info.dart';
import '../models/payment_account.dart';
import '../models/product.dart';
import '../models/shipping_company.dart';
import '../models/table.dart';
import '../models/user_permissions.dart';
import '../../utils/we2up_constants.dart';

part 'db_init.dart';

part 'permission_functions.dart';

part 'db_reset.dart';

late final ApiRepository _apiRepository;
late Box<dynamic> credentialsBox;
const String settingsBox = "settings";
const String userDetailsBox = "userDetailsBox";
const String businessDetailsBoxKey = "businessDetails";
const String baseUrl = "baseUrl";

Box<Product> productsBox = Hive.box("productsBox");
Box<dynamic> businessDetailsBox = Hive.box(businessDetailsBoxKey);
Box<int> locationRangeBox = Hive.box(locationRange);
Box<ProductCategory> categoriesBox = Hive.box("categories");
Box<UserModel> usersBox = Hive.box("usersBox");
Box<UserModel> commAgentsBox = Hive.box("commAgentsBox");
Box<UserModel> serviceStaffBox = Hive.box("serviceStaffBox");
Box<ShippingCompany> shippingCompaniesBox = Hive.box("shippingCompaniesBox");
Box<BusinessTable> tablesBox = Hive.box("tablesBox");
Box<Unit> unitsBox = Hive.box("unitsBox");
Box<TaxRate> taxRatesBox = Hive.box("taxRatesBox");
Box<Brand> brandsBox = Hive.box("brandsBox");
Box<BusinessLocation> businessLocationsBox = Hive.box("businessLocationsBox");
Box<PaymentAccount> paymentAccountsBox = Hive.box("paymentAccountsBox");
Box<Contact> contactsBox = Hive.box("contactsBox");
Box<Sell> sellsBox = Hive.box("sellsBox");
Box<Purchase> purchasesBox = Hive.box("purchasesBox");
Box<CashRegister> cashRegistersBox = Hive.box("cashRegistersBox");
Box<SellReturn> sellsReturnsBox = Hive.box("sellsReturnsBox");
Box<PurchaseReturn> purchaseReturnsBox = Hive.box("purchaseReturnsBox");
Box<Expense> expensesBox = Hive.box("expensesBox");
Box<FollowUp> followUpBox = Hive.box("followUpBox");
Box<ServiceModel> serviceModelBox = Hive.box("serviceModelBox");
Box<CustomerGroup> customerGroupBox = Hive.box("customerGroupBox");
Box<ExpenseCategory> expenseCategoriesBox = Hive.box("expenseCategoriesBox");
Box<ContactPaymentModel> paymentsReportBox = Hive.box("paymentsReportBox");
Box<SellingPriceGroup> sellingPriceGroupsBox =
    Hive.box("sellingPriceGroupsBox");

// don't reset that box
Box<dynamic> userAndPassBox = Hive.box(userAndPasswordBox);

PaymentAccount? defaultPaymentAccount(BusinessLocation bLocation) {
  PaymentAccount? account;
  final defaultAccount = paymentAccountsBox.get(loginData.defaultAccount);
  if (defaultAccount != null) {
    account = defaultAccount;
  } else {
    if (loginData.isAdmin) {
      account = paymentAccountsBox.get(
        int.tryParse(bLocation.paymentMethods.first.accountId ?? ""),
      );
    }
  }
  return account;
}

bool isDark() => Hive.box(settingsBox).get("isDark", defaultValue: false);

int currentThemeIndex() {
  return Hive.box(settingsBox).get("currentThemeIndex", defaultValue: 0);
}

int? primaryLight() => Hive.box(settingsBox).get("primaryLight");

int? backgroundLight() => Hive.box(settingsBox).get("backgroundLight");

int? primaryDark() => Hive.box(settingsBox).get("primaryDark");

int? backgroundDark() => Hive.box(settingsBox).get("backgroundDark");

String language() => Hive.box(settingsBox).get("language", defaultValue: "ar");

Future<void> fetchProductsAndStoreInHive({
  bool refresh = false,
  StreamController<bool>? controller,
}) async {
  try {
    if (!refresh) {
      loginData.isProductsReady = false;
      loginData.save();
    }
    await for (List<Product> products in _apiRepository.getProductsStream(
        lastUpdated: refresh ? loginData.lastUpdateTime : null)) {
      if (products.isNotEmpty) {
        debugPrint('Products received: ${products.length}');
        bool newDataSaved = false;

        for (var product in products) {
          final existingProduct = productsBox.get(product.id);
          if (existingProduct == null || existingProduct != product) {
            productsBox.put(product.id, product);
            newDataSaved = true;
          }
        }

        if (controller != null && newDataSaved) {
          controller.add(true);
        }
      }
    }
  } catch (e, stack) {
    logAppError('Error fetching products and storing in Hive', e, stack);
  }
}

Future<void> fetchCategoriesAndStoreInHive() async {
  try {
    final categories = await _apiRepository.getCategories();
    if (categories != null) {
      debugPrint('Categories received: ${categories.length}');
      final receivedCategoryIds = categories.map((c) => c.id).toSet();
      final storedCategoryIds = categoriesBox.keys.toSet();
      final categoryIdsToDelete =
          storedCategoryIds.difference(receivedCategoryIds);
      for (var categoryId in categoryIdsToDelete) {
        categoriesBox.delete(categoryId);
      }
      for (var category in categories) {
        final existingCategory = categoriesBox.get(category.id);
        if (existingCategory == null || existingCategory != category) {
          categoriesBox.put(category.id, category);
        }
      }
    }
  } catch (e, stack) {
    logAppError('Error fetching categories and storing in Hive', e, stack);
  }
}

Future<void> fetchUsersAndStoreInHive({bool serviceStaff = false}) async {
  try {
    final usedBox = serviceStaff ? serviceStaffBox : usersBox;
    final users = await _apiRepository.getUsers(serviceStaff: serviceStaff);
    if (users != null) {
      debugPrint(
        '${serviceStaff ? 'Service Staff' : 'Users'} '
        'received: ${users.length}',
      );
      final receivedUserIds = users.map((u) => u.id).toSet();
      final storedUserIds = usedBox.keys.toSet();
      final userIdsToDelete = storedUserIds.difference(receivedUserIds);

      for (var userId in userIdsToDelete) {
        usedBox.delete(userId);
      }

      for (var user in users) {
        final existingUser = usedBox.get(user.id);
        if (existingUser == null || existingUser != user) {
          usedBox.put(user.id, user);
        }
      }
    }
  } catch (e, stack) {
    logAppError(
      'Error fetching ${serviceStaff ? 'Service Staff' : 'Users'} '
      'and storing in Hive',
      e,
      stack,
    );
  }
}

Future<void> fetchCommAgentsAndStoreInHive() async {
  try {
    final users = await _apiRepository.getCommAgents();
    if (users != null) {
      debugPrint('Comm Agents received: ${users.length}');
      final receivedUserIds = users.map((u) => u.id).toSet();
      final storedUserIds = commAgentsBox.keys.toSet();
      final userIdsToDelete = storedUserIds.difference(receivedUserIds);

      for (var userId in userIdsToDelete) {
        commAgentsBox.delete(userId);
      }

      for (var user in users) {
        final existingUser = commAgentsBox.get(user.id);
        if (existingUser == null || existingUser != user) {
          commAgentsBox.put(user.id, user);
        }
      }
    }
  } catch (e, stack) {
    logAppError('Error fetching Comm Agents and storing in Hive', e, stack);
  }
}

Future<void> fetchShippingCompaniesAndStoreInHive() async {
  try {
    final companies = await _apiRepository.getShippingCompanies();
    if (companies != null) {
      debugPrint('Shipping Companies received: ${companies.length}');
      final receivedCompaniesIds = companies.map((u) => u.id).toSet();
      final storedCompaniesIds = shippingCompaniesBox.keys.toSet();
      final companiesIdsToDelete =
          storedCompaniesIds.difference(receivedCompaniesIds);

      for (var companyId in companiesIdsToDelete) {
        shippingCompaniesBox.delete(companyId);
      }

      for (var company in companies) {
        final existingCompany = shippingCompaniesBox.get(company.id);
        if (existingCompany == null || existingCompany != company) {
          shippingCompaniesBox.put(company.id, company);
        }
      }
    }
  } catch (e, stack) {
    logAppError(
        'Error fetching Shipping Companies and storing in Hive', e, stack);
  }
}

Future<void> fetchTablesAndStoreInHive() async {
  try {
    final tables = await _apiRepository.getTables();
    if (tables != null) {
      debugPrint('Tables received: ${tables.length}');
      final receivedTableIds = tables.map((u) => u.id).toSet();
      final storedTablesIds = tablesBox.keys.toSet();
      final tableIdsToDelete = storedTablesIds.difference(receivedTableIds);

      for (var tableId in tableIdsToDelete) {
        tablesBox.delete(tableId);
      }

      for (var table in tables) {
        final existingTable = tablesBox.get(table.id);
        if (existingTable == null || existingTable != table) {
          tablesBox.put(table.id, table);
        }
      }
    }
  } catch (e, stack) {
    logAppError('Error fetching Tables and storing in Hive', e, stack);
  }
}

Future<void> fetchUnitsAndStoreInHive() async {
  try {
    final units = await _apiRepository.getUnits();
    if (units != null) {
      debugPrint('Units received: ${units.length}');
      final receivedUnitIds = units.map((u) => u.id).toSet();
      final storedUnitIds = unitsBox.keys.toSet();
      final unitIdsToDelete = storedUnitIds.difference(receivedUnitIds);
      for (var unitId in unitIdsToDelete) {
        unitsBox.delete(unitId);
      }
      for (var unit in units) {
        final existingUnit = unitsBox.get(unit.id);
        if (existingUnit == null || existingUnit != unit) {
          unitsBox.put(unit.id, unit);
        }
      }
    }
  } catch (e, stack) {
    logAppError('Error fetching units and storing in Hive', e, stack);
  }
}

Future<void> fetchTaxRatesAndStoreInHive() async {
  try {
    final taxRates = await _apiRepository.getTaxRates();
    if (taxRates != null) {
      debugPrint('Tax Rates received: ${taxRates.length}');
      final receivedTaxRateIds = taxRates.map((tr) => tr.id).toSet();
      final storedTaxRateIds = taxRatesBox.keys.toSet();
      final taxRateIdsToDelete =
          storedTaxRateIds.difference(receivedTaxRateIds);
      for (var taxRateId in taxRateIdsToDelete) {
        taxRatesBox.delete(taxRateId);
      }
      for (var taxRate in taxRates) {
        final existingTaxRate = taxRatesBox.get(taxRate.id);
        if (existingTaxRate == null || existingTaxRate != taxRate) {
          taxRatesBox.put(taxRate.id, taxRate);
        }
      }
    }
  } catch (e, stack) {
    logAppError('Error fetching tax rates and storing in Hive', e, stack);
  }
}

Future<void> fetchBrandsAndStoreInHive() async {
  try {
    final brands = await _apiRepository.getBrands();
    if (brands != null) {
      debugPrint('Brands received: ${brands.length}');
      final receivedBrandIds = brands.map((b) => b.id).toSet();
      final storedBrandIds = brandsBox.keys.toSet();
      final brandIdsToDelete = storedBrandIds.difference(receivedBrandIds);
      for (var brandId in brandIdsToDelete) {
        brandsBox.delete(brandId);
      }
      for (var brand in brands) {
        final existingBrand = brandsBox.get(brand.id);
        if (existingBrand == null || existingBrand != brand) {
          brandsBox.put(brand.id, brand);
        }
      }
    }
  } catch (e, stack) {
    logAppError('Error fetching brands and storing in Hive', e, stack);
  }
}

Future<void> fetchBusinessLocationsAndStoreInHive() async {
  try {
    var businessLocations = await _apiRepository.getBusinessLocations();
    if (businessLocations != null && businessLocations.isNotEmpty) {
      debugPrint('Business Locations received: ${businessLocations.length}');
      if (loginData.permittedLocations.isNotEmpty) {
        // Filter business locations based on permitted locations
        businessLocations.removeWhere((bLocation) =>
            !loginData.permittedLocations.contains(bLocation.id));
      }
      // Remove existing locations not present in the updated list
      for (var key in businessLocationsBox.keys.toList()) {
        if (!businessLocations.any((bLocation) => bLocation.id == key)) {
          businessLocationsBox.delete(key);
        }
      }
      // Store or update business locations in Hive
      for (var businessLocation in businessLocations) {
        final existingLocation = businessLocationsBox.get(businessLocation.id);
        if (existingLocation == null || existingLocation != businessLocation) {
          businessLocationsBox.put(businessLocation.id, businessLocation);
        }
      }
    }
  } catch (e, stack) {
    logAppError(
        'Error fetching business locations and storing in Hive', e, stack);
  }
}

Future<void> fetchPaymentAccountsAndStoreInHive() async {
  try {
    final paymentAccounts = await _apiRepository.getPaymentAccounts();
    if (paymentAccounts != null) {
      debugPrint('Payment Accounts received: ${paymentAccounts.length}');
      final receivedAccountIds = paymentAccounts.map((pa) => pa.id).toSet();
      final storedAccountIds = paymentAccountsBox.keys.toSet();
      final accountIdsToDelete =
          storedAccountIds.difference(receivedAccountIds);
      for (var accountId in accountIdsToDelete) {
        paymentAccountsBox.delete(accountId);
      }
      for (var pAccount in paymentAccounts) {
        final currentAccount = paymentAccountsBox.get(pAccount.id);
        if (currentAccount == null || currentAccount != pAccount) {
          paymentAccountsBox.put(pAccount.id, pAccount);
        }
      }
    }
  } catch (e, stack) {
    logAppError(
        'Error fetching payment accounts and storing in Hive', e, stack);
  }
}

Future<void> fetchContactsAndStoreInHive({
  bool refresh = false,
  StreamController<bool>? controller,
}) async {
  try {
    if (!refresh) {
      loginData.isContactsReady = false;
      loginData.save();
    }

    bool newDataSaved = false;

    await for (List<Contact>? contacts in _apiRepository.getContactsStream(
        lastUpdated: refresh ? loginData.lastUpdateTime : null)) {
      if (contacts != null && contacts.isNotEmpty) {
        debugPrint('Contacts received: ${contacts.length}');

        for (var contact in contacts) {
          final existingContact = contactsBox.get(contact.id);
          if (existingContact == null || existingContact != contact) {
            contactsBox.put(contact.id, contact);
            newDataSaved = true;
          }
        }

        if (controller != null && newDataSaved) {
          controller.add(true);
        }
      }
    }

    if (controller != null) {
      controller.add(true);
    }
  } catch (e, stack) {
    logAppError('Error fetching contacts and storing in Hive', e, stack);
  }
}

Future<void> fetchCustomerGroupsAndStoreInHive() async {
  try {
    final customerGroups = await _apiRepository.getCustomerGroups();
    if (customerGroups != null) {
      debugPrint('Customer Groups received: ${customerGroups.length}');
      final receivedGroupIds = customerGroups.map((cg) => cg.id).toSet();
      final storedGroupIds = customerGroupBox.keys.toSet();
      final groupIdsToDelete = storedGroupIds.difference(receivedGroupIds);
      for (var groupId in groupIdsToDelete) {
        customerGroupBox.delete(groupId);
      }
      for (var group in customerGroups) {
        final existingGroup = customerGroupBox.get(group.id);
        if (existingGroup == null || existingGroup != group) {
          customerGroupBox.put(group.id, group);
        }
      }
    }
  } catch (e, stack) {
    logAppError('Error fetching customer groups and storing in Hive', e, stack);
  }
}

Future<void> fetchTypesOfServiceAndStoreInHive() async {
  try {
    final typesOfService = await _apiRepository.getTypesOfService();
    if (typesOfService != null) {
      debugPrint('Types of Service received: ${typesOfService.length}');
      final receivedServiceIds = typesOfService.map((ts) => ts.id).toSet();
      final storedServiceIds = serviceModelBox.keys.toSet();
      final serviceIdsToDelete =
          storedServiceIds.difference(receivedServiceIds);
      for (var serviceId in serviceIdsToDelete) {
        serviceModelBox.delete(serviceId);
      }
      for (var service in typesOfService) {
        final existingService = serviceModelBox.get(service.id);
        if (existingService == null || existingService != service) {
          serviceModelBox.put(service.id, service);
        }
      }
    }
  } catch (e, stack) {
    logAppError(
        'Error fetching types of service and storing in Hive', e, stack);
  }
}

Future<void> fetchSellsAndStoreInHive({
  DateTime? startDate,
  DateTime? endDate,
  bool update = false,
  bool summary = false,
  int? page,
}) async {
  try {
    List<Sell> sells = await _apiRepository.getSells(
      startDate: startDate,
      endDate: endDate,
      summary: summary,
      lastUpdated: update ? loginData.lastUpdateTime : null,
      page: page,
    );

    if (sells.isNotEmpty) {
      debugPrint('Sells received: ${sells.length}');
      sells = sells.where((sell) {
        return canViewOwnSellOnly() ? sell.createdBy == loginData.userId : true;
      }).toList();
      for (var sell in sells) {
        final existingSell = sellsBox.get(sell.id);
        if (existingSell == null || existingSell != sell) {
          sellsBox.put(sell.id, sell);
        }
      }
    }
  } catch (e, stack) {
    logAppError('Error fetching sells and storing in Hive', e, stack);
  }
}

Future<void> fetchPurchasesAndStoreInHive({
  DateTime? startDate,
  DateTime? endDate,
  bool update = false,
  bool summary = false,
  int? page,
}) async {
  try {
    var purchases = await _apiRepository.getPurchases(
      startDate: startDate,
      endDate: endDate,
      lastUpdated: update ? loginData.lastUpdateTime : null,
      summary: summary,
      page: page,
    );
    if (purchases != null) {
      debugPrint('Purchases received: ${purchases.length}');

      purchases = purchases.where((purchase) {
        return canOnlyViewOwnPurchase()
            ? purchase.createdBy == loginData.userId
            : true;
      }).toList();
      for (var purchase in purchases) {
        final existingPurchase = purchasesBox.get(purchase.id);
        if (existingPurchase == null || existingPurchase != purchase) {
          purchasesBox.put(purchase.id, purchase);
        }
      }
    }
  } catch (e, stack) {
    logAppError('Error fetching purchases and storing in Hive', e, stack);
  }
}

Future<bool> fetchCashRegistersAndStoreInHive(
  context, {
  bool refresh = false,
}) async {
  try {
    final cRegisters = await _apiRepository.getCashRegisters();
    if (cRegisters != null) {
      debugPrint('Cash Registers received: ${cRegisters.length}');
      final receivedCashRegistersIds = cRegisters.map((cr) => cr.id).toSet();
      final storedCashRegistersIds = cashRegistersBox.keys.toSet();
      final cashRegistersIdsToDelete =
          storedCashRegistersIds.difference(receivedCashRegistersIds);

      for (var cashRegisterId in cashRegistersIdsToDelete) {
        cashRegistersBox.delete(cashRegisterId);
      }

      for (CashRegister cr in cRegisters) {
        final existingCashRegister = cashRegistersBox.get(cr.id);
        if (existingCashRegister == null || existingCashRegister != cr) {
          cashRegistersBox.put(cr.id, cr);
        }
      }
      // Check if there is any open CashRegister for the user
      final userId = loginData.userId;
      final openCashRegisters = cashRegistersBox.values
          .where((cr) => cr.userId == userId && cr.status == "open")
          .toList()
        ..sort((a, b) => b.createdAt.compareTo(a.createdAt));

      if (openCashRegisters.isEmpty && refresh) {
        // periodic checking
        loginData.cashRegisteredTime = null;
        loginData.cashRegistered = false;
        loginData.cashRegisteredId = null;
        loginData.save();
        return true;
      }

      if (openCashRegisters.isNotEmpty) {
        // Update the cashRegistered flag
        // in credentialsBox with the latest open CashRegister
        if (openCashRegisters.first.createdAt.isAfter(
          DateTime.now().subtract(const Duration(days: 1)),
        )) {
          // shift is in last 24 hours.
          loginData.cashRegisteredTime = openCashRegisters.first.createdAt;
          loginData.cashRegistered = true;
          loginData.cashRegisteredId = openCashRegisters.first.id;
          loginData.save();
          return true;
        } else {
          if (!refresh ||
              (refresh &&
                  (loginData.cashRegisteredTime == null ||
                      !loginData.cashRegisteredTime!.isAfter(
                          DateTime.now().subtract(const Duration(days: 1)))))) {
            final strings = AppLocalizations.of(context)!;
            EasyLoading.dismiss();
            bool isOldShiftClosed = await showDialog(
              context: context,
              builder: (context) {
                return AlertDialog(
                  title: Text(
                    canCloseCashRegister()
                        ? strings.old_shift_title
                        : strings.cannot_end_old_shift_title,
                  ),
                  content: Text(
                    canCloseCashRegister()
                        ? strings.old_shift_content
                        : strings.cannot_end_old_shift_content,
                  ),
                  actions: [
                    if (canCloseCashRegister())
                      ElevatedButton(
                        onPressed: () async {
                          EasyLoading.show(status: strings.loading);
                          final lInfo =
                              await _apiRepository.getCurrentLocation();
                          await _apiRepository
                              .sendCashRegister(
                            cashRegister: CashRegisterToAPI(
                              id: openCashRegisters.first.id,
                              locationInfo: lInfo,
                              locationId: openCashRegisters.first.locationId,
                              createdAt: DateTime.now(),
                              status: CashRegisterStatus.close,
                              closingAmount: 0,
                            ),
                          )
                              .then((value) {
                            EasyLoading.dismiss();
                            if (context.mounted) context.pop(true);
                          }).onError((error, stackTrace) {
                            EasyLoading.show(status: "Something went wrong");
                          });
                        },
                        child: Text(strings.okay),
                      ),
                    ElevatedButton(
                      onPressed: () => context.pop(false),
                      child: Text(strings.cancel),
                    ),
                  ],
                );
              },
            );
            if (isOldShiftClosed) {
              EasyLoading.show(status: strings.update_start);
            }
            return isOldShiftClosed;
          } else {
            return true;
          }
        }
      } else {
        return true;
      }
    }
    // If cRegisters is null, return false or handle it accordingly
    return false;
  } catch (e, stack) {
    logAppError('Error fetching Cash Registers and storing in Hive', e, stack);
    return false;
  }
}

Future<void> fetchSellsReturnsAndStoreInHive({
  DateTime? startDate,
  DateTime? endDate,
  bool summary = false,
  int? page,
}) async {
  try {
    final sReturns = await _apiRepository.getSellsReturns(
      startDate: startDate,
      endDate: endDate,
      summary: summary,
      page: page,
    );
    if (sReturns != null) {
      debugPrint('Sells Returns received: ${sReturns.length}');
      for (var sReturn in sReturns) {
        final existingSell = sellsReturnsBox.get(sReturn.id);
        if (existingSell == null || existingSell != sReturn) {
          sellsReturnsBox.put(sReturn.id, sReturn);
        }
      }
    }
  } catch (e, stack) {
    logAppError('Error fetching Sell Returns and storing in Hive', e, stack);
  }
}

Future<void> fetchPurchasesReturnsAndStoreInHive({
  DateTime? startDate,
  DateTime? endDate,
  bool summary = false,
  int? page,
}) async {
  try {
    final pReturns = await _apiRepository.getPurchasesReturns(
      startDate: startDate,
      endDate: endDate,
      summary: summary,
      page: page,
    );
    if (pReturns != null) {
      debugPrint('Purchases Returns received: ${pReturns.length}');
      for (var pReturn in pReturns) {
        final existingPurchase = purchaseReturnsBox.get(pReturn.id);
        if (existingPurchase == null || existingPurchase != pReturn) {
          purchaseReturnsBox.put(pReturn.id, pReturn);
        }
      }
    }
  } catch (e, stack) {
    logAppError(
        'Error fetching Purchase Returns and storing in Hive', e, stack);
  }
}

Future<void> fetchSellingPriceGroupsAndStoreInHive() async {
  try {
    final sellingPriceGroups = await _apiRepository.getSellingPriceGroups();
    if (sellingPriceGroups != null) {
      debugPrint('Selling Price Groups received: ${sellingPriceGroups.length}');
      final receivedGroupIds = sellingPriceGroups.map((spg) => spg.id).toSet();
      final storedGroupIds = sellingPriceGroupsBox.keys.toSet();
      final groupIdsToDelete = storedGroupIds.difference(receivedGroupIds);
      for (var groupId in groupIdsToDelete) {
        sellingPriceGroupsBox.delete(groupId);
      }
      for (var sellingPriceGroup in sellingPriceGroups) {
        final existingGroup = sellingPriceGroupsBox.get(sellingPriceGroup.id);
        if (existingGroup == null || existingGroup != sellingPriceGroup) {
          sellingPriceGroupsBox.put(sellingPriceGroup.id, sellingPriceGroup);
        }
      }
    }
  } catch (e, stack) {
    logAppError(
        'Error fetching selling price groups and storing in Hive', e, stack);
  }
}

Future<void> fetchExpensesAndStoreInHive({
  DateTime? startDate,
  DateTime? endDate,
  bool summary = false,
  int? page,
}) async {
  try {
    var expenses = await _apiRepository.getExpenses(
      startDate: startDate,
      endDate: endDate,
      summary: summary,
      page: page,
    );
    if (expenses != null) {
      debugPrint('Expenses received: ${expenses.length}');
      expenses = expenses.where((e) {
        return canViewOwnExpense() ? e.createdBy == loginData.userId : true;
      }).toList();
      for (var expense in expenses) {
        final existingExpense = expensesBox.get(expense.id);
        if (existingExpense == null || existingExpense != expense) {
          expensesBox.put(expense.id, expense);
        }
      }
    }
  } catch (e, stack) {
    logAppError('Error fetching expenses and storing in Hive', e, stack);
  }
}

Future<void> fetchExpenseCategoriesAndStoreInHive() async {
  try {
    final categories = await _apiRepository.getExpenseCategories();
    if (categories != null) {
      debugPrint('Expense Categories received: ${categories.length}');
      final receivedCategoryIds = categories.map((ec) => ec.id).toSet();
      final storedCategoryIds = expenseCategoriesBox.keys.toSet();
      final categoryIdsToDelete =
          storedCategoryIds.difference(receivedCategoryIds);
      for (var categoryId in categoryIdsToDelete) {
        expenseCategoriesBox.delete(categoryId);
      }
      for (var category in categories) {
        final existingCategory = expenseCategoriesBox.get(category.id);
        if (existingCategory == null || existingCategory != category) {
          expenseCategoriesBox.put(category.id, category);
        }
      }
    }
  } catch (e, stack) {
    logAppError(
        'Error fetching expense categories and storing in Hive', e, stack);
  }
}

Future<void> fetchFollowUpsAndStoreInHive(
    {DateTime? startDate, DateTime? endDate, bool update = false}) async {
  try {
    final followUps = await _apiRepository.getFollowUps(
        startDate: startDate, endDate: endDate);
    if (followUps != null) {
      debugPrint('Follow-Ups received: ${followUps.length}');
      if (update) {
        final receivedFollowUpIds = followUps.map((fu) => fu.id).toSet();
        final storedFollowUpIds = followUpBox.keys.toSet();
        final followUpIdsToDelete =
            storedFollowUpIds.difference(receivedFollowUpIds);
        for (var followUpId in followUpIdsToDelete) {
          followUpBox.delete(followUpId);
        }
      }
      for (var followUp in followUps) {
        final existingFollowUp = followUpBox.get(followUp.id);
        if (existingFollowUp == null || existingFollowUp != followUp) {
          followUpBox.put(followUp.id, followUp);
        }
      }
    }
  } catch (e, stack) {
    logAppError('Error fetching Follow-Ups and storing in Hive', e, stack);
  }
}

Future<void> fetchContactPaymentReports({
  bool? supplier,
  DateTime? startDate,
  DateTime? endDate,
}) async {
  try {
    List<ContactPaymentModel>? customerPaymentReports;
    List<ContactPaymentModel>? supplierPaymentReports;

    customerPaymentReports = supplier == null || !supplier
        ? await _apiRepository.getCustomerPaymentReports(
            startDate: startDate, endDate: endDate)
        : null;

    supplierPaymentReports = supplier == null || supplier
        ? await _apiRepository.getSupplierPaymentReports(
            startDate: startDate, endDate: endDate)
        : null;

    debugPrint(
      'Customer Payment Reports received: ${customerPaymentReports?.length}',
    );
    debugPrint(
      'Supplier Payment Reports received: ${supplierPaymentReports?.length}',
    );

    // Combine customer and supplier payment reports
    final allPaymentReports = [
      ...?customerPaymentReports,
      ...?supplierPaymentReports
    ];

    for (var paymentReport in allPaymentReports) {
      final existingPaymentReport = paymentsReportBox.get(paymentReport.id);
      if (existingPaymentReport == null ||
          existingPaymentReport != paymentReport) {
        paymentsReportBox.put(paymentReport.id, paymentReport);
      }
    }
  } catch (e, stack) {
    logAppError('Error fetching payment reports and storing in Hive', e, stack);
  }
}

Future<void> fetchDataAndStoreAllInHive(
  BuildContext context, {
  bool refresh = false,
}) async {
  final strings = AppLocalizations.of(context)!;
  late final ProductsCubit pCubit;
  late final LandingCubit lCubit;
  final hasConnection = await InternetConnectionChecker.instance.hasConnection;
  if (hasConnection) {
    try {
      late final bool shiftRangeOkay;
      EasyLoading.show(status: strings.update_start);
      await _apiRepository.getLoggedinData().then((_) async {
        if (context.mounted) {
          shiftRangeOkay = await fetchCashRegistersAndStoreInHive(
            context,
            refresh: refresh,
          );
        }
      });
      if (shiftRangeOkay) {
        await Future.wait([
          _apiRepository.getDefaultServiceGroup(),
          if (refresh) fetchProductsAndStoreInHive(refresh: refresh),
          fetchCategoriesAndStoreInHive(),
          fetchUsersAndStoreInHive(),
          fetchUsersAndStoreInHive(serviceStaff: true),
          fetchCommAgentsAndStoreInHive(),
          fetchShippingCompaniesAndStoreInHive(),
          fetchTablesAndStoreInHive(),
          fetchUnitsAndStoreInHive(),
          fetchTaxRatesAndStoreInHive(),
          fetchBrandsAndStoreInHive(),
          fetchBusinessLocationsAndStoreInHive(),
          fetchPaymentAccountsAndStoreInHive(),
          if (refresh) fetchContactsAndStoreInHive(refresh: refresh),
          fetchSellingPriceGroupsAndStoreInHive(),
          fetchExpenseCategoriesAndStoreInHive(),
          fetchCustomerGroupsAndStoreInHive(),
          fetchTypesOfServiceAndStoreInHive(),
        ]).then((_) {
          if (context.mounted) {
            pCubit = context.read<ProductsCubit>();
            lCubit = context.read<LandingCubit>();
          }
          if (!refresh &&
              businessLocationsBox.values.isNotEmpty &&
              canGoToHomeBasedOnShift()) {
            pCubit.resetAll();
            pCubit.initialLoadProductsAndContacts().then((_) {
              lCubit.updateLandingScreen();
            });
          } else if (refresh) {
            loginData.isProductsReady = true;
            loginData.isContactsReady = true;
            loginData.save();
          }
          lCubit.startButtonUpdateStream(runImmediately: true);
          lCubit.updateTotalReturnsPayments();
        });
        await fetchTransactions(refresh);
        loginData.lastUpdateTime = DateTime.now();
        loginData.save();
        EasyLoading.showSuccess(strings.update_done).then((_) {
          try {
            lCubit.updateLandingScreen();
            if (context.mounted) context.go(landingScreen);
            lCubit.switchPage(0);
            pCubit.initAllProducts();
            pCubit.initCart();
            pCubit.updateProducts();
          } catch (e, stack) {
            logAppError('Error while trying to refresh after update', e, stack);
          }
        });
      }
    } catch (e, stack) {
      logAppError("fetchDataAndStoreAllInHive Error", e, stack);
    }
  } else {
    EasyLoading.showError(strings.offline);
  }
}

Future<void> fetchTransactions(bool update) async {
  if (loginData.cashRegistered && productsBox.values.isNotEmpty) {
    await fetchSellsAndStoreInHive(
        startDate: loginData.cashRegisteredTime, update: update);
    await fetchSellsReturnsAndStoreInHive(
        startDate: loginData.cashRegisteredTime);
    await fetchPurchasesAndStoreInHive(
        startDate: loginData.cashRegisteredTime, update: update);
    await fetchPurchasesReturnsAndStoreInHive(
        startDate: loginData.cashRegisteredTime);
    await fetchFollowUpsAndStoreInHive(
        startDate: loginData.cashRegisteredTime, update: update);
    await fetchExpensesAndStoreInHive(startDate: loginData.cashRegisteredTime);
    await fetchContactPaymentReports(startDate: loginData.cashRegisteredTime);
  }
  if (loginData.cashRegisteredTime != null) {
    await fetchDeletedItems(loginData.cashRegisteredTime!);
  }
}

Future<void> fetchDeletedItems(DateTime lastUpdated) async {
  final itemsToDelete = await _apiRepository.getItemsToDelete(lastUpdated);

  if (itemsToDelete != null) {
    final Map<Box, List<int>> boxIdMap = {
      sellsBox: itemsToDelete.sell,
      sellsReturnsBox: itemsToDelete.sellReturn,
      purchasesBox: itemsToDelete.purchase,
      purchaseReturnsBox: itemsToDelete.purchaseReturn,
      expensesBox: itemsToDelete.expense,
      productsBox: itemsToDelete.product,
      contactsBox: itemsToDelete.contact,
      paymentsReportBox: itemsToDelete.payment,
    };

    for (var entry in boxIdMap.entries) {
      final box = entry.key;
      final ids = entry.value;
      for (var id in ids) {
        box.delete(id);
      }
    }
  }
}
