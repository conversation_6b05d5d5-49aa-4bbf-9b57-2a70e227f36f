import 'package:flutter/cupertino.dart';
import 'package:intl/intl.dart';
import 'package:we2up/data/models/contact_payment_model.dart';
import 'package:we2up/data/models/expense.dart';
import 'package:we2up/data/models/follow_up.dart';
import 'package:we2up/data/models/payment.dart';
import 'package:we2up/data/models/purchase.dart';
import 'package:we2up/data/models/purchase_return.dart';
import 'package:we2up/data/models/sell.dart';
import 'package:we2up/data/models/sell_return.dart';
import 'package:we2up/data/db/db_manager.dart';

import '../models/expense_to_api.dart';
import '../models/follow_up_to_api.dart';
import '../models/location_info.dart';
import '../models/payment_status.dart';
import '../models/purchase_return_to_api.dart';
import '../models/purchase_to_api.dart';
import '../models/sell_return_to_api.dart';
import '../models/sell_to_api.dart';

String createNewId() {
  var now = DateTime.now();
  return "${loginData.userId}_${DateFormat('yMdHms').format(now)}";
}

class OfflineManagement {
  Future<bool> sendSell({required SellToAPI sellToAPI, int? sellID}) async {
    try {
      List<SellLine> sellLines = [];
      List<PaymentLine> paymentLines = [];
      String invoiceNo = sellToAPI.offlineID ??
          (sellID == null ? createNewId() : sellsBox.get(sellID)!.invoiceNo!);
      for (var product in sellToAPI.products) {
        final subunit = unitsBox.get(product.subUnitId)!;
        final double unitPrice = product.unitPrice / subunit.baseUnitMultiplier;
        final unitDiscount = product.discountAmount;
        final unitPriceIncTax = ((unitPrice - unitDiscount) *
            (1 + ((taxRatesBox.get(product.taxRateId)?.amount ?? 0) / 100)));
        sellLines.add(
          SellLine(
            productId: product.productId,
            variationId: product.variationId,
            quantity: product.quantity * subunit.baseUnitMultiplier,
            unitPrice: unitPrice.toString(),
            unitPriceIncTax: unitPriceIncTax.toString(),
            taxId: product.taxRateId,
            subUnitId: product.subUnitId,
            lineDiscountAmount: product.discountAmount.toString(),
            lineDiscountType: product.discountType,
          ),
        );
      }
      for (var payment in sellToAPI.payments) {
        paymentLines.add(PaymentLine.fromPayment(payment));
      }
      Sell offlineSell = Sell(
        id: sellID,
        offline: true,
        status: sellToAPI.isQuotation == 0 ? "final" : "draft",
        paymentStatus: PaymentStatus.offline,
        sellToAPI: sellToAPI.copyWith(invoiceNo: invoiceNo),
        invoiceNo: invoiceNo,
        locationId: sellToAPI.locationId,
        contactId: sellToAPI.contactId,
        taxId: sellToAPI.taxRateId,
        discountType: sellToAPI.discountType,
        shippingCharges: sellToAPI.shippingCharges.toString(),
        discountAmount: sellToAPI.discountAmount.toString(),
        additionalNotes: sellToAPI.saleNote,
        staffNote: sellToAPI.staffNote,
        transactionDate: DateTime.parse(sellToAPI.transactionDate),
        isQuotation: sellToAPI.isQuotation,
        finalTotal: sellToAPI.invoiceAmount,
        sellLines: sellLines,
        paymentLines: paymentLines,
        locationInfo: sellToAPI.locationInfo,
        typesOfServiceId: sellToAPI.typesOfServiceId,
        commissionAgent: sellToAPI.commissionAgent,
        resTableId: sellToAPI.tableId,
        shippingCompanyId: sellToAPI.shippingCompanyId,
        shippingDetails: sellToAPI.shippingDetails,
        shippingAddress: sellToAPI.shippingAddress,
      );
      double paidAmount = paymentLines.fold(0, (sum, paymentDetail) {
        String value = paymentDetail.amount ?? "0";
        double invoiceValue = double.tryParse(value) ?? 0.0;
        return sum + invoiceValue;
      });
      final contact = contactsBox.get(sellToAPI.contactId)!;
      final updatedContact = contact.copyWith(
        due: (double.parse(contact.due ?? "0") +
                double.parse(sellToAPI.invoiceAmount) -
                paidAmount)
            .toStringAsFixed(4),
      );

      if (sellID != null) {
        // updating a sell that is not offline
        sellsBox.put(sellID, offlineSell);
        if (offlineSell.isQuotation != 1) {
          contactsBox.put(sellToAPI.contactId, updatedContact);
        }
        return true;
      } else {
        sellsBox.put(invoiceNo, offlineSell);
        if (offlineSell.isQuotation != 1) {
          contactsBox.put(sellToAPI.contactId, updatedContact);
        }
        return true;
      }
    } catch (e) {
      debugPrint(e.toString());
      return false;
    }
  }

  Future<bool> sendSellReturn(
      {required SellReturnToAPI sellReturnToAPI, String? offlineID}) async {
    Sell returnParentSell = sellsBox.get(sellReturnToAPI.transactionId)!;
    for (var sellLine in returnParentSell.sellLines!.where(
      (sl) => sellReturnToAPI.products
          .any((element) => element.sellLineId == sl.id),
    )) {
      sellLine.quantityReturned = sellReturnToAPI.products
          .firstWhere((element) => element.sellLineId == sellLine.id)
          .quantity
          .toString();
    }
    double finalTotal = 0;
    for (var sellLine in returnParentSell.sellLines!) {
      finalTotal += double.parse(sellLine.quantityReturned ?? "0") *
          double.parse(sellLine.unitPriceIncTax ?? "0");
    }
    int? contactId = sellsBox.get(sellReturnToAPI.transactionId)!.contactId;
    SellReturn offlineSellReturn = SellReturn(
      offline: true,
      sellReturnToAPI: sellReturnToAPI,
      id: sellReturnToAPI.transactionId,
      businessId: sellsBox.get(sellReturnToAPI.transactionId)!.businessId!,
      contactId: contactId,
      transactionDate: DateTime.parse(sellReturnToAPI.transactionDate),
      discountType: sellReturnToAPI.discountType,
      invoiceNo: offlineID.toString(),
      finalTotal: finalTotal.toStringAsFixed(2),
      discountAmount: sellReturnToAPI.discountAmount.toString(),
      returnParentSell: returnParentSell,
    );
    final contact = contactsBox.get(contactId)!;
    final updatedContact = contact.copyWith(
      due: (double.parse(contact.due ?? "0") - finalTotal).toStringAsFixed(4),
    );
    try {
      sellsReturnsBox.put(offlineID, offlineSellReturn);
      contactsBox.put(contactId, updatedContact);
      return true;
    } catch (e) {
      debugPrint(e.toString());
      return false;
    }
  }

  Future<bool> sendPurchase({
    required PurchaseToAPI purchaseToAPI,
    int? purchaseID,
  }) async {
    String refNo = purchaseToAPI.offlineID ??
        (purchaseID == null
            ? createNewId()
            : purchasesBox.get(purchaseID)!.refNo!);
    List<PaymentLine> paymentLines = [];
    List<PurchaseLine> purchaseLines = [];
    for (var payment in purchaseToAPI.payment) {
      paymentLines.add(PaymentLine.fromPayment(payment));
    }
    for (var product in purchaseToAPI.products) {
      purchaseLines.add(
        PurchaseLine(
          productId: product.productId,
          variationId: product.variationId,
          quantity: (double.tryParse(product.quantity ?? "0") ?? 0),
          purchasePrice: product.purchasePrice,
          purchasePriceIncTax: product.purchasePriceIncTax,
          subUnitId: int.tryParse(product.subUnitId ?? "0") ?? 0,
          discountPercent: product.discountPercent,
        ),
      );
    }
    Purchase offlinePurchase = Purchase(
      id: purchaseID,
      offline: true,
      status: "Offline",
      purchaseToAPI: purchaseToAPI,
      refNo: refNo,
      locationId: purchaseToAPI.locationId,
      contactId: purchaseToAPI.contactId,
      taxId: purchaseToAPI.taxId,
      discountType: purchaseToAPI.discountType,
      shippingCharges: purchaseToAPI.shippingCharges,
      discountAmount: purchaseToAPI.discountAmount,
      additionalNotes: purchaseToAPI.additionalNotes,
      transactionDate: purchaseToAPI.transactionDate,
      finalTotal: purchaseToAPI.finalTotal,
      paymentLines: paymentLines,
      purchaseLines: purchaseLines,
      locationInfo: purchaseToAPI.locationInfo,
    );
    double paidAmount = paymentLines.fold(0, (sum, paymentDetail) {
      String value = paymentDetail.amount ?? "0";
      double invoiceValue = double.tryParse(value) ?? 0.0;
      return sum + invoiceValue;
    });
    final contact = contactsBox.get(purchaseToAPI.contactId)!;
    final updatedContact = contact.copyWith(
      due: (double.parse(contact.due ?? "0") +
              double.parse(purchaseToAPI.finalTotal) -
              paidAmount)
          .toStringAsFixed(4),
    );
    try {
      if (purchaseID != null) {
        purchasesBox.put(purchaseID, offlinePurchase);
        contactsBox.put(purchaseToAPI.contactId, updatedContact);
        return true;
      } else {
        purchasesBox.put(refNo, offlinePurchase);
        contactsBox.put(purchaseToAPI.contactId, updatedContact);
        return true;
      }
    } catch (e) {
      debugPrint(e.toString());
      return false;
    }
  }

  Future<bool> purchaseReturn(
      {required PurchaseReturnToAPI purchaseReturnToAPI}) async {
    Purchase purchase = purchasesBox.get(
      int.parse(purchaseReturnToAPI.transactionId),
    )!;
    String finalTotal() {
      double total = 0.0;
      for (var item in purchaseReturnToAPI.products) {
        total += (double.parse(item.returnedQuantity) *
            double.parse(purchase.purchaseLines
                    ?.firstWhere((element) =>
                        element.id == int.parse(item.purchaseLineId))
                    .purchasePrice ??
                "0"));
      }
      return total.toStringAsFixed(4);
    }

    List<PurchaseLine> purchaseLines = [];
    for (var item in purchaseReturnToAPI.products) {
      purchaseLines.add(
        PurchaseLine(
            quantityReturned: item.returnedQuantity,
            id: int.parse(item.purchaseLineId),
            productId: purchase.purchaseLines
                ?.firstWhere(
                    (element) => element.id == int.parse(item.purchaseLineId))
                .productId),
      );
    }
    PurchaseReturn purchaseReturn = PurchaseReturn(
      offline: true,
      purchaseReturnToAPI: purchaseReturnToAPI,
      contactId: purchase.contactId,
      transactionDate: purchase.transactionDate,
      refNo: createNewId(),
      finalTotal: finalTotal(),
      returnParentPurchase: Purchase(
        purchaseLines: purchaseLines,
        transactionDate: DateTime.now(),
        locationInfo: purchaseReturnToAPI.locationInfo,
      ),
    );
    final contact = contactsBox.get(purchase.contactId)!;
    final updatedContact = contact.copyWith(
      due: (double.parse(contact.due ?? "0") - double.parse(finalTotal()))
          .toStringAsFixed(4),
    );
    try {
      purchaseReturnsBox.put(purchaseReturnToAPI.transactionId, purchaseReturn);
      contactsBox.put(purchase.contactId, updatedContact);
      return true;
    } catch (e) {
      debugPrint(e.toString());
      return false;
    }
  }

  Future<bool> sendExpense({
    required ExpenseToAPI expenseToAPI,
    int? expenseID,
  }) async {
    String refNo = expenseToAPI.offlineID ??
        (expenseID == null
            ? createNewId()
            : expensesBox.get(expenseID)!.refNo!);
    List<Payment> payments = [];
    for (var payment in expenseToAPI.payment!) {
      payments.add(
        Payment(
          amount: payment.amount,
          method: payment.method,
          accountId: payment.accountId,
          note: payment.note,
          paymentFor: expenseToAPI.contactId,
        ),
      );
    }
    Expense offlineExpense = Expense(
      transactionDate: DateFormat('yyyy-MM-dd HH:mm:ss')
          .parse(expenseToAPI.transactionDate!),
      isRefund: expenseToAPI.isRefund,
      taxId: expenseToAPI.taxRateId,
      locationId: expenseToAPI.locationId,
      expenseFor: contactsBox.get(expenseToAPI.contactId)?.toJson(),
      finalTotal: expenseToAPI.finalTotal.toString(),
      additionalNotes: expenseToAPI.additionalNotes,
      payments: payments,
      refNo: refNo,
      offline: true,
      expenseToAPI: expenseToAPI,
      contactId: expenseToAPI.contactId,
      locationInfo: expenseToAPI.locationInfo,
    );
    try {
      if (expenseID != null) {
        expensesBox.put(expenseID, offlineExpense);
        return true;
      } else {
        expensesBox.put(refNo, offlineExpense);
        return true;
      }
    } catch (e) {
      debugPrint(e.toString());
      return false;
    }
  }

  Future<bool> sendContactPayment({
    required int contactID,
    required Payment payment,
    LocationInfo? locationInfo,
  }) async {
    final contact = contactsBox.get(contactID)!;
    final updatedContact = contact.copyWith(
      due: (double.parse(contact.due ?? "0") - (payment.amount ?? 0))
          .toStringAsFixed(4),
    );
    try {
      paymentsReportBox.put(
        payment.refNo,
        ContactPaymentModel.fromOfflinePayment(
            contactID, payment, locationInfo),
      );
      contactsBox.put(contactID, updatedContact);
      return true;
    } catch (e) {
      return false;
    }
  }

  Future<bool> sendFollowUp({
    required FollowUpToAPI followUpToAPI,
    int? followUpID,
  }) async {
    String refNo = followUpToAPI.offlineID ??
        (followUpID == null
            ? createNewId()
            : followUpBox.get(followUpID)!.refNo!);
    FollowUp offlineFollowUp = FollowUp(
      offline: true,
      followUpToAPI: followUpToAPI,
      refNo: refNo,
      contactId: followUpToAPI.contactId,
      title: followUpToAPI.title,
      status: followUpToAPI.status,
      startDatetime: followUpToAPI.startDatetime,
      endDatetime: followUpToAPI.endDatetime,
      scheduleType: followUpToAPI.scheduleType,
      notifyVia: followUpToAPI.notifyVia,
      notifyBefore: followUpToAPI.notifyBefore,
      notifyType: followUpToAPI.notifyType,
      users: [usersBox.get(followUpToAPI.userId.first)!],
    );
    try {
      if (followUpID != null) {
        followUpBox.put(followUpID, offlineFollowUp);
        return true;
      } else {
        followUpBox.put(refNo, offlineFollowUp);
        return true;
      }
    } catch (e) {
      return false;
    }
  }
}
