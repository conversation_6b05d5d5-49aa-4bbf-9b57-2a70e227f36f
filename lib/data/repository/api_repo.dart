import 'dart:async';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:geolocator/geolocator.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:we2up/bloc/landing/landing_cubit.dart';
import 'package:we2up/bloc/products/products_cubit.dart';
import 'package:we2up/data/api/we2up_api.dart';
import 'package:we2up/data/models/cash_register.dart';
import 'package:we2up/data/models/cash_register_to_api.dart';
import 'package:we2up/data/models/clock.dart';
import 'package:we2up/data/models/contact_to_api.dart';
import 'package:we2up/data/models/customer_group.dart';
import 'package:we2up/data/models/expense_category.dart';
import 'package:we2up/data/models/expense_to_api.dart';
import 'package:we2up/data/models/follow_up.dart';
import 'package:we2up/data/models/follow_up_to_api.dart';
import 'package:we2up/data/models/items_to_delete.dart';
import 'package:we2up/data/models/location_info.dart';
import 'package:we2up/data/models/product.dart';
import 'package:we2up/data/models/purchase.dart';
import 'package:we2up/data/models/purchase_return.dart';
import 'package:we2up/data/models/purchase_return_to_api.dart';
import 'package:we2up/data/models/purchase_to_api.dart';
import 'package:we2up/data/models/sell.dart';
import 'package:we2up/data/models/sell_return.dart';
import 'package:we2up/data/models/sell_to_api.dart';
import 'package:we2up/data/models/selling_price_group.dart';
import 'package:we2up/data/models/service_model.dart';
import 'package:we2up/data/models/shift_details.dart';
import 'package:we2up/data/models/user_model.dart';
import 'package:we2up/data/db/db_manager.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:we2up/data/models/user_permissions.dart';

import '../../utils/location_manager.dart';
import '../../utils/we2up_constants.dart';
import '../models/business_location.dart';
import '../models/business_settings.dart';
import '../models/contact.dart';
import '../models/contact_payment_model.dart';
import '../models/expense.dart';
import '../models/new_product_to_api.dart';
import '../models/payment.dart';
import '../models/payment_account.dart';
import '../models/profit_loss_params.dart';
import '../models/profit_loss_report.dart';
import '../models/selected_price_group_info.dart';
import '../models/sell_return_to_api.dart';
import '../models/shipping_company.dart';
import '../models/table.dart';
import 'offline_management.dart';

class ApiRepository {
  final We2upAPI _we2upAPI = We2upAPI();
  final OfflineManagement _offlineManagement = OfflineManagement();

  static ApiRepository get() => ApiRepository();

  Future<bool> hasConnection() async {
    return await InternetConnectionChecker.instance.hasConnection;
  }

  void dataNotInSync() {
    loginData.inSync = false;
    loginData.save();
  }

  Future<bool> login({
    required String username,
    required String password,
  }) async {
    final auth = await _we2upAPI.auth(username: username, password: password);
    return auth;
  }

  Future<bool?> doesUserHaveToken({
    required String username,
    required String password,
  }) async {
    final auth = await _we2upAPI.doesUserHaveToken(
      username: username,
      password: password,
    );
    return auth;
  }

  Future<bool> logout() async {
    final loggedOut = await _we2upAPI.logout();
    return loggedOut;
  }

  Future<bool> getLoggedinData() async {
    final data = await _we2upAPI.fetchLoggedData();
    final roles = await _we2upAPI.fetchRoles();
    return data && roles;
  }

  Future<UserPermissions> getNewPermissions() async =>
      await _we2upAPI.getOldPermissions();

  Future<ClockResponse> clock(
      {required Clock clock, bool clockIn = true}) async {
    final ClockResponse response =
        await _we2upAPI.clock(clock: clock, clockIn: clockIn);
    return response;
  }

  Future<bool> updateCurrentLocation({LocationInfo? info}) async {
    final bool internetConnected = await hasConnection();
    if (internetConnected) {
      final response = await _we2upAPI.updateCurrentLocation(info: info);
      return response;
    } else {
      return false;
    }
  }

  Future<bool> updateBusinessLocation({
    required LocationInfo position,
    required int locationId,
  }) async {
    final response = await _we2upAPI.updateBusinessLocation(
      position: position,
      locationId: locationId,
    );
    return response;
  }

  Stream<List<Product>> getProductsStream({DateTime? lastUpdated}) async* {
    await for (List<Map<String, dynamic>> productsJsonList
        in _we2upAPI.getProductsStream(lastUpdated: lastUpdated)) {
      if (productsJsonList.isNotEmpty) {
        final List<Product> products = [];
        for (Map<String, dynamic> json in productsJsonList) {
          try {
            final product = Product.fromJson(json);
            products.add(product);
          } catch (e, stack) {
            logAppError(
              "Error converting product: ${json['id'] ?? json}",
              e,
              stack,
            );
          }
        }
        if (products.isNotEmpty) {
          yield products;
        }
      }
    }
  }

  Future<List<ProductCategory>?> getCategories() async {
    List<Map<String, dynamic>>? jsonList = await _we2upAPI.getCategoriesList();
    if (jsonList != null && jsonList.isNotEmpty) {
      final List<ProductCategory> categories = [];
      for (Map<String, dynamic> json in jsonList) {
        try {
          final category = ProductCategory.fromJson(json);
          categories.add(category);
        } catch (e, stack) {
          logAppError(
            "Error converting category: ${json['id'] ?? json}",
            e,
            stack,
          );
        }
      }

      if (categories.isNotEmpty) {
        return categories;
      } else {
        return null;
      }
    } else {
      return null;
    }
  }

  Future<ItemsToDelete?> getItemsToDelete(DateTime lastUpdated) async {
    Map<String, dynamic>? json = await _we2upAPI.getItemsToDelete(
      lastUpdated,
    );
    if (json == null) {
      return null;
    }
    return ItemsToDelete.fromJson(json);
  }

  Future<List<CustomerGroup>?> getCustomerGroups() async {
    List<Map<String, dynamic>>? jsonList = await _we2upAPI.getCustomerGroups();
    if (jsonList != null && jsonList.isNotEmpty) {
      final List<CustomerGroup> customerGroups = [];
      for (Map<String, dynamic> json in jsonList) {
        try {
          final customerGroup = CustomerGroup.fromJson(json);
          customerGroups.add(customerGroup);
        } catch (e, stack) {
          logAppError(
            "Error converting customer group: ${json['id'] ?? json}",
            e,
            stack,
          );
        }
      }

      if (customerGroups.isNotEmpty) {
        return customerGroups;
      } else {
        return null;
      }
    } else {
      return null;
    }
  }

  Future<List<ServiceModel>?> getTypesOfService() async {
    List<Map<String, dynamic>>? jsonList = await _we2upAPI.getTypesOfService();
    if (jsonList != null && jsonList.isNotEmpty) {
      final List<ServiceModel> serviceModels = [];
      for (Map<String, dynamic> json in jsonList) {
        try {
          final serviceModel = ServiceModel.fromJson(json);
          serviceModels.add(serviceModel);
        } catch (e, stack) {
          logAppError(
            "Error converting service model: ${json['id'] ?? json}",
            e,
            stack,
          );
        }
      }

      if (serviceModels.isNotEmpty) {
        return serviceModels;
      } else {
        return null;
      }
    } else {
      return null;
    }
  }

  Future<List<UserModel>?> getUsers({bool serviceStaff = false}) async {
    List<Map<String, dynamic>>? jsonList = await _we2upAPI.getUsersList(
      serviceStaff: serviceStaff,
    );
    if (jsonList != null && jsonList.isNotEmpty) {
      final List<UserModel> userModels = [];
      for (Map<String, dynamic> json in jsonList) {
        try {
          final userModel = UserModel.fromJson(json);
          userModels.add(userModel);
        } catch (e, stack) {
          logAppError(
            "Error converting user model: ${json['id'] ?? json}",
            e,
            stack,
          );
        }
      }

      if (userModels.isNotEmpty) {
        return userModels;
      } else {
        return null;
      }
    } else {
      return null;
    }
  }

  Future<List<UserModel>?> getCommAgents() async {
    List<Map<String, dynamic>>? jsonList = await _we2upAPI.getCommAgents();
    if (jsonList != null && jsonList.isNotEmpty) {
      final List<UserModel> userModels = [];
      for (Map<String, dynamic> json in jsonList) {
        try {
          final userModel = UserModel.fromJson(json);
          userModels.add(userModel);
        } catch (e, stack) {
          logAppError(
            "Error converting Comm Agent model: ${json['id'] ?? json}",
            e,
            stack,
          );
        }
      }

      if (userModels.isNotEmpty) {
        return userModels;
      } else {
        return null;
      }
    } else {
      return null;
    }
  }

  Future<List<ShippingCompany>?> getShippingCompanies() async {
    List<Map<String, dynamic>>? jsonList =
        await _we2upAPI.getShippingCompanies();
    if (jsonList != null && jsonList.isNotEmpty) {
      final List<ShippingCompany> shippingCompanies = [];
      for (Map<String, dynamic> json in jsonList) {
        try {
          final shippingCompany = ShippingCompany.fromJson(json);
          shippingCompanies.add(shippingCompany);
        } catch (e, stack) {
          logAppError(
            "Error converting Shipping Companies model: ${json['id'] ?? json}",
            e,
            stack,
          );
        }
      }

      if (shippingCompanies.isNotEmpty) {
        return shippingCompanies;
      } else {
        return null;
      }
    } else {
      return null;
    }
  }

  Future<List<BusinessTable>?> getTables() async {
    List<Map<String, dynamic>>? jsonList = await _we2upAPI.getTables();
    if (jsonList != null && jsonList.isNotEmpty) {
      final List<BusinessTable> tables = [];
      for (Map<String, dynamic> json in jsonList) {
        try {
          final table = BusinessTable.fromJson(json);
          tables.add(table);
        } catch (e, stack) {
          logAppError(
            "Error converting Table model: ${json['id'] ?? json}",
            e,
            stack,
          );
        }
      }

      if (tables.isNotEmpty) {
        return tables;
      } else {
        return null;
      }
    } else {
      return null;
    }
  }

  Future<List<Unit>?> getUnits() async {
    List<Map<String, dynamic>>? jsonList = await _we2upAPI.getUnitsList();
    if (jsonList != null && jsonList.isNotEmpty) {
      final List<Unit> units = [];
      for (Map<String, dynamic> json in jsonList) {
        try {
          final unit = Unit.fromJson(json);
          units.add(unit);
        } catch (e, stack) {
          logAppError(
            "Error converting unit: ${json['id'] ?? json}",
            e,
            stack,
          );
        }
      }

      if (units.isNotEmpty) {
        return units;
      } else {
        return null;
      }
    } else {
      return null;
    }
  }

  Future<List<TaxRate>?> getTaxRates() async {
    List<Map<String, dynamic>>? jsonList = await _we2upAPI.getTaxRateList();
    if (jsonList != null && jsonList.isNotEmpty) {
      final List<TaxRate> taxRates = [];
      for (Map<String, dynamic> json in jsonList) {
        try {
          final taxRate = TaxRate.fromJson(json);
          taxRates.add(taxRate);
        } catch (e, stack) {
          logAppError(
            "Error converting tax rate: ${json['id'] ?? json}",
            e,
            stack,
          );
        }
      }

      if (taxRates.isNotEmpty) {
        return taxRates;
      } else {
        return null;
      }
    } else {
      return null;
    }
  }

  Future<List<Brand>?> getBrands() async {
    List<Map<String, dynamic>>? jsonList = await _we2upAPI.getBrandList();
    if (jsonList != null && jsonList.isNotEmpty) {
      final List<Brand> brands = [];
      for (Map<String, dynamic> json in jsonList) {
        try {
          final brand = Brand.fromJson(json);
          brands.add(brand);
        } catch (e, stack) {
          logAppError(
            "Error converting brand: ${json['id'] ?? json}",
            e,
            stack,
          );
        }
      }

      if (brands.isNotEmpty) {
        return brands;
      } else {
        return null;
      }
    } else {
      return null;
    }
  }

  Future<ShiftDetails?> fetchShiftDetails(int id) async {
    Map<String, dynamic>? json = await _we2upAPI.fetchShiftDetails(id);
    if (json != null) {
      try {
        return ShiftDetails.fromJson(json);
      } catch (e, stack) {
        logAppError(
          "Error converting fetchShiftDetails: ${json['id'] ?? json}",
          e,
          stack,
        );
        return null;
      }
    } else {
      return null;
    }
  }

  Future<ShiftDetails?> fetchRangeSummaryDetails({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    Map<String, dynamic>? json = await _we2upAPI.fetchRangeSummaryDetails(
        startDate: startDate, endDate: endDate);
    if (json != null) {
      try {
        return ShiftDetails.fromJson(json);
      } catch (e, stack) {
        logAppError(
          "Error converting RangeSummaryDetails: ${json['id'] ?? json}",
          e,
          stack,
        );
        return null;
      }
    } else {
      return null;
    }
  }

  Future<List<BusinessLocation>?> getBusinessLocations() async {
    List<Map<String, dynamic>>? jsonList =
        await _we2upAPI.getBusinessLocationList();
    if (jsonList != null && jsonList.isNotEmpty) {
      final List<BusinessLocation> businessLocations = [];
      for (Map<String, dynamic> json in jsonList) {
        try {
          final businessLocation = BusinessLocation.fromJson(json);
          businessLocations.add(businessLocation);
        } catch (e, stack) {
          logAppError(
            "Error converting business location: ${json['id'] ?? json}",
            e,
            stack,
          );
        }
      }

      if (businessLocations.isNotEmpty) {
        return businessLocations;
      } else {
        return null;
      }
    } else {
      return null;
    }
  }

  Future<List<PaymentAccount>?> getPaymentAccounts() async {
    List<Map<String, dynamic>>? jsonList =
        await _we2upAPI.getPaymentAccountList();
    if (jsonList != null && jsonList.isNotEmpty) {
      final List<PaymentAccount> paymentAccounts = [];
      for (Map<String, dynamic> json in jsonList) {
        try {
          final paymentAccount = PaymentAccount.fromJson(json);
          paymentAccounts.add(paymentAccount);
        } catch (e, stack) {
          logAppError(
            "Error converting payment account: ${json['id'] ?? json}",
            e,
            stack,
          );
        }
      }

      if (paymentAccounts.isNotEmpty) {
        return paymentAccounts;
      } else {
        return null;
      }
    } else {
      return null;
    }
  }

  Stream<List<Contact>?> getContactsStream({DateTime? lastUpdated}) async* {
    await for (List<Map<String, dynamic>> contactsJsonList
        in _we2upAPI.getContactsStream(lastUpdated: lastUpdated)) {
      if (contactsJsonList.isNotEmpty) {
        final List<Contact> contacts = [];
        for (Map<String, dynamic> json in contactsJsonList) {
          try {
            final contact = Contact.fromJson(json);
            contacts.add(contact);
          } catch (e, stack) {
            logAppError(
              "Error converting contact: ${json['id'] ?? json}",
              e,
              stack,
            );
          }
        }

        if (contacts.isNotEmpty) {
          yield contacts;
        }
      }
    }
  }

  Future<List<Sell>> getSells({
    DateTime? startDate,
    DateTime? endDate,
    DateTime? lastUpdated,
    required bool summary,
    int? page,
  }) async {
    List<Sell> sells = [];
    try {
      List<Map<String, dynamic>> sellsMapList = await _we2upAPI.getSellPage(
        startDate: startDate,
        endDate: endDate,
        lastUpdated: lastUpdated,
        summary: summary,
        page: page,
      );
      if (sellsMapList.isNotEmpty) {
        for (Map<String, dynamic> json in sellsMapList) {
          List<Map<String, dynamic>> sellLinesJson =
              (json['sell_lines'] as List?)?.cast<Map<String, dynamic>>() ?? [];
          for (Map<String, dynamic> sellLineJson in sellLinesJson) {
            int variationId = sellLineJson['variation_id'];
            int updatedProductId = getUpdatedProductId(variationId);
            sellLineJson['product_id'] = updatedProductId;
          }
          json['sell_lines'] = sellLinesJson;

          try {
            final sell = Sell.fromJson(json);
            sells.add(sell);
          } catch (e, stack) {
            logAppError(
              "Error converting sell: ${json['id'] ?? json}",
              e,
              stack,
            );
          }
        }
      }
      return sells;
    } catch (e) {
      return sells;
    }
  }

  Future<List<Purchase>?> getPurchases({
    DateTime? startDate,
    DateTime? endDate,
    DateTime? lastUpdated,
    required bool summary,
    int? page,
  }) async {
    var purchasesList = await _we2upAPI.getPurchaseList(
      startDate: startDate,
      endDate: endDate,
      lastUpdated: lastUpdated,
      summary: summary,
      page: page,
    );
    if (purchasesList != null && purchasesList.isNotEmpty) {
      final List<Purchase> purchases = [];
      for (Map<String, dynamic> json in purchasesList) {
        List<Map<String, dynamic>> purchaseLinesJson =
            (json['purchase_lines'] as List?)?.cast<Map<String, dynamic>>() ??
                [];

        for (Map<String, dynamic> purchaseLineJson in purchaseLinesJson) {
          int variationId = purchaseLineJson['variation_id'];
          int updatedProductId = getUpdatedProductId(variationId);
          purchaseLineJson['product_id'] = updatedProductId;
        }

        json['purchase_lines'] = purchaseLinesJson;

        try {
          final purchase = Purchase.fromJson(json);
          purchases.add(purchase);
        } catch (e, stack) {
          logAppError(
            "Error converting purchase: ${json['id'] ?? json}",
            e,
            stack,
          );
        }
      }

      if (purchases.isNotEmpty) {
        return purchases;
      } else {
        return null;
      }
    } else {
      return null;
    }
  }

  Future<List<CashRegister>?> getCashRegisters() async {
    var jsonList = await _we2upAPI.getCashRegisterList();
    if (jsonList != null) {
      final List<CashRegister> cashRegisters = [];
      for (Map<String, dynamic> json in jsonList) {
        try {
          final cashRegister = CashRegister.fromJson(json);
          cashRegisters.add(cashRegister);
        } catch (e, stack) {
          logAppError(
            "Error converting cash register: ${json['id'] ?? json}",
            e,
            stack,
          );
        }
      }
      return cashRegisters;
    } else {
      return null;
    }
  }

  Future<List<CashRegister>?> getShiftsList({
    DateTime? startDate,
    DateTime? endDate,
    int? userId,
    int page = 1,
  }) async {
    var jsonList = await _we2upAPI.getShiftsList(
      startDate: startDate,
      endDate: endDate,
      page: page,
      userId: userId,
    );
    if (jsonList != null) {
      final List<CashRegister> cashRegisters = [];
      for (Map<String, dynamic> json in jsonList) {
        try {
          final cashRegister = CashRegister.fromJson(json);
          cashRegisters.add(cashRegister);
        } catch (e, stack) {
          logAppError(
            "Error converting cash register: ${json['id'] ?? json}",
            e,
            stack,
          );
        }
      }
      return cashRegisters;
    } else {
      return null;
    }
  }

  Future<List<FollowUp>?> getFollowUps(
      {DateTime? startDate, DateTime? endDate}) async {
    List<Map<String, dynamic>>? jsonList = await _we2upAPI.getFollowUpsList(
        startDate: startDate, endDate: endDate);
    if (jsonList != null && jsonList.isNotEmpty) {
      final List<FollowUp> followUps = [];
      for (Map<String, dynamic> json in jsonList) {
        try {
          final followUp = FollowUp.fromJson(json);
          followUps.add(followUp);
        } catch (e, stack) {
          logAppError(
            "Error converting follow-up: ${json['id'] ?? json}",
            e,
            stack,
          );
        }
      }

      if (followUps.isNotEmpty) {
        return followUps;
      } else {
        return null;
      }
    } else {
      return null;
    }
  }

  Future<List<ContactPaymentModel>?> getCustomerPaymentReports(
      {DateTime? startDate, DateTime? endDate}) async {
    List<Map<String, dynamic>>? jsonList = await _we2upAPI.getPaymentReports(
        supplier: false, startDate: startDate, endDate: endDate);
    if (jsonList != null && jsonList.isNotEmpty) {
      final List<ContactPaymentModel> customerPaymentReports = [];
      for (Map<String, dynamic> json in jsonList) {
        try {
          final contactPaymentModel = ContactPaymentModel.fromJson(json);
          customerPaymentReports.add(contactPaymentModel);
        } catch (e, stack) {
          logAppError(
            "Error converting customer payment report: ${json['id'] ?? json}",
            e,
            stack,
          );
        }
      }

      if (customerPaymentReports.isNotEmpty) {
        return customerPaymentReports;
      } else {
        return null;
      }
    } else {
      return null;
    }
  }

  Future<List<ContactPaymentModel>?> getSupplierPaymentReports(
      {DateTime? startDate, DateTime? endDate}) async {
    List<Map<String, dynamic>>? jsonList = await _we2upAPI.getPaymentReports(
        supplier: true, startDate: startDate, endDate: endDate);
    if (jsonList != null && jsonList.isNotEmpty) {
      final List<ContactPaymentModel> supplierPaymentReports = [];
      for (Map<String, dynamic> json in jsonList) {
        try {
          final contactPaymentModel = ContactPaymentModel.fromJson(json);
          supplierPaymentReports.add(contactPaymentModel);
        } catch (e, stack) {
          logAppError(
            "Error converting supplier payment report: ${json['id'] ?? json}",
            e,
            stack,
          );
        }
      }

      if (supplierPaymentReports.isNotEmpty) {
        return supplierPaymentReports;
      } else {
        return null;
      }
    } else {
      return null;
    }
  }

  Future<List<SellReturn>?> getSellsReturns({
    DateTime? startDate,
    DateTime? endDate,
    required bool summary,
    int? page,
  }) async {
    List<Map<String, dynamic>>? jsonList = await _we2upAPI.getSellsReturnList(
      startDate: startDate,
      endDate: endDate,
      summary: summary,
      page: page,
    );
    if (jsonList != null && jsonList.isNotEmpty) {
      final List<SellReturn> sellsReturns = [];
      for (Map<String, dynamic> json in jsonList) {
        try {
          final sellReturn = SellReturn.fromJson(json);
          sellsReturns.add(sellReturn);
        } catch (e, stack) {
          logAppError(
            "Error converting sell return: ${json['id'] ?? json}",
            e,
            stack,
          );
        }
      }

      if (sellsReturns.isNotEmpty) {
        return sellsReturns;
      } else {
        return null;
      }
    } else {
      return null;
    }
  }

  Future<List<PurchaseReturn>?> getPurchasesReturns({
    DateTime? startDate,
    DateTime? endDate,
    required bool summary,
    int? page,
  }) async {
    List<Map<String, dynamic>>? jsonList =
        await _we2upAPI.getPurchasesReturnList(
      startDate: startDate,
      endDate: endDate,
      summary: summary,
      page: page,
    );
    if (jsonList != null && jsonList.isNotEmpty) {
      final List<PurchaseReturn> purchasesReturns = [];
      for (Map<String, dynamic> json in jsonList) {
        try {
          final purchaseReturn = PurchaseReturn.fromJson(json);
          purchasesReturns.add(purchaseReturn);
        } catch (e, stack) {
          logAppError(
            "Error converting purchase return: ${json['id'] ?? json}",
            e,
            stack,
          );
        }
      }

      if (purchasesReturns.isNotEmpty) {
        return purchasesReturns;
      } else {
        return null;
      }
    } else {
      return null;
    }
  }

  Future<ProfitLossReport?> getProfitLossReport(ProfitLossParams params) async {
    Map<String, dynamic> json = await _we2upAPI.getProfitLossReport(params);
    if (json.isNotEmpty) {
      try {
        final profitLossReport = ProfitLossReport.fromJson(json);
        return profitLossReport;
      } catch (e, stack) {
        logAppError(
          "Error converting profit loss report: ${json['id'] ?? json}",
          e,
          stack,
        );
        return null;
      }
    } else {
      return null;
    }
  }

  Future<List<SellingPriceGroup>?> getSellingPriceGroups() async {
    List<Map<String, dynamic>>? jsonList =
        await _we2upAPI.getSellingPriceGroupList();
    if (jsonList != null && jsonList.isNotEmpty) {
      final List<SellingPriceGroup> sellingPriceGroups = [];
      for (Map<String, dynamic> json in jsonList) {
        try {
          final sellingPriceGroup = SellingPriceGroup.fromJson(json);
          sellingPriceGroups.add(sellingPriceGroup);
        } catch (e, stack) {
          logAppError(
            "Error converting selling price group: ${json['id'] ?? json}",
            e,
            stack,
          );
        }
      }

      if (sellingPriceGroups.isNotEmpty) {
        return sellingPriceGroups;
      } else {
        return null;
      }
    } else {
      return null;
    }
  }

  Future<List<Expense>?> getExpenses({
    DateTime? startDate,
    DateTime? endDate,
    required bool summary,
    int? page,
  }) async {
    List<Expense>? eList = await _we2upAPI.getAllExpenses(
      startDate: startDate,
      endDate: endDate,
      summary: summary,
      page: page,
    );
    if (eList != null && eList.isNotEmpty) {
      return eList;
    } else {
      return null;
    }
  }

  Future<List<ExpenseCategory>?> getExpenseCategories() async {
    try {
      List<Map<String, dynamic>>? jsonList =
          await _we2upAPI.getExpensesCategoriesList();
      if (jsonList != null && jsonList.isNotEmpty) {
        final List<ExpenseCategory> expenseCategories = [];
        for (Map<String, dynamic> json in jsonList) {
          try {
            final expenseCategory = ExpenseCategory.fromJson(json);
            expenseCategories.add(expenseCategory);
          } catch (e, stack) {
            logAppError(
              "Error converting expense category: ${json['id'] ?? json}",
              e,
              stack,
            );
          }
        }
        if (expenseCategories.isNotEmpty) {
          return expenseCategories;
        } else {
          return null;
        }
      } else {
        return null;
      }
    } catch (e, stack) {
      logAppError(
        "Error fetching expense categories",
        e,
        stack,
      );
      return null;
    }
  }

  Future<List<Variation>?> getVariations() async {
    try {
      List<Map<String, dynamic>>? jsonList = await _we2upAPI.getVariationList();
      if (jsonList != null && jsonList.isNotEmpty) {
        final List<Variation> variations = [];
        for (Map<String, dynamic> json in jsonList) {
          try {
            final variation = Variation.fromJson(json);
            variations.add(variation);
          } catch (e, stack) {
            logAppError(
              "Error converting variation: ${json['id'] ?? json}",
              e,
              stack,
            );
          }
        }
        if (variations.isNotEmpty) {
          return variations;
        } else {
          return null;
        }
      } else {
        return null;
      }
    } catch (e, stack) {
      logAppError(
        "Error fetching variations",
        e,
        stack,
      );
      return null;
    }
  }

  Future<void> sendProductSell({required SellToAPI sell, int? sellID}) async {
    final bool internetConnected = await hasConnection();
    if (internetConnected) {
      final success = await _we2upAPI.sendSell(sellToAPI: sell, sellID: sellID);
      if (!success) {
        dataNotInSync();
        await _offlineManagement.sendSell(sellToAPI: sell, sellID: sellID);
      }
    } else {
      dataNotInSync();
      await _offlineManagement.sendSell(sellToAPI: sell, sellID: sellID);
    }
  }

  Future<void> sendProductPurchase(
      {required PurchaseToAPI purchase, int? purchaseID}) async {
    final bool internetConnected = await hasConnection();

    if (internetConnected) {
      final success = await _we2upAPI.sendPurchase(
          purchase: purchase, purchaseID: purchaseID);

      if (!success) {
        dataNotInSync();
        await _offlineManagement.sendPurchase(
            purchaseToAPI: purchase, purchaseID: purchaseID);
      }
    } else {
      dataNotInSync();
      await _offlineManagement.sendPurchase(
          purchaseToAPI: purchase, purchaseID: purchaseID);
    }
  }

  Future<void> sendFollowUp(
      {required FollowUpToAPI followUpToAPI, int? followUpID}) async {
    final bool internetConnected = await hasConnection();

    if (internetConnected) {
      final success = await _we2upAPI.sendFollowUp(
        followUpToAPI: followUpToAPI,
        followUpID: followUpID,
      );

      if (!success) {
        dataNotInSync();
        await _offlineManagement.sendFollowUp(
          followUpToAPI: followUpToAPI,
          followUpID: followUpID,
        );
      }
    } else {
      dataNotInSync();
      await _offlineManagement.sendFollowUp(
        followUpToAPI: followUpToAPI,
        followUpID: followUpID,
      );
    }
  }

  Future<bool?> deleteSell({required sellID, required bool offline}) async {
    if (offline) {
      final totalAmount = sellsBox.get(sellID)?.finalTotal ?? "0";
      final contactID = sellsBox.get(sellID)!.contactId;
      for (PaymentLine payment in (sellsBox.get(sellID)!.paymentLines ?? [])) {
        await paymentsReportBox.delete(payment.id);
      }
      await sellsBox.delete(sellID);
      final contact = contactsBox.get(contactID)!;
      final updatedContact = contact.copyWith(
        due: (double.parse(contact.due ?? "0") + double.parse(totalAmount))
            .toStringAsFixed(4),
      );
      contactsBox.put(contactID, updatedContact);
      return true;
    } else {
      return await _we2upAPI.deleteSell(sellID: sellID);
    }
  }

  Future<bool?> deletePurchase({
    required purchaseID,
    required bool offline,
  }) async {
    if (offline) {
      final purchase = purchasesBox.get(purchaseID);
      final totalAmount = purchase!.finalTotal ?? "0";
      final contactID = purchase.contactId;
      for (PaymentLine payment in (purchase.paymentLines ?? [])) {
        await paymentsReportBox.delete(payment.id);
      }
      await purchasesBox.delete(purchaseID);
      final contact = contactsBox.get(contactID)!;
      final updatedContact = contact.copyWith(
        due: (double.parse(contact.due ?? "0") + double.parse(totalAmount))
            .toStringAsFixed(4),
      );
      contactsBox.put(contactID, updatedContact);
      return true;
    } else {
      return await _we2upAPI.deletePurchase(purchaseID: purchaseID);
    }
  }

  Future<bool?> updateShipmentStatus({
    required int sellID,
    required String? status,
  }) async {
    return await _we2upAPI.sendShipmentStatus(sellID: sellID, status: status);
  }

  Future<void> sendSellReturn(
      {required SellReturnToAPI sellReturnToAPI, String? offlineID}) async {
    final bool internetConnected = await hasConnection();

    if (internetConnected) {
      final success =
          await _we2upAPI.sellReturn(sellReturnToAPI: sellReturnToAPI);

      if (!success) {
        dataNotInSync();
        await _offlineManagement.sendSellReturn(
          sellReturnToAPI: sellReturnToAPI,
          offlineID: offlineID,
        );
      }
    } else {
      dataNotInSync();
      await _offlineManagement.sendSellReturn(
        sellReturnToAPI: sellReturnToAPI,
        offlineID: offlineID,
      );
    }
  }

  Future<void> sendPurchaseReturn(
      {required PurchaseReturnToAPI purchaseReturnToAPI}) async {
    final bool internetConnected = await hasConnection();

    if (internetConnected) {
      final success = await _we2upAPI.purchaseReturn(
          purchaseReturnToAPI: purchaseReturnToAPI);

      if (!success) {
        dataNotInSync();
        await _offlineManagement.purchaseReturn(
            purchaseReturnToAPI: purchaseReturnToAPI);
      }
    } else {
      dataNotInSync();
      await _offlineManagement.purchaseReturn(
          purchaseReturnToAPI: purchaseReturnToAPI);
    }
  }

  Future<void> sendExpenseToAPI({
    required ExpenseToAPI expenseToAPI,
    int? expenseID,
  }) async {
    final bool internetConnected = await hasConnection();

    if (internetConnected) {
      final success = await _we2upAPI.sendExpense(
        expenseToAPI: expenseToAPI,
        expenseID: expenseID,
      );

      if (!success) {
        dataNotInSync();
        await _offlineManagement.sendExpense(
          expenseToAPI: expenseToAPI,
          expenseID: expenseID,
        );
      }
    } else {
      dataNotInSync();
      await _offlineManagement.sendExpense(
        expenseToAPI: expenseToAPI,
        expenseID: expenseID,
      );
    }
  }

  Future<void> sendContactPaymentToAPI({
    required Payment paymentToAPI,
    required int contactID,
    LocationInfo? locationInfo,
  }) async {
    if (!paymentToAPI.offline) {
      final success = await _we2upAPI.sendContactPayment(
        paymentToAPI: paymentToAPI,
        contactID: contactID,
        locationInfo: locationInfo,
      );

      if (!success) {
        dataNotInSync();
        await _offlineManagement.sendContactPayment(
          payment: paymentToAPI,
          contactID: contactID,
          locationInfo: locationInfo,
        );
      }
    } else {
      dataNotInSync();
      await _offlineManagement.sendContactPayment(
        payment: paymentToAPI,
        contactID: contactID,
        locationInfo: locationInfo,
      );
    }
  }

  Future<int> sendContactToAPI({
    required ContactToAPI contactToAPI,
    int? contactID,
  }) async {
    return await _we2upAPI.sendContactToAPI(
      contactToAPI: contactToAPI,
      contactID: contactID,
    );
  }

  Future<int> sendNewProduct({
    required NewProductToAPI newProductToAPI,
    required List<SelectedPriceGroupInfo> selectedPriceGroups,
  }) async {
    return await _we2upAPI.sendNewProduct(
      newProductToAPI: newProductToAPI,
      selectedPriceGroups: selectedPriceGroups,
    );
  }

  Future<void> getDefaultServiceGroup() async {
    var id = await _we2upAPI.fetchBusinessDetails();
    if (id != null && id != 0) {
      final newSettings = businessSettings.copyWith(defaultServiceId: id);
      await businessDetailsBox.put(businessSettingsDataKey, newSettings);
    }
  }

  Future<bool> sendCashRegister({
    required CashRegisterToAPI cashRegister,
  }) async {
    return await _we2upAPI.sendCashRegister(cashRegister: cashRegister);
  }

  Future<CashRegister?> closeCashRegister(
      {required CashRegisterToAPI cashRegister}) async {
    return await _we2upAPI.closeCashRegister(cashRegister: cashRegister);
  }

  Future<LocationInfo?> getCurrentLocation() async {
    final LocationManager appLocation = LocationManager.get();
    try {
      LocationPermission permission = await Geolocator.checkPermission();
      // Request location permission if not granted
      if (permission == LocationPermission.denied ||
          permission == LocationPermission.deniedForever) {
        permission = await Geolocator.requestPermission();
      }

      if (permission == LocationPermission.always ||
          permission == LocationPermission.whileInUse) {
        Position? position = await appLocation.determinePosition();
        if (position != null) {
          LocationInfo info = LocationInfo.fromJson({
            "longitude": position.longitude.toString(),
            "latitude": position.latitude.toString(),
          });
          return info;
        } else {
          return null;
        }
      } else {
        return null;
      }
    } catch (e) {
      log("Error occurred while getting location: $e");
      return null;
    }
  }

  Future<bool> syncOfflineDataWithAPI({BuildContext? context}) async {
    if (stopSyncingTransactions()){
      return false;
    }
    if (context != null) {
      EasyLoading.show(
        status: AppLocalizations.of(context)!.syncingOfflineData,
      );
    }
    final bool internetConnected = await hasConnection();
    if (internetConnected) {
      try {
        // sells
        for (var sell in sellsBox.values.where((element) => element.offline)) {
          await _we2upAPI
              .sendSell(sellToAPI: sell.sellToAPI!, sellID: sell.id)
              .then((v) => v ? sellsBox.delete(sell.invoiceNo) : null);
        }
        // purchases
        for (var purchase in purchasesBox.values.where(
          (element) => element.offline,
        )) {
          await _we2upAPI
              .sendPurchase(
                purchase: purchase.purchaseToAPI!,
                purchaseID: purchase.id,
              )
              .then((v) => v ? purchasesBox.delete(purchase.refNo) : null);
        }
        for (var sr in sellsReturnsBox.values.where((e) => e.offline)) {
          await _we2upAPI
              .sellReturn(sellReturnToAPI: sr.sellReturnToAPI!)
              .then((v) => v ? sellsReturnsBox.delete(sr.invoiceNo) : null);
        }
        for (var pReturn in purchaseReturnsBox.values.where((e) => e.offline)) {
          await _we2upAPI
              .purchaseReturn(purchaseReturnToAPI: pReturn.purchaseReturnToAPI!)
              .then(
                (v) => v
                    ? purchaseReturnsBox
                        .delete(pReturn.purchaseReturnToAPI!.transactionId)
                    : null,
              );
        }
        // expenses
        for (var expense in expensesBox.values.where((e) => e.offline)) {
          await _we2upAPI
              .sendExpense(
                expenseToAPI: expense.expenseToAPI!,
                expenseID: expense.id,
              )
              .then((v) => v ? expensesBox.delete(expense.refNo) : null);
        }
        // follow ups
        for (var followup in followUpBox.values.where((f) => f.offline)) {
          await _we2upAPI
              .sendFollowUp(
                followUpToAPI: followup.followUpToAPI!,
                followUpID: followup.id,
              )
              .then((v) => v ? expensesBox.delete(followup.refNo) : null);
        }
        // contact payment
        for (var cp in paymentsReportBox.values.where((cp) => cp.offline)) {
          await _we2upAPI
              .sendContactPayment(
                paymentToAPI: cp.payment!,
                contactID: cp.contactId,
                locationInfo: cp.locationInfo,
              )
              .then((v) => v ? paymentsReportBox.delete(cp.refNo) : null);
        }
        if (context != null) {
          // ignore: use_build_context_synchronously
          ProductsCubit.get(context).getFilteredSales(refresh: true);
          // ignore: use_build_context_synchronously
          ProductsCubit.get(context).updateProducts();
        }
        if (context != null) {
          // ignore: use_build_context_synchronously
          LandingCubit.get(context).updateLandingScreen();
        }
        if (context != null) {
          // ignore: use_build_context_synchronously
          EasyLoading.showSuccess(AppLocalizations.of(context)!.syncDone);
        }
        if (sellsReturnsBox.values.where((e) => e.offline).isEmpty &&
            sellsBox.values.where((element) => element.offline).isEmpty &&
            purchasesBox.values.where((element) => element.offline).isEmpty &&
            purchaseReturnsBox.values.where((e) => e.offline).isEmpty &&
            expensesBox.values.where((e) => e.offline).isEmpty &&
            followUpBox.values.where((f) => f.offline).isEmpty &&
            paymentsReportBox.values.where((cp) => cp.offline).isEmpty) {
          loginData.inSync = true;
          loginData.save();
          return true;
        } else {
          final troubleSaleReturn = sellsReturnsBox.values
              .where((e) => e.offline)
              .firstOrNull
              ?.sellReturnToAPI
              ?.toJson();
          final troubleSale = sellsBox.values
              .where((e) => e.offline)
              .firstOrNull
              ?.sellToAPI
              ?.toJson();
          final troublePurchase = purchasesBox.values
              .where((e) => e.offline)
              .firstOrNull
              ?.purchaseToAPI
              ?.toJson();
          final troublePReturn = purchaseReturnsBox.values
              .where((e) => e.offline)
              .firstOrNull
              ?.purchaseReturnToAPI
              ?.toJson();
          final troubleExpense = expensesBox.values
              .where((e) => e.offline)
              .firstOrNull
              ?.expenseToAPI
              ?.toJson();
          final troubleFollowUp = followUpBox.values
              .where((e) => e.offline)
              .firstOrNull
              ?.followUpToAPI
              ?.toJson();
          final troublePayment = paymentsReportBox.values
              .where((e) => e.offline)
              .map((e) => {
                    ...?e.payment?.toJson(),
                    'contact_id': e.contactId,
                    'location_info': e.locationInfo,
                  })
              .firstOrNull;
          logAppError(
            "An Offline transaction can't be finished.",
            {
              "username": loginData.username,
              "troubleSaleReturn": troubleSaleReturn,
              "troubleSale": troubleSale,
              "troublePurchase": troublePurchase,
              "troublePReturn": troublePReturn,
              "troubleExpense": troubleExpense,
              "troubleFollowUp": troubleFollowUp,
              "troublePayment": troublePayment,
            },
            null,
          );
          loginData.inSync = false;
          loginData.save();
          if (context != null) {
            // ignore: use_build_context_synchronously
            final strings = AppLocalizations.of(context)!;
            await showDialog(
              // ignore: use_build_context_synchronously
              context: context,
              builder: (context) {
                return AlertDialog(
                  title: const Text("sync problem"),
                  content: Text(strings.sync_problem_message),
                );
              },
            );
          }
          return false;
        }
      } catch (e) {
        log("ERROR WHEN SYNCING: $e");
        EasyLoading.showError("$e");
        return false;
      }
    } else {
      if (context != null) {
        // ignore: use_build_context_synchronously
        EasyLoading.showError(AppLocalizations.of(context)!.offlineMessage);
      }

      return false;
    }
  }
}
