import 'dart:async';
import 'dart:convert';
import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:dio_smart_retry/dio_smart_retry.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:http_parser/http_parser.dart';
import 'package:logger/logger.dart';
import 'package:restart_app/restart_app.dart';
import 'package:talker_dio_logger/talker_dio_logger_interceptor.dart';
import 'package:talker_dio_logger/talker_dio_logger_settings.dart';
import 'package:we2up/data/models/business_location.dart';
import 'package:we2up/data/models/cash_register.dart';
import 'package:we2up/data/models/cash_register_to_api.dart';
import 'package:we2up/data/models/clock.dart';
import 'package:we2up/data/models/expense_to_api.dart';
import 'package:we2up/data/models/follow_up.dart';
import 'package:we2up/data/models/follow_up_to_api.dart';
import 'package:we2up/data/models/location_info.dart';
import 'package:we2up/data/models/new_product_to_api.dart';
import 'package:we2up/data/models/payment.dart';
import 'package:we2up/data/models/purchase.dart';
import 'package:we2up/data/models/purchase_return.dart';
import 'package:we2up/data/models/purchase_return_to_api.dart';
import 'package:we2up/data/models/sell_return.dart';
import 'package:we2up/data/models/sell_return_to_api.dart';
import 'package:we2up/data/models/sell_to_api.dart';
import 'package:we2up/data/models/user_model.dart';

import '../../main.dart';
import '../../utils/custom_router.dart';
import '../../utils/we2up_constants.dart';
import '../db/db_manager.dart';
import '../models/base_url.dart';
import '../models/business_settings.dart';
import '../models/contact.dart';
import '../models/contact_payment_model.dart';
import '../models/contact_to_api.dart';
import '../models/expense.dart';
import '../models/product.dart';
import '../models/profit_loss_params.dart';
import '../models/purchase_to_api.dart';
import '../models/selected_price_group_info.dart';
import '../models/sell.dart';
import '../models/user_loggedin_data.dart';
import '../models/user_permissions.dart';

const int itemsPerPage = 100;

class We2upAPI {
  late Dio dio;

  We2upAPI() {
    BaseOptions options = BaseOptions(
      receiveDataWhenStatusError: true,
      connectTimeout: const Duration(seconds: 20),
      receiveTimeout: const Duration(seconds: 40),
    );
    dio = Dio(options);
    dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) {
          if (stopSyncingTransactions()) {
            return handler.reject(
              DioException(
                requestOptions: options,
                type: DioExceptionType.cancel,
                error: 'Transactions stopped by stopSyncingTransactions.',
              ),
            );
          }
          handler.next(options);
        },
      ),
    );
    dio.interceptors.add(
      RetryInterceptor(
        dio: dio,
        logPrint: log,
        retries: 1,
        retryableExtraStatuses: {status403Forbidden},
        retryDelays: const [Duration(seconds: 10)],
      ),
    );
    dio.interceptors.add(
      TalkerDioLogger(
        settings: const TalkerDioLoggerSettings(
          printRequestHeaders: false,
          printResponseHeaders: false,
          printResponseMessage: false,
          printResponseData: false,
        ),
      ),
    );
  }

  Null handleError(e, String methodName, {bool isLogout = false}) {
    if (e is DioException && e.response?.statusCode == 401 && !isLogout) {
      logout().then((loggedOut) async {
        if (loggedOut) {
          EasyLoading.showToast(
            "تم تسجيل الدخول من جهاز اخر .. سيتم تسجيل خروجك من هذا الجهاز.",
            maskType: EasyLoadingMaskType.clear,
            dismissOnTap: true,
          );
          await Future.delayed(const Duration(seconds: 3));
          loginData.resetValues();
          loginData.save();
          if (defaultTargetPlatform == TargetPlatform.windows || defaultTargetPlatform == TargetPlatform.macOS) {
            EasyLoading.dismiss();
            runApp(MyApp(appRouter: AppRouter()));
          } else {
            EasyLoading.dismiss();
            Restart.restartApp();
          }
        } else {
          EasyLoading.showError("Error!");
        }
      });
    }
    if (e is DioException && e.response != null) {
      if (defaultTargetPlatform != TargetPlatform.windows && defaultTargetPlatform != TargetPlatform.macOS) {
        logAppError(
          "API ERROR in method: $methodName",
          {
            "Response statusCode": e.response?.statusCode,
            "Response data": e.response?.data,
            "Response headers": e.response?.headers,
          },
          e.stackTrace,
        );
      }
    } else {
      if (defaultTargetPlatform != TargetPlatform.windows && defaultTargetPlatform != TargetPlatform.macOS) {
        logAppError(
          "APP ERROR in method: $methodName",
          {
            "Response statusCode": e.toString(),
          },
          e.stackTrace,
        );
      }
    }
    return null;
  }

  Map<String, String> we2upHeaders() {
    return {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Authorization': 'Bearer ${loginData.accessToken}'
    };
  }

  Options getOptions() {
    return Options(method: 'GET', headers: we2upHeaders());
  }

  BaseUrl _baseUrl() => credentialsBox.get(baseUrl);

  Future<bool> auth(
      {required String username, required String password}) async {
    FormData data = FormData.fromMap({
      'grant_type': 'password',
      'client_id': _baseUrl().id,
      'client_secret': _baseUrl().secret,
      'username': username,
      'password': password,
    });
    try {
      Response response = await dio.request(
        "${_baseUrl().url}oauth/token",
        data: data,
        options: Options(method: 'POST'),
      );
      if (response.statusCode == 200) {
        loginData.accessToken = response.data['access_token'];
        loginData.expiresIn = response.data['expires_in'];
        loginData.accessToken = response.data['access_token'];
        loginData.refreshToken = response.data['refresh_token'];
        loginData.username = username;
        loginData.password = password;
        loginData.save();
        return true;
      } else {
        return false;
      }
    } catch (e) {
      handleError(e, "auth");
      return false;
    }
  }

  Future<bool> logout() async {
    Options options = Options(headers: we2upHeaders(), method: 'POST');
    try {
      Response response = await dio.post(
        "${_baseUrl().url}/connector/api/logout",
        options: options,
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        return true;
      } else {
        log("logout response statusCode: ${response.statusCode}");
        return false;
      }
    } catch (e) {
      handleError(e, "logout", isLogout: true);
      return true;
    }
  }

  Future<bool?> doesUserHaveToken(
      {required String username, required String password}) async {
    Options options = Options(headers: we2upHeaders(), method: 'POST');
    try {
      Response response = await dio.post(
        "${_baseUrl().url}public/connector/api/is-user-have-token",
        options: options,
        data: {'username': username, 'password': password},
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        return response.data["isTokenExist"];
      } else {
        log("logout response statusCode: ${response.statusCode}");
        return null;
      }
    } catch (e) {
      handleError(e, "logout");
      return null;
    }
  }

  Future<bool> fetchLoggedData() async {
    try {
      Response response = await dio.get(
        "${_baseUrl().url}public/connector/api/user/loggedin",
        options: getOptions(),
      );
      if (response.statusCode == 200) {
        final data = UserLoggedinData.fromJson(response.data);
        loginData.userId = data.id;
        loginData.allowOverSelling = data.overSelling;
        loginData.locationRequired = data.locationRequired;
        loginData.defaultAccount = data.defaultAccount;
        loginData.permittedLocations = data.permittedLocations;
        loginData.save();
        return true;
      }
      return false;
    } catch (e) {
      handleError(e, "fetchLoggedData");
      return false;
    }
  }

  Future<bool> fetchRoles() async {
    try {
      Response response = await dio.get(
        "${_baseUrl().url}public/connector/api/roles",
        options: getOptions(),
      );
      if (response.statusCode == 200) {
        final permissions = UserPermissions.fromPermissionsList(
          (response.data["permissions"] ?? []).cast<String>(),
        );
        userAndPassBox.put("oldPermissions", permissions);
        loginData.isAdmin = response.data["is_admin"] ?? false;
        loginData.permissions = permissions;
        loginData.save();
        return true;
      }
      return false;
    } catch (e) {
      handleError(e, "fetchRoles");
      return false;
    }
  }

  Future<UserPermissions> getOldPermissions() async {
    try {
      Response response = await dio.get(
        "${_baseUrl().url}public/connector/api/roles",
        options: getOptions(),
      );
      if (response.statusCode == 200) {
        return UserPermissions.fromPermissionsList(
          (response.data["permissions"] ?? []).cast<String>(),
        );
      }
      return UserPermissions.fromPermissionsList(const []);
    } catch (e) {
      handleError(e, "getOldPermissions");
      return UserPermissions.fromPermissionsList(const []);
    }
  }

  Future<Map<String, dynamic>?> fetchShiftDetails(int id) async {
    try {
      Response response = await dio.get(
        "${_baseUrl().url}public/connector/api/cash-register-total/$id",
        options: getOptions(),
      );
      if (response.statusCode == 200) {
        return response.data as Map<String, dynamic>;
      }
      return null;
    } catch (e) {
      handleError(e, "fetchShiftDetails");
      return null;
    }
  }

  Future<Map<String, dynamic>?> fetchRangeSummaryDetails({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      Response response = await dio.get(
        "${_baseUrl().url}public/connector/api/cash-register-get-all-total",
        options: getOptions(),
        queryParameters: {
          if (startDate != null)
            "start_date": dateToString(startDate, report: true),
          if (endDate != null) "end_date": dateToString(endDate, report: true),
        },
      );
      if (response.statusCode == 200) {
        return response.data as Map<String, dynamic>;
      }
      return null;
    } catch (e) {
      handleError(e, "fetchRangeSummaryDetails");
      return null;
    }
  }

  Future<bool> updateCurrentLocation({LocationInfo? info}) async {
    Map<String, dynamic> data = {};
    data["location_info"] = info?.toJson();
    try {
      Options options = Options(headers: we2upHeaders(), method: 'POST');
      Response response = await dio.post(
        "${_baseUrl().url}public/connector/api/updateCurrentLocation",
        data: data,
        options: options,
      );

      if (response.statusCode == 201 || response.statusCode == 200) {
        final UserModel um = UserModel.fromJson(response.data["data"][0]);
        usersBox.put(um.id, um);
        return true;
      } else {
        return false;
      }
    } catch (e) {
      handleError(e, "updateCurrentLocation");
      return false;
    }
  }

  Future<bool> updateBusinessLocation({
    required LocationInfo position,
    required int locationId,
  }) async {
    Map<String, dynamic> data = {};
    data["location_info"] = position.toJson();
    data["location_id"] = locationId;
    try {
      Options options = Options(headers: we2upHeaders(), method: 'POST');
      Response response = await dio.post(
        "${_baseUrl().url}public/connector/api/business-location-update",
        data: data,
        options: options,
      );

      if (response.statusCode == 201 || response.statusCode == 200) {
        final BusinessLocation bl = BusinessLocation.fromJson(
          response.data["data"][0],
        );
        businessLocationsBox.put(bl.id, bl);
        return bl.id == locationId;
      } else {
        return false;
      }
    } catch (e) {
      handleError(e, "updateBusinessLocation");
      return false;
    }
  }

  /// Attendance management start

  Future<ClockResponse> clock(
      {required Clock clock, required bool clockIn}) async {
    Map<String, dynamic> data =
        clockIn ? clock.clockInToJson() : clock.clockOutToJson();
    try {
      String endPoint = clockIn ? "clock-in" : "clock-out";
      Response response = await dio.post(
        "${_baseUrl().url}public/connector/api/$endPoint",
        data: data,
        options: Options(headers: we2upHeaders(), method: 'POST'),
      );
      return ClockResponse(
        success: response.data['success'],
        message: response.data['msg'],
        type: response.data['type'],
        currentShift: response.data['current_shift'],
      );
    } catch (e) {
      handleError(e, "clock");
      final errorMessage = e is DioException
          ? e.response?.data['error']['message']
          : e.toString();
      return ClockResponse(
        success: false,
        message: errorMessage,
      );
    }
  }

  /// Attendance management END

  Future<bool> sendSell({
    required SellToAPI sellToAPI,
    int? sellID,
    bool isSync = false,
  }) async {
    Map<String, dynamic> data = sellToAPI.toJson();
    Logger().d(data);
    String editID = sellID != null ? "/$sellID" : "";
    String method = sellID != null ? "PUT" : "POST";
    Options options = Options(headers: we2upHeaders(), method: method);
    try {
      Response response = sellID == null
          ? await dio.post("${_baseUrl().url}public/connector/api/sell",
              data: jsonEncode({
                'sells': [data]
              }),
              options: options)
          : await dio.put(
              "${_baseUrl().url}public/connector/api/sell$editID",
              data: data,
              options: options,
            );

      final Sell sell =
          Sell.fromJson(updateProductIdsInSellLines(response.data[0][0]));
      final contact = Contact.fromJson(response.data[0][0]["contact"]);
      final mergedContact = contactsBox.get(contact.id)!.merge(contact);
      if (sell.id != null) {
        Logger().i(sell.toJson());
        sellsBox.put(sell.id!, sell);
        contactsBox.put(contact.id, mergedContact);
        savePaymentsInReports(sell.paymentLines);
      }
      return sell.createdBy == loginData.userId;
    } catch (e) {
      handleError(e, "sendSell");
      return false;
    }
  }

  Future<bool> sellReturn({required SellReturnToAPI sellReturnToAPI}) async {
    Map<String, dynamic> data = sellReturnToAPI.toJson();
    try {
      Options options = Options(headers: we2upHeaders(), method: 'POST');

      Response response = await dio.post(
        "${_baseUrl().url}public/connector/api/sell-return",
        data: data,
        options: options,
      );

      if (response.statusCode == 201 || response.statusCode == 200) {
        final SellReturn sr = SellReturn.fromJson(response.data);
        sellsReturnsBox.put(sr.id, sr);
        if (sr.returnParentSell != null) {
          sellsBox.put(sr.returnParentSell!.id, sr.returnParentSell!);
        }
        final contact = Contact.fromJson(response.data["contact"]);
        final mergedContact = contactsBox.get(contact.id)!.merge(contact);
        contactsBox.put(contact.id, mergedContact);
        return sr.createdBy != null;
      } else {
        return false;
      }
    } catch (e) {
      handleError(e, "sellReturn");
      return false;
    }
  }

  Future<bool?> deleteSell({required int sellID}) async {
    try {
      Response response = await dio.delete(
        "${_baseUrl().url}public/connector/api/sell/$sellID",
        options: Options(headers: we2upHeaders(), method: 'DELETE'),
      );
      if (response.statusCode == 200) {
        final contactId = sellsBox.get(sellID)!.contactId!;
        for (PaymentLine p in (sellsBox.get(sellID)!.paymentLines ?? [])) {
          await paymentsReportBox.delete(p.id);
        }
        await sellsBox.delete(sellID);
        bool updatedContact = await getSpecifiedContact(contactId);
        if (updatedContact) {
          return true;
        } else {
          return false;
        }
      } else {
        return false;
      }
    } catch (e) {
      handleError(e, "deleteSell");
      return null;
    }
  }

  Future<int?> fetchBusinessDetails() async {
    try {
      Response response = await dio.get(
        "${_baseUrl().url}public/connector/api/business-details",
        options: Options(headers: we2upHeaders(), method: 'GET'),
      );
      if (response.statusCode == 200) {
        final data = response.data["data"];
        if (data != null) {
          loginData.serviceStaffAllowed =
              data["enabled_modules"].contains("service_staff");
          loginData.tablesAllowed = data["enabled_modules"].contains("tables");
          loginData.commAgntAllowed = data['sales_cmsn_agnt'];
          loginData.transactionEditDays = data['transaction_edit_days'];
          loginData.save();
        }
        if (data != null &&
            data['pos_settings'] != null &&
            data['pos_settings']['is_commission_agent_required'] != null) {
          loginData.isCommissionAgentRequired =
              data['pos_settings']['is_commission_agent_required'] == "1";
          loginData.save();
        }
        if (data != null &&
            data['pos_settings'] != null &&
            data['pos_settings']['defalut_service_id'] != null) {
          final id = data['pos_settings']['defalut_service_id'];
          return int.tryParse(id.toString());
        } else {
          return null;
        }
      } else {
        return null;
      }
    } catch (e) {
      handleError(e, "fetchBusinessDetails");
      return null;
    }
  }

  Future<bool> sendExpense({
    required ExpenseToAPI expenseToAPI,
    int? expenseID,
  }) async {
    String editID = expenseID != null ? "/$expenseID" : "";
    String method = expenseID != null ? "PUT" : "POST";
    Options options = Options(headers: we2upHeaders(), method: method);
    Map<String, dynamic> data = expenseToAPI.toJson();
    try {
      Response response = expenseID == null
          ? await dio.post(
              "${_baseUrl().url}public/connector/api/expense$editID",
              data: data,
              options: options)
          : await dio.put(
              "${_baseUrl().url}public/connector/api/expense$editID",
              data: data,
              options: options);
      if (response.statusCode == 201 || response.statusCode == 200) {
        Map<String, dynamic> data = response.data["data"];
        data["isRefund"] = expenseToAPI.isRefund;
        final Expense expense = Expense.fromJson(response.data["data"]);
        expensesBox.put(expense.id, expense);
        if (response.data["data"]["contact"] is Map) {
          final contact = Contact.fromJson(response.data["data"]["contact"]);
          final mergedContact = contactsBox.get(contact.id)!.merge(contact);
          contactsBox.put(contact.id, mergedContact);
        }
        return expense.createdBy != null;
      } else {
        return false;
      }
    } catch (e) {
      handleError(e, "sendExpense");
      return false;
    }
  }

  Stream<List<Map<String, dynamic>>> getProductsStream(
      {DateTime? lastUpdated}) async* {
    try {
      List<Map<String, dynamic>> allProducts = [];
      int currentPage = 1;

      while (true) {
        final response = await dio.get(
          "${_baseUrl().url}public/connector/api/product",
          options: getOptions(),
          queryParameters: {
            "page": currentPage,
            "selling_price_group": 1,
            "per_page": 1000,
            "forStore": 1,
            if (lastUpdated != null) "last_updated": dateToString(lastUpdated),
          },
        );

        if (response.statusCode == 200) {
          final List<Map<String, dynamic>> products =
              List<Map<String, dynamic>>.from(response.data['data']);

          // Call the function to create clones
          allProducts.addAll(createClones(products));

          yield List.from(allProducts);

          final lastPage = response.data['meta']['last_page'];
          if (currentPage >= lastPage) {
            break;
          } else {
            currentPage++;
            await Future.delayed(const Duration(seconds: 5));
          }
        } else {
          break;
        }
      }
    } catch (e) {
      handleError(e, "getProductStream");
    }
  }

  List<Map<String, dynamic>> createClones(originalData) {
    List<Map<String, dynamic>> clones = [];

    for (var product in originalData) {
      var productVariations = product['product_variations'];
      if (product['type'] == "variable") {
        // Set type to single
        product['type'] = "single";
      }
      if (productVariations != null && productVariations.isNotEmpty) {
        var variations = productVariations[0]['variations'];

        if (variations != null && variations.length > 1) {
          for (int i = 0; i < variations.length; i++) {
            var variation = variations[i];

            // Create a new clone structure
            var clone = Map<String, dynamic>.from(product);

            // Clone product_variations with all fields
            clone['product_variations'] = [
              Map<String, dynamic>.from(productVariations[0])
            ];

            // Set variations field within the cloned product_variations
            clone['product_variations'][0]
                ['variations'] = [Map<String, dynamic>.from(variation)];

            clone['id'] = int.parse("${product['id']}$i");

            if (variation['media'] != null && variation['media'].isNotEmpty) {
              clone['image_url'] = variation['media'][0]['display_url'];
            }

            clone['name'] = clone['name'] +
                " (${clone['product_variations'][0]['variations'][0]["name"]})";

            clones.add(clone);
          }
        } else {
          product['id'] = int.parse("${product['id']}9");
          clones.add(Map<String, dynamic>.from(product));
        }
      } else {
        product['id'] = int.parse("${product['id']}9");
        clones.add(Map<String, dynamic>.from(product));
      }
    }
    return clones;
  }

  Future<List<Map<String, dynamic>>?> getCategoriesList() async {
    try {
      final response = await dio.get(
        "${_baseUrl().url}public/connector/api/taxonomy",
        options: getOptions(),
      );
      if (response.statusCode == 200) {
        return List<Map<String, dynamic>>.from(response.data['data']);
      } else {
        return [];
      }
    } catch (e) {
      handleError(e, "getCategoriesList");
      return null;
    }
  }

  Future<List<Map<String, dynamic>>?> getUsersList({
    bool serviceStaff = false,
  }) async {
    try {
      final response = await dio.get(
        "${_baseUrl().url}public/connector/api/user",
        options: getOptions(),
        queryParameters: {'service_staff': serviceStaff ? 1 : 0},
      );
      if (response.statusCode == 200) {
        return List<Map<String, dynamic>>.from(response.data['data']);
      } else {
        return [];
      }
    } catch (e) {
      handleError(e, "getUsersList");
      return null;
    }
  }

  Future<List<Map<String, dynamic>>?> getCommAgents() async {
    try {
      final response = await dio.get(
        "${_baseUrl().url}public/connector/api/comm-agents",
        options: getOptions(),
      );
      if (response.statusCode == 200) {
        return List<Map<String, dynamic>>.from(response.data['data']);
      } else {
        return [];
      }
    } catch (e) {
      handleError(e, "getCommAgents");
      return null;
    }
  }

  Future<List<Map<String, dynamic>>?> getShippingCompanies() async {
    try {
      final response = await dio.get(
        "${_baseUrl().url}public/connector/api/get-shipping-companies",
        options: getOptions(),
      );
      if (response.statusCode == 200) {
        return List<Map<String, dynamic>>.from(response.data);
      } else {
        return [];
      }
    } catch (e) {
      handleError(e, "getShippingCompanies");
      return null;
    }
  }

  Future<List<Map<String, dynamic>>?> getTables() async {
    try {
      final response = await dio.get(
        "${_baseUrl().url}public/connector/api/table",
        options: getOptions(),
      );
      if (response.statusCode == 200) {
        return List<Map<String, dynamic>>.from(response.data['data']);
      } else {
        return [];
      }
    } catch (e) {
      handleError(e, "getTables");
      return null;
    }
  }

  Future<Map<String, dynamic>?> getItemsToDelete(DateTime lastUpdated) async {
    try {
      final response = await dio.get(
          "${_baseUrl().url}public/connector/api/deleted-items",
          options: getOptions(),
          queryParameters: {
            'last_updated': dateToString(lastUpdated, report: true),
          });
      if (response.statusCode == 200) {
        return response.data['data'];
      } else {
        return null;
      }
    } catch (e) {
      handleError(e, "getItemsToDelete");
      return null;
    }
  }

  Future<List<Map<String, dynamic>>?> getUnitsList() async {
    try {
      final response = await dio.get(
        "${_baseUrl().url}public/connector/api/unit",
        options: getOptions(),
      );
      if (response.statusCode == 200) {
        return List<Map<String, dynamic>>.from(response.data['data']);
      } else {
        return [];
      }
    } catch (e) {
      handleError(e, "getUnitsList");
      return null;
    }
  }

  Future<List<Map<String, dynamic>>?> getTaxRateList() async {
    try {
      final response = await Dio().get(
        "${_baseUrl().url}public/connector/api/tax",
        options: getOptions(),
      );

      if (response.statusCode == 200) {
        return List<Map<String, dynamic>>.from(response.data['data']);
      } else {
        return [];
      }
    } catch (e) {
      handleError(e, "getTaxRateList");
      return null;
    }
  }

  Future<List<Map<String, dynamic>>?> getBrandList() async {
    try {
      final response = await dio.get(
        "${_baseUrl().url}public/connector/api/brand",
        options: getOptions(),
      );

      if (response.statusCode == 200) {
        return List<Map<String, dynamic>>.from(response.data['data']);
      } else {
        return [];
      }
    } catch (e) {
      handleError(e, "getBrandList");
      return null;
    }
  }

  Future<List<Map<String, dynamic>>?> getBusinessLocationList() async {
    try {
      final response = await dio.get(
        "${_baseUrl().url}public/connector/api/business-location",
        options: getOptions(),
      );

      if (response.statusCode == 200) {
        return List<Map<String, dynamic>>.from(response.data['data']);
      } else {
        return [];
      }
    } catch (e) {
      handleError(e, "getBusinessLocationList");
      return null;
    }
  }

  Future<List<Map<String, dynamic>>?> getPaymentAccountList() async {
    try {
      final response = await Dio().get(
        "${_baseUrl().url}public/connector/api/payment-accounts",
        options: getOptions(),
      );

      if (response.statusCode == 200) {
        return List<Map<String, dynamic>>.from(response.data['data']);
      } else {
        return [];
      }
    } catch (e) {
      handleError(e, "getPaymentAccountList");
      return null;
    }
  }

  Future<List<Map<String, dynamic>>> getSellPage({
    DateTime? startDate,
    DateTime? endDate,
    DateTime? lastUpdated,
    required bool summary,
    int? page,
  }) async {
    final List<Map<String, dynamic>> allSells = [];
    try {
      int currentPage = page ?? 1;

      while (page == null || currentPage == page) {
        final queryParams = {
          "per_page": page != null ? 10 : itemsPerPage,
          "page": currentPage,
          if (startDate != null && lastUpdated == null)
            "start_date": dateToString(startDate, report: true),
          if (endDate != null && lastUpdated == null)
            "end_date": dateToString(endDate, report: true),
          if (lastUpdated != null) "last_updated": dateToString(lastUpdated),
          if (canViewOwnSellOnly()) "user_id": loginData.userId,
          'summery': summary ? 1 : 0,
          "order_by_date": "desc",
        };

        final response = await dio.get(
          "${_baseUrl().url}public/connector/api/sell",
          options: getOptions(),
          queryParameters: queryParams,
        );

        if (response.statusCode == 200) {
          final dynamic responseJson = json.decode(response.toString());
          final List<dynamic>? data = responseJson['data'];
          if (data != null && data.isNotEmpty) {
            allSells.addAll(data.cast<Map<String, dynamic>>());
          }

          if (page != null ||
              currentPage >= (responseJson['meta']?['last_page'] ?? 1)) {
            break; // Exit the loop if page is specified or all pages are fetched
          }

          currentPage++; // Move to the next page
        } else {
          return [];
        }
      }
      return allSells;
    } catch (e) {
      handleError(e, "getSellPage");
      return [];
    }
  }

  Future<List<Map<String, dynamic>>?> getPaymentReports({
    required bool supplier,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      List<Map<String, dynamic>> allPayments = [];
      int currentPage = 1;
      while (true) {
        final response = await dio.get(
          "${_baseUrl().url}public/connector/api/payment-report",
          options: getOptions(),
          queryParameters: {
            "per_page": 100,
            if (startDate != null)
              "start_date": dateToString(startDate, report: true),
            if (endDate != null)
              "end_date": dateToString(endDate, report: true),
            "type": supplier ? "supplier" : "customer",
            "page": currentPage,
          },
        );

        if (response.statusCode == 200) {
          final List<Map<String, dynamic>> pagePayments =
              List<Map<String, dynamic>>.from(response.data['data']);
          allPayments.addAll(pagePayments);

          final lastPage = response.data['meta']['last_page'];
          if (currentPage >= lastPage) {
            break; // Exit the loop when all pages have been fetched
          } else {
            currentPage++; // Move to the next page
          }
        } else {
          return [];
        }
      }

      return allPayments;
    } catch (e) {
      handleError(e, "getPaymentReports");
      return null;
    }
  }

  Future<List<Map<String, dynamic>>?> getSellsReturnList({
    DateTime? startDate,
    DateTime? endDate,
    required bool summary,
    int? page,
  }) async {
    try {
      List<Map<String, dynamic>> allSellsReturn = [];
      int currentPage = page ?? 1;

      while (page == null || currentPage == page) {
        final response = await dio.get(
          "${_baseUrl().url}public/connector/api/list-sell-return",
          options: getOptions(),
          queryParameters: {
            "per_page": page != null ? 10 : itemsPerPage,
            "page": currentPage,
            if (startDate != null)
              "start_date": dateToString(startDate, report: true),
            if (endDate != null)
              "end_date": dateToString(endDate, report: true),
            "summery": summary ? 1 : 0,
          },
        );

        if (response.statusCode == 200) {
          final List<Map<String, dynamic>> sellsReturn =
              List<Map<String, dynamic>>.from(response.data['data']);
          allSellsReturn.addAll(sellsReturn);

          if (page != null) {
            break; // Exit the loop if page is specified
          }

          final lastPage = response.data['meta']['last_page'];
          if (currentPage >= lastPage) {
            break; // Exit the loop when all pages have been fetched
          } else {
            currentPage++; // Move to the next page
          }
        } else {
          return [];
        }
      }

      return allSellsReturn;
    } catch (e) {
      handleError(e, "getSellsReturnList");
      return null;
    }
  }

  Stream<List<Map<String, dynamic>>> getContactsStream(
      {DateTime? lastUpdated}) async* {
    final controller = StreamController<List<Map<String, dynamic>>>();

    try {
      List<Map<String, dynamic>> allContacts = [];
      int currentPage = 1;

      while (true) {
        final response = await dio.get(
          "${_baseUrl().url}public/connector/api/contactapi?page=$currentPage",
          queryParameters: {
            "per_page": 1000,
            if (lastUpdated != null) "last_updated": dateToString(lastUpdated),
            if (canOnlyViewOwnSupplier() && canOnlyViewOwnCustomer())
              "user_id": loginData.userId,
          },
          options: getOptions(),
        );

        if (response.statusCode == 200) {
          final List<Map<String, dynamic>> contacts =
              List<Map<String, dynamic>>.from(response.data['data']);
          allContacts.addAll(contacts);

          controller.add(List.from(allContacts)); // Emit the current list

          final lastPage = response.data['meta']['last_page'];
          if (currentPage >= lastPage) {
            break;
          } else {
            currentPage++; // Move to the next page
            await Future.delayed(const Duration(seconds: 5));
          }
        } else {
          controller.addError("Failed to fetch contacts."); // Emit an error
          break;
        }
      }
    } catch (e) {
      handleError(e, "getContactListStream");
    } finally {
      controller.close(); // Close the stream when done
    }

    yield* controller.stream;
  }

  Future<bool> getSpecifiedContact(int id) async {
    try {
      final response = await Dio().get(
        "${_baseUrl().url}public/connector/api/contactapi/$id",
        options: getOptions(),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        final contact = Contact.fromJson(response.data["data"][0]);
        contactsBox.put(contact.id, contact);
        return true;
      } else {
        return false;
      }
    } catch (e) {
      handleError(e, "getSpecifiedContact");
      return false;
    }
  }

  Future<dynamic> getProfitLossReport(ProfitLossParams params) async {
    try {
      final response = await Dio().get(
        "${_baseUrl().url}public/connector/api/profit-loss-report",
        options: getOptions(),
        queryParameters: params.toJson(),
      );

      if (response.statusCode == 200) {
        return Map<String, dynamic>.from(response.data['data']);
      } else {
        return null;
      }
    } catch (e) {
      handleError(e, "getProfitLossReport");
      return null;
    }
  }

  Future<List<Map<String, dynamic>>?> getSellingPriceGroupList() async {
    try {
      final response = await dio.get(
        "${_baseUrl().url}public/connector/api/selling-price-group",
        options: getOptions(),
      );

      if (response.statusCode == 200) {
        return List<Map<String, dynamic>>.from(response.data['data']);
      } else {
        return [];
      }
    } catch (e) {
      handleError(e, "getSellingPriceGroupList");
      return null;
    }
  }

  Future<List<Map<String, dynamic>>?> getTypesOfService() async {
    try {
      final response = await dio.get(
        "${_baseUrl().url}public/connector/api/types-of-service",
        options: getOptions(),
      );

      if (response.statusCode == 200) {
        return List<Map<String, dynamic>>.from(response.data['data']);
      } else {
        return [];
      }
    } catch (e) {
      handleError(e, "getTypesOfService");
      return null;
    }
  }

  Future<List<Expense>?> getAllExpenses({
    DateTime? startDate,
    DateTime? endDate,
    required bool summary,
    int? page,
  }) async {
    final List<Expense> allExpenses = [];

    int currentPage = page ?? 1;
    bool hasRegularNextPage = true;
    bool hasRefundNextPage = true;

    try {
      while (hasRegularNextPage || hasRefundNextPage) {
        final List<Future<List<Expense>>> fetchFutures = [
          _fetchExpenses(
            "",
            currentPage,
            startDate: startDate,
            endDate: endDate,
            summary: summary,
          ),
          _fetchExpenses(
            "-refund",
            currentPage,
            startDate: startDate,
            endDate: endDate,
            summary: summary,
          ),
        ];

        final List<List<Expense>> fetchedPages =
            await Future.wait(fetchFutures);

        final List<Expense> regularExpenses = fetchedPages[0];
        final List<Expense> refundExpenses = fetchedPages[1];

        if (regularExpenses.isEmpty) {
          hasRegularNextPage = false;
        }
        if (refundExpenses.isEmpty) {
          hasRefundNextPage = false;
        }

        allExpenses.addAll(regularExpenses);
        allExpenses.addAll(refundExpenses);

        currentPage++;
      }

      allExpenses.sort((a, b) => a.refNo!.compareTo(b.refNo!));
      return allExpenses;
    } catch (e) {
      handleError(e, "getAllExpenses");
      return null;
    }
  }

  Future<List<Expense>> _fetchExpenses(String suffix, int page,
      {DateTime? startDate, DateTime? endDate, required bool summary}) async {
    try {
      final response = await dio.get(
        "${_baseUrl().url}public/connector/api/expense$suffix?page=$page",
        options: getOptions(),
        queryParameters: {
          "per_page": itemsPerPage,
          if (startDate != null)
            "start_date": dateToString(startDate, report: true),
          if (endDate != null) "end_date": dateToString(endDate, report: true),
          if (canViewOwnExpense()) "user_id": loginData.userId,
          "summery": summary ? 1 : 0,
        },
      );

      if (response.statusCode == 200) {
        var dataList = List<Map<String, dynamic>>.from(response.data['data']);
        final List<Expense> expenses = [];

        for (Map<String, dynamic> json in dataList) {
          try {
            final expense = Expense.fromJson(
              {'isRefund': suffix.isNotEmpty ? 1 : 0, ...json},
            );
            expenses.add(expense);
          } catch (e, stack) {
            logAppError(
                "Error converting expense: ${json['id'] ?? json}", e, stack);
          }
        }

        return expenses;
      } else {
        return [];
      }
    } catch (e, stack) {
      logAppError("Error fetching expenses", e, stack);
      return [];
    }
  }

  Future<List<Map<String, dynamic>>?> getExpensesCategoriesList() async {
    try {
      final response = await Dio().get(
        "${_baseUrl().url}public/connector/api/expense-categories",
        options: getOptions(),
      );

      if (response.statusCode == 200) {
        return List<Map<String, dynamic>>.from(response.data['data']);
      } else {
        return [];
      }
    } catch (e) {
      handleError(e, "getExpensesCategoriesList");
      return null;
    }
  }

  Future<List<Map<String, dynamic>>?> getVariationList() async {
    try {
      List<Map<String, dynamic>> allVariations = [];
      int currentPage = 1;

      while (true) {
        final response = await Dio().get(
          "${_baseUrl().url}public/connector/api/variation?page=$currentPage",
          options: getOptions(),
        );

        if (response.statusCode == 200) {
          final List<Map<String, dynamic>> variations =
              List<Map<String, dynamic>>.from(response.data['data']);
          allVariations.addAll(variations);

          final lastPage = response.data['meta']['last_page'];
          if (currentPage >= lastPage) {
            break; // Exit the loop when all pages have been fetched
          } else {
            currentPage++; // Move to the next page
          }
        } else {
          return [];
        }
      }

      return allVariations;
    } catch (e) {
      handleError(e, "getVariationList");
      return null;
    }
  }

  Future<List<Map<String, dynamic>>?> getCustomerGroups() async {
    try {
      List<Map<String, dynamic>> allVariations = [];
      int currentPage = 1;

      while (true) {
        final response = await Dio().get(
          "${_baseUrl().url}public/connector/api/customer-groups"
          "?page=$currentPage",
          options: getOptions(),
        );

        if (response.statusCode == 200) {
          final customerGroups =
              List<Map<String, dynamic>>.from(response.data['data']);
          allVariations.addAll(customerGroups);

          final lastPage = response.data['meta']['last_page'];
          if (currentPage >= lastPage) {
            break; // Exit the loop when all pages have been fetched
          } else {
            currentPage++; // Move to the next page
          }
        } else {
          return [];
        }
      }

      return allVariations;
    } catch (e) {
      handleError(e, "getCustomerGroups");
      return null;
    }
  }

  Future<bool> sendContactPayment({
    required int contactID,
    required Payment paymentToAPI,
    LocationInfo? locationInfo,
  }) async {
    Map<String, dynamic> dataToApi = paymentToAPI.toJson();
    dataToApi['contact_id'] = contactID;
    dataToApi['location_info'] = locationInfo?.toJson();
    try {
      Response response = await dio.post(
        "${_baseUrl().url}public/connector/api/contactapi-payment",
        data: dataToApi,
        options: Options(headers: we2upHeaders(), method: 'POST'),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        final contact = Contact.fromJson(response.data["data"]["contact"]);
        final mergedContact = contactsBox.get(contact.id)!.merge(contact);
        final payment =
            ContactPaymentModel.fromJson(response.data["data"]["payment"][0]);
        contactsBox.put(contact.id, mergedContact);
        paymentsReportBox.put(payment.id, payment);
        return contact.createdBy != null;
      } else {
        return false;
      }
    } catch (e) {
      handleError(e, "sendContactPayment");
      return false;
    }
  }

  Future<int> _sendPriceGroups({
    required List<SelectedPriceGroupInfo> selectedPriceGroups,
    required int variationId,
    required int productId,
  }) async {
    var priceGroupsList = selectedPriceGroups.map((info) {
      return {
        "variation_id": variationId,
        "price_group_id": info.priceGroup.id,
        "sell_price": double.tryParse(info.sellPrice.text) ?? 0,
      };
    }).toList();
    var data = {"product_id": productId, "price_groups": priceGroupsList};
    try {
      Response response = await dio.post(
        "${_baseUrl().url}public/connector/api/product-price-group",
        data: data,
        options: Options(headers: we2upHeaders(), method: 'POST'),
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        var clone = Map<String, dynamic>.from(response.data["data"][0]);
        clone['id'] = int.parse("${clone['id']}9");
        final Product product = Product.fromJson(clone);
        productsBox.put(product.id, product);
        return product.id;
      } else {
        return -1;
      }
    } catch (e) {
      handleError(e, "_sendPriceGroups");
      return -1;
    }
  }

  Future<int> sendNewProduct({
    required NewProductToAPI newProductToAPI,
    required List<SelectedPriceGroupInfo> selectedPriceGroups,
  }) async {
    Map<String, dynamic> dataToApi = newProductToAPI.toJson();

    String? imagePath = dataToApi['image'];
    dataToApi.remove('image');

    try {
      MultipartFile? imageFile;
      if (imagePath != null && imagePath.isNotEmpty) {
        RegExp regExp = RegExp(r"[^/\\&?]+\.\w{3,4}(?=([?&].*$|$))");
        String fileName = regExp.stringMatch(imagePath) ??
            '${DateTime.now().millisecondsSinceEpoch}.jpg';

        // Determine MIME type based on file extension
        String mimeType = '';
        if (fileName.endsWith('.jpg') || fileName.endsWith('.jpeg')) {
          mimeType = 'image/jpeg';
        } else if (fileName.endsWith('.png')) {
          mimeType = 'image/png';
        } else {
          // Default to application/octet-stream if the MIME type cannot be determined
          mimeType = 'application/octet-stream';
        }

        imageFile = await MultipartFile.fromFile(
          imagePath,
          filename: fileName,
          contentType: MediaType.parse(mimeType),
        );
      }

      FormData formData = FormData.fromMap(dataToApi);
      if (imageFile != null) {
        formData.files.add(MapEntry('image', imageFile));
      }

      // print("FormData fields:");
      // formData.fields.forEach((field) {
      //   print("${field.key}: ${field.value}");
      // });
      // formData.files.forEach((file) {
      //   print("File field: ${file.key}");
      //   print("File name: ${file.value.filename}");
      //   print("File contentType: ${file.value.contentType}");
      //   print("File headers: ${file.value.headers}");
      //   print("File length: ${file.value.length}");
      // });

      // File file = File(imagePath!);
      // List<int> fileBytes = await file.readAsBytes();
      //
      // // Convert to Base64 string
      // String base64Image = base64Encode(fileBytes);
      //
      // // Log the Base64 string
      // log(base64Image);

      Response response = await dio.post(
        "${_baseUrl().url}public/connector/api/product",
        data: formData,
        options: Options(headers: we2upHeaders(), method: 'POST'),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        log(response.data.toString());
        var clone = Map<String, dynamic>.from(response.data["data"][0]);
        if (selectedPriceGroups.isNotEmpty) {
          return await _sendPriceGroups(
            productId: clone['id'],
            variationId: clone['product_variations'][0]['variations'][0]['id'],
            selectedPriceGroups: selectedPriceGroups,
          );
        } else {
          clone['id'] = int.parse("${clone['id']}9");
          final Product product = Product.fromJson(clone);
          productsBox.put(product.id, product);
          return product.id;
        }
      } else {
        return -1;
      }
    } catch (e) {
      handleError(e, "sendNewProduct");
      return -1;
    }
  }

  Future<bool> sendCashRegister(
      {required CashRegisterToAPI cashRegister}) async {
    Map<String, dynamic> dataToApi = cashRegister.toJson();
    log(dataToApi.toString());
    try {
      Response response = await dio.post(
        "${_baseUrl().url}public/connector/api/cash-register"
        "${cashRegister.id != null ? "-close" : ""}",
        data: dataToApi,
        options: Options(headers: we2upHeaders(), method: 'POST'),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        final CashRegister cr = CashRegister.fromJson(response.data["data"]);
        cashRegistersBox.put(cr.id, cr);
        return true;
      } else {
        return false;
      }
    } catch (e) {
      handleError(e, "sendCashRegister");
      return false;
    }
  }

  Future<CashRegister?> closeCashRegister(
      {required CashRegisterToAPI cashRegister}) async {
    Map<String, dynamic> dataToApi = cashRegister.toJson();
    try {
      Response response = await dio.post(
        "${_baseUrl().url}public/connector/api/cash-register-close",
        data: dataToApi,
        options: Options(headers: we2upHeaders(), method: 'POST'),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        return CashRegister.fromJson(response.data["data"]);
      } else {
        return null;
      }
    } catch (e) {
      handleError(e, "closeCashRegister");
      return null;
    }
  }

  Future<bool?> sendShipmentStatus({
    required int sellID,
    required String? status,
  }) async {
    Map<String, dynamic> dataToApi = {"id": sellID, "shipping_status": status};
    log(dataToApi.toString());
    try {
      Response response = await dio.post(
        "${_baseUrl().url}public/connector/api/update-shipping-status",
        data: dataToApi,
        options: Options(headers: we2upHeaders(), method: 'POST'),
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        Response updatedSellResponse = await dio.get(
            "${_baseUrl().url}public/connector/api/sell/$sellID",
            options: Options(headers: we2upHeaders(), method: 'get'));
        Sell sell = Sell.fromJson(updatedSellResponse.data['data'][0]);
        sellsBox.put(sell.id, sell);
        return sell.createdBy != null;
      } else {
        return null;
      }
    } catch (e) {
      handleError(e, "sendShipmentStatus");
      return null;
    }
  }

  Future<int> sendContactToAPI({
    required ContactToAPI contactToAPI,
    int? contactID,
  }) async {
    Map<String, dynamic> data = contactToAPI.toJson();
    try {
      String editID = contactID != null ? "/$contactID" : "";
      String method = contactID != null ? "PUT" : "POST";
      Options options = Options(headers: we2upHeaders(), method: method);

      Response response = contactID == null
          ? await dio.post(
              "${_baseUrl().url}public/connector/api/contactapi$editID",
              data: data,
              options: options)
          : await dio.put(
              "${_baseUrl().url}public/connector/api/contactapi$editID",
              data: data,
              options: options);

      if (response.statusCode == 201 || response.statusCode == 200) {
        var contact = Contact.fromJson(response.data['data']);
        if (contactsBox.get(contact.id) != null) {
          contact = contactsBox.get(contact.id)!.merge(contact);
        }
        contactsBox.put(contact.id, contact);
        return contact.id;
      } else {
        return -1;
      }
    } catch (e) {
      handleError(e, "sendContactToAPI");
      return -1;
    }
  }

  /// Purchase Management
  Future<bool> sendPurchase(
      {required PurchaseToAPI purchase, int? purchaseID}) async {
    Map<String, dynamic> data = purchase.toJson();
    String editID = purchaseID != null ? "/$purchaseID" : "";
    String method = purchaseID != null ? "PUT" : "POST";
    Options options = Options(headers: we2upHeaders(), method: method);
    try {
      Response response = purchaseID == null
          ? await dio.post("${_baseUrl().url}public/connector/api/purchase",
              data: jsonEncode({
                'purchases': [data]
              }),
              options: options)
          : await dio.put(
              "${_baseUrl().url}public/connector/api/purchase$editID",
              data: data,
              options: options);

      if (response.statusCode == 201 || response.statusCode == 200) {
        final Purchase purchase = Purchase.fromJson(
            updateProductIdsInPurchaseLines(response.data[0][0]));
        final contact = Contact.fromJson(response.data[0][0]["contact"]);
        final mergedContact = contactsBox.get(contact.id)!.merge(contact);
        purchasesBox.put(purchase.id!, purchase);
        contactsBox.put(contact.id, mergedContact);
        savePaymentsInReports(purchase.paymentLines);
        return purchase.createdBy != null;
      } else {
        return false;
      }
    } catch (e) {
      handleError(e, "sendPurchase");
      return false;
    }
  }

  Future<List<Map<String, dynamic>>?> getPurchaseList({
    DateTime? startDate,
    DateTime? endDate,
    DateTime? lastUpdated,
    required bool summary,
    int? page,
  }) async {
    final List<Map<String, dynamic>> allPurchases = [];

    try {
      int currentPage = page ?? 1;

      while (page == null || currentPage == page) {
        final queryParams = {
          "per_page": page != null ? 10 : itemsPerPage,
          "page": currentPage,
          if (startDate != null && lastUpdated == null)
            "start_date": dateToString(startDate, report: true),
          if (endDate != null && lastUpdated == null)
            "end_date": dateToString(endDate, report: true),
          if (lastUpdated != null) "last_updated": dateToString(lastUpdated),
          if (canOnlyViewOwnPurchase()) "user_id": loginData.userId,
          'summery': summary ? 1 : 0,
          "order_by_date": "desc",
        };

        final response = await dio.get(
          "${_baseUrl().url}public/connector/api/purchase",
          options: getOptions(),
          queryParameters: queryParams,
        );

        if (response.statusCode == 200) {
          final dynamic responseJson = json.decode(response.toString());
          final List<dynamic>? data = responseJson['data'];
          if (data != null && data.isNotEmpty) {
            allPurchases.addAll(data.cast<Map<String, dynamic>>());
          }

          if (page != null ||
              currentPage >= (responseJson['meta']?['last_page'] ?? 1)) {
            break; // Exit the loop if page is specified or all pages are fetched
          }
          currentPage++; // Move to the next page
        } else {
          return null; // Handle the error case as needed
        }
      }
      return allPurchases;
    } catch (e) {
      handleError(e, "getPurchaseList");
      return null;
    }
  }

  Future<List<Map<String, dynamic>>?> getCashRegisterList() async {
    final List<Map<String, dynamic>> allCashRegisters = [];
    int currentPage = 1;

    try {
      while (true) {
        final response = await dio.get(
          "${_baseUrl().url}public/connector/api/cash-register?page=$currentPage",
          options: getOptions(),
          queryParameters: {
            "per_page": 2,
            "user_id": loginData.userId,
            "status": "open",
          },
        );

        if (response.statusCode == 200) {
          final dynamic responseJson = json.decode(response.toString());

          final List<dynamic>? data = responseJson['data'];

          if (data != null && data.isNotEmpty) {
            allCashRegisters.addAll(data.cast<Map<String, dynamic>>());

            final meta = responseJson['meta'];
            final lastPage = meta != null ? meta['last_page'] ?? 1 : 1;

            if (currentPage >= lastPage) {
              break; // Exit the loop when all pages have been fetched
            } else {
              currentPage++; // Move to the next page
            }
          } else {
            break; // Exit the loop if 'data' is empty or null
          }
        } else {
          return null; // Handle the error case as needed
        }
      }

      return allCashRegisters;
    } catch (e) {
      handleError(e, "getCashRegisterList");
      return null;
    }
  }

  Future<List<Map<String, dynamic>>?> getShiftsList({
    DateTime? startDate,
    DateTime? endDate,
    int? userId,
    int page = 1,
  }) async {
    final List<Map<String, dynamic>> allShifts = [];

    try {
      final response = await dio.get(
        "${_baseUrl().url}public/connector/api/cash-register",
        options: getOptions(),
        queryParameters: {
          "page": page,
          "per_page": 10,
          "user_id": userId,
          if (startDate != null)
            "start_date": dateToString(startDate, report: true),
          if (endDate != null) "end_date": dateToString(endDate, report: true),
          "order_id_by": "desc",
        },
      );

      if (response.statusCode == 200) {
        final dynamic responseJson = json.decode(response.toString());
        final List<dynamic>? data = responseJson['data'];

        if (data != null && data.isNotEmpty) {
          allShifts.addAll(data.cast<Map<String, dynamic>>());
          return allShifts;
        }
      }

      return null;
    } catch (e) {
      handleError(e, "getShiftsList");
      return null;
    }
  }

  Future<bool?> deletePurchase({required int purchaseID}) async {
    try {
      Response response = await dio.delete(
        "${_baseUrl().url}public/connector/api/purchase/$purchaseID",
        options: Options(headers: we2upHeaders(), method: 'DELETE'),
      );
      if (response.statusCode == 200) {
        final contactId = purchasesBox.get(purchaseID)!.contactId!;
        for (var p in (purchasesBox.get(purchaseID)!.paymentLines ?? [])) {
          await paymentsReportBox.delete(p.id);
        }
        await purchasesBox.delete(purchaseID);
        bool updatedContact = await getSpecifiedContact(contactId);
        if (updatedContact) {
          return true;
        } else {
          return false;
        }
      } else {
        return false;
      }
    } catch (e) {
      handleError(e, "deletePurchase");
      return null;
    }
  }

  Future<bool> purchaseReturn(
      {required PurchaseReturnToAPI purchaseReturnToAPI}) async {
    Map<String, dynamic> data = purchaseReturnToAPI.toJson();
    try {
      Options options = Options(headers: we2upHeaders(), method: 'POST');

      Response response = await dio.post(
        "${_baseUrl().url}public/connector/api/purchase-return",
        data: data,
        options: options,
      );

      if (response.statusCode == 201 || response.statusCode == 200) {
        final PurchaseReturn pr = PurchaseReturn.fromJson(response.data);
        purchaseReturnsBox.put(pr.id, pr);
        if (pr.returnParentPurchase != null) {
          purchasesBox.put(
            pr.returnParentPurchase!.id,
            pr.returnParentPurchase!,
          );
        }
        return pr.createdBy != null;
      } else {
        return false;
      }
    } catch (e) {
      handleError(e, "purchaseReturn");
      return false;
    }
  }

  Future<List<Map<String, dynamic>>?> getPurchasesReturnList({
    DateTime? startDate,
    DateTime? endDate,
    required bool summary,
    int? page,
  }) async {
    try {
      List<Map<String, dynamic>> allPurchasesReturn = [];
      int currentPage = page ?? 1;

      while (page == null || currentPage == page) {
        final response = await dio.get(
          "${_baseUrl().url}public/connector/api/list-purchase-return",
          options: getOptions(),
          queryParameters: {
            "per_page": page != null ? 10 : itemsPerPage,
            "page": currentPage,
            if (startDate != null)
              "start_date": dateToString(startDate, report: true),
            if (endDate != null)
              "end_date": dateToString(endDate, report: true),
            "summery": summary ? 1 : 0,
          },
        );

        if (response.statusCode == 200) {
          final List<Map<String, dynamic>> purchasesReturn =
              List<Map<String, dynamic>>.from(response.data['data']);
          allPurchasesReturn.addAll(purchasesReturn);

          if (page != null) {
            break; // Exit the loop if page is specified
          }

          final lastPage = response.data['meta']['last_page'];
          if (currentPage >= lastPage) {
            break; // Exit the loop when all pages have been fetched
          } else {
            currentPage++; // Move to the next page
          }
        } else {
          return [];
        }
      }

      return allPurchasesReturn;
    } catch (e) {
      handleError(e, "getPurchasesReturnList");
      return null;
    }
  }

  /// Purchase Management End

  /// Follow-ups Management
  Future<List<Map<String, dynamic>>?> getFollowUpsList(
      {DateTime? startDate, DateTime? endDate}) async {
    List<Map<String, dynamic>> followUpsList = [];
    int currentPage = 1;

    try {
      while (true) {
        final response = await Dio().get(
          "${_baseUrl().url}public/connector/api/crm/follow-ups?page=$currentPage",
          options: getOptions(),
          queryParameters: {
            "per_page": itemsPerPage,
            if (startDate != null)
              "start_date": dateToString(startDate, report: true),
            if (endDate != null)
              "end_date": dateToString(endDate, report: true),
            if (canAccessOwnCrmSchedule()) "user_id": loginData.userId,
          },
        );

        if (response.statusCode == 200) {
          final List<Map<String, dynamic>> followUps =
              List<Map<String, dynamic>>.from(response.data['data']);
          followUpsList.addAll(followUps);

          final lastPage = response.data['meta']['last_page'];
          if (currentPage >= lastPage) {
            break;
          } else {
            currentPage++;
          }
        } else {
          return [];
        }
      }

      return followUpsList;
    } catch (e) {
      handleError(e, "getFollowUpsList");
      return null;
    }
  }

  Future<bool> sendFollowUp(
      {required FollowUpToAPI followUpToAPI, int? followUpID}) async {
    Map<String, dynamic> data = followUpToAPI.toJson();
    try {
      String editID = followUpID != null ? "/$followUpID" : "";
      String method = followUpID != null ? "PUT" : "POST";
      Options options = Options(headers: we2upHeaders(), method: method);

      Response response = followUpID == null
          ? await dio.post(
              "${_baseUrl().url}public/connector/api/crm/follow-ups$editID",
              data: data,
              options: options)
          : await dio.put(
              "${_baseUrl().url}public/connector/api/crm/follow-ups$editID",
              data: data,
              options: options);

      if (response.statusCode == 201 || response.statusCode == 200) {
        final followUp = FollowUp.fromJson(response.data["data"][0]);
        followUpBox.put(followUp.id, followUp);
        return followUp.createdBy != null;
      } else {
        return false;
      }
    } catch (e) {
      handleError(e, "sendFollowUp");
      return false;
    }
  }

  /// Follow-ups Management End
}
