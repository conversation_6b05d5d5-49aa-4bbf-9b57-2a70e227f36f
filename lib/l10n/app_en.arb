{"@@locale": "en", "add_contact": "Add Contact", "add_contact_button": "Add Contact", "add_follow_up": "Add Follow-Up", "add_payment": "Add payment: ", "add_price_offer": "Add price offer", "additional_info": "Additional Info", "agent_name": "Agent Name", "all_branches": "All Branches", "choose_bluetooth_printer": "Choose Bluetooth printer", "current_printer": "Current Printer", "enable_bluetooth_message": "Please Enable Bluetooth to continue", "no_paired_devices": "There are no Bluetooth devices paired with the device. Connect your printer from the device’s Bluetooth settings.", "choose_default_bluetooth_device": "Please choose the default printer from the commercial data settings", "enable_bluetooth": "enable Bluetooth", "follow_up_ref": "Follow-Up No.", "continue_text": "Continue", "showPopupMenuToSelectNumber": "Show popup menu to select number?", "wait_products_load": "Processes will be uploaded after the products and customers are finished uploading", "font_type": "Print Font Type", "since": "Since", "agents_and_services": "Agents and Services", "shipping_companies": "Shipping Companies", "service_staff": "Service Staff", "tables": "Carries", "commission_seller": "Commission Seller", "color_scheme": "Color Scheme", "shifts": "Shifts", "shift_details": "Shift Details", "product_note": "Product Note", "close_shift": "End Shift", "font_size": "Print Font Size", "logout_confirmation": "Login Confirmation", "logout_confirmation_content": "Warning: Logging in on this device will log you out of all other devices. Are you sure you want to continue?", "can_login_title": "Database deletion alert", "can_login_content": "Continuing will result in deletion of the current database. Do you wish to continue?", "printer": "Printer", "paper_size": "Paper size", "all_price_groups": "All Price Groups", "all_brands": "all brands", "all_categories": "All categories", "all_customers": "All Customers", "all_locations": "all locations", "all_products": "All Products", "all_sales": "All sales", "all_sub_categories": "all sub categories", "all_suppliers": "All Suppliers", "allow_location_message": "Please allow access to your location to use this feature. You can enable location access in your device settings.", "allow_notification": "Allow Notification", "alphabetical_order": "alphabetical order", "alternate_mobile_number": "Alternate Mobile Number", "amount": "Amount: ", "amount_entered": "Amount Entered", "amount_withdrawn": "Amount Withdrawn", "apply": "apply", "attendance_registration": "Attendance registration", "balance": "Balance", "bank_transfer": "Bank Transfer", "bill_of_sale": "Bill of sale", "branch": "Branch: ", "branch_has_no_location": "This Branch Doesn't Have A Location", "branch_location": "Branch Location", "branch_location_range": "Branch Location Range", "brand": "brand", "browser_size": "Browser Size :", "business_data": "Business Data", "business_mobile_number": "Business Mobile Number", "business_name": "Business Name", "button_return": "Return", "call": "Call", "cancel": "cancel", "cancelled": "cancelled", "cancelled_status": "Cancelled", "card": "Card", "cart": "<PERSON><PERSON>", "cash": "Cash", "cash_left": "cash left: ", "cash_purchase": "cash purchase", "cash_sale": "Cash", "category": "category", "change_branch_location": "Change Branch Location", "cheque": "Cheque", "choose_customer": "Choose customer", "choose_location": "Choose Location", "city": "City", "clear": "clear", "clear_all_filters": "clear all filters?", "clockIn": "Record attendance", "clockInNote": "Attendance Note", "clockOut": "Record departure", "clockOutNote": "Departure Note", "closeCashRegister": "End Shift", "closeCashRegisterNote": "End Shift Note", "closest_location": "Closest Location", "closest_location_range": "Closest Location Range", "collect_amount": "Collect amount", "due_after_this_bill": "Due After Bill", "collected_amount": "Collected amount:", "collector_name": "Collector Name", "no_search_results": "There are no search results", "kilometer": "Kilometer", "combo_products": "Combo products", "completed": "Completed", "contact": "Contact", "contact_location_range": "Contact Location Range", "contact_type": "Contact Type", "contact_us": "Contact us", "contacts": "Contacts", "count": "Count", "country": "Country", "current_branch_location": "Current Location", "custom_pay_1": "Custom Payment 1", "custom_pay_2": "Custom Payment 2", "custom_pay_3": "Custom Payment 3", "custom_pay_4": "Custom Payment 4", "custom_pay_5": "Custom Payment 5", "custom_pay_6": "Custom Payment 6", "custom_pay_7": "Custom Payment 7", "customer": "Customer", "customer_code": "Customer Code", "supplier_code": "Supplier Code", "code_already_used": "This code already exists", "customer_collection": "customer collection: ", "customer_data": "Customer data", "customer_name": "Customer Name: ", "daily": "Daily", "dark_mode": "Dark Mode", "date": "Date: ", "date_and_time": "Date and Time", "day": "Day", "days": "Days", "default_text": "<PERSON><PERSON><PERSON>", "delete": "Delete", "delete_item": "Delete Item", "delete_question": "Are you sure you want to delete this item?", "delete_sell_item": "Delete Sell", "delete_sell_question": "Are you sure you want to delete this Sell?", "delivered": "delivered", "description": "Description", "discount": "Discount: ", "distance_between_user_and_branch": "Distance To Branch", "distance_from_company": "Distance From Company", "distance_from_contact": "Distance From Contact", "due": "due: ", "due_amount": "Due amount", "edit": "Edit", "edit_contact": "Edit Contact", "edit_contact_button": "Edit contact", "edit_expense": "Edit Expense", "edit_follow_up": "Edit Follow-Up", "edit_purchase": "<PERSON> Purchase", "edit_sell": "<PERSON>", "email": "Email", "email_address": "Email Address", "employee_note": "Employee note", "empty_password_validation": "Please enter your password", "empty_user_validation": "Please enter your username", "enable_stock": "Enable Stock?", "expense_category": "Expense Category: ", "opening_quantity": "Opening Stock Quantity", "recurring_expense": "Recurring Expense?", "recur_interval_expense": "Recur Interval", "recur_interval_type_expense": "Recur Interval Type", "purchase_price": "Purchase Price", "customer_collections": "Customer Collections", "supplier_payments": "Supplier Payments", "main_site": "Main Site", "customers_dues": "Customers Dues", "suppliers_dues": "Suppliers Dues", "pay": "Pay", "total_dues": "Total Dues", "receipt_of_cash": "Receipt Of Cash", "total_bills": "Total Bills", "total_paid": "Total Paid", "sum_report_paid": "Total Paid:", "sum_report_collected": "Total Collected:", "collect": "Collect", "end": "End", "end_date": "To Date", "expense": "Expense", "unit_price_without_tax": "Unit Price Without Tax: ", "unit_price_after_tax": "Unit Price With Tax: ", "edit_piece_price": "Edit Piece Price", "end_date_time": "End Date-Time", "end_rosary": "End Shift", "enter_an_expense": "Enter an expense", "exclusive": "Exclusive", "exit_app": "Exit App?", "exit_app_q": "Do you want to exit the app?", "exit_page": "Exit Page?", "exit_page_content": "Exiting the page will reset any action you took.", "expense_amount": "Expense amount:", "expense_note": "Expense note", "expenses": "Expenses", "expenses_and_revenues": "Expenses and Revenues", "total_revenues": "Total Revenues:", "total_expenses": "Total Expenses:", "expiry_period": "Expiry Period", "fetching_branches_message": "Updating Branches", "fetching_brands_message": "Updating Brands", "fetching_cash_registers_message": "Updating Cash Registers", "fetching_categories_message": "Updating Categories", "fetching_contacts_message": "Updating Contacts", "fetching_expenses_message": "Updating Expenses", "fetching_followups_message": "Updating Follow Ups", "fetching_payment_account_message": "Updating Payment Account", "fetching_price_groups_message": "Updating Price Group List", "fetching_products_message": "Updating Products", "fetching_purchases_message": "Updating Purchases", "fetching_purchases_returns_message": "Updating Purchases Returns", "fetching_sells_message": "Updating Sells", "fetching_sells_returns_message": "Updating Sells Returns", "fetching_taxes_message": "Updating Tax Rates", "fetching_units_message": "Updating Units", "fetching_users_message": "Updating Users", "field_cannot_be_empty": "Kindly fill in this field", "filter": "Filter", "filter_date": "Filter Date", "finish_edit": "Finish Edit", "finish_payment": "Finish payment", "first_name": "First Name", "fixed_discount": "Fixed Discount", "follow_up_contact": "Follow-Up Contact", "follow_ups": "Follow-ups", "home": "Home", "hour": "Hour", "in_stock": "in stock", "inclusive": "Inclusive", "invoice_amount": "Invoice amount", "invoice_c_name": "c. name", "invoice_c_number": "c. mobile", "invoice_invoice_quantities": "Invoice Quantities", "invoice_number_of_items": "Number Of Items", "invoice_previous_balance": "Previous Balance", "invoice_products": "Invoice products", "invoice_remaining_due": "Remaining Due", "invoice_return_details": "Return Details", "invoice_total_amount": "Total Amount", "invoice_total_paid": "Total Paid", "invoice_total_payment": "Total Payment", "invoice_value": "Invoice value: ", "is_refund_expense": "Expense refund?", "is_supplier": "Is Supplier?", "itinerary": "Itinerary", "itinerary_report": "Itinerary Report", "landline_number": "Landline Number", "language": "Language", "last_name": "Last Name", "last_transaction_location": "Last Transaction Location", "load_image": "Load Business Image", "loading": "Loading...", "location": "Location", "location_all": "All", "location_high": "Identical", "location_loading": "Loading Current Location", "location_low": "Wide Range", "location_medium": "Medium Range", "location_put_of_range": "Out Of Range", "location_range": "Location Range Settings", "location_reset": "Default Location Selected", "location_search": "location Search", "location_settings": "Location Settings", "revenues": "Revenues", "settings": "Settings", "location_state": "Location State", "locations": "Locations", "login": "<PERSON><PERSON>", "meeting": "Meeting", "minute": "Minute", "mobile": "mobile", "mobile_number": "Mobile Number", "mobile_size": "Mobile Size :", "modern_payment": "supplier payment: ", "monthly": "Monthly", "months": "Months", "my_location": "My Location", "name": "name", "net": "Net", "net_account": "Net account: ", "net_bill": "Net Bill:", "new_expense": "New Expense", "new_product": "New Product", "new_product_button": "Create Product", "no": "NO", "no_location": "No Location", "no_products": "No Products", "not_selected": "Choose Term Type", "stop_syncing": "Stop Syncing Transactions?", "showShopProductsGridView": "Show List of Products in POS?", "nothing": "nothing", "notify_before": "Notify Before", "notify_type": "Notify Type", "notify_via": "Notify Via", "number": "number: ", "offer_price": "Offer price", "offer_prices": "Offer Prices", "offlineMessage": "You are Offline!", "okay": "Okay", "open": "Open", "open_rosary": "Open Shift", "open_shift": "Please open a shift first", "open_shift_location": "Shift Opening Location", "open_shift_message": "Kindly open a shift to use this feature", "open_status": "Open", "openCashRegister": "Open shift", "openCashRegisterNote": "Open shift Note", "opening_balance": "Opening Balance", "order_sale": "Credit", "ordered": "ordered", "other": "Other", "packed": "packed", "paid_amount": "Paid amount", "paid_up": "paid up: ", "password": "Password", "pay_and_continue": "Pay and continue", "pay_purchases": "pay purchases: ", "pay_term_number": "Payment Term Number", "pay_term_type": "Payment Term Type", "payment": "Payment", "payment_account": "Payment account: ", "payment_data": "Payment data", "payment_method": "Payment method: ", "payment_note": "Payment note: ", "percentage_discount": "Percentage Discount", "permission_disclosure": "We2Up, the app, uses your location to register sales and purchases. It also helps you find nearby customers on the map. Not enabling location will result in limited app usage and may not be accepted by most business managers.", "permission_disclosure_title": "Location Permission Disclosure", "pounds": "pounds", "previous_balance": "Previous balance: ", "price_group_list": "Price Group", "primary_color": "Primary Color", "print": "Print", "print_invoice": "Print Invoice", "product": "Product", "product_description": "Product Description", "product_name": "Product Name", "product_single_dpp": "Default Buy Price", "product_single_dsp": "<PERSON><PERSON><PERSON><PERSON>", "product_weight": "Product Weight (Kg)", "sell_price": "<PERSON><PERSON>", "productLocations": "Product Locations", "priceGroups": "Price Groups", "products": "Products", "purchase": "purchase", "purchase_note": "Purchase note", "purchase_payment": "purchase payment", "purchase_products": "Purchase products", "purchase_return": "Purchase return", "purchase_return_payment": "Purchase return Payment", "purchase_returns": "Purchase returns", "sum_returns": "returns :", "purchase_value": "Purchase value:", "purchases": "Purchases", "quotation": "offer price", "customer_address": "Customer Address:", "receipt": "Purchase invoice", "recent_sales": "Recent sales", "ref_no": "Ref Number", "remaining": "remaining", "reset": "reset", "reset_ranges": "<PERSON><PERSON><PERSON>", "rosary_summary": "Shift summary", "sale_collection": "sales collection: ", "sale_collection_button": "Sale collection", "sale_note": "Sale note", "sale_return": "Sale return", "sale_return_payment": "Sale return payment", "sales": "Sales", "sales_returns": "Sales returns", "customer_group": "Customer Group", "service": "Service", "include_service_in_taxes": "Include Service In Taxes?", "service_price": "Service Price", "ad_space": "Ad Space", "save": "Save", "save_credentials_question": "Save Credentials?", "schedule_type": "Schedule Type", "scheduled": "Scheduled", "search": "Search", "search_location_range": "Search Location Range", "select_primary_color": "Select Primary Color", "selected_location": "Selected Location", "sell": "<PERSON>ll", "sell_id": "Invoice no.", "share": "Share PDF", "shipment_status": "Shipment Status:", "shipments": "Shipments", "shipped": "shipped", "shipping_address": "Shipping Address", "shipping_and_taxes_data": "Discount, Shipping and Taxes data", "shipping_details": "Shipping details", "shipping_fee": "shipping fee: ", "shipping_status": "Shipping status: ", "show_combo_products": "Show Combo Products", "sign_out": "Sign out", "signedOut": "Signed out!", "sms": "SMS", "start_date": "From Date", "start_date_time": "Start Date-Time", "status": "status", "sub_category": "sub category", "subtotal": "Subtotal: ", "sum": "Sum", "supplier": "Supplier", "supplier_data": "Supplier Data", "supplier_name": "Supplier Name: ", "supplier_name_note": "Business Name", "sync": "sync", "syncDone": "Sync done", "syncingOfflineData": "Syncing Offline Data", "table_discount": "Discount", "table_item": "<PERSON><PERSON>", "table_price": "Price", "table_qty": "Qty", "table_tax": "Tax", "table_total": "Total", "tax": "Tax: ", "tax_number": "Tax Number", "tax_type": "Product Tax Type", "title": "Title", "total": "total: ", "total_bill": "Total bill: ", "total_expense_amount": "Total expense:", "total_payable": "Total payable", "total_payment": "Total Payment", "total_purchase": "Total Purchase:", "total_quantities": "Total quantities", "total_selling": "Total selling: ", "transaction": "Transaction", "reference_number": "Reference Number", "transaction_date": "Transaction Date", "cannot_end_old_shift_title": "There is a shift that has been open for more than 24 hours", "cannot_end_old_shift_content": "Sorry but you can't continue. Please ask your administrator to close your shift first before trying again.", "old_shift_title": "Would you like to close the old shift?", "old_shift_content": "There has been a shift that has been open for more than 24 hours. Do you want to close it now so you can use the application?", "must_close_old_shifts": "Old shifts must be closed to continue!", "unavailable": "Unavailable", "unit": "unit", "unit_price": "Unit price: ", "unit_total_price": "Total price: ", "update": "update", "update_done": "Update Done Successfully", "update_start": "Updating", "user": "User", "user_name": "User Name", "username": "Username", "users_locations": "Users Locations", "welcome": "Welcome", "whatsapp": "Contact us on WhatsApp", "wrongCredentials": "Wrong credentials", "phoneNumberAlreadyUsed": "This phone number is already used", "yearly": "Yearly", "years": "Years", "yes": "Yes", "paid": "Paid", "credit": "Credit", "partial": "Partial", "offline": "Offline", "paidAmount": "Paid Amount: ", "payAmount": "Pay Amount", "profit_loss_report": "Profit / Loss Report", "reports": "Reports", "with_purchase_price": "(At purchase price)", "not_including_tax_and_discount": "(Excluding tax, discount)", "total_purchase_shipping_charge": "Total Purchase Shipping Charge:", "total_sell_shipping_charge": "Total Sell Shipping Charge:", "total_purchase_additional_expense": "Total Purchase Additional Expense:", "total_transfer_shipping_charges": "Total Transfer Shipping Charges:", "opening_stock": "Opening Stock:", "closing_stock": "Closing Stock:", "total_purchases": "Total Purchases:", "total_purchase_discount": "Total Purchase Discount:", "total_purchase_return": "Total Purchase Return:", "total_sell": "Total Sell:", "total_sell_discount": "Total Sell Discount:", "total_sell_return": "Total Sell Return:", "total_expense": "Total Expense:", "total_report_paid": "Total Paid:", "total_report_collected": "Total Collected:", "total_recovered": "Total Recovered:", "net_profit": "Net Profit:", "gross_profit": "Gross Profit:", "footer_text": "End of Receipt Text", "choose_currency": "choose currency used", "choose": "choose", "chosen_currency": "chosen currency:", "today": "Today", "yesterday": "Yesterday", "last7days": "Last 7 Days", "quotation_button": "offer", "shift": "Shift", "sku": "SKU code", "toggle_sound": "Toggle Sound on Product Addition", "last30days": "Last 30 Days", "thisMonth": "This Month", "lastMonth": "Last Month", "thisMonthLastYear": "This Month Last Year", "thisYear": "This Year", "lastYear": "Last Year", "customRange": "Custom Range", "sell_return_receipt": "Sales return invoice", "date_range": "Date Range", "no_transactions_in_range": "No Transactions In Specified Range", "zip_code": "ZIP Code", "no_business_locations": "Sorry, But there was an issue while retrieving your data!!", "use_arabic_numbers": "use Hindi numbers?", "total_weight": "Items weight", "duplicate_sku": "This code already exists.", "bluetooth_device": "Choose Bluetooth printer", "no_bluetooth_devices": "No Bluetooth printers connected", "cannot_connect_to_bluetooth": "Can't connect to this printer!", "print_error": "An error occurred during the printing process!", "choose_image": "Choose Image", "custom_service_price": "Custom Service Price:", "background_color": "Background Color", "background_color_message": "Choose Background Color", "print_sequence_message": "Arranging products during printing:", "first_added_last": "Added first is printed last", "first_added_first": "Added first is printed first", "alert_quantity": "Alert Quantity", "application_version": "application version:", "print_qr_code": "print QR Code", "discount_options": "Discount Print Type", "percentage_option": "Percentage", "amount_option": "Amount", "showUnitInReceipt": "Show units column in invoice?", "sync_problem_message": "A problem occurred while trying to synchronize offline operations.\nPlease contact us to review the issue.", "loading_contacts_and_products_message": "The list of products and customers is loading .. Please wait", "tracking_dialog_title": "Location Tracking", "tracking_dialog_subtitle": "To provide our services, we need to:", "tracking_dialog_point1": "Record transaction locations", "tracking_dialog_point2": "Track employee location during work hours", "deleting_sell": "Deleting Sell", "deleting_purchase": "Deleting Purchase...", "sell_deleted_successfully": "<PERSON><PERSON> deleted successfully", "purchase_deleted_successfully": "Purchase deleted successfully", "deletion_failed": "Deletion Failed", "payment_loading": "Payment Loading", "payment_sent_successfully": "Payment sent Successfully", "shipment_sent": "Shipment sent", "updating": "Updating", "scan_failed": "Scan Failed!", "product_creation_failed": "Product Creation Failed", "purchase_return_added_successfully": "Purchase return added successfully", "logged_out_from_another_device": "You have been logged out from another device", "error": "Error!", "sync_problem_occurred": "A problem occurred while trying to synchronize offline operations", "tracking_dialog_note": "Location tracking only occurs while using the app.", "tracking_dialog_cancel": "Cancel", "tracking_dialog_continue": "Continue", "revenue": "Revenue", "keyboard_shortcuts": "Keyboard Shortcuts", "cash_sale_completed": "Cash sale completed successfully", "partial_sale_completed": "Partial sale completed successfully", "credit_sale_completed": "Credit sale completed successfully", "quotation_created": "Price quotation created successfully", "bill_cleared": "<PERSON> cleared successfully", "new_bill_started": "New bill started", "payment_section_opened": "Payment section opened", "discount_section_opened": "Discount section opened", "product_search_opened": "Product search opened", "panel_cycled": "Panel navigation completed", "focus_cleared": "Focus cleared", "customer_add_screen": "Add customer screen opened", "number_incremented": "Number incremented", "number_decremented": "Number decremented", "bill_deleted": "Bill deleted successfully", "operation_cancelled": "Operation cancelled", "shortcut_f1_description": "Complete cash sale with receipt printing", "shortcut_f2_description": "Process partial payment sale", "shortcut_f3_description": "Create credit sale (no immediate payment)", "shortcut_f4_description": "Open payment methods and focus payment field", "shortcut_f5_description": "Open discount section and focus discount field", "shortcut_f6_description": "Save current bill as price quotation", "shortcut_f7_description": "Clear current bill and start fresh", "shortcut_f8_description": "Open product search dropdown", "shortcut_f9_description": "Cycle through expandable panels", "shortcut_enter_description": "Open product search (when not in text field)", "shortcut_tab_description": "Navigate through different panels", "shortcut_plus_description": "Increment number in focused field", "shortcut_minus_description": "Decrement number in focused field", "shortcut_shift_delete_description": "Delete current bill with confirmation", "shortcut_ctrl_shift_c_description": "Open add customer/contact screen", "shortcut_escape_description": "Clear focus and close dropdowns", "validation_customer_required": "Customer selection is required", "validation_products_required": "Please add products to the cart", "validation_quotation_permission": "You don't have permission to create quotations", "validation_credit_default_customer": "Credit sales cannot be made to default customer", "validation_commission_agent_required": "Commission agent selection is required", "validation_insufficient_stock": "Insufficient stock for some products", "validation_payment_accounts_required": "Please select payment accounts", "validation_payment_amounts_required": "Please enter valid payment amounts", "validation_field_required": "This field is required", "validation_invalid_amount": "Please enter a valid amount", "success_operation": "Operation completed successfully", "error_operation": "Operation failed", "warning_operation": "Warning: Please check your input", "info_operation": "Information", "keyboard_shortcuts_help": "Keyboard Shortcuts Help", "keyboard_shortcuts_help_description": "Use these shortcuts to speed up your workflow", "sales_shortcuts": "Sales Shortcuts", "navigation_shortcuts": "Navigation Shortcuts", "field_operation_shortcuts": "Field Operations", "advanced_shortcuts": "Advanced Operations"}